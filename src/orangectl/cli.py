import argparse

from orangectl.commands.connect import connect_cmd
from orangectl.commands.pendingjob import pendingjob
from orangectl.commands.quota import quota
from orangectl.commands.repo import repo_cmd

def main():
    parser = argparse.ArgumentParser(prog="orangectl", description="orange helper commands")
    subparsers = parser.add_subparsers(dest="subcommand", help="Available subcommands")

    # connect command
    connect_parser = subparsers.add_parser("connect", help="Connects to Azure VPN then tailscale triggerring authentication if needed. After connecting to tailscale, it disconnects the Azure VPN.")
    connect_parser.set_defaults(func=connect_cmd)

    # pendingjob command
    pendingjob_parser = subparsers.add_parser("pendingjob", help="View and manage pending GPU jobs in Kubernetes clusters. This tool queries pool objects with GPU requirements that are currently waiting to be scheduled.")
    pendingjob_parser.set_defaults(func=pendingjob)
    pendingjob_parser.add_argument("-f", "--file", type=argparse.FileType('r'), help="Path to the YAML file containing pool objects.")
    pendingjob_parser.add_argument("-c", "--context", help="Kubernetes context to use. If not specified, uses the current context.")
    pendingjob_parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging.")

    # quota command
    quota_parser = subparsers.add_parser("quota", help='Display quota information for the Orange cluster')
    quota_parser.set_defaults(func=quota)
    quota_parser.add_argument('-f', '--file', type=argparse.FileType('r'), help='Path to YAML file containing quota information, used for debugging')
    quota_parser.add_argument('-c', '--cluster', help='Name of the Orange cluster to use')
    quota_parser.add_argument('-t', '--team', help='Filter results by team name, partial matches are supported and are case-insensitive')

    # repo command
    repo_parser = subparsers.add_parser("repo", help="Navigate versions of the code in the `orange/main` branch of the `torchflow-mirror` repo.")
    repo_parser.add_argument("--repo-path", dest="repo_path", default=".", help="Path to torchflow-mirror repo (current dir by default)")
    repo_sub = repo_parser.add_subparsers(dest="repo_command", required=True)
    # repo list
    repo_list = repo_sub.add_parser("list", help="List available oai- tags")
    repo_list.add_argument("--details", action="store_true", help="Show detailed commit table")
    repo_list.add_argument("--repo-path", dest="repo_path", help=argparse.SUPPRESS, default=argparse.SUPPRESS)
    # repo checkout
    repo_co = repo_sub.add_parser("checkout", help="Checkout MSFT head for a tag")
    repo_co.add_argument("tag", help="Tag to checkout, e.g. oai-YYYYMMDD")
    repo_co.add_argument("--repo-path", dest="repo_path", help=argparse.SUPPRESS, default=argparse.SUPPRESS)
    repo_parser.set_defaults(func=repo_cmd)

    args = parser.parse_args()

    # If the user didn't provide any subcommand, print help
    if not args.subcommand:
        parser.print_help()
    else:
        # Dispatch to the selected subcommand function
        args.func(args)

if __name__ == "__main__":
    main()
