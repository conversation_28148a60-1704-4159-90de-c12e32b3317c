import subprocess
import sys
import time

VPN_NAME = "MSFT-AzVPN-Manual"
VPN_TIMEOUT = 30

def connect_vpn_macos():
    print(f"🔌 Connecting VPN: {VPN_NAME}")
    subprocess.run(["scutil", "--nc", "start", VPN_NAME], check=True)

def wait_for_vpn_connected_macos():
    print(f"⏳ Waiting for VPN {VPN_NAME}...")
    for _ in range(VPN_TIMEOUT):
        status = subprocess.check_output(["scutil", "--nc", "status", VPN_NAME]).decode().splitlines()[0]
        print(f"🔄 VPN status: {status}")
        if status == "Connected":
            print(f"✅ VPN {VPN_NAME} connected.")
            return
        time.sleep(1)
    print(f"❌ Timeout reached waiting for VPN {VPN_NAME}")
    sys.exit(1)

def disconnect_vpn_macos():
    print(f"🛑 Disconnecting VPN: {VPN_NAME}")
    subprocess.run(["scutil", "--nc", "stop", VPN_NAME], check=True)

def connect_vpn_wsl():
    print(f"🔌 Connecting VPN: {VPN_NAME} (WSL)")
    subprocess.run(["powershell.exe", "-Command", f"rasdial '{VPN_NAME}'"], check=True)

def wait_for_vpn_connected_wsl():
    print(f"⏳ Waiting for VPN {VPN_NAME}...")
    for _ in range(VPN_TIMEOUT):
        result = subprocess.check_output(["powershell.exe", "-Command", "rasdial"]).decode()
        print(f"🔄 VPN status:\n{result}")
        if VPN_NAME in result:
            print(f"✅ VPN {VPN_NAME} connected (WSL).")
            return
        time.sleep(1)
    print(f"❌ Timeout reached waiting for VPN {VPN_NAME} (WSL)")
    sys.exit(1)

def disconnect_vpn_wsl():
    print(f"🛑 Disconnecting VPN: {VPN_NAME} (WSL)")
    subprocess.run(["powershell.exe", "-Command", f"rasdial '{VPN_NAME}' /disconnect"], check=True)
