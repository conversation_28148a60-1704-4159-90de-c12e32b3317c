import platform
import os
import sys

TAILSCALE_CMD = "/Applications/Tailscale.app/Contents/MacOS/Tailscale"

def is_macos():
    return platform.system() == "Darwin"

def is_wsl():
    return "microsoft" in platform.release().lower()

def check_wsl_tailscale_setup():
    if not (os.path.isfile(TAILSCALE_CMD) and os.access(TAILSCALE_CMD, os.X_OK)):
        if is_wsl():
            print(f"❌ Tailscale symlink at {TAILSCALE_CMD} is not available.")
            print("🔗 Please follow the WSL Tailscale setup instructions at: https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4621/WSL-Setup?anchor=configure-wsl-tailscale")
            sys.exit(1)
        else:
            print(f"❌ Tailscale not found or not executable at {TAILSCALE_CMD}")
