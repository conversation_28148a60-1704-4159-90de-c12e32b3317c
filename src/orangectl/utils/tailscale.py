import subprocess
import sys

TAILSCALE_CMD = "/Applications/Tailscale.app/Contents/MacOS/Tailscale"

def tailscale_up():
    print("🔐 Starting Tailscale...")
    # Check if already connected
    result = subprocess.run([TAILSCALE_CMD, "status"], capture_output=True)
    if result.returncode == 0:
        print("✅ Tailscale is already connected.")
        return
    subprocess.run([TAILSCALE_CMD, "up", "--accept-dns", "--accept-routes", "--login-server=https://login.tailscale.com/"], check=True)
    print("✅ Tailscale is up.")

def assert_green_tailscale_tailnet():
    try:
        result = subprocess.run([TAILSCALE_CMD, "status", "--json"], check=True, capture_output=True)
        status = result.stdout.decode('utf-8')
        import json
        status_json = json.loads(status)
        tailnet_name = status_json.get("CurrentTailnet", {}).get("Name")
        if tailnet_name == "green.microsoft.com":
            print("✅ Tailscale is connected to green tailnet.")
        else:
            print(f"❌ Tailscale is not connected to green tailnet. Current tailnet: {tailnet_name}")
            sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error getting Tailscale status: {e}")
        sys.exit(1)
