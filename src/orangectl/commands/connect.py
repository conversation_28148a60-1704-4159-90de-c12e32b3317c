from orangectl.utils.os_utils import is_macos, is_wsl, check_wsl_tailscale_setup
from orangectl.utils.vpn import (
    connect_vpn_macos,
    wait_for_vpn_connected_macos,
    disconnect_vpn_macos,
    connect_vpn_wsl,
    wait_for_vpn_connected_wsl,
    disconnect_vpn_wsl
)
from orangectl.utils.tailscale import tailscale_up, assert_green_tailscale_tailnet

def connect_cmd(args):
    print("🍊 Connecting to orange...")

    if is_macos():
        connect_vpn_macos()
        wait_for_vpn_connected_macos()
        tailscale_up()
        disconnect_vpn_macos()
    elif is_wsl():
        check_wsl_tailscale_setup()
        connect_vpn_wsl()
        wait_for_vpn_connected_wsl()
        tailscale_up()
        disconnect_vpn_wsl()
    else:
        print("❌ Unsupported OS. Only macOS and WSL are supported.")
        exit(1)

    assert_green_tailscale_tailnet()
    print("🎉 Connection complete. 🍊")
