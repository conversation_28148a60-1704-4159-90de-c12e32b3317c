import yaml
import subprocess
import sys

def load_pools_from_file(file_path):
    """Load pool objects from a YAML file."""
    with open(file_path, 'r') as file:
        data = yaml.safe_load(file)
    return data.get('items', [])

def load_pools_from_kubectl(context=None):
    """
    Load pool objects using 'kubectl get pool -A -o yaml'.

    Args:
        context: Optional kubectl context to use. If None, uses current context.
    """
    cmd = ["kubectl"]
    if context:
        cmd.extend(["--context", context])
    cmd.extend(["get", "pool", "-A", "-o", "yaml"])

    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        data = yaml.safe_load(result.stdout)
        return data.get('items', [])
    except subprocess.CalledProcessError as e:
        print(f"Error invoking kubectl: {e.stderr}", file=sys.stderr)
        sys.exit(1)

def get_current_context():
    """Get the current kubectl context."""
    try:
        result = subprocess.run(
            ["kubectl", "config", "current-context"],
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error getting current context: {e.stderr}", file=sys.stderr)
        sys.exit(1)

def pendingjob(args):
    # Load pool objects based on the provided options
    if args.file:
        all_pools = load_pools_from_file(args.file)
        if args.verbose:
            print(f"[INFO] Loaded {len(all_pools)} pool objects from file.")
    else:
        # Use specified context or current context
        context = args.context
        if not context:
            context = get_current_context()
            print(f"[INFO] Using current kubectl context: {context}")
        else:
            print(f"[INFO] Using specified kubectl context: {context}")

        all_pools = load_pools_from_kubectl(context)
        if args.verbose:
            print(f"[INFO] Loaded {len(all_pools)} pool objects from kubectl.")

    # Step 1: Get all GPU pool objects
    gpu_pools = []
    for pool in all_pools:
        annotations = pool.get("spec", {}).get("workers", {}).get("template", {}).get("metadata", {}).get("annotations", {})
        gpu_limit_str = annotations.get("brix.openai.com/resource-limit-gpu", "0")
        try:
            gpu_limit = int(gpu_limit_str)
            if gpu_limit > 0:
                gpu_pools.append(pool)
                if args.verbose:
                    print(f"[DEBUG] Found GPU pool with limit: {gpu_limit}")
        except ValueError:
            if args.verbose:
                print(f"[WARNING] Failed to parse GPU limit '{gpu_limit_str}' as integer")
    if args.verbose:
        print(f"[INFO] Found {len(gpu_pools)} GPU pool objects.")

    # Step 2: Filter pending pool objects
    pending_gpu_pools = []
    for pool in gpu_pools:
        status = pool.get("status", {})
        conditions = status.get("conditions", [])
        scheduled_false = False
        suspended_false = False

        for cond in conditions:
            if cond.get("type") == "Scheduled" and cond.get("status") == "False":
                scheduled_false = True
            if cond.get("type") == "Suspended" and cond.get("status") == "False":
                suspended_false = True

        if scheduled_false and suspended_false:
            pending_gpu_pools.append(pool)
    if args.verbose:
        print(f"[INFO] Found {len(pending_gpu_pools)} pending GPU pool objects.")

    # Step 3: Extract key attributes
    formatted_data = []
    for pool in pending_gpu_pools:
        metadata = pool.get("metadata", {})
        spec = pool.get("spec", {})
        annotations = pool.get("spec", {}).get("workers", {}).get("template", {}).get("metadata", {}).get("annotations", {})
        pool_name = metadata.get("name", "Unknown")
        phases = pool.get("status", {}).get("workers", {}).get("phases", {})
        message = pool.get("status", {}).get("workers", {}).get("message", {})
        team_name = annotations.get("brix.openai.com/target-quotas", "Unknown")
        priority = pool.get("spec", {}).get("workers", {}).get("template", {}).get("spec", {}).get("priorityClassName", "Unknown")
        if args.verbose:
            print(f"[INFO] Extracted priority {priority}")
        total_node_required = spec.get("workers", {}).get("replicas", "Unknown")
        creation_timestamp = metadata.get("creationTimestamp", "Unknown")
        namespace = metadata.get("namespace", "Unknown")

        formatted_data.append({
            "Team Name": team_name,
            "Priority": priority,
            "Phase": phases,
            "Message": message,
            "Creation Timestamp": creation_timestamp,
            "Total Node Required": total_node_required,
            "Namespace": namespace,
            "Pool Name": pool_name
        })
    if args.verbose:
        print(f"[INFO] Extracted attributes for {len(formatted_data)} pool objects.")

    # Step 4: Sort the data
    sorted_data = sorted(
        formatted_data,
        key=lambda x: (x["Team Name"], (x["Priority"] or ""), x["Creation Timestamp"], -int(x["Total Node Required"] or "0"), x["Namespace"])
    )
    if args.verbose:
        print(f"[INFO] Sorted {len(sorted_data)} pool objects.")

    # Step 5: Display the data in table format
    print(f"This table lists all pending jobs in this cluster")
    print(f"{'Team Name':<25} {'Priority':<15}{'Message':<10}  {'Creation Timestamp':<25} {'Total Node':<15} {'Namespace':<15} {'Pool Name':<30}")
    for entry in sorted_data:
        # Convert all values to strings, replacing None with empty string
        team_name = str(entry.get('Team Name') or "")
        priority = str(entry.get('Priority') or "")
        phase = str(entry.get('Phase') or "")
        message = str(entry.get('Message') or "")
        creation_timestamp = str(entry.get('Creation Timestamp') or "")
        total_node = str(entry.get('Total Node Required') or "")
        namespace = str(entry.get('Namespace') or "")
        pool_name = str(entry.get('Pool Name') or "")

        print(f"{team_name:<25} {priority:<15} {message:<10} {creation_timestamp:<25} {total_node:<15} {namespace:<15} {pool_name:<30}")
