import argparse
import subprocess
import sys
from pathlib import Path


def validate_repo(repo_path):
    # Check git repo
    git_check = subprocess.run(
        ["git", "rev-parse", "--git-dir"], cwd=repo_path, capture_output=True, text=True
    )
    if git_check.returncode != 0:
        print(f"Error: {repo_path} is not a git repository.", file=sys.stderr)
        print("Please run within a clone of the 'torchflow-mirror' repo.", file=sys.stderr)
        sys.exit(1)
    # Check branch exists
    branch_check = subprocess.run(
        ["git", "rev-parse", "--verify", "origin/orange/main"],
        cwd=repo_path, capture_output=True, text=True
    )
    if branch_check.returncode != 0:
        print("Error: 'origin/orange/main' not found.", file=sys.stderr)
        print("Make sure you have a clone of 'torchflow-mirror' with that remote branch.", file=sys.stderr)
        sys.exit(1)


def list_tags(repo_path, details):
    # copy list_tags implementation
    # Extract merge entries
    proc = subprocess.run(
        ["git", "log", "origin/orange/main", "--grep", "Start the merging-rebase to",
         "--pretty=format:%H|%P|%s", "--reverse"],
        cwd=repo_path, capture_output=True, text=True
    )
    if proc.returncode != 0:
        print("Error: Failed to retrieve git log.", file=sys.stderr)
        sys.exit(1)
    entries = []
    for line in proc.stdout.splitlines():
        parts = line.split("|", 2)
        if len(parts) != 3:
            continue
        commit, parents_str, subject = parts
        if subject.startswith("Start the merging-rebase to "):
            tag = subject[len("Start the merging-rebase to "):]
            parents = parents_str.split()
            entries.append((tag, parents, commit))
    # sort and map heads
    entries_desc = list(reversed(entries))
    head = subprocess.run(["git", "rev-parse", "HEAD"], cwd=repo_path, capture_output=True, text=True).stdout.strip()
    company_heads = {}
    for i in range(len(entries) - 1):
        nxt = entries[i+1][1]
        if len(nxt) > 1:
            company_heads[entries[i][0]] = nxt[1]
    if entries:
        latest_tip = subprocess.run(["git", "rev-parse", "origin/orange/main"], cwd=repo_path, capture_output=True, text=True).stdout.strip()
        company_heads[entries[-1][0]] = latest_tip
    # output
    if details:
        print("Base tag name".ljust(25) + "Top commit".ljust(12) + "Start merging-rebase commit")
        print("-" * 65)
        for tag, parents, merge in entries_desc:
            top = company_heads.get(tag, "N/A")[:10] if tag in company_heads else "N/A"
            merge_short = merge[:10]
            marker = "* " if company_heads.get(tag) == head else "  "
            if marker.startswith("*"):
                print(f"{marker}\033[32m{tag.ljust(23)}\033[0m {top.ljust(10)} {merge_short}")
            else:
                print(f"{marker}{tag.ljust(23)} {top.ljust(10)} {merge_short}")
    else:
        print("Available tags in origin/orange/main (run `git fetch` to update):")
        print("-" * 40)
        for tag, _, _ in entries_desc:
            if company_heads.get(tag) == head:
                # highlight current tag in green
                print(f"* \033[32m{tag}\033[0m")
            else:
                print(f"  {tag}")


def checkout_tag(repo_path, tag):
    # copy checkout_tag implementation
    proc = subprocess.run(
        ["git", "log", "origin/orange/main", "--grep", "Start the merging-rebase to",
         "--pretty=format:%H|%P|%s", "--reverse"],
        cwd=repo_path, capture_output=True, text=True
    )
    entries = []
    for line in proc.stdout.splitlines():
        parts = line.split("|", 2)
        if len(parts) != 3:
            continue
        commit, parents_str, subject = parts
        if subject.startswith("Start the merging-rebase to "):
            tagname = subject[len("Start the merging-rebase to "):]
            parents = parents_str.split()
            entries.append((tagname, parents, commit))
    for i, (tagname, parents, _) in enumerate(entries):
        if tagname == tag:
            if i + 1 < len(entries):
                nxt = entries[i+1][1]
                if len(nxt) > 1:
                    target = nxt[1]
                    result = subprocess.run(["git", "checkout", target], cwd=repo_path)
                    if result.returncode != 0:
                        print(f"Error: Failed to checkout to {target}.", file=sys.stderr)
                        sys.exit(1)
                    return
            else:
                print("Checking out to 'orange/main'; run 'git pull' to update.")
                result = subprocess.run(["git", "checkout", "orange/main"], cwd=repo_path)
                if result.returncode != 0:
                    print("Error: Failed to checkout to 'orange/main'.", file=sys.stderr)
                    sys.exit(1)
                return
    print(f"Tag {tag} not found.", file=sys.stderr)
    sys.exit(1)


def repo_cmd(args):
    repo = args.repo_path
    validate_repo(repo)
    if args.repo_command == 'list':
        list_tags(repo, args.details)
    elif args.repo_command == 'checkout':
        checkout_tag(repo, args.tag)
