import sys
import subprocess
import yaml
from tabulate import tabulate
from termcolor import colored, cprint


def get_quota_yaml(cluster=None):
    """Get quota information from kubectl command"""
    cmd = ["kubectl", "get", "quotas", "-o", "yaml"]
    if cluster:
        cmd.extend(["--cluster", cluster])

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error executing kubectl command: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)


def parse_yaml_file(file_path):
    """Parse YAML from a file"""
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        print(f"Error reading YAML file: {e}")
        sys.exit(1)


def extract_quota_info(yaml_data, team_filter=None):
    """Extract relevant quota information from YAML data"""
    quota_info = []
    usage_info = []
    availability_info = []

    if not yaml_data or 'items' not in yaml_data:
        print("No quota items found in the YAML data")
        return quota_info, usage_info, availability_info

    for item in yaml_data['items']:
        if item['kind'] == 'Quota':
            team_name = item['metadata']['name']

            # Skip if team filter is provided and doesn't match
            if team_filter and team_filter.lower() not in team_name.lower():
                continue

            if 'allocations' in item['spec']:
                for i, allocation in enumerate(item['spec']['allocations']):
                    cluster_name = allocation.get('cluster', 'N/A')
                    quota_count = allocation.get('quotaCount', 'N/A')

                    # Get the usable quota from status.allocations.allocatedCount
                    usable_quota = 'N/A'
                    if ('status' in item and
                        'allocations' in item['status'] and
                        i < len(item['status']['allocations'])):
                        usable_quota = item['status']['allocations'][i].get('allocatedCount', 'N/A')

                    # We maintain Quota resources with zero assigned quota for some teams just to enable Perhonen to report low-priority usage.
                    # Don't show assigned quota for such cases unless explicitly matching the team_filter.
                    if quota_count != 0 or team_filter:
                        quota_info.append({
                            'Team Name': team_name,
                            'Cluster': cluster_name,
                            'Assigned Quota': quota_count,
                            'Usable Quota': usable_quota
                        })

                    # Extract quota usage information
                    if ('status' in item and
                        'allocations' in item['status'] and
                        i < len(item['status']['allocations']) and
                        'quotaStatusPerLocation' in item['status']['allocations'][i]):

                        for location_status in item['status']['allocations'][i]['quotaStatusPerLocation']:
                            if 'userUsageEntry' in location_status and location_status['userUsageEntry']:
                                for user_entry in location_status['userUsageEntry']:
                                    # Skip Mid priority entries (case-insensitive)
                                    priority = user_entry.get('priority', 'N/A')
                                    if isinstance(priority, str) and priority.lower() == 'mid':
                                        continue

                                    usage_info.append({
                                        'Cluster': cluster_name,
                                        'Team Name': team_name,
                                        'Priority': priority,
                                        'User': user_entry.get('user', 'N/A'),
                                        'Count': user_entry.get('count', 'N/A')
                                    })

                    # Extract availability information
                    if ('status' in item and
                        'allocations' in item['status'] and
                        i < len(item['status']['allocations']) and
                        'availability' in item['status']['allocations'][i]):

                        availability = item['status']['allocations'][i]['availability']
                        for priority, count in availability.items():
                            # Skip Mid priority entries (case-insensitive)
                            if isinstance(priority, str) and priority.lower() == 'mid':
                                continue

                            availability_info.append({
                                'Cluster': cluster_name,
                                'Team Name': team_name,
                                'Priority': priority,
                                'Count': count
                            })

    return quota_info, usage_info, availability_info

def colorize_priority(data_table: list[list], priority_field_index: int, quota_value_field_index: int = None):
    for row in data_table:
        colorize_priority_in_row(row, priority_field_index, quota_value_field_index)

def colorize_priority_in_row(data_row: list, priority_index: int, quota_value_index: int = None):
    """Colorize the Priority field and the quota usage or availability field in a data row"""
    priority = data_row[priority_index]
    if quota_value_index is None:
        quota_value_index = priority_index + 1
    if isinstance(priority, str):
        # Match the colors in the Quota dashboard: https://ai-infra-ephgecb0cucpbkg8.wus2.grafana.azure.com/d/ceml927bmf9j4c/quota
        color = None
        if priority == 'Critical':
            color = (250, 100, 0)
        elif priority == 'High':
            color = (255, 166, 176)
        elif priority == 'Low':
            color = (192, 216, 255)

        if color:
            data_row[priority_index] = colored(priority, color)
            data_row[quota_value_index] = colored(data_row[quota_value_index], color)

def display_quota_info(quota_info, usage_info, availability_info):
    """Display quota information in formatted tables"""
    if not quota_info:
        print("No quota information to display.")
        return

    # Create a more prominent header for the first table
    print("\n" + "="*50)
    print("               QUOTA OVERVIEW               ")
    print("="*50 + "\n")

    headers = ['Team Name', 'Cluster', 'Assigned Quota', 'Usable Quota']
    table_data = [[item[header] for header in headers] for item in quota_info]

    print(tabulate(table_data, headers=headers, tablefmt="grid"))

    # Display usage information if available
    if usage_info:
        # Add some spacing between tables
        print("\n\n" + "="*50)
        print("               QUOTA USAGE               ")
        print("="*50 + "\n")

        # usage_info is a list of (dictionary) entries with these properties:
        #  {
        #     'Cluster': cluster_name,
        #     'Team Name': team_name,
        #     'Priority': priority,
        #     'User': user,
        #     'Count': resource_count
        #  )

        usage_headers = ['Cluster', 'Team Name', 'Priority', 'User', 'Count']
        usage_data = [[item[header] for header in usage_headers] for item in usage_info]
        # Sort usage data by Cluster, then by Team Name, then by Count (descending) and finally by User
        usage_data.sort(key=lambda x: (x[0], x[1], x[2], -x[4], x[3]))
        colorize_priority(usage_data, priority_field_index=2, quota_value_field_index=4)
        print(tabulate(usage_data, headers=usage_headers, tablefmt="grid"))

        # Also summarize the usage by cluster and priority
        print("\nUSAGE SUMMARY BY WORKLOAD PRIORITY\n")
        summary_data = {}
        for item in usage_info:
            cluster = item['Cluster']
            priority = item['Priority']
            count = item['Count']
            if cluster not in summary_data:
                summary_data[cluster] = {}
            if priority not in summary_data[cluster]:
                summary_data[cluster][priority] = 0
            summary_data[cluster][priority] += count
        summary_table_data = []
        for cluster, priorities in summary_data.items():
            for priority, count in priorities.items():
                summary_table_data.append([cluster, priority, count])
        summary_headers = ['Cluster', 'Priority', 'Total Count']
        summary_table_data.sort(key=lambda x: (x[0], x[1]))
        colorize_priority(summary_table_data, priority_field_index=1)
        print(tabulate(summary_table_data, headers=summary_headers, tablefmt="grid"))

        # And finally summarize the total cluster usage
        print("\nTOTAL USAGE SUMMARY:")
        total_usage_data = {}
        for item in usage_info:
            cluster = item['Cluster']
            count = item['Count']
            if cluster not in total_usage_data:
                total_usage_data[cluster] = 0
            total_usage_data[cluster] += count
        # To reduce clutter, print just a line by cluster.
        for cluster, total_count in total_usage_data.items():
            print(f"  {cluster}: {total_count}")

    # Display availability information if available
    if availability_info:
        # Add some spacing between tables
        print("\n\n" + "="*50)
        print("             QUOTA AVAILABILITY             ")
        print("="*50 + "\n")

        # Group availability data by team
        teams = {}
        for item in availability_info:
            team_name = item['Team Name']
            if team_name not in teams:
                teams[team_name] = []
            teams[team_name].append(item)

        # Display availability for each team separately
        availability_headers = ['Cluster', 'Team Name', 'Priority', 'Count']
        first_team = True

        for team_name, team_data in teams.items():
            if not first_team:
                # Add separator between teams
                print("\n" + "-"*50 + "\n")
            else:
                first_team = False

            print(f"Team: {team_name}\n")
            team_table_data = [[item[header] for header in availability_headers] for item in team_data]
            colorize_priority(team_table_data, priority_field_index=2)
            print(tabulate(team_table_data, headers=availability_headers, tablefmt="grid"))


def quota(args):
    if args.file:
        yaml_data = parse_yaml_file(args.file)
    else:
        yaml_data = yaml.safe_load(get_quota_yaml(args.cluster))

    quota_info, usage_info, availability_info = extract_quota_info(yaml_data, args.team)

    if not quota_info:
        if args.team:
            print(f"No quota information found for team '{args.team}'")
        else:
            print("No quota information found")
        return

    display_quota_info(quota_info, usage_info, availability_info)

    print("\n" + "="*50)
    print("\U00002139  To query pools/jobs pending allocation, run `orangectl pendingjob`. " +
          "See the other diagnostic options in the Quota guide: https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6381/Orange-Quota?anchor=troubleshooting.\n")
