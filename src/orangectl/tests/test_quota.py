# write test to check that pendingjob comamnd works with the yaml file provided in laptop-utils
import os
import pytest
from orangectl.commands.quota import quota
from types import SimpleNamespace

def test_quota():
    # Assuming the yaml file is in the current directory
    yaml_file = 'laptop-utils/orange_get_quota-testdata.yaml'
    if not os.path.exists(yaml_file):
        pytest.skip(f"YAML file {yaml_file} does not exist.")

    args = SimpleNamespace(file=yaml_file, team="test_team")
    quota(args)

    assert "Pending job created successfully", "Expected output not found in command result"
