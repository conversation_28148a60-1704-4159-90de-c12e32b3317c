# orangectl  
![Orange Swiss Knife](https://via.placeholder.com/80/ffa500/000000?text=KNIFE)


**orangectl** is a Swiss Army Knife CLI for Orange users. It aims to streamline various tasks, including configuring and managing VPN connections and Tailscale connectivity across macOS and WSL.

---

## Table of Contents
- [Overview](#overview)
- [Features](#features)
- [Installation](#installation)
- [Usage](#usage)
- [Requirements](#requirements)
- [Development](#development)
- [License](#license)

---

## Overview
This CLI centralizes key operations for Orange users—everything from setting up a VPN connection to bringing up Tailscale, and in the future, other tasks that improve the Orange user experience.

---


## Usage
### Connect
```bash
orangectl connect
```
- Connects to your Azure VPN, starts Tailscale (triggering authentication if needed), and then disconnects the VPN once Tailscale is up and running.

### Future Commands
A future `diagnose` subcommand, for example, might validate environment readiness and highlight any setup problems.

---


## Development
### Adding a New Command
1. Create a new file in `orangectl/commands/` (e.g. `diagnose.py`).
2. Implement your logic.
3. Register the command in your CLI entry point (using argparse or click).

### Testing
```bash
# Directly with Python
python -m orangectl.cli diagnose

# After editable install
orangectl diagnose
```

---
