# Build Stage
FROM mcr.microsoft.com/oss/go/microsoft/golang:1.24 AS builder

ENV CGO_ENABLED=0

# Set up working directory
WORKDIR /src

RUN --mount=target=/src,type=bind,source=. --mount=type=cache,target=/root/.cache/go-build go build -o /juicer

# Final Stage
FROM mcr.microsoft.com/cbl-mariner/base/core:2.0 AS base
COPY --from=builder /juicer /juicer

# ensures local time is PDT
RUN tdnf install -y tzdata && ln -sf /usr/share/zoneinfo/America/Los_Angeles /etc/localtime
ENV TZ=America/Los_Angeles

ENTRYPOINT ["/juicer"]
