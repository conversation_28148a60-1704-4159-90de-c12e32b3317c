/*
Copyright (c) 2021 OpenAI
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"

	v1alpha1 "github.com/openai/brix/pkg/apis/brix/v1alpha1"
	scheme "github.com/openai/brix/pkg/client/clientset/versioned/scheme"
)

// GitsGetter has a method to return a GitInterface.
// A group's client should implement this interface.
type GitsGetter interface {
	Gits(namespace string) GitInterface
}

// GitInterface has methods to work with Git resources.
type GitInterface interface {
	Create(ctx context.Context, git *v1alpha1.Git, opts v1.CreateOptions) (*v1alpha1.Git, error)
	Update(ctx context.Context, git *v1alpha1.Git, opts v1.UpdateOptions) (*v1alpha1.Git, error)
	UpdateStatus(ctx context.Context, git *v1alpha1.Git, opts v1.UpdateOptions) (*v1alpha1.Git, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.Git, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.GitList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Git, err error)
	GitExpansion
}

// gits implements GitInterface
type gits struct {
	client rest.Interface
	ns     string
}

// newGits returns a Gits
func newGits(c *BrixV1alpha1Client, namespace string) *gits {
	return &gits{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the git, and returns the corresponding git object, and an error if there is any.
func (c *gits) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.Git, err error) {
	result = &v1alpha1.Git{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("gits").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Gits that match those selectors.
func (c *gits) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.GitList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.GitList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("gits").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested gits.
func (c *gits) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("gits").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a git and creates it.  Returns the server's representation of the git, and an error, if there is any.
func (c *gits) Create(ctx context.Context, git *v1alpha1.Git, opts v1.CreateOptions) (result *v1alpha1.Git, err error) {
	result = &v1alpha1.Git{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("gits").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(git).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a git and updates it. Returns the server's representation of the git, and an error, if there is any.
func (c *gits) Update(ctx context.Context, git *v1alpha1.Git, opts v1.UpdateOptions) (result *v1alpha1.Git, err error) {
	result = &v1alpha1.Git{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("gits").
		Name(git.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(git).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *gits) UpdateStatus(ctx context.Context, git *v1alpha1.Git, opts v1.UpdateOptions) (result *v1alpha1.Git, err error) {
	result = &v1alpha1.Git{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("gits").
		Name(git.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(git).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the git and deletes it. Returns an error if one occurs.
func (c *gits) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("gits").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *gits) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("gits").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched git.
func (c *gits) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Git, err error) {
	result = &v1alpha1.Git{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("gits").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
