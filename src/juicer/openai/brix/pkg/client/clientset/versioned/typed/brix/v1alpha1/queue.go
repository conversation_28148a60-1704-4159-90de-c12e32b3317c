/*
Copyright (c) 2021 OpenAI
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	autoscalingv1 "k8s.io/api/autoscaling/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"

	v1alpha1 "github.com/openai/brix/pkg/apis/brix/v1alpha1"
	scheme "github.com/openai/brix/pkg/client/clientset/versioned/scheme"
)

// QueuesGetter has a method to return a QueueInterface.
// A group's client should implement this interface.
type QueuesGetter interface {
	Queues(namespace string) QueueInterface
}

// QueueInterface has methods to work with Queue resources.
type QueueInterface interface {
	Create(ctx context.Context, queue *v1alpha1.Queue, opts v1.CreateOptions) (*v1alpha1.Queue, error)
	Update(ctx context.Context, queue *v1alpha1.Queue, opts v1.UpdateOptions) (*v1alpha1.Queue, error)
	UpdateStatus(ctx context.Context, queue *v1alpha1.Queue, opts v1.UpdateOptions) (*v1alpha1.Queue, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.Queue, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.QueueList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Queue, err error)
	GetScale(ctx context.Context, queueName string, options v1.GetOptions) (*autoscalingv1.Scale, error)
	UpdateScale(ctx context.Context, queueName string, scale *autoscalingv1.Scale, opts v1.UpdateOptions) (*autoscalingv1.Scale, error)

	QueueExpansion
}

// queues implements QueueInterface
type queues struct {
	client rest.Interface
	ns     string
}

// newQueues returns a Queues
func newQueues(c *BrixV1alpha1Client, namespace string) *queues {
	return &queues{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the queue, and returns the corresponding queue object, and an error if there is any.
func (c *queues) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.Queue, err error) {
	result = &v1alpha1.Queue{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("queues").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Queues that match those selectors.
func (c *queues) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.QueueList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.QueueList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("queues").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested queues.
func (c *queues) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("queues").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a queue and creates it.  Returns the server's representation of the queue, and an error, if there is any.
func (c *queues) Create(ctx context.Context, queue *v1alpha1.Queue, opts v1.CreateOptions) (result *v1alpha1.Queue, err error) {
	result = &v1alpha1.Queue{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("queues").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(queue).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a queue and updates it. Returns the server's representation of the queue, and an error, if there is any.
func (c *queues) Update(ctx context.Context, queue *v1alpha1.Queue, opts v1.UpdateOptions) (result *v1alpha1.Queue, err error) {
	result = &v1alpha1.Queue{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("queues").
		Name(queue.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(queue).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *queues) UpdateStatus(ctx context.Context, queue *v1alpha1.Queue, opts v1.UpdateOptions) (result *v1alpha1.Queue, err error) {
	result = &v1alpha1.Queue{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("queues").
		Name(queue.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(queue).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the queue and deletes it. Returns an error if one occurs.
func (c *queues) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("queues").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *queues) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("queues").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched queue.
func (c *queues) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Queue, err error) {
	result = &v1alpha1.Queue{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("queues").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}

// GetScale takes name of the queue, and returns the corresponding autoscalingv1.Scale object, and an error if there is any.
func (c *queues) GetScale(ctx context.Context, queueName string, options v1.GetOptions) (result *autoscalingv1.Scale, err error) {
	result = &autoscalingv1.Scale{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("queues").
		Name(queueName).
		SubResource("scale").
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// UpdateScale takes the top resource name and the representation of a scale and updates it. Returns the server's representation of the scale, and an error, if there is any.
func (c *queues) UpdateScale(ctx context.Context, queueName string, scale *autoscalingv1.Scale, opts v1.UpdateOptions) (result *autoscalingv1.Scale, err error) {
	result = &autoscalingv1.Scale{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("queues").
		Name(queueName).
		SubResource("scale").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(scale).
		Do(ctx).
		Into(result)
	return
}
