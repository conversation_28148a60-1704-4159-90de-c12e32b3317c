package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Cluster ... TODO
type Cluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec ClusterSpec `json:"spec,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterList is a collection of Clusters.
type ClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	// Items is the list of Clusters.
	Items []Cluster `json:"items"`
}

type ClusterSpec struct {
	Instances []Instance `json:"instances,omitempty"`
}

// Instance describe properties of nodes of that type.
type Instance struct {
	// Name is a name of this instance type.
	Name string `json:"name"`

	// InstanceType is a type of the instance.
	//
	// It corresponds to the node label "node.kubernetes.io/instance-type".
	//
	// For example "m3.medium" or "Standard_ND96asr_v4".
	//
	// More info: https://kubernetes.io/docs/reference/labels-annotations-taints/#nodekubernetesioinstance-type
	// +optional
	InstanceType string `json:"instanceType,omitempty"`

	// Capacity represents the total resources of a node.
	// More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#capacity
	// +optional
	Capacity corev1.ResourceList `json:"capacity,omitempty"`

	// Allocatable represents the resources of a node that are available for scheduling.
	// +optional
	Allocatable corev1.ResourceList `json:"allocatable,omitempty"`

	// HourlyResourceCosts represents how much it cost to use one unit of a resource for one hour.
	//
	// Cost for "memory" is in 1Gi/h, cost for "cpu" is 1 core/h.
	// +optional
	HourlyResourceCosts map[corev1.ResourceName]float64 `json:"hourlyResourceCosts,omitempty"`

	// Devices is a list of devices supported by a node.
	// +optional
	Devices []Device `json:"devices,omitempty"`
}

type Device struct {
	Infiniband *InfinibandDeviceType `json:"infiniband,omitempty"`

	NvidiaGPU *NvidiaGPUDeviceType `json:"nvidiaGPU,omitempty"`
}

type InfinibandDeviceType struct {
	// HostPath to directory with infiniband device.
	//
	// Defaults to "/dev/infiniband".
	// +optional
	HostPath string `json:"hostPath,omitempty"`
}

func SetDefaults_InfinibandDeviceType(infinibandDeviceType *InfinibandDeviceType) {
	if infinibandDeviceType.HostPath == "" {
		infinibandDeviceType.HostPath = "/dev/infiniband"
	}
}

type NvidiaGPUDeviceType struct {
	// Product name.
	//
	// For instance "Tesla-V100-SXM2-32GB" or "A100-SXM4-40GB".
	// +optional
	Product string `json:"product,omitempty"`
}
