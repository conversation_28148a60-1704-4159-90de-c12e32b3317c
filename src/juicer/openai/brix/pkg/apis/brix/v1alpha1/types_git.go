package v1alpha1

import (
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// GitServerGitName is a name of the Git which represent repositories of the git server.
const GitServerGitName = "brix-git"

// GitServerPoolName is a name of the Pool which manages git server pods.
const GitServerPoolName = "brix-git"

// GitServerPersistentVolumeClaimName is a name of the PersistentVolumeClaim which is used by git server pods.
const GitServerPersistentVolumeClaimName = "brix-git"

// GitServerVolumeName is a name of the volume which is used by git server pods.
const GitServerVolumeName = "brix-git"

// GitServerProjectRoot is a path to the directory with git repositories stored on the git server.
const GitServerProjectRoot = "/root/code"

// GitLabel is a pod label with a name of the Git object managing the labeled pod.
//
// If absent or empty, then git features are not enabled.
//
// TODO(validate): Changes to label value are prohibited. Must be present when pod is created.
// TODO(validate): Prevent empty value.
const GitLabel = "brix.openai.com/git"

// GitCommitAnnotation is a pod annotation which indicates current commit of the annotated pod.
func GitCommitAnnotation(repository string) string {
	return fmt.Sprintf("brix.openai.com/git-commit-%s", repository)
}

// GitMaxRequestsAnnotation is a pod annotation with a limit on number of concurrent incoming
// requests to the git server.
const GitMaxRequestsAnnotation = "brix.openai.com/git-max-requests"

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type Git struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   GitSpec   `json:"spec,omitempty"`
	Status GitStatus `json:"status,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type GitList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []Git `json:"items"`
}

type GitSpec struct {
	// ProjectRoot specifies root directory for git repositories.
	//
	// Repositories will be stored directly in this directory.
	//
	// Cannot be changed.
	ProjectRoot string `json:"projectRoot"`

	// Immutable indicates whether Repositories can be modified.
	//
	// If set to true, then UPDATE requests will be rejected if GitRepositorySpec.Commit is changed or
	// new repository is added or old repository is removed.
	//
	// Defaults to false.
	Immutable bool `json:"immutable,omitempty"`

	// Pull indicates whether changes should be pulled to pods managed by this Git.
	Pull bool `json:"pull,omitempty"`

	// Repositories TODO
	//
	// TODO(validate): Validate repository names.
	Repositories map[string]GitRepositorySpec `json:"repositories,omitempty"`
}

type UpdateRepositoryStrategy string

const (
	// AlwaysUpdateRepositoryStrategy is strategy where git repository is updated to the new commit the moment
	// changes are detected.
	AlwaysUpdateRepositoryStrategy UpdateRepositoryStrategy = "Always"

	// OnSetupUpdateRepositoryStrategy is strategy where git repository is updated to the new commit during initial setup.
	OnSetupUpdateRepositoryStrategy UpdateRepositoryStrategy = "OnSetup"
)

type GitRepositorySpec struct {
	// Bare specifies whether repository should be initialized as bare repository.
	//
	// Cannot be changed.
	//
	// Defaults to false.
	Bare bool `json:"bare,omitempty"`

	// Commit to fetch and reset repository to.
	//
	// If empty, then repository will be initialized but HEAD will not be reconciled.
	// This can be used to stop reconciliation of repository.
	//
	// +optional
	Commit string `json:"commit,omitempty"`

	// Remote is URL of the remote repository which contains specified Commit.
	//
	// TODO(validate): Validate correct URL.
	Remote string `json:"remote,omitempty"`

	// Depth is a limit on number of commits to fetch when fetching the Commit.
	//
	// Depth of 0 indicates no limit.
	// +optional
	//
	// TODO(validate): Changes are prohibited.
	Depth int32 `json:"depth,omitempty"`

	// UpdateStrategy is a strategy used to update git repository to the new commit.
	//
	// Defaults to AlwaysUpdateRepositoryStrategy.
	//
	// TODO(validate): Validate enums.
	UpdateStrategy UpdateRepositoryStrategy `json:"updateStrategy,omitempty"`
}

type GitStatus struct {
	// ObservedGeneration is the most recent generation observed for this Git.
	// It corresponds to the Git's generation, which is updated on mutation by the API Server.
	// +optional
	ObservedGeneration int64 `json:"observedGeneration,omitempty"`

	// CurrentReplicas is the total number of ready pods that have all repositories consistent with
	// the current specification.
	CurrentReplicas int32 `json:"currentReplicas"`

	// OutdatedReplicas is the total number of ready pods that have at least one repository inconsistent with
	// the current specification.
	OutdatedReplicas int32 `json:"outdatedReplicas"`

	// OutdatedPods contains names of pods that have at least one repository inconsistent with
	// to the current specification. List is sorted by pod name and contains at most 100 items.
	OutdatedPods []string `json:"outdatedPods,omitempty"`
}
