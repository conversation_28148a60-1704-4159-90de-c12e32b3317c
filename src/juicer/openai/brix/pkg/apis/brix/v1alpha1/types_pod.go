package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// PodRoleLabel is a label used to distinguish pods with different roles.
const PodRoleLabel = "brix.openai.com/pod-role"

// PodLabel is a label which contains name of a pod. It is present in all pods created by Brix.
// This label can be used to select a particular pod using label selectors (when selecting by name is not possible).
// We also use this label to distinguish brix pods from other pods.
const PodLabel = "brix.openai.com/pod"

// InstancesAnnotation is a pod annotation that contains a list of cluster instances to use by the annotated pod.
//
// See Instance for more information.
const InstancesAnnotation = "brix.openai.com/instances"

// ResourceLimitNvidiaGPUAnnotation is a pod annotation that contains a GPU limit for the annotated pod.
const ResourceLimitNvidiaGPUAnnotation = "brix.openai.com/resource-limit-nvidia-gpu"

// ResourceLimitNvidiaHCAAnnotation is a pod annotation that contains a HCA limit for the annotated pod.
const ResourceLimitNvidiaHCAAnnotation = "brix.openai.com/resource-limit-nvidia-hca"

// ResourceLimitCPUAnnotation is a pod annotation that contains a CPU limit for the annotated pod.
const ResourceLimitCPUAnnotation = "brix.openai.com/resource-limit-cpu"

// ResourceLimitMemoryAnnotation is a pod annotation that contains a memory limit for the annotated pod.
const ResourceLimitMemoryAnnotation = "brix.openai.com/resource-limit-memory"

const (
	// ResourceLimitMax is a special value that indicates that resource limit should
	// be set to the maximum quantity that is allocatable.
	ResourceLimitMax = "max"

	// ResourceLimitAuto is a special value that indicates that resource limit should be automatically calculated.
	//
	//  - GPU limit is equal to the maximum allocatable GPUs.
	//  - HCA limit is scaled based on the ratio of GPU limit to the maximum allocatable GPUs.
	//  - CPU limit is scaled based on the ratio of GPU limit to the maximum allocatable GPUs.
	//  - Memory limit is scaled based on the ratio of CPU limit to the maximum allocatable CPUs.
	ResourceLimitAuto = "auto"
)

// AgentContainerAnnotation is an annotation that contains name of a container with the agent.
const AgentContainerAnnotation = "brix.openai.com/agent-container"

// AgentPortName is the name of the container port used by the agent.
const AgentPortName = "brix-agent"

// DefaultAgentPort is default port on which HTTP server listens and serves.
const DefaultAgentPort = 18080

// GrpcPortName is the name of the container port used by the gRPC server.
const GrpcPortName = "brix-grpc"

// DefaultGrpcPort is default port on which gRPC server listens and serves.
const DefaultGrpcPort = 19090

// MetadataVolumeName is the name of the volume that contains metadata provided by the DownwardAPI.
const MetadataVolumeName = "brix-metadata"

// BinariesVolumeName is the name of the volume that is used to provide Brix binaries
// to the container hosting the agent.
const BinariesVolumeName = "brix-binaries"

// Agent contains specification of the agent.
type Agent struct {
	// LoggingLevel specifies a level of verbosity of agent logs.
	//
	// TODO: This should be deprecated in favor of the "agent.brix.openai.com/log-level" pod annotation.
	//
	// +optional
	LoggingLevel *int32 `json:"loggingLevel,omitempty"`

	// Entrypoint configuration.
	//
	// +optional
	Entrypoint Entrypoint `json:"entrypoint,omitempty"`

	// Insecure indicates whether secure transport is used by the agent.
	//
	// Defaults to false.
	//
	// TODO: This should be deprecated in favor of the "agent.brix.openai.com/insecure" pod annotation.
	//
	// +optional
	Insecure bool `json:"insecure,omitempty"`
}

// GetAgentForRole returns agent specification for the specification
// of the provided pool for the specified role.
//
// Returns nil if agent specification is not found.
func GetAgentForRole(pool *Pool, role string) *Agent {
	switch role {
	case PoolWorkerRole:
		return &pool.Spec.Workers.Agent
	case PoolManagerRole:
		if pool.Spec.Manager == nil {
			return nil
		}
		return &pool.Spec.Manager.Agent
	default:
		return nil
	}
}

// GetAgentContainer returns a pointer to agent container of the provided pod.
//
// Returns nil if agent container is not found.
func GetAgentContainer(meta *metav1.ObjectMeta, podSpec *corev1.PodSpec) *corev1.Container {
	if len(podSpec.Containers) == 0 {
		return nil
	}
	// If agent container is not specified, use the first container.
	// It is a caller responsibility to ensure that the first container is the agent container,
	// if annotation is not specified.
	if meta.Annotations[AgentContainerAnnotation] == "" {
		return &podSpec.Containers[0]
	}
	for i := range podSpec.Containers {
		if podSpec.Containers[i].Name == meta.Annotations[AgentContainerAnnotation] {
			return &podSpec.Containers[i]
		}
	}
	return nil
}

// GetAgentContainerStatus returns status of agent container of the provided pod based on the provided agent specification.
//
// Returns nil if status of agent container is not found.
func GetAgentContainerStatus(meta *metav1.ObjectMeta, status *corev1.PodStatus) *corev1.ContainerStatus {
	if len(status.ContainerStatuses) == 0 {
		return nil
	}
	if meta.Annotations[AgentContainerAnnotation] == "" {
		return &status.ContainerStatuses[0]
	}
	for i := range status.ContainerStatuses {
		if status.ContainerStatuses[i].Name == meta.Annotations[AgentContainerAnnotation] {
			return &status.ContainerStatuses[i]
		}
	}
	return nil
}

// StopPolicy describes when the runtime should stop or pause.
//
// TODO(v1): Rename to BreakpointPolicy in the next API version. See https://en.wikipedia.org/wiki/Breakpoint.
// TODO: Deprecate in favor of the "agent.brix.openai.com/breakpoint" pod annotation.
type StopPolicy string

const (
	// StopPolicyAlways indicates that entrypoint should always stop,
	// regardless whether entrypoint failed or succeeded.
	//
	// This is default policy.
	StopPolicyAlways StopPolicy = "Always"

	// StopPolicyOnSuccess indicates that entrypoint should stop only after succeeding.
	//
	// This policy can be used to prevent failing entrypoint from stopping when debugging is required.
	StopPolicyOnSuccess StopPolicy = "OnSuccess"

	// StopPolicyOnFailure indicates that entrypoint should stop only after failing.
	//
	// This policy can be used when entrypoint command is one-shot program and agent should continue
	// to run after execution.
	StopPolicyOnFailure StopPolicy = "OnFailure"

	// StopPolicyNever indicates that entrypoint should never stop,
	// regardless whether entrypoint failed or succeeded.
	//
	// This policy can be used to prevent entrypoint from stopping when debugging is required.
	StopPolicyNever StopPolicy = "Never"
)

type Entrypoint struct {
	// StopPolicy for the entrypoint.
	//
	// One of Always, OnSuccess, OnFailure, Never.
	//
	// Default to Always.
	// +optional
	//
	// TODO(v1): Rename to BreakpointPolicy in the next API version. See https://en.wikipedia.org/wiki/Breakpoint.
	StopPolicy StopPolicy `json:"stopPolicy,omitempty"`
}

func SetDefaults_Agent(agent *Agent) {
	if agent.Entrypoint.StopPolicy == "" {
		agent.Entrypoint.StopPolicy = StopPolicyAlways
	}
}
