package v1alpha1

// AzureAnnotation is a pod annotation which specifies that the azure extension should be used.
//
// Only valid values are "enabled" and "disabled".
const AzureAnnotation = "brix.openai.com/azure"

// AzureLoginTimeoutAnnotation is a pod annotation which specifies how long to wait before timing out an azure login.
const AzureLoginTimeoutAnnotation = "brix.openai.com/azure-login-timeout"
