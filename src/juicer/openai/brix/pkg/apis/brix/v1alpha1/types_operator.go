package v1alpha1

// OperatorLabel is an optional label used to specify name of the operator which should handle labeled object.
//
// Named operator handles objects that were labeled with its name.
// Operator without a name will handle objects without this label and objects with empty value for this label.
const OperatorLabel = "brix.openai.com/operator"

// VersionLabel is a label indicating version of the operator which created the labeled object.
const VersionLabel = "brix.openai.com/version"

// InstallerImageNameAnnotation is an optional annotation used to override name of the installer image
// used in the annotated pod.
const InstallerImageNameAnnotation = "brix.openai.com/installer-image-name"

// InstallerImageTagAnnotation is an optional annotation used to override tag of the installer image
// used in the annotated pod.
const InstallerImageTagAnnotation = "brix.openai.com/installer-image-tag"
