package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"

	utilresources "github.com/openai/brix/pkg/util/resources"
)

// QueueNameLabel specifies name of a queue.
//
// Both QueueNamespaceLabel and QueueNameLabel must be present in an object
// for the object to be queued.
const QueueNameLabel = "brix.openai.com/queue-name"

// QueueNamespaceLabel specifies namespace of a queue.
//
// Both QueueNamespaceLabel and QueueNameLabel must be present in an object
// for the object to be queued.
//
// If QueueNamespaceLabel label is omitted but QueueNameLabel is present,
// then mutating webhook will add QueueNamespaceLabel label with value equal
// to namespace of a task.
const QueueNamespaceLabel = "brix.openai.com/queue-namespace"

// EnqueuedTimestampAnnotation specifies timestamp when the object was enqueued.
// This timestamp is used to determine the order of objects in the queue.
// Timestamp must be in the RFC3339 format.
const EnqueuedTimestampAnnotation = "brix.openai.com/enqueued-timestamp"

// DequeuedQueueNamespaceAnnotation specifies namespace of a queue from which
// the object was dequeued. Keeping this annotation on the object allows
// enqueuing the object back to the original queue without specifying the queue.
const DequeuedQueueNamespaceAnnotation = "brix.openai.com/dequeued-queue-namespace"

// DequeuedQueueNameAnnotation specifies name of a queue from which
// the object was dequeued. Keeping this annotation on the object allows
// enqueuing the object back to the original queue without specifying the queue.
const DequeuedQueueNameAnnotation = "brix.openai.com/dequeued-queue-name"

// QueueSelector returns selector of tasks of a queue with the provided namespace and name.
func QueueSelector(namespace string, name string) labels.Selector {
	return labels.SelectorFromSet(labels.Set{
		QueueNamespaceLabel: namespace,
		QueueNameLabel:      name,
	})
}

// +genclient
// +genclient:method=GetScale,verb=get,subresource=scale,result=k8s.io/api/autoscaling/v1.Scale
// +genclient:method=UpdateScale,verb=update,subresource=scale,input=k8s.io/api/autoscaling/v1.Scale,result=k8s.io/api/autoscaling/v1.Scale
// +genclient:method=ApplyScale,verb=apply,subresource=scale,input=k8s.io/api/autoscaling/v1.Scale,result=k8s.io/api/autoscaling/v1.Scale
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Queue is a custom resource representing queue of tasks to process.
// Queue controller handles task scheduling and monitoring.
type Queue struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	// Spec defines the desired identities of dependent objects of this Queue.
	// +optional
	Spec QueueSpec `json:"spec,omitempty"`

	// Status is the current status of dependent objects of this Queue. This data
	// may be out of date by some window of time.
	// +optional
	Status QueueStatus `json:"status,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// QueueList is a collection of Queues.
type QueueList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	// Items is the list of Queues.
	Items []Queue `json:"items"`
}

// QueueSpec is the specification of a Queue.
type QueueSpec struct {
	// Parallelism is the maximum number of tasks allowed to be active.
	//
	// If not specified, then there is no limit.
	//
	// +optional
	Parallelism *int32 `json:"parallelism,omitempty"`

	// MaxActiveUnscheduled is the maximum number of active tasks with pending pods.
	//
	// If not specified, then there is no limit.
	//
	// +optional
	MaxActiveUnscheduled *int32 `json:"maxActiveUnscheduled,omitempty"`

	// Resources is the maximum amount of compute resource requests allowed by active tasks.
	//
	// If not specified, then there is no limit on compute resource requests.
	//
	// +optional
	Resources corev1.ResourceList `json:"resources,omitempty"`

	// TTLSecondsAfterDone limits the lifetime of a queue with all tasks in the finished state.
	//
	// Done queue will be automatically deleted after TTLSecondsAfterDone seconds since it became done.
	// Queue must remain done to be deleted. Any waiting or active task will reset the timeout.
	//
	// +optional
	TTLSecondsAfterDone *int32 `json:"ttlSecondsAfterDone,omitempty"`
}

// QueueStatus represents the current state of a Queue.
type QueueStatus struct {
	// ObservedGeneration is the most recent generation observed for this Queue.
	// It corresponds to the Queue's generation, which is updated on mutation by the API Server.
	// +optional
	ObservedGeneration int64 `json:"observedGeneration,omitempty"`

	// Waiting is a number of currently waiting tasks.
	Waiting int32 `json:"waiting"`

	// Active is a number of currently active tasks.
	Active int32 `json:"active"`

	// ActiveScheduled is a number of currently active tasks that were scheduled successfully.
	ActiveScheduled int32 `json:"activeScheduled"`

	// ActiveResources is a sum of resource requests of active tasks.
	ActiveResources corev1.ResourceList `json:"activeResources,omitempty"`

	// Succeeded is a number of succeeded tasks.
	Succeeded int32 `json:"succeeded"`

	// Failed is a number of failed tasks.
	Failed int32 `json:"failed"`

	// Total is a total number of tasks.
	Total int32 `json:"total"`

	// Represents the latest available observations of a queue's current state.
	// +optional
	Conditions []QueueCondition `json:"conditions,omitempty"`
}

// Equal returns whether other status is equal to this status.
func (s *QueueStatus) Equal(other *QueueStatus) bool {
	if (s == nil) != (other == nil) {
		return false
	}
	if s == nil {
		return true
	}
	return s.ObservedGeneration == other.ObservedGeneration &&
		s.Waiting == other.Waiting &&
		s.Active == other.Active &&
		s.ActiveScheduled == other.ActiveScheduled &&
		utilresources.EqualLists(s.ActiveResources, other.ActiveResources) &&
		s.Succeeded == other.Succeeded &&
		s.Failed == other.Failed &&
		s.Total == other.Total &&
		s.EqualConditions(other)
}

// EqualConditions returns whether conditions of the other status are equal to conditions of this status.
func (s *QueueStatus) EqualConditions(other *QueueStatus) bool {
	var a []QueueCondition
	if s != nil {
		a = s.Conditions
	}
	var b []QueueCondition
	if other != nil {
		b = other.Conditions
	}
	if len(s.Conditions) != len(other.Conditions) {
		return false
	}
	for _, aa := range a {
		found := false
		for _, bb := range b {
			if aa.Equal(&bb) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	for _, bb := range b {
		found := false
		for _, aa := range a {
			if bb.Equal(&aa) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

// UpdateCondition updates existing condition or adds a new one.
// If status of the condition did not change, then last transition time will be preserved.
//
// Returns true if condition has changed or has been added.
func (s *QueueStatus) UpdateCondition(
	conditionType QueueConditionType,
	conditionStatus corev1.ConditionStatus,
	reason string,
	message string,
	time metav1.Time,
) bool {
	if s == nil {
		panic("cannot transition condition of nil status")
	}
	newCondition := QueueCondition{
		Type:               conditionType,
		Status:             conditionStatus,
		LastProbeTime:      time,
		LastTransitionTime: time,
		Reason:             reason,
		Message:            message,
	}

	oldCondition := s.GetCondition(conditionType)
	if oldCondition == nil {
		// We are adding new condition.
		s.Conditions = append(s.Conditions, newCondition)
		return true
	}
	// We are updating an existing condition, so we need to check if it has changed.
	if newCondition.Status == oldCondition.Status {
		// If it is the same then we need to preserve last transition time
		newCondition.LastTransitionTime = oldCondition.LastTransitionTime
	}

	identical := newCondition.Status == oldCondition.Status &&
		newCondition.LastProbeTime.Equal(&oldCondition.LastProbeTime) &&
		newCondition.LastTransitionTime.Equal(&oldCondition.LastTransitionTime) &&
		newCondition.Reason == oldCondition.Reason &&
		newCondition.Message == oldCondition.Message

	newCondition.DeepCopyInto(oldCondition)
	return !identical
}

// GetCondition returns pointer to condition with the specified type.
// Returns nil if the condition is not present in the status conditions.
func (s *QueueStatus) GetCondition(conditionType QueueConditionType) *QueueCondition {
	if s == nil {
		return nil
	}
	if s.Conditions == nil {
		return nil
	}
	for i := range s.Conditions {
		if s.Conditions[i].Type == conditionType {
			return &s.Conditions[i]
		}
	}
	return nil
}

func (s *QueueStatus) IsDone() bool {
	condition := s.GetCondition(DoneQueueCondition)
	return condition != nil && condition.Status == corev1.ConditionTrue
}

// QueueConditionType is a type for describing queue condition types.
type QueueConditionType string

const (
	// DoneQueueCondition is a condition indicating that all queue tasks are in the finished state.
	DoneQueueCondition QueueConditionType = "Done"
)

func EmptyQueueCondition(conditionType QueueConditionType, time metav1.Time) QueueCondition {
	return QueueCondition{
		Type:               conditionType,
		Status:             corev1.ConditionUnknown,
		LastProbeTime:      time,
		LastTransitionTime: time,
		Reason:             "",
		Message:            "",
	}
}

// QueueCondition describes the state of a queue at a certain point.
type QueueCondition struct {
	// Type of condition.
	Type QueueConditionType `json:"type"`

	// Status of the condition, one of True, False, Unknown.
	Status corev1.ConditionStatus `json:"status"`

	// Last time the condition was checked.
	//
	// This field is ignored when comparing conditions.
	//
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`

	// Unique, one-word, CamelCase reason for a queue to be in this state.
	// +optional
	Reason string `json:"reason,omitempty"`

	// A human-readable message indicating details about the state of a queue.
	// +optional
	Message string `json:"message,omitempty"`
}

// Equal returns whether other condition is equal to this condition.
//
// Last probe time is ignored when comparing conditions.
func (c *QueueCondition) Equal(other *QueueCondition) bool {
	return c.Type == other.Type &&
		c.Status == other.Status &&
		c.LastTransitionTime.Equal(&other.LastTransitionTime) &&
		c.Reason == other.Reason &&
		c.Message == other.Message
}
