package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

// Extensions contains specification of different extensions.
//
// Each extension is optional and can be disabled by omitting it or setting to null.
type Extensions struct {
	// Security ...
	//
	// +optional
	Security *Security `json:"security,omitempty"`

	// Setup ...
	//
	// +optional
	Setup *Setup `json:"setup,omitempty"`

	// Filesync ...
	//
	// +optional
	Filesync []Filesync `json:"filesync,omitempty"`

	// PersistentVolumeClaims ...
	//
	// +optional
	PersistentVolumeClaims *PersistentVolumeClaims `json:"persistentVolumeClaims,omitempty"`

	// Quota ...
	//
	// +optional
	Quota *Quota `json:"quota,omitempty"`

	// Coscheduling, when specified, indicates that worker pods should be scheduled at the same time.
	//
	// +optional
	Coscheduling *Coscheduling `json:"coscheduling,omitempty"`
}

type Security struct {
	// Limits is a list of user limits to override.
	Limits []UserLimit `json:"limits,omitempty"`
}

// UserLimit represents a single user limit (ulimit).
//
// See:
// - https://linux.die.net/man/5/limits.conf
// - https://linux.die.net/man/3/ulimit
type UserLimit struct {
	// Domain represents what users or groups are being limited.
	//
	// For example "root" or "*".
	Domain string `json:"domain"`

	// Type represents limit type.
	//
	// Type must be "hard" or "soft" or "-".
	Type string `json:"type"`

	// Item represents what is being limited.
	//
	// For example "nofile" (maximum number of open files).
	Item string `json:"item"`

	// Value represents limit value.
	//
	// For example: -1 or "unlimited" or "infinity" or 128.
	Value intstr.IntOrString `json:"value"`
}

type Setup struct {
	PostStart []string `json:"postStart,omitempty"`
}

type Filesync struct {
	RemoteRoot    string   `json:"remoteRoot,omitempty"`
	ContainerRoot string   `json:"containerRoot,omitempty"`
	DownloadPaths []string `json:"downloadPaths,omitempty"`
	UploadPaths   []string `json:"uploadPaths,omitempty"`
	// UploadExcludes files whose relative path do not match this regular expression.
	UploadExclude string `json:"uploadExclude,omitempty"`
}

// Watchdog ...
type Watchdog struct {
	// Name of the watchdog described in this specification.
	Name string `json:"name"`

	// InitialTimeoutSeconds ...
	//
	// Defaults to 0.
	// +optional
	InitialTimeoutSeconds int32 `json:"initialTimeoutSeconds,omitempty"`
}

type PersistentVolumeClaims struct {
	// Templates is a list of claim templates that worker pods are allowed to reference.
	//
	// The Pool controller is responsible for mapping network identities to
	// claims in a way that maintains the identity of a pod.
	// Every claim in this list must have at least one matching (by name) volume mount in one
	// container in the worker template. A claim in this list takes precedence over
	// any volumes in the worker template, with the same name.
	//
	// TODO: Define the behavior if a claim already exists with the same name.
	//
	// +optional
	Templates []corev1.PersistentVolumeClaim `json:"templates,omitempty"`
}

type Quota struct {
	Teams []string `json:"teams,omitempty"`
}

// Coscheduling contains specification for co-scheduling.
//
// Co-scheduling allows worker pods to be scheduled at the same time.
//
// For pool with `W` worker pods and `SW` spare worker pods only `W` worker pods are co-scheduled.
type Coscheduling struct {
	// Timeout configuration for co-scheduling.
	Timeout CoschedulingTimeout `json:"timeout,omitempty"`
}

// CoschedulingTimeout is a time scheduler has to find nodes for pending worker pods.
// Longer timeout means that worker pods are more likely to be successfully scheduled.
// However, if pods cannot be scheduled, then reserved resources might be unavailable for other pods
// for the timeout duration.
//
// Total timeout is equal to the base timeout (BaseSeconds) plus
// timeout per worker (PerWorkerMilliseconds) times number of worker pods.
type CoschedulingTimeout struct {
	// BaseSeconds ... TODO
	//
	// Defaults to 10.
	//
	// +optional
	BaseSeconds int32 `json:"baseSeconds,omitempty"`

	// PerWorkerMilliseconds ... TODO
	//
	// Defaults to 50.
	//
	// +optional
	PerWorkerMilliseconds int32 `json:"perWorkerMilliseconds,omitempty"`
}

func SetDefaults_CoschedulingTimeout(coschedulingTimeout *CoschedulingTimeout) {
	if coschedulingTimeout.BaseSeconds == 0 {
		coschedulingTimeout.BaseSeconds = 10
	}
	if coschedulingTimeout.PerWorkerMilliseconds == 0 {
		coschedulingTimeout.PerWorkerMilliseconds = 50
	}
}
