package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/uuid"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

// PoolLabel is a label used to distinguish objects controlled by different pools.
const PoolLabel = "brix.openai.com/pool"

// OrdinalLabel is a label which contains ordinal of the worker pod. It is present in every worker pod.
const OrdinalLabel = "brix.openai.com/ordinal"

// RevisionLabel is a label used to distinguish objects configured by
// controllers with different revisions.
//
// Objects with the revision matching the current revision of the controller are considered
// in sync with the controller.
const RevisionLabel = "brix.openai.com/revision"

// SessionLabel is a pod label which identifies the latest session for which pod was updated.
// Label value contains UUID of the session which originates from the Session.UUID.
const SessionLabel = "brix.openai.com/session"

// PoolPodsFinalizer is a finalizer which blocks deletion of a pool until every pool pod is processed.
// In particular, pool will not be gone until deletion for every pod of a pool has been successfully requested
// and every pod of a pool has pool-specific finalizers removed.
//
// Foreground deletion policy can be used to prevent pool from being deleted before all pods are gone.
const PoolPodsFinalizer = "brix.openai.com/pool-pods"

// FinishedPodTrackingFinalizer is a pod finalizer used to prevent deletion of finished pods
// which status must be first stored in the pool status.
const FinishedPodTrackingFinalizer = "brix.openai.com/finished-pod-tracking"

const (
	// PoolWorkerRole is value of the role label for worker pods.
	PoolWorkerRole = "worker"

	// PoolManagerRole is value of the role label for manager pod.
	PoolManagerRole = "manager"
)

// +genclient
// +genclient:method=GetScale,verb=get,subresource=scale,result=k8s.io/api/autoscaling/v1.Scale
// +genclient:method=UpdateScale,verb=update,subresource=scale,input=k8s.io/api/autoscaling/v1.Scale,result=k8s.io/api/autoscaling/v1.Scale
// +genclient:method=ApplyScale,verb=apply,subresource=scale,input=k8s.io/api/autoscaling/v1.Scale,result=k8s.io/api/autoscaling/v1.Scale
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Pool is a custom resource representing pool workers.
type Pool struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	// Spec defines the desired identities of dependent objects of this Pool.
	// +optional
	Spec PoolSpec `json:"spec,omitempty"`

	// Status is the current status of dependent objects of this Pool. This data
	// may be out of date by some window of time.
	// +optional
	Status PoolStatus `json:"status,omitempty"`
}

func SetDefaults_Pool(pool *Pool) {
	// Queue specific labels and annotations.
	{
		// Ensure queue namespace label is present if queue name label is present.
		if pool.Labels[QueueNameLabel] != "" && pool.Labels[QueueNamespaceLabel] == "" {
			if pool.Namespace != "" {
				pool.Labels[QueueNamespaceLabel] = pool.Namespace
			}
		}
		// If pool is queued, then ensure timestamp when pool was enqueued is present.
		if pool.Labels[QueueNameLabel] != "" {
			// TODO: Note that defaulting is not the best place to set this timestamp.
			//  Ideally we also want to reset the timestamp when queue of the pool changes.
			//  Currently we handle this on the client side by removing the annotation when enqueuing.
			if pool.Annotations[EnqueuedTimestampAnnotation] == "" {
				if pool.Annotations == nil {
					pool.Annotations = map[string]string{}
				}
				rawTimestamp, err := metav1.Now().MarshalQueryParameter()
				if err != nil {
					// This should never happen based on current MarshalQueryParameter() implementation.
					panic(err)
				}
				pool.Annotations[EnqueuedTimestampAnnotation] = rawTimestamp
			}
		}
		// If pool is not queued, then ensure timestamp when pool was enqueued is not present.
		if pool.Labels[QueueNameLabel] == "" {
			delete(pool.Annotations, EnqueuedTimestampAnnotation)
		}
	}

	// Ensure pool pods finalizer is present if pool is not being deleted.
	if pool.DeletionTimestamp == nil && !controllerutil.ContainsFinalizer(pool, PoolPodsFinalizer) {
		pool.Finalizers = append(pool.Finalizers, PoolPodsFinalizer)
	}
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// PoolList is a collection of Pools.
type PoolList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	// Items is the list of Pools.
	Items []Pool `json:"items"`
}

// PoolSpec is the specification of a Pool.
type PoolSpec struct {
	// Suspend specifies whether to suspend all pool pods.
	//
	// If Suspend is true, then new pods will not be created and existing pods will be deleted.
	//
	// Note that changing the Replicas and SpareReplicas field in the PoolWorkersSpec of the suspended pool
	// will not lead to worker creation. However, if such pool is resumed, then number of
	// worker pods created will match the number replicas from the specification.
	//
	// SuspendedPoolCondition is set to true once suspending is done (i.e. pods are gone).
	//
	// Defaults to false.
	//
	// +optional
	Suspend bool `json:"suspend,omitempty"`

	// PoolPodsDeletionPolicy specifies the conditions under which pool pods are deleted when pool is finished.
	//
	// Defaults to "OnSuccess".
	//
	// +optional
	PodDeletionPolicy PoolPodsDeletionPolicy `json:"podDeletionPolicy,omitempty"`

	// Session identifies specific execution of the Pool pods. Session is established when Session field is set,
	// and brought to an end when Session's UUID is changed. Changing Session.UUID establishes new session.
	// Whenever session is established, Pool pods update depending on the Session.
	//
	// +optional
	Session *Session `json:"session,omitempty"`

	// Workers contains specification of pool workers.
	Workers PoolWorkersSpec `json:"workers,omitempty"`

	// Manager contains specification of pool manager.
	//
	// If not specified, then manager pod will not be created.
	//
	// Default to nil.
	// +optional
	Manager *PoolManagerSpec `json:"manager,omitempty"`

	// Extensions ...
	//
	// +optional
	Extensions Extensions `json:"extensions,omitempty"`

	// TTLSecondsAfterFinished allows for automatic deletion of the pool object when finished.
	//
	// The timeout is specified in integer seconds after which the pool is deleted if still finished.
	// If unspecified, the pool will not be deleted by the controller.
	//
	// Default to nil.
	// +optional
	TTLSecondsAfterFinished *int32 `json:"ttlSecondsAfterFinished,omitempty"`
}

// SafePodDeletionPolicy is temporary hack to allow for migration from deprecated field.
// TODO: Remove this migration eventually.
func (p *Pool) SafePodDeletionPolicy() PoolPodsDeletionPolicy {
	if p.Spec.Manager != nil && p.Spec.Manager.DeprecatedWorkerDeletionPolicy != "" {
		// Even if new field is set, we still use old field, because new objects will not have old field set,
		// however old objects (created prior to the introduction of new field) will have old field set,
		// and we want to preserve the behavior.
		return p.Spec.Manager.DeprecatedWorkerDeletionPolicy
	}
	return p.Spec.PodDeletionPolicy
}

func SetDefaults_PoolSpec(poolSpec *PoolSpec) {
	// TODO: Remove this migration eventually.
	// Migration from deprecated fields - if old field is set then use it.
	if poolSpec.Manager != nil && poolSpec.Manager.DeprecatedWorkerDeletionPolicy != "" {
		poolSpec.PodDeletionPolicy = poolSpec.Manager.DeprecatedWorkerDeletionPolicy
	}
	if poolSpec.PodDeletionPolicy == "" {
		poolSpec.PodDeletionPolicy = OnSuccessDeletePoolPods
	}
	// TODO: Remove this migration eventually.
	// If new field was set but the old one was not, then set old field to new field.
	if poolSpec.Manager != nil && poolSpec.Manager.DeprecatedWorkerDeletionPolicy == "" {
		poolSpec.Manager.DeprecatedWorkerDeletionPolicy = poolSpec.PodDeletionPolicy
	}
}

type Session struct {
	// UUID uniquely identifies the session. If set to an empty string,
	// then new session UUID will be generated by the mutating webhook.
	//
	// UUID is a 128 bit (16 byte) universal unique identifier as defined in RFC 4122.
	UUID types.UID `json:"uuid"`

	// UpdateStrategy indicates the SessionUpdateStrategy that will be
	// employed to update session when UUID changes.
	//
	// Defaults to RestartContainerSessionUpdateStrategy.
	UpdateStrategy SessionUpdateStrategy `json:"updateStrategy"`
}

type SessionUpdateStrategy string

const (
	RetryRuntimeSessionUpdateStrategy     SessionUpdateStrategy = "RetryRuntime"
	RestartRuntimeSessionUpdateStrategy   SessionUpdateStrategy = "RestartRuntime"
	RestartContainerSessionUpdateStrategy SessionUpdateStrategy = "RestartContainer"
	RecreatePodSessionUpdateStrategy      SessionUpdateStrategy = "RecreatePod"
)

func SetDefaults_Session(session *Session) {
	if session.UUID == "" {
		session.UUID = uuid.NewUUID()
	}
}

// PoolWorkersSpec is the specification of pool workers.
type PoolWorkersSpec struct {
	// Replicas is the desired number of worker pods.
	//
	// Total number of worker pods is Replicas + SpareReplicas.
	// When Pool is scaled, then Replicas is modified, but SpareReplicas remains the same.
	//
	// Defaults to 0.
	// +optional
	Replicas int32 `json:"replicas,omitempty"`

	// SpareReplicas is the number of additional worker pods.
	//
	// Spare replicas can be used as a replacement for replicas that are unavailable
	// (e.g. replicas that were evicted or landed on the unhealthy node).
	//
	// Defaults to 0.
	// +optional
	SpareReplicas int32 `json:"spareReplicas,omitempty"`

	// RecreateFailed indicate whether worker pod in the "Failed" phase should be recreated
	// by the controller.
	//
	// Defaults to true.
	//
	// TODO: Rename this field in the schema to `recreateFailed`.
	//
	// +optional
	RecreateFailed *bool `json:"deleteFailed,omitempty"`

	// Agent configuration for the worker pods.
	Agent Agent `json:"agent,omitempty"`

	// Template is the object that describes worker pods.
	//
	// Created pods might have specification different from the one in the PoolWorkersSpec.
	// Parts of the template will be modified by the pool controller based on the extensions
	// specified in the PoolSpec.
	Template corev1.PodTemplateSpec `json:"template,omitempty"`
}

func SetDefaults_PoolWorkersSpec(poolWorkersSpec *PoolWorkersSpec) {
	if poolWorkersSpec.RecreateFailed == nil {
		poolWorkersSpec.RecreateFailed = pointer.Bool(true)
	}
}

// PoolManagerSpec is the specification of a pool manager.
type PoolManagerSpec struct {
	// Use PoolSpec.PodDeletionPolicy instead.
	// TODO: Remove this field from schema eventually.
	//
	// +optional
	DeprecatedWorkerDeletionPolicy PoolPodsDeletionPolicy `json:"workerDeletionPolicy,omitempty"`

	// RecreateFailed indicate whether manager pod in the "Failed" phase should be recreated
	// by the controller.
	//
	// Defaults to true.
	//
	// TODO: Rename this field in the schema to `recreateFailed`.
	//
	// +optional
	RecreateFailed *bool `json:"deleteFailed,omitempty"`

	// Agent configuration for the manager pod.
	// +optional
	Agent Agent `json:"agent,omitempty"`

	// Template is the object that describes the manager pod.
	//
	// Created pods might have specification different from the one in the PoolManagerSpec.
	// Parts of the template will be modified by the Pool controller based on the extensions
	// specified in the PoolSpec.
	Template corev1.PodTemplateSpec `json:"template,omitempty"`
}

func SetDefaults_PoolManagerSpec(poolManagerSpec *PoolManagerSpec) {
	if poolManagerSpec.RecreateFailed == nil {
		poolManagerSpec.RecreateFailed = pointer.Bool(true)
	}
}

// PoolPodsDeletionPolicy describes a policy for if/when to delete pool pods.
type PoolPodsDeletionPolicy string

const (
	// AlwaysDeletePoolPods is a policy where pods are always deleted,
	// regardless whether pool succeeded or failed.
	AlwaysDeletePoolPods PoolPodsDeletionPolicy = "Always"

	// OnSuccessDeletePoolPods is a policy where pods are deleted after pool succeeded.
	OnSuccessDeletePoolPods PoolPodsDeletionPolicy = "OnSuccess"

	// NeverDeletePoolPods is a policy where pods are never deleted,
	// regardless whether pool succeeded or failed.
	NeverDeletePoolPods PoolPodsDeletionPolicy = "Never"
)

// PoolStatus represents the current state of a Pool.
type PoolStatus struct {
	// ObservedGeneration is the most recent generation observed for this Pool.
	// It corresponds to the Pool's generation, which is updated on mutation by the API Server.
	// +optional
	ObservedGeneration int64 `json:"observedGeneration,omitempty"`

	// Revision, if not empty, indicates the current revision of the pool.
	Revision string `json:"revision,omitempty"`

	// Workers indicates the state of pool workers.
	Workers PoolWorkersStatus `json:"workers,omitempty"`

	// Manager indicates the state of the pool manager.
	Manager PoolManagerStatus `json:"manager,omitempty"`

	// Represents the latest available observations of a Pool's current state.
	// +optional
	Conditions []PoolCondition `json:"conditions,omitempty"`
}

// Equal returns whether other status is equal to this status.
func (s *PoolStatus) Equal(other *PoolStatus) bool {
	if (s == nil) != (other == nil) {
		return false
	}
	if s == nil {
		return true
	}
	return s.ObservedGeneration == other.ObservedGeneration &&
		s.Revision == other.Revision &&
		s.Workers.Equal(&other.Workers) &&
		s.Manager.Equal(&other.Manager) &&
		s.EqualConditions(other)
}

// EqualConditions returns whether conditions of the other status are equal to conditions of this status.
func (s *PoolStatus) EqualConditions(other *PoolStatus) bool {
	var a []PoolCondition
	if s != nil {
		a = s.Conditions
	}
	var b []PoolCondition
	if other != nil {
		b = other.Conditions
	}
	if len(s.Conditions) != len(other.Conditions) {
		return false
	}
	for _, aa := range a {
		found := false
		for _, bb := range b {
			if aa.Equal(&bb) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	for _, bb := range b {
		found := false
		for _, aa := range a {
			if bb.Equal(&aa) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

// UpdateCondition updates existing condition or adds a new one.
// If status of the condition did not change, then last transition time will be preserved.
//
// Returns true if condition has changed or has been added.
func (s *PoolStatus) UpdateCondition(
	conditionType PoolConditionType,
	conditionStatus corev1.ConditionStatus,
	reason string,
	message string,
	time metav1.Time,
) bool {
	if s == nil {
		panic("cannot transition condition of nil status")
	}
	newCondition := PoolCondition{
		Type:               conditionType,
		Status:             conditionStatus,
		LastProbeTime:      time,
		LastTransitionTime: time,
		Reason:             reason,
		Message:            message,
	}

	oldCondition := s.GetCondition(conditionType)
	if oldCondition == nil {
		// We are adding new condition.
		s.Conditions = append(s.Conditions, newCondition)
		return true
	}
	// We are updating an existing condition, so we need to check if it has changed.
	if newCondition.Status == oldCondition.Status {
		// If it is the same then we need to preserve last transition time
		newCondition.LastTransitionTime = oldCondition.LastTransitionTime
	}

	identical := newCondition.Status == oldCondition.Status &&
		newCondition.LastProbeTime.Equal(&oldCondition.LastProbeTime) &&
		newCondition.LastTransitionTime.Equal(&oldCondition.LastTransitionTime) &&
		newCondition.Reason == oldCondition.Reason &&
		newCondition.Message == oldCondition.Message

	newCondition.DeepCopyInto(oldCondition)
	return !identical
}

// GetCondition returns pointer to condition with the specified type.
// Returns nil if the condition is not present in the status conditions.
func (s *PoolStatus) GetCondition(conditionType PoolConditionType) *PoolCondition {
	if s == nil {
		return nil
	}
	if s.Conditions == nil {
		return nil
	}
	for i := range s.Conditions {
		if s.Conditions[i].Type == conditionType {
			return &s.Conditions[i]
		}
	}
	return nil
}

func (s *PoolStatus) IsConditionTrue(conditionType PoolConditionType) bool {
	condition := s.GetCondition(conditionType)
	return condition != nil && condition.Status == corev1.ConditionTrue
}

func (s *PoolStatus) IsConditionFalse(conditionType PoolConditionType) bool {
	condition := s.GetCondition(conditionType)
	return condition != nil && condition.Status == corev1.ConditionFalse
}

func (s *PoolStatus) IsCurrent() bool {
	return s.IsConditionTrue(CurrentPoolCondition)
}

func (s *PoolStatus) IsScheduled() bool {
	return s.IsConditionTrue(ScheduledPoolCondition)
}

func (s *PoolStatus) IsReady() bool {
	return s.IsConditionTrue(ReadyPoolCondition)
}

func (s *PoolStatus) IsSuspended() bool {
	return s.IsConditionTrue(SuspendedPoolCondition)
}

func (s *PoolStatus) IsSucceeded() bool {
	return s.IsConditionTrue(SucceededPoolCondition)
}

func (s *PoolStatus) IsFailed() bool {
	return s.IsConditionTrue(FailedPoolCondition)
}

// PoolWorkersStatus represents the current state of pool workers.
type PoolWorkersStatus struct {
	// Replicas is the number of existing worker pods.
	Replicas int32 `json:"replicas"`

	// CurrentReplicas is the number of existing worker pods with the revision matching pool revision.
	CurrentReplicas int32 `json:"currentReplicas"`

	// ScheduledReplicas is the number of existing worker pods with satisfied pod scheduled condition.
	ScheduledReplicas int32 `json:"scheduledReplicas"`

	// ReadyReplicas is the number of existing worker pods that have a ready condition.
	ReadyReplicas int32 `json:"readyReplicas"`

	// UnhealthyReplicas is the number of existing worker pods that are unhealthy.
	// Currently, this includes crash looping and failed pods.
	UnhealthyReplicas int32 `json:"unhealthyReplicas"`

	// Phases is a string which represents latest phase of each current worker pod.
	// Each character in the string represents phase of a worker pod:
	//
	//  P - Pending
	//  R - Running
	//  S - Succeeded
	//  F - Failed
	//  _ - Unknown
	//
	// Even if finished pod is deleted, its phase is preserved in this field.
	// This is a string of length equal to the number of replicas plus spare replicas.
	//
	// Purpose of this field is primarily to keep track of finished workers that were deleted.
	// For instance, controller uses this field to ensure that succeeded pods are not recreated.
	//
	// For example, "FPSS" means that pod 0 has failed, pod 1 is pending, and pods 2 and 3 succeeded.
	// +optional
	Phases string `json:"phases,omitempty"`

	// SucceededReplicas is the number of current worker pods that have reached the Succeeded phase.
	// It corresponds to number of 'S' in the Phases.
	SucceededReplicas int32 `json:"succeededReplicas"`

	// FailedReplicas is the number of current worker pods that have reached the Failed phase.
	// It corresponds to number of 'F' in the Phases.
	FailedReplicas int32 `json:"failedReplicas"`

	// Message is a brief human-readable description of a state of pool workers.
	Message string `json:"message,omitempty"`
}

func (s *PoolWorkersStatus) Phase(ordinal int) corev1.PodPhase {
	if ordinal >= len(s.Phases) || ordinal < 0 {
		return ""
	}
	switch s.Phases[ordinal] {
	case 'P':
		return corev1.PodPending
	case 'R':
		return corev1.PodRunning
	case 'S':
		return corev1.PodSucceeded
	case 'F':
		return corev1.PodFailed
	default:
		return ""
	}
}

// Equal returns whether other status is equal to this status.
func (s *PoolWorkersStatus) Equal(other *PoolWorkersStatus) bool {
	if (s == nil) != (other == nil) {
		return false
	}
	if s == nil {
		return true
	}
	return s.Replicas == other.Replicas &&
		s.CurrentReplicas == other.CurrentReplicas &&
		s.ScheduledReplicas == other.ScheduledReplicas &&
		s.ReadyReplicas == other.ReadyReplicas &&
		s.Phases == other.Phases &&
		s.SucceededReplicas == other.SucceededReplicas &&
		s.FailedReplicas == other.FailedReplicas &&
		s.UnhealthyReplicas == other.UnhealthyReplicas &&
		s.Message == other.Message
}

// PoolManagerStatus represents the current state of a pool manager.
type PoolManagerStatus struct {
	// Restarts is the number of times agent of the manager pod has been restarted.
	//
	// Restarts is reset when pool revision changes.
	//
	// +optional
	Restarts *int32 `json:"restarts,omitempty"`

	// Current is true if the manager pod exists and its revision matches pool revision.
	Current bool `json:"current"`

	// Scheduled is true if the manager pod exists and satisfies pod scheduled condition.
	Scheduled bool `json:"scheduled"`

	// Ready is true if the manager pod exists and satisfies pod ready condition.
	Ready bool `json:"ready"`

	// Phase corresponds to the latest phase of the current manager pod.
	// It is preserved even if manager pod is deleted after succeeding or failing.
	Phase corev1.PodPhase `json:"phase,omitempty"`
}

// Equal returns whether other status is equal to this status.
func (s *PoolManagerStatus) Equal(other *PoolManagerStatus) bool {
	if (s == nil) != (other == nil) {
		return false
	}
	if s == nil {
		return true
	}
	return pointer.Int32Equal(s.Restarts, other.Restarts) &&
		s.Current == other.Current &&
		s.Scheduled == other.Scheduled &&
		s.Ready == other.Ready &&
		s.Phase == other.Phase
}

// PoolConditionType is a type for describing pool condition types.
type PoolConditionType string

const (
	// ManagerSucceededPoolCondition indicates whether manager pod has succeeded.
	//
	// TODO: use SucceededPoolCondition instead.
	ManagerSucceededPoolCondition PoolConditionType = "ManagerSucceeded"

	// ManagerFailedPoolCondition indicates whether manager pod has failed.
	//
	// TODO: use FailedPoolCondition instead.
	ManagerFailedPoolCondition PoolConditionType = "ManagerFailed"
)

const (
	// CurrentPoolCondition indicates whether all pods have the current revision.
	CurrentPoolCondition PoolConditionType = "Current"

	// ScheduledPoolCondition indicates whether all pods have been scheduled.
	ScheduledPoolCondition PoolConditionType = "Scheduled"

	// ReadyPoolCondition indicates whether all pods are ready.
	ReadyPoolCondition PoolConditionType = "Ready"

	// SuspendedPoolCondition indicates whether pool has been successfully suspended.
	// This condition is true only if all pods are gone.
	SuspendedPoolCondition PoolConditionType = "Suspended"

	// SucceededPoolCondition indicates whether manager pod has succeeded.
	// Pool with manager is considered succeeded if manager pod has succeeded.
	// Pool without manager is considered succeeded if at least PoolWorkersSpec.Replicas worker pods have succeeded.
	SucceededPoolCondition PoolConditionType = "Succeeded"

	// FailedPoolCondition indicates whether pool has failed.
	// Pool with manager is considered failed if manager pod has failed and will not be recreated.
	// Pool without manager is considered failed if more than PoolWorkersSpec.SpareReplicas worker pods has failed
	// and will not be recreated.
	FailedPoolCondition PoolConditionType = "Failed"
)

func EmptyPoolCondition(conditionType PoolConditionType, time metav1.Time) PoolCondition {
	return PoolCondition{
		Type:               conditionType,
		Status:             corev1.ConditionUnknown,
		LastProbeTime:      time,
		LastTransitionTime: time,
		Reason:             "",
		Message:            "",
	}
}

// PoolCondition describes the state of a pool at a certain point.
type PoolCondition struct {
	// Type of condition.
	Type PoolConditionType `json:"type"`

	// Status of the condition, one of True, False, Unknown.
	Status corev1.ConditionStatus `json:"status"`

	// Last time the condition was checked.
	//
	// This field is ignored when comparing conditions.
	//
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`

	// Unique, one-word, CamelCase reason for a pool to be in this state.
	// +optional
	Reason string `json:"reason,omitempty"`

	// A human-readable message indicating details about the state of a pool.
	// +optional
	Message string `json:"message,omitempty"`
}

// Equal returns whether other condition is equal to this condition.
//
// Last probe time is ignored when comparing conditions.
func (c *PoolCondition) Equal(other *PoolCondition) bool {
	return c.Type == other.Type &&
		c.Status == other.Status &&
		c.LastTransitionTime.Equal(&other.LastTransitionTime) &&
		c.Reason == other.Reason &&
		c.Message == other.Message
}

// PoolSelector returns selector of dependents of a pool with the provided name.
func PoolSelector(name string) labels.Selector {
	return labels.SelectorFromSet(labels.Set{
		PoolLabel: name,
	})
}

// PoolWorkerSelector returns selector of workers of a pool with the provided name.
func PoolWorkerSelector(name string) labels.Selector {
	return labels.SelectorFromSet(labels.Set{
		PoolLabel:    name,
		PodRoleLabel: PoolWorkerRole,
	})
}
