package v1alpha1

// ServiceLabel is a label used to distinguish pods belonging to different services.
//
// When present on a pod, the value of this label is the name of the service the pod belongs to.
// Pod's subdomain and DNS searches will be configured to include the service name.
//
// When present on a pool, then service with the same name will be created in the pool's namespace.
// Such service will have owner references to every pool with this label.
//
// When present on a service, then service is considered to be managed by Brix.
const ServiceLabel = "brix.openai.com/service"

// DefaultServiceName is the name of the default service used by Brix pods.
const DefaultServiceName = "brix"
