package resources

import (
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/validation"
)

// GetPodSpecRequests returns a resource list that covers the largest
// width in each resource dimension. Because init-containers run sequentially, we collect
// the max in each dimension iteratively. In contrast, we sum the resource vectors for
// regular containers since they run simultaneously.
func GetPodSpecRequests(spec *corev1.PodSpec) corev1.ResourceList {
	result := corev1.ResourceList{}
	for _, container := range spec.Containers {
		result = AddLists(result, GetContainerRequests(&container))
	}
	for _, container := range spec.InitContainers {
		result = MaxLists(result, GetContainerRequests(&container))
	}
	return result
}

// GetContainerRequests returns a resource list that covers actual requests made by a Container.
//
// If a Container specifies its own CPU limit, but does not specify a CPU request,
// then actual CPU request is equal to the specified CPU limit.
//
// If a Container specifies its own memory limit, but does not specify a memory request,
// then actual memory request is equal to the specified memory limit.
//
// If a Container specifies limit for extended resource, but does not specify a request,
// then actual extended resource request is equal the specified limit.
func GetContainerRequests(container *corev1.Container) corev1.ResourceList {
	result := corev1.ResourceList{}
	for resourceName, quantity := range container.Resources.Requests {
		result[resourceName] = quantity.DeepCopy()
	}
	for resourceName, quantity := range container.Resources.Limits {
		if _, hasRequest := container.Resources.Requests[resourceName]; !hasRequest {
			if resourceName == corev1.ResourceCPU || resourceName == corev1.ResourceMemory || IsExtendedResource(resourceName) {
				result[resourceName] = quantity.DeepCopy()
			}
		}
	}
	return result
}

// IsHugePageResource returns whether resource is one of the "hugepages-" resources.
func IsHugePageResource(name corev1.ResourceName) bool {
	return strings.HasPrefix(string(name), corev1.ResourceHugePagesPrefix)
}

// IsAttachableVolumeResource returns whether resource is one of the "attachable-volumes-" resources.
func IsAttachableVolumeResource(name corev1.ResourceName) bool {
	return strings.HasPrefix(string(name), corev1.ResourceAttachableVolumesPrefix)
}

// IsExtendedResource returns whether resource is extended resource (e.g. "nvidia.com/gpu").
func IsExtendedResource(name corev1.ResourceName) bool {
	if IsNativeResource(name) || strings.HasPrefix(string(name), corev1.DefaultResourceRequestsPrefix) {
		return false
	}
	// Ensure it satisfies the rules in IsQualifiedName() after converted into quota resource name
	nameForQuota := fmt.Sprintf("%s%s", corev1.DefaultResourceRequestsPrefix, string(name))
	if errs := validation.IsQualifiedName(nameForQuota); len(errs) != 0 {
		return false
	}
	return true
}

// IsNativeResource returns whether resource is native Kubernetes resource (e.g. "cpu" or "memory").
func IsNativeResource(name corev1.ResourceName) bool {
	return !strings.Contains(string(name), "/") || IsPrefixedNativeResource(name)
}

// IsPrefixedNativeResource returns whether resource is prefixed with "kubernetes.io/".
func IsPrefixedNativeResource(name corev1.ResourceName) bool {
	return strings.Contains(string(name), corev1.ResourceDefaultNamespacePrefix)
}

// AddLists returns new resource list which is a sum of the provided resource lists.
func AddLists(a, b corev1.ResourceList) corev1.ResourceList {
	result := corev1.ResourceList{}
	for resourceName, quantity := range a {
		result[resourceName] = quantity.DeepCopy()
	}
	for resourceName, quantity := range b {
		currentQuantity, hasResource := result[resourceName]
		if hasResource {
			currentQuantity.Add(quantity)
			result[resourceName] = currentQuantity
		} else {
			result[resourceName] = quantity.DeepCopy()
		}
	}
	return result
}

// MinLists returns new resource list which is a min of the provided resource lists.
func MinLists(a, b corev1.ResourceList) corev1.ResourceList {
	result := corev1.ResourceList{}
	for resourceName, quantity := range a {
		result[resourceName] = quantity.DeepCopy()
	}
	for resourceName, quantity := range b {
		currentQuantity, hasResource := result[resourceName]
		if (hasResource && quantity.Cmp(currentQuantity) == -1) || !hasResource {
			result[resourceName] = quantity.DeepCopy()
		}
	}
	return result
}

// MaxLists returns new resource list which is a max of the provided resource lists.
func MaxLists(a, b corev1.ResourceList) corev1.ResourceList {
	result := corev1.ResourceList{}
	for resourceName, quantity := range a {
		result[resourceName] = quantity.DeepCopy()
	}
	for resourceName, quantity := range b {
		currentQuantity, hasResource := result[resourceName]
		if (hasResource && quantity.Cmp(currentQuantity) == 1) || !hasResource {
			result[resourceName] = quantity.DeepCopy()
		}
	}
	return result
}

// FilterList new resource list with resources from the provided resource lists
// matching the specified resource names.
func FilterList(a corev1.ResourceList, names ...corev1.ResourceName) corev1.ResourceList {
	result := corev1.ResourceList{}
	for _, resourceName := range names {
		if quantity, hasResource := a[resourceName]; hasResource {
			result[resourceName] = quantity.DeepCopy()
		}
	}
	return result
}

// EqualLists returns whether two resource lists are equal.
func EqualLists(a, b corev1.ResourceList) bool {
	for resourceName, quantity := range a {
		if !b[resourceName].Equal(quantity) {
			return false
		}
	}
	for resourceName, quantity := range b {
		if !a[resourceName].Equal(quantity) {
			return false
		}
	}
	return true
}
