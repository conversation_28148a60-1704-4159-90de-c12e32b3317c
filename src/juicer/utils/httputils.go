package utils

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"math/rand/v2"
	"net/http"
	"regexp"
	"strings"
	"time"
)

const (
	MAX_RETRIES     = 5
	DEFAULT_TIMEOUT = 30 * time.Second
)

var (
	RetriableHttpStatusCode = map[int]struct{}{
		http.StatusTooManyRequests: {},
		http.StatusBadGateway:      {},
		http.StatusGatewayTimeout:  {},
	}
)

// Checks if http response is retriable.
func IsRetriable(httpResponse *http.Response) bool {
	if httpResponse == nil {
		return true
	}
	_, ok := RetriableHttpStatusCode[httpResponse.StatusCode]
	return ok
}

// Cleans up the message string and removes sensitive data like tokens or signatures
func GetLoggableMessage(message string) string {
	// Replace HTML entities
	replacer := strings.NewReplacer(
		"&amp;", "&",
		"&lt;", "<",
		"&gt;", ">",
	)

	sanitized := replacer.Replace(message)

	// Match and sanitize all key=value pairs in the string
	sensitiveKeys := []string{"sig", "token", "password", "apikey", "access_token", "auth"}
	for _, key := range sensitiveKeys {
		pattern := regexp.MustCompile(key + `=[^&\s"]+`)
		sanitized = pattern.ReplaceAllString(sanitized, key+"=REDACTED")
	}

	return sanitized
}

// Requests httpUrl with httpMethod, headers, and body shared.
// Returning bodyBytes and error if any. Err would include response body for non successful http status code.
func HttpRequestWithContext(ctx context.Context, httpMethod string, httpUrl string, headers map[string]string, payload []byte) ([]byte, error) {
	var (
		err            error
		responseBytes  []byte
		httpResponse   *http.Response
		httpStatusCode int
	)

	for retryCount := 0; retryCount <= MAX_RETRIES && ctx.Err() == nil; retryCount++ {
		slog.Info("HttpRequestWithContext", "url", GetLoggableMessage(httpUrl), "retryCount", retryCount)

		// Create new request for each retry to avoid issues with reusing body
		req, reqErr := http.NewRequestWithContext(ctx, httpMethod, httpUrl, bytes.NewBuffer(payload))
		if reqErr != nil {
			err = fmt.Errorf("failed to create http request: %w", reqErr)
			break
		}

		// Set headers
		for k, v := range headers {
			req.Header.Set(k, v)
		}

		// Execute the request
		httpResponse, err = http.DefaultClient.Do(req)
		if err == nil && !IsRetriable(httpResponse) {
			break
		}

		if retryCount < MAX_RETRIES {
			// Exponential backoff with jitter
			jitter := time.Millisecond * time.Duration(rand.IntN(1000))
			delay := time.Second*(1<<retryCount) + jitter
			slog.Info("Wait before retrying", "delayInSeconds", delay.Seconds())
			// context-aware sleep
			select {
			case <-time.After(delay):
				// Continue to next retry
			case <-ctx.Done():
				return responseBytes, ctx.Err()
			}
		}
	}

	if ctx.Err() != nil {
		slog.Error("httpRequestWithContext context error", "error", GetLoggableMessage(ctx.Err().Error()))
		return responseBytes, ctx.Err()
	}

	if err != nil {
		slog.Error("httpRequest failed", "isRetriable", IsRetriable(httpResponse), "error", GetLoggableMessage(err.Error()))
		return responseBytes, err
	}

	if httpResponse == nil {
		return responseBytes, errors.New("nil http response")
	}

	defer httpResponse.Body.Close()

	httpStatusCode = httpResponse.StatusCode
	responseBytes, err = io.ReadAll(httpResponse.Body)
	if err != nil {
		slog.Error("failed to read response body", "error", GetLoggableMessage(err.Error()))
		return responseBytes, err
	}

	if httpStatusCode < 200 || httpStatusCode >= 300 {
		httpErrorMsg := fmt.Errorf("unsuccessful statusCode=%d. ResponseBody='%s'", httpStatusCode, GetLoggableMessage(string(responseBytes)))
		return responseBytes, httpErrorMsg
	}

	return responseBytes, nil
}
