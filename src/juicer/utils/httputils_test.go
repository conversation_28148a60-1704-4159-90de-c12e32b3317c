package utils

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsRetriable(t *testing.T) {
	tests := []struct {
		name           string
		response       *http.Response
		expectedResult bool
	}{
		{
			name:           "nil response should be retriable",
			response:       nil,
			expectedResult: true,
		},
		{
			name: "429 Too Many Requests should be retriable",
			response: &http.Response{
				StatusCode: http.StatusTooManyRequests,
			},
			expectedResult: true,
		},
		{
			name: "502 Bad Gateway should be retriable",
			response: &http.Response{
				StatusCode: http.StatusBadGateway,
			},
			expectedResult: true,
		},
		{
			name: "504 Gateway Timeout should be retriable",
			response: &http.Response{
				StatusCode: http.StatusGatewayTimeout,
			},
			expectedResult: true,
		},
		{
			name: "200 OK should not be retriable",
			response: &http.Response{
				StatusCode: http.StatusOK,
			},
			expectedResult: false,
		},
		{
			name: "400 Bad Request should not be retriable",
			response: &http.Response{
				StatusCode: http.StatusBadRequest,
			},
			expectedResult: false,
		},
		{
			name: "500 Internal Server Error should not be retriable",
			response: &http.Response{
				StatusCode: http.StatusInternalServerError,
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetriable(tt.response)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestHttpRequestWithContext_Success(t *testing.T) {
	expectedResponse := `{"message": "success"}`
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify headers
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "Bearer token123", r.Header.Get("Authorization"))

		// Verify method
		assert.Equal(t, http.MethodPost, r.Method)

		// Verify body
		body, err := io.ReadAll(r.Body)
		assert.NoError(t, err)
		assert.Equal(t, `{"test": "data"}`, string(body))

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(expectedResponse))
	}))
	defer server.Close()

	ctx := context.Background()
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer token123",
	}
	responseBytes, err := HttpRequestWithContext(ctx, http.MethodPost, server.URL, headers, []byte(`{"test": "data"}`))

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, string(responseBytes))
}

func TestHttpRequestWithContext_SuccessAfterRetries(t *testing.T) {
	callCount := 0
	expectedResponse := `{"message": "success"}`

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		callCount++
		if callCount < 3 {
			// Return retriable error for first 2 calls
			w.WriteHeader(http.StatusTooManyRequests)
			w.Write([]byte("too many requests"))
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(expectedResponse))
	}))
	defer server.Close()

	ctx := context.Background()
	headers := map[string]string{"Content-Type": "application/json"}

	responseBytes, err := HttpRequestWithContext(ctx, http.MethodGet, server.URL, headers, nil)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, string(responseBytes))
	assert.Equal(t, 3, callCount)
}

func TestHttpRequestWithContext_NonRetriableError(t *testing.T) {
	callCount := 0

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		callCount++
		// Return non-retriable error
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte("bad request"))
	}))
	defer server.Close()

	ctx := context.Background()
	headers := map[string]string{"Content-Type": "application/json"}

	responseBytes, err := HttpRequestWithContext(ctx, http.MethodGet, server.URL, headers, nil)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsuccessful statusCode=400")
	assert.Contains(t, string(responseBytes), "bad request")
	assert.Equal(t, 1, callCount) // Should not retry non-retriable errors
}

func TestHttpRequestWithContext_InvalidURL(t *testing.T) {
	ctx := context.Background()
	headers := map[string]string{"Content-Type": "application/json"}

	responseBytes, err := HttpRequestWithContext(ctx, http.MethodGet, "://invalid-url", headers, nil)

	assert.Error(t, err)
	assert.Empty(t, responseBytes)
	assert.Contains(t, err.Error(), "missing protocol scheme")
}

func TestHttpRequestWithContext_EmptyResponse(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		// Don't write any body
	}))
	defer server.Close()

	ctx := context.Background()
	headers := map[string]string{"Content-Type": "application/json"}

	responseBytes, err := HttpRequestWithContext(ctx, http.MethodGet, server.URL, headers, nil)

	assert.NoError(t, err)
	assert.Empty(t, responseBytes)
}

func TestHttpRequestWithContext_WithBody(t *testing.T) {
	expectedBody := `{"key": "value", "number": 42}`

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		body, err := io.ReadAll(r.Body)
		assert.NoError(t, err)
		assert.Equal(t, expectedBody, string(body))

		w.WriteHeader(http.StatusCreated)
		w.Write([]byte(`{"status": "created"}`))
	}))
	defer server.Close()

	ctx := context.Background()
	headers := map[string]string{"Content-Type": "application/json"}
	responseBytes, err := HttpRequestWithContext(ctx, http.MethodPost, server.URL, headers, []byte(expectedBody))

	assert.NoError(t, err)
	assert.Equal(t, `{"status": "created"}`, string(responseBytes))
}

func TestHttpRequestWithContext_ContextCancelled(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		select {}
	}))
	defer server.Close()

	// Create context and cancel immediately
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	headers := map[string]string{"Content-Type": "application/json"}

	responseBytes, err := HttpRequestWithContext(ctx, http.MethodGet, server.URL, headers, nil)

	assert.Error(t, err)
	assert.Empty(t, responseBytes)
	assert.Contains(t, err.Error(), "context canceled")
}

func TestGetLoggableMessage(t *testing.T) {
	tests := []struct {
		name           string
		inputError     string
		expectedOutput string
	}{
		{
			name:           "No sensitive data",
			inputError:     "An error occurred",
			expectedOutput: "An error occurred",
		},
		{
			name:           "Single key=value pair",
			inputError:     "Error: token=abc123",
			expectedOutput: "Error: token=REDACTED",
		},
		{
			name:           "Multiple key=value pairs",
			inputError:     "Error: sig=sign123 token=abc123 password=secret",
			expectedOutput: "Error: sig=REDACTED token=REDACTED password=REDACTED",
		},
		{
			name:           "HTML entities",
			inputError:     "Error &amp; token=abc123 &lt;script&gt;",
			expectedOutput: "Error & token=REDACTED <script>",
		},
		{
			name:           "Test with workflow URL",
			inputError:     `HttpRequest failed cluster=default-cluster isRetriable=true error="Post \"https://prod-14.northcentralus.logic.azure.com:443/workflows/a9ca35a2598248c5a810b06c98bc4a18/triggers/When_a_HTTP_request_is_received/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2FWhen_a_HTTP_request_is_received%2Frun&sv=1.0&sig=sign": http: ContentLength=834 with Body length 0"`,
			expectedOutput: `HttpRequest failed cluster=default-cluster isRetriable=true error="Post \"https://prod-14.northcentralus.logic.azure.com:443/workflows/a9ca35a2598248c5a810b06c98bc4a18/triggers/When_a_HTTP_request_is_received/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2FWhen_a_HTTP_request_is_received%2Frun&sv=1.0&sig=REDACTED": http: ContentLength=834 with Body length 0"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := GetLoggableMessage(tt.inputError)
			if output != tt.expectedOutput {
				t.Errorf("Expected '%s', but got '%s'", tt.expectedOutput, output)
			}
		})
	}
}
