package experiment

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"math/rand/v2"
	"net/http"
	"os"
	"runtime/debug"
	"strings"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
	"github.com/sethvargo/go-envconfig"
)

const (
	/*
	   Prometheus queries to get the list of experiments with 0% GPU utilization.
	   We are breaking up the queries into two parts to avoid 429 error exceeding query cost limit when looking back over a long period of time:
	   1. currentIdleGPUQueryTemplate: This query gets the list of experiments with 0% GPU utilization at the current time.
	   2. lastNHoursIdleGPUQueryTemplate: This query gets the list of experiments with 0% GPU utilization over the last N hours, using the namespace and experiment name filters from first query.
	   Note on the naming and extracting experiment name:
	   <PERSON><PERSON>hooter created pod names are in the format <rapid_id>-<train/clip/rollout/controller>-<worker_number>-<int_count>
	   where <rapid_id> is a unique identifier for the experiment.
	   Brix created pod names are in the format <cluster_name>-<int_count> where integer is appended
	*/
	// Query to get the list of experiments with 0% GPU utilization at the current time.
	currentIdleGPUQueryTemplate = `sum by (exported_namespace, experiment) (
		label_replace(
			label_replace(dcgm_fi_dev_gpu_util{cluster="%s", endpoint="dcgm-metrics"}, "experiment", "$1", "exported_pod", "^(.*)-\\d+$")
			, "experiment", "$1", "exported_pod", "(.+)-(train|clip|rollout|controller)-(.+)"
		)) == 0`
	// Look back N hours
	// To avoid 429, we are filtering labels based on namespace and experiment from above currentIdleGPUQuery
	lastNHoursIdleGPUQueryTemplate = `
		clamp_max(
			count by(exported_namespace, experiment)(
				count by(exported_namespace, experiment, exported_pod)(
					count_over_time(
						label_replace(
							label_replace(
								dcgm_fi_dev_gpu_util{cluster="%s", exported_namespace=~"%s", exported_pod=~"%s", endpoint="dcgm-metrics"}, "experiment", "$1", "exported_pod", "^(.*)-\\d+$"
							),"experiment", "$1", "experiment", "(.+)-(train|clip|rollout|controller)-(.+)"
						)[%s:1m]
					) == %d
				)
			)
			unless
			count by(exported_namespace, experiment)(
				avg by (exported_namespace, experiment, exported_pod)(
					label_replace(
						label_replace(
							avg_over_time(dcgm_fi_dev_gpu_util{cluster="%s", exported_namespace=~"%s", exported_pod=~"%s", endpoint="dcgm-metrics"}[%s]), "experiment", "$1", "exported_pod", "^(.*)-\\d+$"
						),"experiment", "$1", "experiment", "(.+)-(train|clip|rollout|controller)-(.+)"
					)
				) > 0
			)
		, 1)
		* on (exported_namespace, experiment) group_left()
		count(
			label_replace(
				label_replace(
					dcgm_fi_dev_gpu_util{cluster="%s", exported_namespace=~"%s", exported_pod=~"%s", endpoint="dcgm-metrics"}
					, "experiment", "$1", "exported_pod", "^(.*)-\\d+$"
				),"experiment", "$1", "experiment", "(.+)-(train|clip|rollout|controller)-(.+)"
			)
		) by (exported_namespace, experiment)`
	maxUtilizationQueryTemplate = `max(dcgm_fi_dev_gpu_util{cluster="%s", exported_namespace="%s", exported_pod=~"%s.*", endpoint="dcgm-metrics"})`
	maxQueryRetries             = 3
	minQueryRetryDelay          = 30 * time.Second
)

type Cluster struct {
	Config
}

func NewCluster(ctx context.Context) *Cluster {
	var config Config
	err := envconfig.Process(ctx, &config)
	if err != nil {
		slog.Error("Failed to process environment variables", "error", err)
		os.Exit(1)
	}

	slog.SetDefault(slog.Default().With("cluster", config.ClusterName))

	return &Cluster{
		Config: config,
	}
}

type promQueryFunc func() (model.Value, v1.Warnings, error)

func retryQuery(ctx context.Context, queryFn promQueryFunc, logPrefix string) (model.Value, v1.Warnings, error) {
	var (
		result model.Value
		warn   v1.Warnings
		err    error
	)

	for i := 0; i <= maxQueryRetries && ctx.Err() == nil; i++ {
		slog.Info(logPrefix, "retryCount", i)
		result, warn, err = queryFn()
		if err == nil {
			break
		}
		slog.Info("query failed with error", "retryCount", i, "error", err)
		if i < maxQueryRetries {
			backoff := (1 << i) * minQueryRetryDelay
			jitter := time.Duration(rand.IntN(10)) * time.Second
			delay := backoff + jitter
			slog.Info("Wait before retrying", "delayInSeconds", delay.Seconds())
			// context-aware sleep
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return result, warn, ctx.Err()
			}
		}
	}

	if ctx.Err() != nil {
		slog.Error("context error", "error", ctx.Err())
		return result, warn, ctx.Err()
	}

	return result, warn, err
}

func promQuery(ctx context.Context, promClient v1.API, queryString string, lookbackPeriod time.Duration) (model.Value, v1.Warnings, error) {
	return retryQuery(ctx, func() (model.Value, v1.Warnings, error) {
		return promClient.Query(ctx, queryString, time.Now(), v1.WithLookbackDelta(lookbackPeriod))
	}, "promQuery")
}

func promQueryRange(ctx context.Context, promClient v1.API, queryString string, queryRange v1.Range) (model.Value, v1.Warnings, error) {
	return retryQuery(ctx, func() (model.Value, v1.Warnings, error) {
		return promClient.QueryRange(ctx, queryString, queryRange)
	}, "promQueryRange")
}

func (c *Cluster) GetIdleGPUFilters(ctx context.Context, promClient v1.API, period time.Duration) (string, string, error) {
	// 1. Query Prometheus for experiments with 0% active GPUs
	currentIdleGPUQuery := fmt.Sprintf(currentIdleGPUQueryTemplate, c.ClusterName)
	slog.Info("Querying Prometheus for experiments that are at 0% active GPUs currently")
	slog.Debug(currentIdleGPUQuery)
	currentIdleGPUResult, _, err := promQuery(ctx, promClient, currentIdleGPUQuery, period)

	if err != nil {
		slog.Error("failed to query Prometheus", "error", err)
		return "", "", err
	}
	if currentIdleGPUResult == nil {
		slog.Info("No idle GPU experiments found")
		return "", "", nil
	}

	// 2. Extract unique exported namespaces and experiments from the result
	exportedNamespaceSet := make(map[string]struct{})
	experimentSet := make(map[string]struct{})
	v, ok := currentIdleGPUResult.(model.Vector)
	if !ok {
		slog.Error("unexpected type for currentIdleGPUResult", "type", fmt.Sprintf("%T", currentIdleGPUResult))
		return "", "", fmt.Errorf("unexpected type for currentIdleGPUResult: %T", currentIdleGPUResult)
	}
	for _, s := range v {
		labels := s.Metric
		namespace := string(labels["exported_namespace"])
		experiment := string(labels["experiment"])
		if namespace != "" {
			exportedNamespaceSet[namespace] = struct{}{}
		}
		if experiment != "" {
			experimentSet[experiment] = struct{}{}
		}
	}

	// 3. Format label filters exported_namespaces and experiments from the result
	var namespaces []string
	for namespace := range exportedNamespaceSet {
		namespaces = append(namespaces, namespace)
	}
	var experiments []string
	for experiment := range experimentSet {
		experiments = append(experiments, experiment)
	}
	exportedNamespaceFilter := strings.Join(namespaces, "|")
	experimentFilter := strings.Join(experiments, "|")

	slog.Info("Found idle GPU experiments", "exportedNamespaces", exportedNamespaceFilter, "experiments", experimentFilter)

	return exportedNamespaceFilter, experimentFilter, nil
}

func (c *Cluster) GetInactiveExperiments(ctx context.Context, period time.Duration) ([]*InactiveExperiment, error) {
	promClient, err := c.GetPrometheusClient(ctx)
	if err != nil {
		return nil, err
	}

	// 1. Get the list of experiments with 0% GPU utilization at the current time
	exportedNamespaceFilter, experimentFilter, err := c.GetIdleGPUFilters(ctx, promClient, period)
	if err != nil {
		slog.Error("failed to get idle GPU filters", "error", fmt.Errorf("%w\nStack:\n%s", err, debug.Stack()))
		return nil, err
	}
	if exportedNamespaceFilter == "" || experimentFilter == "" {
		slog.Info("No idle GPU experiments found")
		return nil, nil
	}

	// 2. Get the list of experiments with 0% GPU utilization over the last N hours
	// 2a. Query Prometheus for experiments with 0% active GPUs over the last N hours
	queryPeriodInHours := fmt.Sprintf("%dh", int(period.Hours()))
	queryPeriodInMinutes := int(period.Minutes())
	lastNHoursIdleGPUQuery := fmt.Sprintf(
		lastNHoursIdleGPUQueryTemplate,
		c.ClusterName, exportedNamespaceFilter, experimentFilter, queryPeriodInHours, queryPeriodInMinutes,
		c.ClusterName, exportedNamespaceFilter, experimentFilter, queryPeriodInHours,
		c.ClusterName, exportedNamespaceFilter, experimentFilter)
	slog.Info(fmt.Sprintf("Querying Prometheus for experiments with 0%% active GPUs for the last %s hours", queryPeriodInHours))
	slog.Debug(lastNHoursIdleGPUQuery)
	lastNHoursIdleGPUResult, _, err := promQuery(ctx, promClient, lastNHoursIdleGPUQuery, period)
	if err != nil {
		slog.Error("failed to query Prometheus", "error", fmt.Errorf("%w\nStack:\n%s", err, debug.Stack()))
		return nil, err
	}

	// 2b. Get the list of experiments with 0% GPU utilization over the last N hours
	experiments := []*InactiveExperiment{}
	lastNHoursIdleGPUVector := lastNHoursIdleGPUResult.(model.Vector)

	for _, s := range lastNHoursIdleGPUVector {
		labels := s.Metric
		// Extract namespace, experiment, and GPU count from the labels
		namespace := string(labels["exported_namespace"])
		experiment := string(labels["experiment"])
		gpuCount := int(s.Value)
		if namespace == "" || experiment == "" {
			slog.Debug("skipping empty namespace or experiment")
			continue
		}
		e := &InactiveExperiment{
			Namespace:       namespace,
			Experiment:      experiment,
			Cluster:         c.ClusterName,
			Region:          c.Region,
			CurrentGPUCount: gpuCount,
			cluster:         c,
		}

		experiments = append(experiments, e)
	}

	// 3. get date of inactivity for each experiment
	slog.Info("Querying Prometheus for datetime of inactivity for each experiment")
	for _, e := range experiments {
		q := fmt.Sprintf(maxUtilizationQueryTemplate, c.ClusterName, e.Namespace, e.Experiment)
		result, _, err := promQueryRange(ctx, promClient, q, v1.Range{
			Start: startInactiveGPUTime,
			End:   time.Now(),
			Step:  1 * time.Hour,
		})

		if err != nil {
			slog.Error("failed to query Prometheus for datetime inactivity", "error", fmt.Errorf("%w\nStack:\n%s", err, debug.Stack()))
			return nil, err
		}

		matrix := result.(model.Matrix)
		if len(matrix) == 0 {
			slog.Debug(fmt.Sprintf("no data found for experiment %s/%s", e.Namespace, e.Experiment))
			e.InactiveSince = startInactiveGPUTime
			continue
		}
		lastTimestamp := model.TimeFromUnix(0)
		values := matrix[0].Values
		for i := len(values) - 1; i >= 0; i-- {
			v := values[i]
			if v.Value != 0 {
				slog.Debug(fmt.Sprintf("found non-zero value: %v", v))
				break
			} else {
				lastTimestamp = v.Timestamp
			}
		}

		if !lastTimestamp.Equal(model.TimeFromUnix(0)) {
			e.InactiveSince = lastTimestamp.Time()
		}
		slog.Debug(fmt.Sprintf("experiment %s/%s inactive since %s", e.Namespace, e.Experiment, e.InactiveSince))
	}

	return experiments, nil
}

func (c *Cluster) GetPrometheusClient(ctx context.Context) (v1.API, error) {
	client, err := api.NewClient(api.Config{
		Address:      c.PrometheusURL,
		RoundTripper: &DynamicTokenTransport{},
	})

	if err != nil {
		return nil, err
	}

	return v1.NewAPI(client), nil
}

type DynamicTokenTransport struct {
	Transport http.RoundTripper
}

func (t *DynamicTokenTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	token, err := GetToken(req.Context())
	if err != nil {
		return nil, err
	}
	newReq := req.Clone(req.Context())
	newReq.Header.Set("Authorization", "Bearer "+token)

	if t.Transport == nil {
		t.Transport = http.DefaultTransport
	}
	return t.Transport.RoundTrip(newReq)
}

func GetToken(ctx context.Context) (string, error) {
	cred, err := azidentity.NewWorkloadIdentityCredential(nil)
	if err != nil {
		log.Fatalf("Failed to get Identity credential: %v", err)
	}

	// Azure Monitor for Prometheus uses this scope
	scope := "https://prometheus.monitor.azure.com/.default"
	token, err := cred.GetToken(ctx, policy.TokenRequestOptions{
		Scopes: []string{scope},
	})
	if err != nil {
		log.Fatalf("Failed to get token: %v", err)
	}

	return token.Token, nil
}
