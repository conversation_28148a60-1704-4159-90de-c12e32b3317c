package experiment

import (
	"context"
	"fmt"
	"log/slog"
	"regexp"
	"strings"
	"time"

	brixv1alpha1 "github.com/openai/brix/pkg/apis/brix/v1alpha1"
	brixclientset "github.com/openai/brix/pkg/client/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

const (
	TargetQuotaKey = "brix.openai.com/target-quotas"
)

var (
	grafanaExperimentDashboardTemplate = "https://ai-infra-ephgecb0cucpbkg8.wus2.grafana.azure.com/d/sci-infra-monitor---/scientists-infrastructure-monitor?orgId=1&refresh=15m&var-datasource=aeijxz20txtdsa&var-region=%s&var-cluster=%s&var-namespace=%s&var-pool=%s&var-pod=All&from=now-24h&to=now"
)

type InactiveExperiment struct {
	Namespace       string    `json:"namespace"`
	Region          string    `json:"region"`
	Cluster         string    `json:"cluster"`
	Experiment      string    `json:"experiment"`
	InactiveSince   time.Time `json:"inactive_since"`
	PriorityClass   string    `json:"priority_class"`
	TargetQuota     string    `json:"target_quota"`
	CurrentGPUCount int       `json:"current_gpu_count"`
	StartedAt       time.Time `json:"started_at"`
	Paused          bool      `json:"paused"`

	cluster *Cluster `json:"-"`
}

// add for comparison
func GetIdentifier(cluster string, namespace string, experiment string) string {
	return fmt.Sprintf("%s/%s/%s", cluster, namespace, experiment)
}

func (e InactiveExperiment) GetIdentifier() string {
	return fmt.Sprintf("%s/%s/%s", e.Cluster, e.Namespace, e.Experiment)
}

func (e InactiveExperiment) String() string {
	return fmt.Sprintf("%s/%s/%s: %v - %v", e.Cluster, e.Namespace, e.Experiment, e.CurrentGPUCount, e.InactiveSince)
}

func (e *InactiveExperiment) GetStartTimestamp(brixclient *brixclientset.Clientset) (time.Time, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var started time.Time

	pools, err := GetPools(ctx, brixclient, e.Namespace, e.Experiment)
	if err != nil {
		slog.Error("failed to get pools", "namespace", e.Namespace, "experiment", e.Experiment, "err", err)
		return time.Time{}, err
	}

	for _, pool := range pools {
		if pool.CreationTimestamp.Time.After(started) {
			started = pool.CreationTimestamp.Time
		}
	}

	return started, nil
}

func (e *InactiveExperiment) GrafanaURL() string {
	return fmt.Sprintf(grafanaExperimentDashboardTemplate, e.Region, e.Cluster, e.Namespace, e.Experiment)
}

func (e *InactiveExperiment) Update(pools []*brixv1alpha1.Pool) {
	if len(pools) == 0 {
		return
	}

	for _, pool := range pools {
		if pool.CreationTimestamp.Time.After(e.InactiveSince) {
			e.InactiveSince = pool.CreationTimestamp.Time
			slog.Info(fmt.Sprintf("Updated InactiveSince for %s to %v", e.GetIdentifier(), e.InactiveSince))
		}

		if e.PriorityClass == "" && pool.Spec.Workers.Template.Spec.PriorityClassName != "" {
			e.PriorityClass = pool.Spec.Workers.Template.Spec.PriorityClassName
			slog.Info(fmt.Sprintf("Updated PriorityClass for %s to %s", e.GetIdentifier(), e.PriorityClass))
		}

		if e.TargetQuota == "" && pool.Spec.Workers.Template.ObjectMeta.Annotations[TargetQuotaKey] != "" {
			e.TargetQuota = pool.Spec.Workers.Template.ObjectMeta.Annotations[TargetQuotaKey]
			slog.Info(fmt.Sprintf("Updated TargetQuota for %s to %s", e.GetIdentifier(), e.TargetQuota))
		}
	}
}

// Helper function to check if a pool is a devbox pool.
func isDevboxPool(pool *brixv1alpha1.Pool) bool {
	return pool.Labels["type"] == "devbox" || strings.Contains(pool.Name, "box") || strings.Contains(pool.Name, "dev")
}

func isEnginePool(pool *brixv1alpha1.Pool) bool {
	return strings.Contains(pool.Name, "engine") || strings.Contains(pool.Name, "grader")
}

func isControllerpool(pool *brixv1alpha1.Pool) bool {
	return strings.Contains(pool.Name, "controller")
}

// Users have been complaining about notifications regarding devbox per day.
// Since devbox is expected to be idle from time to time, we should skip notifications for it.
func (e *InactiveExperiment) ShouldSkip(brixclient *brixclientset.Clientset, skipSpecialPool bool) NotificationDecision {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pools, err := GetPools(ctx, brixclient, e.Namespace, e.Experiment)
	if err != nil {
		slog.Error(fmt.Sprintf("Failed to get pools for %s/%s", e.Namespace, e.Experiment), "err", err)
		return NotificationDecision{Skip: true, Reason: "Failed to fetch pools"}
	}
	if len(pools) == 0 {
		slog.Error(fmt.Sprintf("No pools found for %s/%s", e.Namespace, e.Experiment))
		return NotificationDecision{Skip: true, Reason: "No pools found"}
	}

	e.Update(pools)

	isPaused := All(pools, func(p *brixv1alpha1.Pool) bool {
		return p.Spec.Suspend
	})

	if isPaused {
		slog.Info(fmt.Sprintf("Skipping notification for paused job %s/%s", e.Namespace, e.Experiment))
		return NotificationDecision{Skip: true, Reason: ReasonPaused}
	}

	if skipSpecialPool && len(pools) == 1 {
		pool := pools[0]
		// pool.Labels["torchflow.openai.com/component"] == "ray-devbox" is how devbox is launched by `twdev create-ray-devbox
		// strings.HasPrefix(pool.Labels["log_relpath"], "interactive/") is how devbox is launched by `b create`
		// We only skip notifications for devbox which uses <= 8 GPUs.
		if isDevboxPool(pool) {
			slog.Info(fmt.Sprintf("Skipping notification for devbox %s", e.Experiment))
			return NotificationDecision{Skip: true, Reason: ReasonDevbox}
		}

		if isEnginePool(pool) {
			slog.Info(fmt.Sprintf("Skipping notification for engine %s", e.Experiment))
			return NotificationDecision{Skip: true, Reason: ReasonEngine}
		}

		if isControllerpool(pool) {
			slog.Info(fmt.Sprintf("Skipping notification for controller %s", e.Experiment))
			return NotificationDecision{Skip: true, Reason: ReasonController}
		}
	}

	return NotificationDecision{Skip: false, Reason: ""}
}

func pausePool(ctx context.Context, brixclient *brixclientset.Clientset, pool *brixv1alpha1.Pool) error {
	// Check if the pool is already paused
	if pool.Spec.Suspend {
		slog.Info(fmt.Sprintf("Pool %s/%s is already paused", pool.Namespace, pool.Name))
		return nil
	}

	// First dequeue the pool and then we could pause it.
	currentQueue := types.NamespacedName{
		Namespace: pool.Labels[brixv1alpha1.QueueNamespaceLabel],
		Name:      pool.Labels[brixv1alpha1.QueueNameLabel],
	}
	dequeuePatch := []byte(fmt.Sprintf(
		`{"metadata":{"labels":{"%s":null,"%s":null},"annotations":{"%s":"%s","%s":"%s"}}}`,
		brixv1alpha1.QueueNamespaceLabel,
		brixv1alpha1.QueueNameLabel,
		brixv1alpha1.DequeuedQueueNamespaceAnnotation, currentQueue.Namespace,
		brixv1alpha1.DequeuedQueueNameAnnotation, currentQueue.Name,
	))
	_, err := brixclient.BrixV1alpha1().Pools(pool.Namespace).Patch(
		ctx, pool.GetName(), types.MergePatchType, dequeuePatch, metav1.PatchOptions{},
	)
	if err != nil {
		return fmt.Errorf("failed to dequeue pool %s/%s: %w", pool.Namespace, pool.Name, err)
	}

	// Second pause the pool
	_, err = brixclient.BrixV1alpha1().Pools(pool.Namespace).Patch(
		ctx,
		pool.GetName(),
		types.MergePatchType,
		[]byte(`{"spec":{"suspend":true}}`),
		metav1.PatchOptions{},
	)

	return err
}

// Pause, pauses the experiment by pausing all pools associated with it.
// It does not have any skip criteria like AutoPause.
func (e *InactiveExperiment) Pause(ctx context.Context, brixclient *brixclientset.Clientset) bool {
	// Get the pools
	pools, err := GetPools(ctx, brixclient, e.Namespace, e.Experiment)
	if err != nil {
		slog.Error(fmt.Sprintf("Failed to get pool %s/%s", e.Namespace, e.Experiment), "err", err)
		return false
	}

	for _, pool := range pools {
		if err := pausePool(ctx, brixclient, pool); err != nil {
			slog.Error(fmt.Sprintf("Failed to pause pool %s/%s", e.Namespace, e.Experiment), "err", err)
			return false
		}
	}

	slog.Info("Paused experiment", "experiment", e.GetIdentifier(), "autoPause", false)
	e.Paused = true
	return true
}

// AutoPause skips pausing the experiment if it matches the skip criteria. Pauses it otherwise.
func (e *InactiveExperiment) AutoPause(ctx context.Context, brixclient *brixclientset.Clientset) bool {
	// check if skip namespace is set
	regex, err := regexp.Compile(e.cluster.SkipNamespaces)
	if err != nil {
		slog.Error("Failed to compile regex SKIP_NAMESPACES", "err", err)
		return false
	}

	if regex.MatchString(e.Namespace) {
		slog.Info(fmt.Sprintf("Skipping AutoPause for %s/%s due to SKIP_NAMESPACES %s", e.Namespace, e.Experiment, e.cluster.SkipNamespaces))
		return false
	}

	// get the pools
	pools, err := GetPools(ctx, brixclient, e.Namespace, e.Experiment)
	if err != nil {
		slog.Error(fmt.Sprintf("Failed to get pool %s/%s", e.Namespace, e.Experiment), "err", err)
		return false
	}

	// check priority
	if e.cluster.SkipCriticalPriority && Any(pools, func(p *brixv1alpha1.Pool) bool {
		return p.Spec.Workers.Template.Spec.PriorityClassName == "team-critical"
	}) {
		slog.Info(fmt.Sprintf("Skipping AutoPause for %s/%s due to team-critical priority", e.Namespace, e.Experiment))
		return false
	}

	for _, pool := range pools {
		if err := pausePool(ctx, brixclient, pool); err != nil {
			slog.Error(fmt.Sprintf("Failed to pause pool %s/%s", e.Namespace, e.Experiment), "err", err)
			return false
		}
	}

	slog.Info("Paused experiment", "experiment", e.GetIdentifier(), "autoPause", true)
	e.Paused = true
	return true
}

func GetPools(ctx context.Context, brixclient *brixclientset.Clientset, namespace string, experiment string) ([]*brixv1alpha1.Pool, error) {
	pools, err := brixclient.BrixV1alpha1().Pools(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		slog.Error("Failed to list pools", "namespace", namespace, "err", err)
		return nil, err
	}

	result := make([]*brixv1alpha1.Pool, 0)
	for _, pool := range pools.Items {
		if strings.HasPrefix(pool.Name, experiment) {
			result = append(result, &pool)
		}
	}
	return result, nil
}

func All[T any](s []T, pred func(T) bool) bool {
	for _, t := range s {
		if !pred(t) {
			return false
		}
	}
	return true
}

func Any[T any](s []T, pred func(T) bool) bool {
	for _, t := range s {
		if pred(t) {
			return true
		}
	}
	return false
}
