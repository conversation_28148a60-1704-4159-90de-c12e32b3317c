package experiment

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockPrometheusAPI is a mock implementation of the Prometheus API
type MockPrometheusAPI struct {
	mock.Mock
}

func (m *MockPrometheusAPI) Query(ctx context.Context, query string, ts time.Time, opts ...v1.Option) (model.Value, v1.Warnings, error) {
	args := m.Called(ctx, query, ts, opts)
	var result model.Value
	if args.Get(0) != nil {
		result = args.Get(0).(model.Value)
	}
	return result, args.Get(1).(v1.Warnings), args.Error(2)
}

func (m *MockPrometheusAPI) QueryRange(ctx context.Context, query string, r v1.Range, opts ...v1.Option) (model.Value, v1.Warnings, error) {
	args := m.Called(ctx, query, r, opts)
	var result model.Value
	if args.Get(0) != nil {
		result = args.Get(0).(model.Value)
	}
	return result, args.Get(1).(v1.Warnings), args.Error(2)
}

func (m *MockPrometheusAPI) QueryExemplars(ctx context.Context, query string, startTime time.Time, endTime time.Time) ([]v1.ExemplarQueryResult, error) {
	args := m.Called(ctx, query, startTime, endTime)
	return args.Get(0).([]v1.ExemplarQueryResult), args.Error(1)
}

func (m *MockPrometheusAPI) LabelNames(ctx context.Context, matches []string, startTime time.Time, endTime time.Time, opts ...v1.Option) ([]string, v1.Warnings, error) {
	args := m.Called(ctx, matches, startTime, endTime, opts)
	return args.Get(0).([]string), args.Get(1).(v1.Warnings), args.Error(2)
}

func (m *MockPrometheusAPI) LabelValues(ctx context.Context, label string, matches []string, startTime time.Time, endTime time.Time, opts ...v1.Option) (model.LabelValues, v1.Warnings, error) {
	args := m.Called(ctx, label, matches, startTime, endTime, opts)
	return args.Get(0).(model.LabelValues), args.Get(1).(v1.Warnings), args.Error(2)
}

func (m *MockPrometheusAPI) Series(ctx context.Context, matches []string, startTime time.Time, endTime time.Time, opts ...v1.Option) ([]model.LabelSet, v1.Warnings, error) {
	args := m.Called(ctx, matches, startTime, endTime, opts)
	return args.Get(0).([]model.LabelSet), args.Get(1).(v1.Warnings), args.Error(2)
}

func (m *MockPrometheusAPI) GetValue(ctx context.Context, query string, ts time.Time, opts ...v1.Option) (model.Value, v1.Warnings, error) {
	args := m.Called(ctx, query, ts, opts)
	return args.Get(0).(model.Value), args.Get(1).(v1.Warnings), args.Error(2)
}

func (m *MockPrometheusAPI) Buildinfo(ctx context.Context) (v1.BuildinfoResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.BuildinfoResult), args.Error(1)
}

func (m *MockPrometheusAPI) Config(ctx context.Context) (v1.ConfigResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.ConfigResult), args.Error(1)
}

func (m *MockPrometheusAPI) Flags(ctx context.Context) (v1.FlagsResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.FlagsResult), args.Error(1)
}

func (m *MockPrometheusAPI) Snapshot(ctx context.Context, skipHead bool) (v1.SnapshotResult, error) {
	args := m.Called(ctx, skipHead)
	return args.Get(0).(v1.SnapshotResult), args.Error(1)
}

func (m *MockPrometheusAPI) Rules(ctx context.Context) (v1.RulesResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.RulesResult), args.Error(1)
}

func (m *MockPrometheusAPI) Alerts(ctx context.Context) (v1.AlertsResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.AlertsResult), args.Error(1)
}

func (m *MockPrometheusAPI) AlertManagers(ctx context.Context) (v1.AlertManagersResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.AlertManagersResult), args.Error(1)
}

func (m *MockPrometheusAPI) CleanTombstones(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockPrometheusAPI) DeleteSeries(ctx context.Context, matches []string, startTime time.Time, endTime time.Time) error {
	args := m.Called(ctx, matches, startTime, endTime)
	return args.Error(0)
}

func (m *MockPrometheusAPI) Targets(ctx context.Context) (v1.TargetsResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.TargetsResult), args.Error(1)
}

func (m *MockPrometheusAPI) TargetsMetadata(ctx context.Context, matchTarget string, metric string, limit string) ([]v1.MetricMetadata, error) {
	args := m.Called(ctx, matchTarget, metric, limit)
	return args.Get(0).([]v1.MetricMetadata), args.Error(1)
}

func (m *MockPrometheusAPI) Metadata(ctx context.Context, metric string, limit string) (map[string][]v1.Metadata, error) {
	args := m.Called(ctx, metric, limit)
	return args.Get(0).(map[string][]v1.Metadata), args.Error(1)
}

func (m *MockPrometheusAPI) TSDB(ctx context.Context, opts ...v1.Option) (v1.TSDBResult, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(v1.TSDBResult), args.Error(1)
}

func (m *MockPrometheusAPI) WalReplay(ctx context.Context) (v1.WalReplayStatus, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.WalReplayStatus), args.Error(1)
}

func (m *MockPrometheusAPI) Exemplars(ctx context.Context, query string, startTime time.Time, endTime time.Time) ([]v1.ExemplarQueryResult, error) {
	args := m.Called(ctx, query, startTime, endTime)
	return args.Get(0).([]v1.ExemplarQueryResult), args.Error(1)
}

func (m *MockPrometheusAPI) Runtimeinfo(ctx context.Context) (v1.RuntimeinfoResult, error) {
	args := m.Called(ctx)
	return args.Get(0).(v1.RuntimeinfoResult), args.Error(1)
}

func TestRetryQuery_Success(t *testing.T) {
	// Test successful query on first attempt
	expectedResult := model.Vector{
		&model.Sample{
			Metric:    model.Metric{"test": "value"},
			Value:     1.0,
			Timestamp: model.TimeFromUnix(123456789),
		},
	}
	expectedWarnings := v1.Warnings{"test warning"}

	queryFn := func() (model.Value, v1.Warnings, error) {
		return expectedResult, expectedWarnings, nil
	}

	result, warnings, err := retryQuery(context.Background(), queryFn, "test query")

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, expectedWarnings, warnings)
}

func TestRetryQuery_SuccessAfterRetries(t *testing.T) {
	// Test successful query after 1 retry
	callCount := 0
	expectedResult := model.Vector{
		&model.Sample{
			Metric:    model.Metric{"test": "value"},
			Value:     1.0,
			Timestamp: model.TimeFromUnix(123456789),
		},
	}

	queryFn := func() (model.Value, v1.Warnings, error) {
		callCount++
		if callCount < 2 {
			return nil, nil, errors.New("temporary error")
		}
		return expectedResult, nil, nil
	}

	// Override maxQueryRetries for testing
	// Note: maxQueryRetries is a constant so we can't change it, but the test still validates the retry logic

	result, warnings, err := retryQuery(context.Background(), queryFn, "test query")

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Empty(t, warnings)
	assert.Equal(t, 2, callCount)
}

func TestPromQuery(t *testing.T) {
	ctx := context.Background()
	mockAPI := new(MockPrometheusAPI)
	query := "test_query"
	period := time.Hour

	expectedResult := model.Vector{
		&model.Sample{
			Metric:    model.Metric{"test": "value"},
			Value:     1.0,
			Timestamp: model.TimeFromUnix(123456789),
		},
	}
	expectedWarnings := v1.Warnings{"test warning"}

	// Mock the Query method
	mockAPI.On("Query", ctx, query, mock.AnythingOfType("time.Time"), mock.AnythingOfType("[]v1.Option")).
		Return(expectedResult, expectedWarnings, nil)

	result, warnings, err := promQuery(ctx, mockAPI, query, period)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, expectedWarnings, warnings)
	mockAPI.AssertExpectations(t)
}

func TestPromQueryRange(t *testing.T) {
	ctx := context.Background()
	mockAPI := new(MockPrometheusAPI)
	query := "test_query"
	queryRange := v1.Range{
		Start: time.Now().Add(-time.Hour),
		End:   time.Now(),
		Step:  time.Minute,
	}

	expectedResult := model.Matrix{
		&model.SampleStream{
			Metric: model.Metric{"test": "value"},
			Values: []model.SamplePair{
				{Timestamp: model.TimeFromUnix(123456789), Value: 1.0},
			},
		},
	}
	expectedWarnings := v1.Warnings{"test warning"}

	// Mock the QueryRange method
	mockAPI.On("QueryRange", ctx, query, queryRange, mock.AnythingOfType("[]v1.Option")).
		Return(expectedResult, expectedWarnings, nil)

	result, warnings, err := promQueryRange(ctx, mockAPI, query, queryRange)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, expectedWarnings, warnings)
	mockAPI.AssertExpectations(t)
}

func TestGetIdleGPUFilters_Success(t *testing.T) {
	ctx := context.Background()
	mockAPI := new(MockPrometheusAPI)
	cluster := &Cluster{
		Config: Config{
			ClusterName: "test-cluster",
		},
	}
	period := time.Hour

	// Mock successful query result with multiple namespaces and experiments
	expectedResult := model.Vector{
		&model.Sample{
			Metric: model.Metric{
				"exported_namespace": "namespace1",
				"experiment":         "experiment1",
			},
			Value:     0,
			Timestamp: model.TimeFromUnix(123456789),
		},
		&model.Sample{
			Metric: model.Metric{
				"exported_namespace": "namespace2",
				"experiment":         "experiment2",
			},
			Value:     0,
			Timestamp: model.TimeFromUnix(123456789),
		},
		&model.Sample{
			Metric: model.Metric{
				"exported_namespace": "namespace1",
				"experiment":         "experiment3",
			},
			Value:     0,
			Timestamp: model.TimeFromUnix(123456789),
		},
	}

	expectedQuery := fmt.Sprintf(currentIdleGPUQueryTemplate, cluster.ClusterName)
	mockAPI.On("Query", ctx, expectedQuery, mock.AnythingOfType("time.Time"), mock.AnythingOfType("[]v1.Option")).
		Return(expectedResult, v1.Warnings{}, nil)

	namespaceFilter, experimentFilter, err := cluster.GetIdleGPUFilters(ctx, mockAPI, period)

	assert.NoError(t, err)
	assert.Contains(t, namespaceFilter, "namespace1")
	assert.Contains(t, namespaceFilter, "namespace2")
	assert.Contains(t, experimentFilter, "experiment1")
	assert.Contains(t, experimentFilter, "experiment2")
	assert.Contains(t, experimentFilter, "experiment3")
	mockAPI.AssertExpectations(t)
}

func TestGetIdleGPUFilters_NoResults(t *testing.T) {
	ctx := context.Background()
	mockAPI := new(MockPrometheusAPI)
	cluster := &Cluster{
		Config: Config{
			ClusterName: "test-cluster",
		},
	}
	period := time.Hour

	expectedQuery := fmt.Sprintf(currentIdleGPUQueryTemplate, cluster.ClusterName)
	mockAPI.On("Query", ctx, expectedQuery, mock.AnythingOfType("time.Time"), mock.AnythingOfType("[]v1.Option")).
		Return(nil, v1.Warnings{}, nil)

	namespaceFilter, experimentFilter, err := cluster.GetIdleGPUFilters(ctx, mockAPI, period)

	assert.NoError(t, err)
	assert.Empty(t, namespaceFilter)
	assert.Empty(t, experimentFilter)
	mockAPI.AssertExpectations(t)
}

func TestGetIdleGPUFilters_UnexpectedResultType(t *testing.T) {
	ctx := context.Background()
	mockAPI := new(MockPrometheusAPI)
	cluster := &Cluster{
		Config: Config{
			ClusterName: "test-cluster",
		},
	}
	period := time.Hour

	// Return a Matrix instead of Vector to test error handling
	unexpectedResult := model.Matrix{
		&model.SampleStream{
			Metric: model.Metric{"test": "value"},
			Values: []model.SamplePair{
				{Timestamp: model.TimeFromUnix(123456789), Value: 1.0},
			},
		},
	}

	expectedQuery := fmt.Sprintf(currentIdleGPUQueryTemplate, cluster.ClusterName)
	mockAPI.On("Query", ctx, expectedQuery, mock.AnythingOfType("time.Time"), mock.AnythingOfType("[]v1.Option")).
		Return(unexpectedResult, v1.Warnings{}, nil)

	namespaceFilter, experimentFilter, err := cluster.GetIdleGPUFilters(ctx, mockAPI, period)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unexpected type for currentIdleGPUResult")
	assert.Empty(t, namespaceFilter)
	assert.Empty(t, experimentFilter)
	mockAPI.AssertExpectations(t)
}

func TestNewCluster(t *testing.T) {
	// Set environment variables for testing
	t.Setenv("REGION", "test-region")
	t.Setenv("CLUSTER_NAME", "test-cluster")
	t.Setenv("PROMETHEUS_URL", "http://prometheus.test")

	ctx := context.Background()
	cluster := NewCluster(ctx)

	assert.NotNil(t, cluster)
	assert.Equal(t, "test-region", cluster.Config.Region)
	assert.Equal(t, "test-cluster", cluster.Config.ClusterName)
	assert.Equal(t, "http://prometheus.test", cluster.Config.PrometheusURL)
}
