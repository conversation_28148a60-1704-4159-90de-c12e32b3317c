package experiment

import "time"

type Config struct {
	Region               string `env:"REGION,required"`
	ClusterName          string `env:"CLUSTER_NAME,required"`
	PrometheusURL        string `env:"PROMETHEUS_URL,required"`
	SkipNamespaces       string `env:"SKIP_NAMESPACES,default=.*"`
	SkipCriticalPriority bool   `env:"SKIP_CRITICAL_PRIORITY,default=false"`
}

var (
	startInactiveGPUTime = time.Now().Add(-30 * 24 * time.Hour) // Azure monitoring limits to 32 days
)
