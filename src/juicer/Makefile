IMAGE_REGISTRY=${ACR}.azurecr.io
IMAGE_REPOSITORY=infra/juicer
GIT_VERSION = $(shell git describe --tags --always)
IMAGE_VERSION?=${GIT_VERSION}

.PHONY: test
test:
	go test -v ./... -cover -timeout 5m -parallel 4

.PHONY: build
build:
	docker build -t ${IMAGE_REGISTRY}/${IMAGE_REPOSITORY}:latest .
	docker tag ${IMAGE_REGISTRY}/${IMAGE_REPOSITORY}:latest ${IMAGE_REGISTRY}/${IMAGE_REPOSITORY}:${IMAGE_VERSION}

.PHONY: push
push:
	az acr login -n ${ACR}
	docker push ${IMAGE_REGISTRY}/${IMAGE_REPOSITORY}:${IMAGE_VERSION}
