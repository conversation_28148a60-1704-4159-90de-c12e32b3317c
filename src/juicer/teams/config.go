package teams

type Config struct {
	LogicAppSubscriptionID     string `env:"LOGIC_APP_SUBSCRIPTION_ID,default=57ef2365-3a4a-4150-ac28-1ec2563c43c4"`
	LogicAppResourceGroup      string `env:"LOGIC_APP_RESOURCE_GROUP,default=orange-juicer-ame"`
	LogicAppWorkflowName       string `env:"LOGIC_APP_WORKFLOW_NAME,default=juicer-notification-logicapp"`
	LogicAppWorkflowActionName string `env:"LOGIC_APP_WORKFLOW_DM_RESPONSE_ACTION_NAME,default=DMResponse"`
	LogicAppTriggerName        string `env:"LOGIC_APP_TRIGGER_NAME,default=When_a_HTTP_request_is_received"`
	LogicAppAPIVersion         string `env:"LOGIC_APP_API_VERSION,default=2016-10-01"`
}
