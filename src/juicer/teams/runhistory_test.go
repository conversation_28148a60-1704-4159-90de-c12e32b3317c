package teams

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/logic/armlogic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test setup and teardown
func setupRunHistoryTest() {
	// Set test mode to skip config processing in init()
	os.Setenv("JUICER_TEST_MODE", "true")

	// Set up test configuration for workflowConfig
	workflowConfig = Config{
		LogicAppSubscriptionID:     "test-subscription-id",
		LogicAppResourceGroup:      "test-resource-group",
		LogicAppWorkflowName:       "test-workflow",
		LogicAppTriggerName:        "test-trigger",
		LogicAppWorkflowActionName: "test-action",
	}
}

func teardownRunHistoryTest() {
	// Clean up test environment
	os.Unsetenv("JUICER_TEST_MODE")
}

// Mock implementations for interfaces
type MockWorkflowRunActionsInterface struct {
	mock.Mock
}

type MockWorkflowRunsInterface struct {
	mock.Mock
}

func (m *MockWorkflowRunActionsInterface) Get(ctx context.Context, resourceGroupName string, workflowName string, runName string, actionName string, options *armlogic.WorkflowRunActionsClientGetOptions) (armlogic.WorkflowRunActionsClientGetResponse, error) {
	args := m.Called(ctx, resourceGroupName, workflowName, runName, actionName, options)
	var result armlogic.WorkflowRunActionsClientGetResponse
	if args.Get(0) != nil {
		result = args.Get(0).(armlogic.WorkflowRunActionsClientGetResponse)
	}
	return result, args.Error(1)
}

func (m *MockWorkflowRunsInterface) NewListPager(resourceGroupName string, workflowName string, options *armlogic.WorkflowRunsClientListOptions) *runtime.Pager[armlogic.WorkflowRunsClientListResponse] {
	args := m.Called(resourceGroupName, workflowName, options)
	if args.Get(0) != nil {
		return args.Get(0).(*runtime.Pager[armlogic.WorkflowRunsClientListResponse])
	}
	return nil
}

// Helper functions to create pointers
func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

func statusPtr(s armlogic.WorkflowStatus) *armlogic.WorkflowStatus {
	return &s
}

// Test data helpers
func createTestWorkflowRun(id string, status armlogic.WorkflowStatus, startTime time.Time, endTime *time.Time, outputURI *string) *armlogic.WorkflowRun {
	run := &armlogic.WorkflowRun{
		Name: &id,
		Properties: &armlogic.WorkflowRunProperties{
			Status:    &status,
			StartTime: &startTime,
		},
	}

	if endTime != nil {
		run.Properties.EndTime = endTime
	}

	if outputURI != nil {
		run.Properties.Trigger = &armlogic.WorkflowRunTrigger{
			OutputsLink: &armlogic.ContentLink{
				URI: outputURI,
			},
		}
	}

	return run
}

// Test GetRunActionStatus
func TestGetRunActionStatus_Success(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server for action output
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": map[string]interface{}{
				"submitActionId": "Release",
			},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// Create mock actions client
	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &server.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-action", result.Name)
	assert.Equal(t, armlogic.WorkflowStatusSucceeded, result.Status)
	assert.Equal(t, startTime.Unix(), result.StartTime.Unix())
	assert.Equal(t, endTime.Unix(), result.EndTime.Unix())
	assert.Equal(t, server.URL, result.OutputLinkURI)
	assert.Equal(t, "Release", result.ActionResponse)

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_ClientError(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	mockActionsClient := new(MockWorkflowRunActionsInterface)
	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(armlogic.WorkflowRunActionsClientGetResponse{}, errors.New("API error"))

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "API error")

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_NilProperties(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: nil,
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "nil actionResp.Properties")

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_NilStartTime(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				StartTime: nil,
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "nil actionResp.Properties.StartTime")

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_NilEndTime(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				StartTime: &startTime,
				EndTime:   nil,
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "nil actionResp.Properties.EndTime")

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_FailedStatus(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusFailed}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, err.Error(), "unexpected action status Failed")
	assert.Equal(t, armlogic.WorkflowStatusFailed, result.Status)

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_NilOutputsLink(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:      &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime:   &startTime,
				EndTime:     &endTime,
				OutputsLink: nil,
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, err.Error(), "missing action response link")

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_HTTPError(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal Server Error"))
	}))
	defer server.Close()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &server.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, server.URL, result.OutputLinkURI)

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_InvalidJSON(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server that returns invalid JSON
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("invalid json"))
	}))
	defer server.Close()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &server.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.NotNil(t, result)

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_MissingSubmitActionId(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server without submitActionId
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": map[string]interface{}{
				"someOtherField": "value",
			},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &server.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, err.Error(), "missing or invalid 'submitActionId' in body")

	mockActionsClient.AssertExpectations(t)
}

func TestGetRunActionStatus_InvalidBodyStructure(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server with invalid body structure
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": "should be object not string",
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &server.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	ctx := context.Background()
	result, err := GetRunActionStatus(ctx, mockActionsClient, "test-run-123", "test-action")

	assert.Error(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, err.Error(), "invalid or missing 'body' in responseJson")

	mockActionsClient.AssertExpectations(t)
}

func TestRunActionDetails_String(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	startTime := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC)

	actionDetails := RunActionDetails{
		Name:           "test-action",
		Status:         armlogic.WorkflowStatusSucceeded,
		StartTime:      startTime,
		EndTime:        endTime,
		ActionResponse: "approve",
	}

	result := actionDetails.String()
	assert.Contains(t, result, "test-action")
	assert.Contains(t, result, "approve")
	assert.Contains(t, result, "Succeeded")
}

// Test GetFilterForDurationSince function
func TestGetFilterForDurationSince(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	duration := 24 * time.Hour

	// We can't test exact time strings since time.Now() changes, but we can test the format
	filter := GetFilterForDurationSince(duration)

	assert.Contains(t, filter, "startTime ge")
	assert.Contains(t, filter, "startTime le")
	assert.Contains(t, filter, "and")

	// Should contain RFC3339 formatted timestamps
	assert.Regexp(t, `\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:Z|[+-]\d{2}:\d{2})`, filter)
}

// Helper function to create a runtime pager for testing
func createMockPager(runs []*armlogic.WorkflowRun) *runtime.Pager[armlogic.WorkflowRunsClientListResponse] {
	// Create a mock pager that returns the runs in a single page
	responses := []armlogic.WorkflowRunsClientListResponse{
		{
			WorkflowRunListResult: armlogic.WorkflowRunListResult{
				Value: runs,
			},
		},
	}

	i := 0
	return runtime.NewPager(runtime.PagingHandler[armlogic.WorkflowRunsClientListResponse]{
		More: func(page armlogic.WorkflowRunsClientListResponse) bool {
			return i < len(responses)
		},
		Fetcher: func(ctx context.Context, page *armlogic.WorkflowRunsClientListResponse) (armlogic.WorkflowRunsClientListResponse, error) {
			if i >= len(responses) {
				return armlogic.WorkflowRunsClientListResponse{}, errors.New("no more pages")
			}
			result := responses[i]
			i++
			return result, nil
		},
	})
}

func TestGetRunRequestData_Success(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server for trigger output
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:             "Test Notification",
			Message:           "Test message",
			ResponseRequested: true,
			ClusterName:       "test-cluster",
			Experiments: []Experiment{
				{
					Name:          "test-exp",
					User:          "<EMAIL>",
					Namespace:     "test-ns",
					GPUCount:      2,
					InactiveSince: "2023-01-01T00:00:00Z",
					PriorityClass: "normal",
					TargetQuota:   "4",
					Status:        "Pending",
				},
			},
		}

		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// Create test run with trigger output link
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

	// Execute test
	ctx := context.Background()
	result, err := GetRunRequestData(ctx, run)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "Test Notification", result.Title)
	assert.Equal(t, "Test message", result.Message)
	assert.True(t, result.ResponseRequested)
	assert.Equal(t, "test-cluster", result.ClusterName)
	assert.Len(t, result.Experiments, 1)
	assert.Equal(t, "test-exp", result.Experiments[0].Name)
	assert.Equal(t, "<EMAIL>", result.Experiments[0].User)
	assert.Equal(t, "test-ns", result.Experiments[0].Namespace)
	assert.Equal(t, 2, result.Experiments[0].GPUCount)
	assert.Equal(t, "normal", result.Experiments[0].PriorityClass)
	assert.Equal(t, "4", result.Experiments[0].TargetQuota)
	assert.Equal(t, "Pending", result.Experiments[0].Status)
}

func TestGetRunRequestData_MissingTriggerLink(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Create test run without trigger output link
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, time.Now(), nil, nil)

	// Execute test
	ctx := context.Background()
	result, err := GetRunRequestData(ctx, run)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "input payload link missing")
	assert.NotNil(t, result)
}

func TestGetRunRequestData_NilTrigger(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Create test run with nil trigger
	run := &armlogic.WorkflowRun{
		Name: stringPtr("test-run-123"),
		Properties: &armlogic.WorkflowRunProperties{
			Status:    statusPtr(armlogic.WorkflowStatusSucceeded),
			StartTime: timePtr(time.Now()),
			Trigger:   nil,
		},
	}

	// Execute test
	ctx := context.Background()
	result, err := GetRunRequestData(ctx, run)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "input payload link missing")
	assert.NotNil(t, result)
}

func TestGetRunRequestData_HTTPError(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Server error"))
	}))
	defer server.Close()

	// Create test run with trigger output link
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

	// Execute test
	ctx := context.Background()
	result, err := GetRunRequestData(ctx, run)

	// Verify results
	assert.Error(t, err)
	assert.NotNil(t, result)
}

func TestGetRunRequestData_InvalidJSON(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server with invalid JSON
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("invalid json"))
	}))
	defer server.Close()

	// Create test run with trigger output link
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

	// Execute test
	ctx := context.Background()
	result, err := GetRunRequestData(ctx, run)

	// Verify results
	assert.Error(t, err)
	assert.NotNil(t, result)
}

func TestGetRunRequestData_InvalidBodyStructure(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server with invalid body structure
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": "invalid body structure", // Should be an object, not string
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// Create test run with trigger output link
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

	// Execute test
	ctx := context.Background()
	result, err := GetRunRequestData(ctx, run)

	// Verify results
	assert.Error(t, err)
	assert.NotNil(t, result)
}

func TestGetRunDetails_RunningStatus(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server for trigger output
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:             "Test Notification",
			Message:           "Test message",
			ResponseRequested: true,
			ClusterName:       "test-cluster",
			Experiments:       []Experiment{},
		}

		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// Create test run that's still running (no end time)
	runID := "test-run-123"
	startTime := time.Now().Add(-1 * time.Hour)
	run := createTestWorkflowRun(runID, armlogic.WorkflowStatusRunning, startTime, nil, &server.URL)

	// Execute test
	ctx := context.Background()
	result, err := GetRunDetails(ctx, run, nil)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, runID, result.ID)
	assert.Equal(t, armlogic.WorkflowStatusRunning, result.Status)
	assert.Equal(t, startTime.Unix(), result.StartTime.Unix())
	assert.True(t, result.EndTime.IsZero())
	assert.NotNil(t, result.RequestData)
	assert.Nil(t, result.ActionDetails)
}

func TestGetRunDetails_FailedStatus(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server for trigger output
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:             "Test Notification",
			Message:           "Test message",
			ResponseRequested: true,
			ClusterName:       "test-cluster",
			Experiments:       []Experiment{},
		}

		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// Create test run that failed
	runID := "test-run-123"
	startTime := time.Now().Add(-1 * time.Hour)
	endTime := time.Now()
	run := createTestWorkflowRun(runID, armlogic.WorkflowStatusFailed, startTime, &endTime, &server.URL)

	// Execute test
	ctx := context.Background()
	result, err := GetRunDetails(ctx, run, nil)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, runID, result.ID)
	assert.Equal(t, armlogic.WorkflowStatusFailed, result.Status)
	assert.Equal(t, endTime.Unix(), result.EndTime.Unix())
	assert.NotNil(t, result.RequestData)
	assert.Nil(t, result.ActionDetails)
}

func TestGetRunDetails_NilRun(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Execute test with nil run
	ctx := context.Background()
	result, err := GetRunDetails(ctx, nil, nil)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "nil run")
	assert.Nil(t, result)
}

func TestGetRunDetails_NilProperties(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Test various nil properties scenarios
	testCases := []struct {
		name     string
		run      *armlogic.WorkflowRun
		errorMsg string
	}{
		{
			name: "nil run name",
			run: &armlogic.WorkflowRun{
				Name: nil,
			},
			errorMsg: "nil run.Name",
		},
		{
			name: "nil run properties",
			run: &armlogic.WorkflowRun{
				Name:       stringPtr("test-run"),
				Properties: nil,
			},
			errorMsg: "nil run.Properties",
		},
		{
			name: "nil start time",
			run: &armlogic.WorkflowRun{
				Name: stringPtr("test-run"),
				Properties: &armlogic.WorkflowRunProperties{
					StartTime: nil,
				},
			},
			errorMsg: "nil run.Properties.StartTime",
		},
		{
			name: "nil status",
			run: &armlogic.WorkflowRun{
				Name: stringPtr("test-run"),
				Properties: &armlogic.WorkflowRunProperties{
					StartTime: timePtr(time.Now()),
					Status:    nil,
				},
			},
			errorMsg: "nil run.Properties.Status",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := GetRunDetails(ctx, tc.run, nil)

			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.errorMsg)
			assert.Nil(t, result)
		})
	}
}

// Test GetRunDetails with successful action client
func TestGetRunDetails_WithActionClient(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP servers
	requestServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:   "Test Notification",
			Message: "Test message",
		}
		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer requestServer.Close()

	actionServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": map[string]interface{}{
				"submitActionId": "Release",
			},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer actionServer.Close()

	// Create mock actions client
	mockActionsClient := new(MockWorkflowRunActionsInterface)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	expectedResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &actionServer.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(expectedResponse, nil)

	// Create test run
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, startTime, &endTime, &requestServer.URL)

	ctx := context.Background()
	result, err := GetRunDetails(ctx, run, mockActionsClient)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-run-123", result.ID)
	assert.Equal(t, armlogic.WorkflowStatusSucceeded, result.Status)
	assert.NotNil(t, result.RequestData)
	assert.Equal(t, "Test Notification", result.RequestData.Title)
	assert.NotNil(t, result.ActionDetails)
	assert.Equal(t, "test-action", result.ActionDetails.Name)
	assert.Equal(t, "Release", result.ActionDetails.ActionResponse)

	mockActionsClient.AssertExpectations(t)
}

// Test error handling in GetRunDetails when action client fails
func TestGetRunDetails_ActionClientError(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server for request data
	requestServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:   "Test Notification",
			Message: "Test message",
		}
		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer requestServer.Close()

	// Create mock actions client that returns error
	mockActionsClient := new(MockWorkflowRunActionsInterface)
	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-123", "test-action", mock.Anything).Return(armlogic.WorkflowRunActionsClientGetResponse{}, errors.New("action client error"))

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()
	run := createTestWorkflowRun("test-run-123", armlogic.WorkflowStatusSucceeded, startTime, &endTime, &requestServer.URL)

	ctx := context.Background()
	result, err := GetRunDetails(ctx, run, mockActionsClient)

	assert.Error(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, err.Error(), "action client error")
	// Request data should still be populated even if action fails
	assert.NotNil(t, result.RequestData)
	assert.Equal(t, "Test Notification", result.RequestData.Title)

	mockActionsClient.AssertExpectations(t)
}

func TestRunDetails_String(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	startTime := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC)

	actionDetails := &RunActionDetails{
		Name:           "test-action",
		ActionResponse: "Release",
	}

	requestData := &Notification{
		Title:             "Test Notification",
		Message:           "Test message",
		ResponseRequested: true,
		ClusterName:       "test-cluster",
		Experiments: []Experiment{
			{
				Name:          "test-exp",
				User:          "<EMAIL>",
				Namespace:     "user1",
				GPUCount:      1,
				InactiveSince: "2023-01-01T00:00:00Z",
				PriorityClass: "high",
				TargetQuota:   "2",
				Status:        "Running",
			},
		},
	}

	runDetails := RunDetails{
		ID:            "test-run-123",
		Status:        armlogic.WorkflowStatusSucceeded,
		StartTime:     startTime,
		EndTime:       endTime,
		ActionDetails: actionDetails,
		RequestData:   requestData,
	}

	result := runDetails.String()

	// Verify action details are present
	assert.Contains(t, result, "Name: test-action")
	assert.Contains(t, result, "ActionResponse: Release")

	// Verify request data details are present
	assert.Contains(t, result, "ClusterName: test-cluster")
	assert.Contains(t, result, "Name: test-exp")
	assert.Contains(t, result, "Namespace: user1")
	assert.Contains(t, result, "User: <EMAIL>")
	assert.Contains(t, result, "GPUCount: 1")
	assert.Contains(t, result, "PriorityClass: high")
	assert.Contains(t, result, "TargetQuota: 2")
	assert.Contains(t, result, "Status: Running")
	assert.Contains(t, result, "ResponseRequested: true")

	// Verify top level run details are present
	assert.Contains(t, result, "ID: test-run-123")
	assert.Contains(t, result, "Status: Succeeded")
}

// Test edge cases for GetRunRequestData with various JSON structures
func TestGetRunRequestData_EdgeCases(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	testCases := []struct {
		name          string
		responseData  interface{}
		expectError   bool
		errorContains string
	}{
		{
			name: "Missing body field",
			responseData: map[string]interface{}{
				"notBody": "test",
			},
			expectError:   true,
			errorContains: "unexpected end of JSON input",
		},
		{
			name: "Body field with null value",
			responseData: map[string]interface{}{
				"body": nil,
			},
			expectError: false, // nil body is handled gracefully
		},
		{
			name: "Empty body object",
			responseData: map[string]interface{}{
				"body": map[string]interface{}{},
			},
			expectError: false,
		},
		{
			name: "Complex nested notification",
			responseData: map[string]interface{}{
				"body": Notification{
					Title:             "Complex Test",
					Message:           "Complex message with multiple experiments",
					ResponseRequested: true,
					ClusterName:       "production-cluster",
					Experiments: []Experiment{
						{
							Name:          "ml-experiment-1",
							User:          "<EMAIL>",
							Namespace:     "ml-team",
							GPUCount:      8,
							InactiveSince: "2023-12-01T10:00:00Z",
							PriorityClass: "high",
							TargetQuota:   "16",
							Status:        "Running",
						},
						{
							Name:          "ml-experiment-2",
							User:          "<EMAIL>",
							Namespace:     "ml-team",
							GPUCount:      4,
							InactiveSince: "2023-12-01T11:00:00Z",
							PriorityClass: "normal",
							TargetQuota:   "8",
							Status:        "Pending",
						},
					},
				},
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				json.NewEncoder(w).Encode(tc.responseData)
			}))
			defer server.Close()

			run := createTestWorkflowRun("test-run", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

			ctx := context.Background()
			result, err := GetRunRequestData(ctx, run)

			if tc.expectError {
				assert.Error(t, err)
				if tc.errorContains != "" {
					assert.Contains(t, err.Error(), tc.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// For the complex case, verify all the data
				if tc.name == "Complex nested notification" {
					assert.Equal(t, "Complex Test", result.Title)
					assert.Equal(t, "Complex message with multiple experiments", result.Message)
					assert.True(t, result.ResponseRequested)
					assert.Equal(t, "production-cluster", result.ClusterName)
					assert.Len(t, result.Experiments, 2)

					exp1 := result.Experiments[0]
					assert.Equal(t, "ml-experiment-1", exp1.Name)
					assert.Equal(t, "<EMAIL>", exp1.User)
					assert.Equal(t, "ml-team", exp1.Namespace)
					assert.Equal(t, 8, exp1.GPUCount)
					assert.Equal(t, "high", exp1.PriorityClass)
					assert.Equal(t, "16", exp1.TargetQuota)
					assert.Equal(t, "Running", exp1.Status)
				}
			}
		})
	}
}

// Test context handling and cancellation
func TestGetRunRequestData_ContextCancellation(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Create a slow server to test context cancellation
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simulate slow response
		time.Sleep(100 * time.Millisecond)
		response := map[string]interface{}{
			"body": Notification{Title: "Test"},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	run := createTestWorkflowRun("test-run", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

	// Create context that cancels quickly
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Millisecond)
	defer cancel()

	result, err := GetRunRequestData(ctx, run)

	// Should get context cancellation error
	assert.Error(t, err)
	assert.NotNil(t, result) // Function should still return a struct even on error
}

// Test GetRunRequestData with various HTTP error scenarios
func TestGetRunRequestData_HTTPErrors(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	testCases := []struct {
		name       string
		statusCode int
		body       string
	}{
		{"Bad Request", http.StatusBadRequest, "Bad request"},
		{"Unauthorized", http.StatusUnauthorized, "Unauthorized"},
		{"Forbidden", http.StatusForbidden, "Forbidden"},
		{"Not Found", http.StatusNotFound, "Not found"},
		{"Internal Server Error", http.StatusInternalServerError, "Internal error"},
		{"Service Unavailable", http.StatusServiceUnavailable, "Service unavailable"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tc.statusCode)
				w.Write([]byte(tc.body))
			}))
			defer server.Close()

			run := createTestWorkflowRun("test-run", armlogic.WorkflowStatusSucceeded, time.Now(), nil, &server.URL)

			ctx := context.Background()
			result, err := GetRunRequestData(ctx, run)

			assert.Error(t, err)
			assert.NotNil(t, result)
		})
	}
}

// Test GetRunHistoryInternal with successful workflow and action clients
func TestGetRunHistoryInternal_Success(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP servers
	requestServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:   "Test Notification",
			Message: "Test message",
		}
		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer requestServer.Close()

	actionServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": map[string]interface{}{
				"submitActionId": "Release",
			},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer actionServer.Close()

	// Create test runs
	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	runs := []*armlogic.WorkflowRun{
		createTestWorkflowRun("test-run-1", armlogic.WorkflowStatusSucceeded, startTime, &endTime, &requestServer.URL),
		createTestWorkflowRun("test-run-2", armlogic.WorkflowStatusSucceeded, startTime.Add(-30*time.Minute), &endTime, &requestServer.URL),
	}

	// Create mock clients
	mockWorkflowClient := new(MockWorkflowRunsInterface)
	mockActionsClient := new(MockWorkflowRunActionsInterface)

	// Set up pager mock
	pager := createMockPager(runs)
	mockWorkflowClient.On("NewListPager", "test-resource-group", "test-workflow", mock.Anything).Return(pager)

	// Set up actions client mock for both runs
	actionResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &actionServer.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-1", "test-action", mock.Anything).Return(actionResponse, nil)
	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-2", "test-action", mock.Anything).Return(actionResponse, nil)

	ctx := context.Background()
	duration := 24 * time.Hour

	result := GetRunHistoryInternal(ctx, duration, mockWorkflowClient, mockActionsClient)

	assert.NotNil(t, result)
	assert.Len(t, result, 2)
	assert.Contains(t, result, "test-run-1")
	assert.Contains(t, result, "test-run-2")

	// Verify run details
	run1 := result["test-run-1"]
	assert.NotNil(t, run1)
	assert.Equal(t, "test-run-1", run1.ID)
	assert.Equal(t, armlogic.WorkflowStatusSucceeded, run1.Status)
	assert.NotNil(t, run1.RequestData)
	assert.Equal(t, "Test Notification", run1.RequestData.Title)
	assert.NotNil(t, run1.ActionDetails)
	assert.Equal(t, "Release", run1.ActionDetails.ActionResponse)

	run2 := result["test-run-2"]
	assert.NotNil(t, run2)
	assert.Equal(t, "test-run-2", run2.ID)
	assert.Equal(t, armlogic.WorkflowStatusSucceeded, run2.Status)

	mockWorkflowClient.AssertExpectations(t)
	mockActionsClient.AssertExpectations(t)
}

// Test GetRunHistoryInternal with pager error
func TestGetRunHistoryInternal_PagerError(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Create mock clients
	mockWorkflowClient := new(MockWorkflowRunsInterface)
	mockActionsClient := new(MockWorkflowRunActionsInterface)

	// Create a pager that will return an error
	pager := runtime.NewPager(runtime.PagingHandler[armlogic.WorkflowRunsClientListResponse]{
		More: func(page armlogic.WorkflowRunsClientListResponse) bool {
			return true
		},
		Fetcher: func(ctx context.Context, page *armlogic.WorkflowRunsClientListResponse) (armlogic.WorkflowRunsClientListResponse, error) {
			return armlogic.WorkflowRunsClientListResponse{}, errors.New("pager error")
		},
	})

	mockWorkflowClient.On("NewListPager", "test-resource-group", "test-workflow", mock.Anything).Return(pager)

	ctx := context.Background()
	duration := 24 * time.Hour

	result := GetRunHistoryInternal(ctx, duration, mockWorkflowClient, mockActionsClient)

	// Should return empty map when pager fails
	assert.NotNil(t, result)
	assert.Len(t, result, 0)

	mockWorkflowClient.AssertExpectations(t)
}

// Test GetRunHistoryInternal with mixed success and failure scenarios
func TestGetRunHistoryInternal_MixedResults(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Mock HTTP server for successful requests
	requestServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		notification := Notification{
			Title:   "Test Notification",
			Message: "Test message",
		}
		response := map[string]interface{}{
			"body": notification,
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer requestServer.Close()

	actionServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"body": map[string]interface{}{
				"submitActionId": "Release",
			},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer actionServer.Close()

	// Create test runs - one successful, one with missing trigger
	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now()

	successfulRun := createTestWorkflowRun("test-run-success", armlogic.WorkflowStatusSucceeded, startTime, &endTime, &requestServer.URL)
	failedRun := createTestWorkflowRun("test-run-failed", armlogic.WorkflowStatusSucceeded, startTime, &endTime, nil) // No trigger link

	runs := []*armlogic.WorkflowRun{successfulRun, failedRun}

	// Create mock clients
	mockWorkflowClient := new(MockWorkflowRunsInterface)
	mockActionsClient := new(MockWorkflowRunActionsInterface)

	// Set up pager mock
	pager := createMockPager(runs)
	mockWorkflowClient.On("NewListPager", "test-resource-group", "test-workflow", mock.Anything).Return(pager)

	// Set up actions client mock for successful run only
	actionResponse := armlogic.WorkflowRunActionsClientGetResponse{
		WorkflowRunAction: armlogic.WorkflowRunAction{
			Properties: &armlogic.WorkflowRunActionProperties{
				Status:    &[]armlogic.WorkflowStatus{armlogic.WorkflowStatusSucceeded}[0],
				StartTime: &startTime,
				EndTime:   &endTime,
				OutputsLink: &armlogic.ContentLink{
					URI: &actionServer.URL,
				},
			},
		},
	}

	mockActionsClient.On("Get", mock.Anything, "test-resource-group", "test-workflow", "test-run-success", "test-action", mock.Anything).Return(actionResponse, nil)

	ctx := context.Background()
	duration := 24 * time.Hour

	result := GetRunHistoryInternal(ctx, duration, mockWorkflowClient, mockActionsClient)

	// Should only contain the successful run
	assert.NotNil(t, result)
	assert.Len(t, result, 1)
	assert.Contains(t, result, "test-run-success")
	assert.NotContains(t, result, "test-run-failed")

	// Verify successful run details
	successRun := result["test-run-success"]
	assert.NotNil(t, successRun)
	assert.Equal(t, "test-run-success", successRun.ID)
	assert.Equal(t, armlogic.WorkflowStatusSucceeded, successRun.Status)

	mockWorkflowClient.AssertExpectations(t)
	mockActionsClient.AssertExpectations(t)
}

// Test GetRunHistoryInternal with empty results
func TestGetRunHistoryInternal_EmptyResults(t *testing.T) {
	setupRunHistoryTest()
	defer teardownRunHistoryTest()

	// Create mock clients
	mockWorkflowClient := new(MockWorkflowRunsInterface)
	mockActionsClient := new(MockWorkflowRunActionsInterface)

	// Set up pager mock with no runs
	pager := createMockPager([]*armlogic.WorkflowRun{})
	mockWorkflowClient.On("NewListPager", "test-resource-group", "test-workflow", mock.Anything).Return(pager)

	ctx := context.Background()
	duration := 24 * time.Hour

	result := GetRunHistoryInternal(ctx, duration, mockWorkflowClient, mockActionsClient)

	assert.NotNil(t, result)
	assert.Len(t, result, 0)

	mockWorkflowClient.AssertExpectations(t)
}
