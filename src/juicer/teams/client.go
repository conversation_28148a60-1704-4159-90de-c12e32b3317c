package teams

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"juicer/experiment"
	"juicer/utils"
	"log/slog"
	"net/http"
	"os"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/sethvargo/go-envconfig"
)

type Client struct {
	*Config
	cred  *azidentity.WorkloadIdentityCredential
	token *string
}

func NewClient() (*Client, error) {
	var config Config
	if err := envconfig.Process(context.Background(), &config); err != nil {
		slog.Error("Failed to process environment variables", "error", err)
		return nil, err
	}

	// Get the Workload Identity Credential
	cred, err := azidentity.NewWorkloadIdentityCredential(nil)
	if err != nil {
		slog.Error("Failed to create Workload Identity Credential", "error", err)
		return nil, err
	}

	// Get Token for Logic App
	slog.Info("Getting user token for Logic App...")
	token, err := GetUserToken(cred, []string{"https://management.azure.com/.default"})
	if err != nil {
		slog.Error("Failed to get user token", "error", err)
		return nil, err
	}
	slog.Info("Successfully retrieved user token")

	return &Client{
		Config: &config,
		cred:   cred,
		token:  token,
	}, nil
}

func (c *Client) GetLogicAppTriggerUrl() (*string, error) {
	logicAppGetTriggerUrl := fmt.Sprintf(
		"https://management.azure.com/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Logic/workflows/%s/triggers/%s/listCallbackUrl?api-version=%s",
		c.LogicAppSubscriptionID, c.LogicAppResourceGroup, c.LogicAppWorkflowName, c.LogicAppTriggerName, c.LogicAppAPIVersion,
	)

	req, err := http.NewRequest("POST", logicAppGetTriggerUrl, bytes.NewBuffer([]byte("{}")))
	if err != nil {
		slog.Error("Error creating request:", "error", err)
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+*c.token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		slog.Error("Error sending request:", "error", err)
		return nil, err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode == http.StatusOK {
		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			slog.Error("Error parsing response:", "error", err)
			return nil, err
		}
		logicAppTriggerUrl := result["value"].(string)

		return &logicAppTriggerUrl, nil
	} else {
		slog.Error("Failed to retrieve URL", "status", resp.Status, "response", string(body))
	}

	return nil, err
}

func (c *Client) SendTeamsMessageWithLogicApp(ctx context.Context, message string, responseRequested bool, experiments map[string]*experiment.InactiveExperiment) error {
	// Get the Logic App Trigger URL
	slog.Info("Getting Logic App Trigger URL...")
	logicAppTriggerURL, err := c.GetLogicAppTriggerUrl()
	if err != nil {
		slog.Error("Failed to get Logic App Trigger URL", "error", err)
		return err
	}
	slog.Info("Successfully retrieved Logic App Trigger URL")

	payload := Notification{
		Title:             "Idle GPU(s) detected",
		Message:           message,
		ResponseRequested: responseRequested,
		ClusterName:       os.Getenv("CLUSTER_NAME"),
		Experiments:       make([]Experiment, 0),
	}

	for _, exp := range experiments {
		experiment := Experiment{
			Name:          exp.Experiment,
			User:          fmt.Sprintf("%<EMAIL>", exp.Namespace), // Needs to be a valid email
			Namespace:     exp.Namespace,
			GPUCount:      exp.CurrentGPUCount,
			InactiveSince: exp.InactiveSince.Format(time.RFC3339),
			PriorityClass: exp.PriorityClass,
			TargetQuota:   exp.TargetQuota,
			GrafanaURL:    exp.GrafanaURL(),
			Status:        "Pending", // Default status
		}
		if exp.Paused {
			experiment.Status = "Paused"
		}
		payload.Experiments = append(payload.Experiments, experiment)
	}

	jsonBody, err := json.Marshal(payload)
	if err != nil {
		slog.Error("Failed to marshal payload", "error", err)
		return err
	}

	reqHeaders := map[string]string{"Content-Type": "application/json"}
	reqCtx, cancel := context.WithTimeout(ctx, utils.DEFAULT_TIMEOUT)
	defer cancel()

	if _, err := utils.HttpRequestWithContext(reqCtx, http.MethodPost, *logicAppTriggerURL, reqHeaders, jsonBody); err != nil {
		slog.Error("Webhook call failed", "error", err)
		return err
	}

	return nil
}

func GetUserToken(cred *azidentity.WorkloadIdentityCredential, scopes []string) (*string, error) {
	token, err := cred.GetToken(context.Background(), policy.TokenRequestOptions{
		Scopes: scopes,
	})
	if err != nil {
		return nil, err
	}

	return &token.Token, nil
}
