package teams

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"juicer/utils"
	"log"
	"log/slog"
	"net/http"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/logic/armlogic"
	"github.com/sethvargo/go-envconfig"
)

var workflowConfig Config

const (
	// 60000 read calls over 5mins. Equivalent to 200 calls per second. 20 if distibuted over 10 regions
	// Ref: https://learn.microsoft.com/en-us/azure/logic-apps/logic-apps-limits-and-config?tabs=consumption#throughput-limits
	MAX_REQUESTS_PER_SECOND = 20
	// Delay b/w requests once max request is reached
	REQUEST_DELAY = 1 * time.Second
)

type RunDetails struct {
	ID            string
	Status        armlogic.WorkflowStatus
	StartTime     time.Time
	EndTime       time.Time
	RequestData   *Notification
	ActionDetails *RunActionDetails
}

type RunActionDetails struct {
	Name           string
	Status         armlogic.WorkflowStatus
	StartTime      time.Time
	EndTime        time.Time
	OutputLinkURI  string
	ActionResponse string
}

func (a RunActionDetails) String() string {
	return fmt.Sprintf("{Name: %s, ActionResponse: %s, Status: %s, StartTime: %s, EndTime: %s}", a.Name, a.ActionResponse, a.Status, a.StartTime, a.EndTime)
}

func (r RunDetails) String() string {
	return fmt.Sprintf("{WorkflowRunID: %s, Status: %s, StartTime: %s, EndTime: %s, ActionDetails: %s, ResponseData: %s}",
		r.ID, r.Status, r.StartTime, r.EndTime, r.ActionDetails, r.RequestData)
}

func init() {
	if err := envconfig.Process(context.Background(), &workflowConfig); err != nil {
		slog.Error("Failed to process environment variables", "error", err)
		panic(err)
	}
}

// Return workload credential
func getCredential() (*azidentity.WorkloadIdentityCredential, error) {
	// Get the Workload Identity Credential
	cred, err := azidentity.NewWorkloadIdentityCredential(nil)
	if err != nil {
		slog.Error("Failed to create Workload Identity Credential", "error", err)
		return nil, err
	}

	return cred, nil
}

type WorkflowRunsInterface interface {
	NewListPager(resourceGroupName string, workflowName string, options *armlogic.WorkflowRunsClientListOptions) *runtime.Pager[armlogic.WorkflowRunsClientListResponse]
}

type WorkflowRunActionsInterface interface {
	Get(ctx context.Context, resourceGroupName string, workflowName string, runName string, actionName string, options *armlogic.WorkflowRunActionsClientGetOptions) (armlogic.WorkflowRunActionsClientGetResponse, error)
}

// Fetch action details for the run
func GetRunActionStatus(ctx context.Context, actionsClient WorkflowRunActionsInterface, runId string, actionName string) (*RunActionDetails, error) {
	actionResp, err := actionsClient.Get(ctx, workflowConfig.LogicAppResourceGroup, workflowConfig.LogicAppWorkflowName, runId, actionName, nil)

	if err != nil {
		log.Printf("failed to fetch action for runId %s. Err %v", runId, err)
		return nil, err
	}

	if actionResp.Properties == nil {
		return nil, errors.New("nil actionResp.Properties")
	}

	if actionResp.Properties.StartTime == nil {
		return nil, errors.New("nil actionResp.Properties.StartTime")
	}

	if actionResp.Properties.EndTime == nil {
		return nil, errors.New("nil actionResp.Properties.EndTime")
	}

	runActionDetails := RunActionDetails{
		Name:      actionName,
		Status:    *actionResp.Properties.Status,
		StartTime: *actionResp.Properties.StartTime,
		EndTime:   *actionResp.Properties.EndTime,
	}

	if runActionDetails.Status != armlogic.WorkflowStatusSucceeded {
		slog.Error("Unexpected action status", "workflowRunID", runId, "workflowRunActionName", runActionDetails.Name, "workflowRunActionStatus", runActionDetails.Status)
		return &runActionDetails, fmt.Errorf("unexpected action status %s", runActionDetails.Status)
	}

	if actionResp.Properties.OutputsLink == nil {
		slog.Error("No output link for action", "workflowRunID", runId, "workflowRunActionName", actionName)
		return &runActionDetails, errors.New("missing action response link")
	}

	reqCtx, cancel := context.WithTimeout(ctx, utils.DEFAULT_TIMEOUT)
	defer cancel()

	runActionDetails.OutputLinkURI = *actionResp.Properties.OutputsLink.URI
	responseBytes, err := utils.HttpRequestWithContext(reqCtx, http.MethodGet, runActionDetails.OutputLinkURI, nil, nil)
	if err != nil {
		slog.Error("Could not fetch run action status", "workflowRunID", runId, "workflowRunActionName", actionName, "error", err)
		return &runActionDetails, err
	}

	responseJson := make(map[string]any)
	err = json.Unmarshal(responseBytes, &responseJson)
	if err != nil {
		slog.Error("Response could not be unmarshalled for run", "workflowRunID", runId, "workflowRunActionName", actionName, "error", err)
		return &runActionDetails, err
	}

	body, ok := responseJson["body"].(map[string]any)
	if !ok {
		slog.Error("Invalid or missing 'body' in responseJson", "workflowRunID", runId)
		return &runActionDetails, errors.New("invalid or missing 'body' in responseJson")
	}

	actionResponse, ok := body["submitActionId"].(string)
	if !ok {
		slog.Error("Missing or invalid 'submitActionId' in body", "workflowRunID", runId)
		return &runActionDetails, errors.New("missing or invalid 'submitActionId' in body")
	}

	runActionDetails.ActionResponse = actionResponse
	slog.Info("ActionResponse for run", "workflowRunID", runId, "workflowRunActionResponse", actionResponse)

	return &runActionDetails, nil
}

// Fetch input payload for the run
func GetRunRequestData(ctx context.Context, run *armlogic.WorkflowRun) (*Notification, error) {
	var requestData Notification
	var request map[string]json.RawMessage

	runID := *run.Name

	if run.Properties.Trigger == nil || run.Properties.Trigger.OutputsLink == nil {
		slog.Error("Unexpected, input payload link not present", "workflowRunID", runID)
		return &requestData, errors.New("input payload link missing")
	}

	reqCtx, cancel := context.WithTimeout(ctx, utils.DEFAULT_TIMEOUT)
	defer cancel()

	inputRequestPayloadUri := *run.Properties.Trigger.OutputsLink.URI
	responseBytes, err := utils.HttpRequestWithContext(reqCtx, http.MethodGet, inputRequestPayloadUri, nil, nil)

	if err != nil {
		slog.Error("Could not fetch run input payload", "workflowRunID", runID, "error", err)
		return &requestData, err
	}

	err = json.Unmarshal(responseBytes, &request)
	if err != nil {
		slog.Error("Failed to unmarshal input payload", "workflowRunID", runID, "error", err)
		return &requestData, err
	}

	err = json.Unmarshal(request["body"], &requestData)
	if err != nil {
		slog.Error("Error in unmarshalling response body to Notification struct", "workflowRunID", runID, "body", request["body"], "error", err)
		return &requestData, err
	}

	return &requestData, nil
}

// Return RunDetails
func GetRunDetails(ctx context.Context, run *armlogic.WorkflowRun, actionsClient WorkflowRunActionsInterface) (*RunDetails, error) {
	var err error
	var runDetails RunDetails

	if run == nil {
		return nil, errors.New("nil run")
	}

	if run.Name == nil {
		return nil, errors.New("nil run.Name")
	}

	if run.Properties == nil {
		return nil, errors.New("nil run.Properties")
	}

	if run.Properties.StartTime == nil {
		return nil, errors.New("nil run.Properties.StartTime")
	}

	if run.Properties.Status == nil {
		return nil, errors.New("nil run.Properties.Status")
	}

	runDetails.ID = *run.Name
	runDetails.Status = *run.Properties.Status
	runDetails.StartTime = *run.Properties.StartTime

	// fetching request payload earlier to improve logging
	runDetails.RequestData, err = GetRunRequestData(ctx, run)
	if err != nil {
		slog.Error("Failed to fetch request data", "workflowRunID", runDetails.ID, "error", err)
		return &runDetails, err
	}

	if run.Properties.EndTime == nil {
		slog.Info("Run has no endtime. Run is running, waiting, paused or suspended.", "workflowRunID", runDetails.ID, "workflowRunStatus", runDetails.Status)
		return &runDetails, nil
	}

	runDetails.EndTime = *run.Properties.EndTime

	if runDetails.Status != armlogic.WorkflowStatusSucceeded {
		slog.Info("Run not completed or failed", "workflowRunID", *run.Name, "workflowRunStatus", *run.Properties.Status)
		return &runDetails, nil
	}

	// fetching action details
	runDetails.ActionDetails, err = GetRunActionStatus(ctx, actionsClient, runDetails.ID, workflowConfig.LogicAppWorkflowActionName)
	if err != nil {
		slog.Error("Failed to fetch action details", "workflowRunID", runDetails.ID, "error", err)
		return &runDetails, err
	}

	return &runDetails, err
}

// Return filter string for querying run history for given duration
func GetFilterForDurationSince(since time.Duration) string {
	startTime := time.Now().Add(-since)
	endTime := time.Now()
	return fmt.Sprintf("startTime ge %s and startTime le %s", startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))
}

// Get run history internal
func GetRunHistoryInternal(ctx context.Context, since time.Duration, workflowClient WorkflowRunsInterface, actionsClient WorkflowRunActionsInterface) map[string]*RunDetails {
	filter := GetFilterForDurationSince(since)
	slog.Info("Querying workflow with filter", "filter", filter)

	runsFetched := 0
	runHistory := make(map[string]*RunDetails)
	pager := workflowClient.NewListPager(workflowConfig.LogicAppResourceGroup, workflowConfig.LogicAppWorkflowName, &armlogic.WorkflowRunsClientListOptions{Filter: &filter})

	for pager.More() {
		page, err := pager.NextPage(ctx)
		if err != nil {
			slog.Error("Failed to fetch next page", "error", err)
			break
		}

		runsInBatch := 0
		batchStartTime := time.Now()
		slog.Info("Processing page", "runsCount", len(page.Value))
		for _, run := range page.Value {
			runsInBatch++
			runsFetched++

			slog.Info("Fetching run details", "workflowRunID", *run.Name)
			runDetails, err := GetRunDetails(ctx, run, actionsClient)

			if err != nil {
				slog.Error("Error in fetching run details", "workflowRunID", *run.Name, "runDetails", runDetails, "error", err)
			} else {
				slog.Info("Fetched run details", "workflowRunID", *run.Name, "runDetails", runDetails)
				runHistory[runDetails.ID] = runDetails
			}

			// Wait after processing allowed requests
			if runsInBatch%MAX_REQUESTS_PER_SECOND == 0 {
				if time.Since(batchStartTime) <= REQUEST_DELAY {
					slog.Info("Waiting after processing maxrequests/s", "batchStartTime", batchStartTime, "runsInBatch", runsInBatch, "delay", REQUEST_DELAY, "maxRequestsPerSecond", MAX_REQUESTS_PER_SECOND)
					time.Sleep(REQUEST_DELAY)
				}
				runsInBatch = 0
				batchStartTime = time.Now()
			}
		}
	}

	slog.Info("RunHistory fetch completed", "TotalRunsFetched", runsFetched, "TotalRunsInHistory", len(runHistory))
	return runHistory
}

// Fetch run history for `since` duration
func GetRunHistory(ctx context.Context, since time.Duration) map[string]*RunDetails {
	cred, err := getCredential()
	if err != nil {
		log.Fatalf("failed to obtain credential: %v", err)
	}

	workflowClient, err := armlogic.NewWorkflowRunsClient(workflowConfig.LogicAppSubscriptionID, cred, nil)
	if err != nil {
		log.Fatalf("failed to create WorkflowRunsClient: %v", err)
	}

	actionsClient, err := armlogic.NewWorkflowRunActionsClient(workflowConfig.LogicAppSubscriptionID, cred, nil)
	if err != nil {
		log.Fatalf("failed to create WorkflowRunActionsClient: %v", err)
	}

	return GetRunHistoryInternal(ctx, since, workflowClient, actionsClient)
}
