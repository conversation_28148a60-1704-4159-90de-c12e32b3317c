package teams

import "fmt"

type Notification struct {
	Title             string       `json:"title"`
	Message           string       `json:"message"`
	ResponseRequested bool         `json:"response_requested"`
	ClusterName       string       `json:"cluster_name"`
	Experiments       []Experiment `json:"experiments"`
}

type Experiment struct {
	Name          string `json:"name"`
	User          string `json:"user"`
	Namespace     string `json:"namespace"`
	GPUCount      int    `json:"gpu_count"`
	InactiveSince string `json:"inactive_since"`
	PriorityClass string `json:"priority_class"`
	TargetQuota   string `json:"target_quota"`
	GrafanaURL    string `json:"grafana_url"`
	Status        string `json:"status"`
}

func (e Experiment) String() string {
	return fmt.Sprintf("{Name: %s, User: %s, Namespace: %s, GPUCount: %d, InactiveSince: %s, Status: %s, PriorityClass: %s, TargetQuota: %s, GrafanaURL: %s}",
		e.Name, e.User, e.Namespace, e.<PERSON>UCount, e.InactiveSince, e.Status, e.PriorityClass, e.<PERSON>Q<PERSON><PERSON>, e.<PERSON>L)
}

func (n Notification) String() string {
	return fmt.Sprintf("{ResponseRequested: %t, ClusterName: %s, Experiments: %v}",
		n.ResponseRequested, n.ClusterName, n.Experiments)
}
