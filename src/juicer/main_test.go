package main

import (
	"context"
	"fmt"
	"juicer/teams"
	"testing"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/logic/armlogic"
	"github.com/stretchr/testify/assert"
)

func TeardownTestEnvironment() {
	teamsGetRunHistory = teams.GetRunHistory
}

func TestNotificationKind_Default(t *testing.T) {
	// Arrange
	timezone := time.FixedZone("PDT", -7*60*60)
	pdtTime := time.Date(2023, 3, 10, 11, 0, 0, 0, timezone)
	// Execute test
	notificationKind := GetNotificationKind(pdtTime)
	// Verify results
	assert.Equal(t, DefaultNotification, notificationKind)
}

func TestNotificationKind_Default_ChannelHourMinsGreaterThanConfigured(t *testing.T) {
	// Arrange
	timezone := time.FixedZone("PDT", -7*60*60)
	pdtTime := time.Date(2023, 3, 10, 10, 7, 0, 0, timezone)
	// Execute test
	notificationKind := GetNotificationKind(pdtTime)
	// Verify results
	assert.Equal(t, DefaultNotification, notificationKind)
}

func TestNotificationKind_Default_DefaultHourMinsGreaterThanConfigured(t *testing.T) {
	// Arrange
	timezone := time.FixedZone("PDT", -7*60*60)
	pdtTime := time.Date(2023, 3, 10, 11, 7, 0, 0, timezone)
	// Execute test
	notificationKind := GetNotificationKind(pdtTime)
	// Verify results
	assert.Equal(t, DefaultNotification, notificationKind)
}

func TestNotificationKind_Default_DMHourMinsGreaterThanConfigured(t *testing.T) {
	// Arrange
	timezone := time.FixedZone("PDT", -7*60*60)
	pdtTime := time.Date(2023, 3, 10, 12, 7, 0, 0, timezone)
	// Execute test
	notificationKind := GetNotificationKind(pdtTime)
	// Verify results
	assert.Equal(t, DefaultNotification, notificationKind)
}

func TestNotificationKind_ChannelNotification(t *testing.T) {
	// Arrange
	timezone := time.FixedZone("PDT", -7*60*60)
	pdtTime := time.Date(2023, 3, 10, 10, 0, 0, 0, timezone)
	// Execute test
	notificationKind := GetNotificationKind(pdtTime)
	// Verify results
	assert.Equal(t, ChannelNotification, notificationKind)
}

func TestNotificationKind_DirectNotification(t *testing.T) {
	// Arrange
	timezone := time.FixedZone("PDT", -7*60*60)
	pdtTime := time.Date(2023, 3, 10, 12, 0, 0, 0, timezone)
	// Execute test
	notificationKind := GetNotificationKind(pdtTime)
	// Verify results
	assert.Equal(t, DirectMessage, notificationKind)
}

func TestGetActionResponse_nilAction(t *testing.T) {
	// Arrange
	runDetails := teams.RunDetails{}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionDefault, actionResponse)
}

func TestGetActionResponse_WorkflowStatusTimedOut(t *testing.T) {
	// Arrange
	runDetails := teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:   "test-action",
			Status: armlogic.WorkflowStatusTimedOut,
		},
	}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionDefault, actionResponse)
}

func TestGetActionResponse_WorkflowStatusSucessful_NoRequestData(t *testing.T) {
	// Arrange for nil requestData
	runDetails := teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:   "test-action",
			Status: armlogic.WorkflowStatusSucceeded,
		},
	}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionDefault, actionResponse)

	// Arrange for no experiments in requestData
	runDetails = teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:   "test-action",
			Status: armlogic.WorkflowStatusSucceeded,
		},
		RequestData: &teams.Notification{
			Experiments: nil,
		},
	}
	// Execute test
	actionResponse = GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionDefault, actionResponse)
}

func TestGetActionResponse_WorkflowStatusSucessful_NoActionForExperiment(t *testing.T) {
	// Arrange
	runDetails := teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:   "test-action",
			Status: armlogic.WorkflowStatusSucceeded,
		},
		RequestData: &teams.Notification{
			Experiments: []teams.Experiment{
				{
					Name:      "test-experiment",
					Namespace: "test-namespace",
				},
			},
		},
	}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/other-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionDefault, actionResponse)
}

func TestGetActionResponse_WorkflowStatusSucessful_ReleaseActionForExperiment(t *testing.T) {
	// Arrange
	runDetails := teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:           "test-action",
			Status:         armlogic.WorkflowStatusSucceeded,
			ActionResponse: ActionRelease,
		},
		RequestData: &teams.Notification{
			Experiments: []teams.Experiment{
				{
					Name:      "test-experiment",
					Namespace: "test-namespace",
				},
			},
		},
	}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionRelease, actionResponse)
}

func TestGetActionResponse_WorkflowStatusSucessful_PostponeActionForExperiment_DurationLessThan24hrs(t *testing.T) {
	// Arrange
	runDetails := teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:           "test-action",
			Status:         armlogic.WorkflowStatusSucceeded,
			ActionResponse: ActionPostpone,
			EndTime:        time.Now().Add(-23*time.Hour - 59*time.Minute), // Less than 24 hours
		},
		RequestData: &teams.Notification{
			Experiments: []teams.Experiment{
				{
					Name:      "test-experiment",
					Namespace: "test-namespace",
				},
			},
		},
	}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionPostpone, actionResponse)
}

func TestGetActionResponse_WorkflowStatusSucessful_PostponeActionForExperiment_DurationEqualToOrMorethan24hrs(t *testing.T) {
	// Arrange
	startTime := time.Now().Add(-24 * time.Hour)
	runDetails := teams.RunDetails{
		ActionDetails: &teams.RunActionDetails{
			Name:           "test-action",
			Status:         armlogic.WorkflowStatusSucceeded,
			ActionResponse: ActionPostpone,
		},
		RequestData: &teams.Notification{
			Experiments: []teams.Experiment{
				{
					Name:      "test-experiment",
					Namespace: "test-namespace",
				},
			},
		},
		StartTime: startTime,
	}
	// Execute test
	actionResponse := GetActionResponse(&runDetails, "test-cluster/test-namespace/test-experiment")
	// Verify results
	assert.NotNil(t, actionResponse)
	assert.Equal(t, ActionNotifyOnly, actionResponse)
}

func TestGetRunHistory_EmptyResults(t *testing.T) {
	defer TeardownTestEnvironment()

	// Mock teams.GetRunHistory to return empty results
	teamsGetRunHistory = func(ctx context.Context, since time.Duration) map[string]*teams.RunDetails {
		return make(map[string]*teams.RunDetails)
	}

	// Execute test
	ctx := context.Background()
	result := GetRunsAction(ctx)

	// Verify results
	assert.NotNil(t, result)
	assert.Equal(t, 0, len(result))
}

func TestGetRunHistory_WithSuccessfulRuns(t *testing.T) {
	defer TeardownTestEnvironment()

	// Mock teams.GetRunHistory to return test data
	teamsGetRunHistory = func(ctx context.Context, since time.Duration) map[string]*teams.RunDetails {
		return map[string]*teams.RunDetails{
			"run1": {
				ID:     "run1",
				Status: armlogic.WorkflowStatusSucceeded,
				ActionDetails: &teams.RunActionDetails{
					Name:           "test-action",
					Status:         armlogic.WorkflowStatusSucceeded,
					ActionResponse: ActionRelease,
				},
				RequestData: &teams.Notification{
					ClusterName:       "test-cluster",
					ResponseRequested: true,
					Experiments: []teams.Experiment{
						{
							Name:      "test-experiment",
							Namespace: "test-namespace",
						},
					},
				},
				StartTime: time.Now().Add(-time.Hour),
			},
			"run2": {
				ID:     "run2",
				Status: armlogic.WorkflowStatusSucceeded,
				RequestData: &teams.Notification{
					ClusterName: "test-cluster",
					Experiments: []teams.Experiment{
						{
							Name:      "test-experiment01",
							Namespace: "test-namespace01",
						},
						{
							Name:      "test-experiment02",
							Namespace: "test-namespace02",
						},
					},
				},
				StartTime: time.Now().Add(-time.Hour),
			},
			"run3": {
				ID:     "run3",
				Status: armlogic.WorkflowStatusRunning,
			},
			"run4": {
				ID:     "run4",
				Status: armlogic.WorkflowStatusTimedOut,
			},
		}
	}

	// Execute test
	ctx := context.Background()
	result := GetRunsAction(ctx)

	// Verify results
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result)) // Only successful runs with valid experiments should be returned

	// Verify the successful run is processed correctly
	expectedExperimentID := "test-cluster/test-namespace/test-experiment"
	foundRun := false
	for _, runResponse := range result {
		if runResponse.ExperimentID == expectedExperimentID {
			foundRun = true
			assert.Equal(t, ActionRelease, runResponse.Action)
		}
	}
	assert.True(t, foundRun, "Should find the processed run")
}

func TestGetRunHistory_PostponeLogic(t *testing.T) {
	defer TeardownTestEnvironment()

	// Mock teams.GetRunHistory to return postponed runs
	teamsGetRunHistory = func(ctx context.Context, since time.Duration) map[string]*teams.RunDetails {
		return map[string]*teams.RunDetails{
			"postponed-recent": {
				ID:     "postponed-recent",
				Status: armlogic.WorkflowStatusSucceeded,
				ActionDetails: &teams.RunActionDetails{
					Name:           "test-action",
					Status:         armlogic.WorkflowStatusSucceeded,
					ActionResponse: ActionPostpone,
					EndTime:        time.Now().Add(-10 * time.Hour), // Less than 24 hours
				},
				RequestData: &teams.Notification{
					ClusterName:       "test-cluster",
					ResponseRequested: true,
					Experiments: []teams.Experiment{
						{
							Name:      "recent-experiment",
							Namespace: "test-namespace",
						},
					},
				},
			},
			"postponed-old": {
				ID:     "postponed-old",
				Status: armlogic.WorkflowStatusSucceeded,
				ActionDetails: &teams.RunActionDetails{
					Name:           "test-action",
					Status:         armlogic.WorkflowStatusSucceeded,
					ActionResponse: ActionPostpone,
					EndTime:        time.Now().Add(-25 * time.Hour), // More than 24 hours
				},
				RequestData: &teams.Notification{
					ClusterName:       "test-cluster",
					ResponseRequested: true,
					Experiments: []teams.Experiment{
						{
							Name:      "old-experiment",
							Namespace: "test-namespace",
						},
					},
				},
			},
		}
	}

	// Execute test
	ctx := context.Background()
	result := GetRunsAction(ctx)
	// Verify results
	assert.NotNil(t, result)
	assert.Equal(t, 2, len(result)) // Both runs should return results, but with different actions

	// Verify the postponed run logic
	recentFound := false
	oldFound := false
	for _, runResponse := range result {
		if runResponse.ExperimentID == "test-cluster/test-namespace/recent-experiment" {
			recentFound = true
			assert.Equal(t, ActionPostpone, runResponse.Action)
		}
		if runResponse.ExperimentID == "test-cluster/test-namespace/old-experiment" {
			oldFound = true
			assert.Equal(t, ActionNotifyOnly, runResponse.Action)
		}
	}
	assert.True(t, recentFound, "Should find the recent postponed run")
	assert.True(t, oldFound, "Should find the old postponed run")
}

func TestGetRunHistory_NilRequestData(t *testing.T) {
	defer TeardownTestEnvironment()

	// Mock teams.GetRunHistory to return runs with nil request data
	teamsGetRunHistory = func(ctx context.Context, since time.Duration) map[string]*teams.RunDetails {
		return map[string]*teams.RunDetails{
			"nil-request": {
				ID:     "nil-request",
				Status: armlogic.WorkflowStatusSucceeded,
				ActionDetails: &teams.RunActionDetails{
					Name:           "test-action",
					Status:         armlogic.WorkflowStatusSucceeded,
					ActionResponse: ActionRelease,
				},
				RequestData: nil, // This should result in default action
				StartTime:   time.Now().Add(-time.Hour),
			},
		}
	}

	// Execute test
	ctx := context.Background()
	result := GetRunsAction(ctx)

	// Verify results - runs with nil request data should not produce actionable responses
	assert.NotNil(t, result)
	assert.Equal(t, 0, len(result))
}

func TestGetRunHistory_ConcurrentProcessing(t *testing.T) {
	defer TeardownTestEnvironment()

	// Mock teams.GetRunHistory to return multiple successful runs
	teamsGetRunHistory = func(ctx context.Context, since time.Duration) map[string]*teams.RunDetails {
		runs := make(map[string]*teams.RunDetails)

		// Create 10 successful runs
		for i := 0; i < 10; i++ {
			runID := fmt.Sprintf("run%d", i)
			runs[runID] = &teams.RunDetails{
				ID:     runID,
				Status: armlogic.WorkflowStatusSucceeded,
				ActionDetails: &teams.RunActionDetails{
					Name:           "test-action",
					Status:         armlogic.WorkflowStatusSucceeded,
					ActionResponse: ActionRelease,
				},
				RequestData: &teams.Notification{
					ClusterName:       "test-cluster",
					ResponseRequested: true,
					Experiments: []teams.Experiment{
						{
							Name:      fmt.Sprintf("experiment%d", i),
							Namespace: fmt.Sprintf("namespace%d", i),
						},
					},
				},
				StartTime: time.Now().Add(-time.Hour),
			}
		}

		return runs
	}

	// Execute test
	ctx := context.Background()
	result := GetRunsAction(ctx)

	// Verify results
	assert.NotNil(t, result)
	assert.Equal(t, 10, len(result))

	// Verify all runs are processed
	for i := 0; i < 10; i++ {
		expectedExpID := fmt.Sprintf("test-cluster/namespace%d/experiment%d", i, i)

		found := false
		for _, runResponse := range result {
			if runResponse.ExperimentID == expectedExpID {
				found = true
				assert.Equal(t, ActionRelease, runResponse.Action)
				break
			}
		}
		assert.True(t, found, "Should find experiment experiment%d", i)
	}
}
