package main

import (
	"context"
	"fmt"
	"juicer/experiment"
	"juicer/teams"
	"log/slog"
	"math/rand/v2"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/logic/armlogic"
	brixclientset "github.com/openai/brix/pkg/client/clientset/versioned"
	"github.com/sethvargo/go-envconfig"
	"k8s.io/client-go/rest"
)

const (
	directMessage = "Your job **%s** has been inactive for more than 2hrs. " +
		"If your job remains idle for %s or more, it will be automatically terminated to conserve resources."
	notificationMessage = "Hello Orange users, below jobs has been inactive in last *%s*. " +
		"If your job remains idle for *%s*, it will be terminated. " +
		"For more details, including GPU utilization, please check the Job link below. " +
		"If you have any questions, please tag Orange Infra for help. "
	autoPausedNotificationMessage = "Your experiment *%s* has been inactive since *%s* and it is auto-paused. " +
		"If we have paused your devbox by mistake, one way to avoid this is to `kubectl label pool <pool-name> type=devbox --context <cluster>`. " +
		"If you have any questions, please tag Orange Infra for help. "
)

type NotificationKind string

const (
	DirectMessage       NotificationKind = "DirectMessage"
	ChannelNotification NotificationKind = "ChannelNotification"
	DefaultNotification NotificationKind = "DefaultNotification"
)

const (
	ActionDefault    string = "Default"
	ActionRelease    string = "Release"
	ActionPostpone   string = "Postpone"
	ActionNotifyOnly string = "NotifyOnly"
)

type config struct {
	SkipNotificationPastMins   time.Duration `env:"SKIP_NOTIFICATION_MINS,default=5m"`
	DirectNotificationPeriod   time.Duration `env:"DIRECT_NOTIFICATION_PERIOD,default=2h"`
	ChannelNotificationPeriod  time.Duration `env:"CHANNEL_NOTIFICATION_PERIOD,default=8h"`
	PostponeNotificationPeriod time.Duration `env:"POSTPONE_NOTIFICATION_PERIOD,default=24h"`
	RunHistoryLookBackPeriod   time.Duration `env:"RUN_HISTORY_LOOKBACK_PERIOD,default=33h"` // PostponeNotificationPeriod + ChannelNotificationPeriod + 1hr
	AutoPausePeriod            time.Duration `env:"AUTO_PAUSE_PERIOD,default=12h"`
	DryRun                     bool          `env:"DRY_RUN,default=false"`
	SkipSpecialPool            bool          `env:"SKIP_SPECIAL_POOL,default=false"`
}

var cfg config

// Function variable for dependency injection in tests
var teamsGetRunHistory = teams.GetRunHistory

func init() {
	// get the configuration
	if err := envconfig.Process(context.Background(), &cfg); err != nil {
		slog.Error("Failed to process environment variables", "error", err)
		panic(err)
	}
}

// Return notification kind based on PDT location hour
func GetNotificationKind(localTime time.Time) NotificationKind {
	var notificationKind NotificationKind
	pdtHour := localTime.Hour()
	pdtMins := localTime.Minute()

	// To ensure channel notification at 10,18,2 PDT
	// DM at hrs divisble by 2, other than during channel notification hrs
	if pdtHour == 10 || pdtHour == 18 || pdtHour == 2 {
		notificationKind = ChannelNotification
	} else if pdtHour%2 == 0 {
		notificationKind = DirectMessage
	} else {
		notificationKind = DefaultNotification
	}

	// Skip notifications if minutes past hour greater than configured
	// This will be useful if say we reduce cron schedule to every 5mins
	if pdtMins > int(cfg.SkipNotificationPastMins.Minutes()) {
		slog.Warn("Minutes past hour greater than configured, skipping notification", "hour", pdtHour, "minutes", pdtMins)
		notificationKind = DefaultNotification
	}

	slog.Info("GetNotificationKind", "NotificationKind", notificationKind, "PDTHour", pdtHour, "PDTMins", pdtMins)
	return notificationKind
}

type RunActionResponse struct {
	RunDetails   *teams.RunDetails
	ExperimentID string
	Action       string
}

func (r RunActionResponse) String() string {
	return fmt.Sprintf("{ExperimentID: %s, Action: %s, RunDetails.ID: %s, RunDetails.StartTime: %s}", r.ExperimentID, r.Action, r.RunDetails.ID, r.RunDetails.StartTime)
}

// Process Action Response of a Workflow Run
// Default DM action handling would notify but wont pause (autopause might still pause the exp after AutoPausePeriod)
func GetActionResponse(runDetails *teams.RunDetails, expID string) string {
	runID := runDetails.ID
	action := runDetails.ActionDetails

	if action == nil {
		slog.Error("No action response", "workflowRunID", runID)
		return ActionDefault
	}

	if action.Status != armlogic.WorkflowStatusSucceeded {
		slog.Info("Unexpected workflow status", "workflowRunID", runID, "workflowRunActionStatus", action.Status)
		return ActionDefault
	}

	if runDetails.RequestData == nil || len(runDetails.RequestData.Experiments) == 0 {
		slog.Error("Missing experiment data", "workflowRunID", runID)
		return ActionDefault
	}

	switch action.ActionResponse {
	case ActionRelease:
		slog.Info("Release action response. Should pause", "workflowRunID", runID, "experiment", expID)
		return ActionRelease
	case ActionPostpone:
		// time.Since ensures correct calculation of duration left regardless of tz diff
		timeLeft := cfg.PostponeNotificationPeriod - time.Since(runDetails.ActionDetails.EndTime)
		if timeLeft > 0 {
			slog.Info("Postpone action response. Should skip notification & pause", "workflowRunID", runID, "experiment", expID, "durationLeftInMins", timeLeft.Minutes())
			return ActionPostpone
		}
		slog.Info("Postpone action response. Postpone duration expired. Should notify but not pause", "workflowRunID", runID, "experiment", expID)
		return ActionNotifyOnly
	default:
		slog.Warn("Unhandled action response", "workflowRunID", runID, "experiment", expID, "response", action.ActionResponse)
	}

	return ActionDefault
}

// Process workflow run history
func GetRunsAction(ctx context.Context) map[string]RunActionResponse {
	start := time.Now()
	runActionResponse := make(map[string]RunActionResponse)

	// Configure for how long experiment should not be auto-paused after postponed duration
	// Setting RunHistoryLookBackPeriod to 33hrs effectively ensures user get 3 or 4 direct messages to respond to
	slog.Info("Fetching run history", "runHistoryDurationInMins", cfg.RunHistoryLookBackPeriod.Minutes())
	runHistory := teamsGetRunHistory(ctx, cfg.RunHistoryLookBackPeriod)
	slog.Info("Run history fetched", "duration", time.Since(start))

	if len(runHistory) <= 0 {
		slog.Info("No runs found", "runHistoryDurationInMins", cfg.RunHistoryLookBackPeriod.Minutes())
		return runActionResponse
	}

	start = time.Now()
	for runID, run := range runHistory {
		slog.Info("Processing run", "workflowRunID", runID, "workflowRunStatus", run.Status)
		switch run.Status {
		case armlogic.WorkflowStatusSucceeded:

			if run.RequestData == nil || len(run.RequestData.Experiments) == 0 {
				slog.Error("Missing experiment data", "workflowRunID", runID)
				continue
			}

			if !run.RequestData.ResponseRequested {
				slog.Info("Skipping run, response not requested", "workflowRunID", runID, "responseRequested", run.RequestData.ResponseRequested)
				continue
			}

			exp := run.RequestData.Experiments[0]
			expID := experiment.GetIdentifier(run.RequestData.ClusterName, exp.Namespace, exp.Name)

			actionResponse := RunActionResponse{
				RunDetails:   run,
				ExperimentID: expID,
				Action:       GetActionResponse(run, expID),
			}

			// Check for existing experimentID & override with latest entry
			if existing, ok := runActionResponse[actionResponse.ExperimentID]; ok {
				slog.Warn("Multiple run history entry for same experiment, overriding with latest", "experimentID", actionResponse.ExperimentID, "existing", existing, "new", actionResponse)
				if actionResponse.RunDetails.StartTime.After(existing.RunDetails.StartTime) {
					runActionResponse[actionResponse.ExperimentID] = actionResponse
					slog.Info("Overridden with latest run", "existingAction", existing.Action, "newAction", actionResponse.Action, "experimentID", actionResponse.ExperimentID, "workflowRunID", actionResponse.RunDetails.ID)
				}
			} else {
				runActionResponse[actionResponse.ExperimentID] = actionResponse
			}

		case armlogic.WorkflowStatusRunning:
			slog.Info("Run still in progress", "workflowRunID", runID)
		case armlogic.WorkflowStatusTimedOut:
			slog.Info("Run timed out", "workflowRunID", runID)
		default:
			slog.Warn("Unexpected run status", "workflowRunID", runID, "workflowRunStatus", run.Status)
		}
	}

	slog.Info("Run history processing complete", "duration", time.Since(start))
	return runActionResponse
}

// Send direct message to orange user
func SendDirectMessage(ctx context.Context,
	brixClient *brixclientset.Clientset,
	runHistory map[string]RunActionResponse,
	inactiveExperiments []*experiment.InactiveExperiment,
	pausedExperiments map[string]*experiment.InactiveExperiment,
) {
	if len(inactiveExperiments) == 0 {
		slog.Info("No inactive experiments to notify")
		return
	}

	// Wait before sending message
	time.Sleep(10 * time.Second)

	// Send notification
	for _, exp := range inactiveExperiments {
		decision := exp.ShouldSkip(brixClient, cfg.SkipSpecialPool)
		if decision.Skip {
			slog.Info("Experiment pause & notify skipped", "experiment", exp.Experiment, "namespace", exp.Namespace, "reason", decision.Reason, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
			continue
		}

		if _, ok := pausedExperiments[exp.GetIdentifier()]; ok {
			slog.Info("Experiment already paused, skipping notify", "experiment", exp.Experiment, "namespace", exp.Namespace, "reason", "paused", "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
			continue
		}

		actionResponse, ok := runHistory[exp.GetIdentifier()]
		if ok && actionResponse.Action == ActionPostpone {
			slog.Info("Skipping notify for direct messaging", "experiment", exp.Experiment, "namespace", exp.Namespace, "reason", "postponed", "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
			continue
		}

		// Wait before sending message
		time.Sleep(time.Second)

		// Notify user if not pausing experiment
		slog.Info("Sending direct message", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
		message := fmt.Sprintf(directMessage, exp.Experiment, cfg.AutoPausePeriod)

		if err := Notify(ctx, message, true, map[string]*experiment.InactiveExperiment{exp.GetIdentifier(): exp}); err != nil {
			slog.Error("Failed to send message", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince, "error", err)
		} else {
			slog.Info("Sent direct message", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
		}
	}

	slog.Info("Sending direct message completed")
}

// Send channel notification for each cluster with list of unpaused experiments
func SendChannelNotification(ctx context.Context, brixClient *brixclientset.Clientset, cluster *experiment.Cluster, autoPausedExperiments map[string]*experiment.InactiveExperiment) {
	// Wait before querying Prometheus
	time.Sleep(10 * time.Second)

	// fetch list of inactive experiments for past ChannelNotificationPeriod
	experiments, err := cluster.GetInactiveExperiments(ctx, cfg.ChannelNotificationPeriod)
	if err != nil {
		slog.Error("Failed to get inactive experiments", "error", err)
		panic(err)
	}
	slog.Info(fmt.Sprintf("Found %d inactive experiments", len(experiments)))

	notifying := make(map[string]*experiment.InactiveExperiment)
	for _, exp := range experiments {
		decision := exp.ShouldSkip(brixClient, cfg.SkipSpecialPool)
		if decision.Skip {
			slog.Info("Skipping notification for experiment", "experiment", exp.Experiment, "namespace", exp.Namespace, "reason", decision.Reason, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
			continue
		}
		slog.Info("Notifying experiment", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
		notifying[exp.GetIdentifier()] = exp
	}

	if len(notifying) == 0 {
		slog.Info("No experiments to notify")
		return
	}

	for _, exp := range autoPausedExperiments {
		if _, exist := notifying[exp.GetIdentifier()]; !exist {
			slog.Warn("Experiment auto-paused but not in notification list", "experiment", exp.GetIdentifier())
			continue
		}
		notifying[exp.GetIdentifier()].Paused = true
	}

	// Wait before sending message
	time.Sleep(10 * time.Second)

	message := fmt.Sprintf(notificationMessage, cfg.ChannelNotificationPeriod, cfg.AutoPausePeriod)
	Notify(ctx, message, false, notifying)
}

func main() {
	// entire cron job should run within 5 minutes
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// get notification kind at the start of cron job
	notificationKind := GetNotificationKind(time.Now())

	// add a random delay of 0 - 5s
	time.Sleep(time.Millisecond * time.Duration(rand.IntN(5000)))

	brixClient, err := NewBrixClient()
	if err != nil {
		slog.Error("Failed to create Brix client", "error", err)
		panic(err)
	}

	cluster := experiment.NewCluster(ctx)

	// 1. Process run history and get action response for experiments
	runHistory := GetRunsAction(ctx)
	// 2. Auto-pause experiments
	autoPausedExperiments := AutoPause(ctx, cluster, brixClient, runHistory)
	// 3. Pause experiments for workflow run having release action
	inactiveExperimentsForDirectMsg, pausedExperiments := GetPausedExperimentsForReleaseWorkflowAction(ctx, cluster, brixClient, runHistory, autoPausedExperiments)

	// 4. Notify
	switch notificationKind {
	case ChannelNotification:
		slog.Info("sending channel notification")
		SendChannelNotification(ctx, brixClient, cluster, autoPausedExperiments)
	case DirectMessage:
		slog.Info("sending direct message")
		SendDirectMessage(ctx, brixClient, runHistory, inactiveExperimentsForDirectMsg, pausedExperiments)
	default:
		slog.Info("skipping notification")
	}
}

func Notify(ctx context.Context, message string, responseRequested bool, experiments map[string]*experiment.InactiveExperiment) error {
	slog.Info(fmt.Sprintf("Notifying %d inactive experiments", len(experiments)))

	teamsClient, err := teams.NewClient()
	if err != nil {
		slog.Error("Failed to create Teams client", "error", err)
		return err
	}

	// dry run mode
	if cfg.DryRun {
		slog.Info("Dry run mode enabled, skipping actual notification")
		for _, exp := range experiments {
			slog.Info("Dry run notification for experiment", "experiment", exp.Experiment, "namespace", exp.Namespace)
		}
		return nil
	}

	// Send notification
	err = teamsClient.SendTeamsMessageWithLogicApp(ctx, message, responseRequested, experiments)
	if err != nil {
		slog.Error("Failed to send Teams notification", "error", err)
	}

	return err
}

// Process runhistory and pause experiments at every run
func GetPausedExperimentsForReleaseWorkflowAction(
	ctx context.Context,
	cluster *experiment.Cluster,
	brixClient *brixclientset.Clientset,
	runHistory map[string]RunActionResponse,
	autoPausedExperiments map[string]*experiment.InactiveExperiment,
) ([]*experiment.InactiveExperiment, map[string]*experiment.InactiveExperiment) {
	// Wait before querying Prometheus
	time.Sleep(10 * time.Second)
	pausedExperiments := make(map[string]*experiment.InactiveExperiment)

	// fetch list of inactive experiments for past DirectNotificationPeriod
	inactiveExperiments, err := cluster.GetInactiveExperiments(ctx, cfg.DirectNotificationPeriod)
	if err != nil {
		slog.Error("Failed to get inactive experiments", "error", err)
		panic(err)
	}

	slog.Info(fmt.Sprintf("Found %d inactive experiments for direct message inactive duration", len(inactiveExperiments)))

	for _, exp := range inactiveExperiments {
		decision := exp.ShouldSkip(brixClient, cfg.SkipSpecialPool)
		if decision.Skip {
			slog.Info("Experiment pause & notify skipped", "experiment", exp.Experiment, "namespace", exp.Namespace, "reason", decision.Reason, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
			continue
		}

		if _, ok := autoPausedExperiments[exp.GetIdentifier()]; ok {
			slog.Info("Experiment already auto-paused", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince)
			pausedExperiments[exp.GetIdentifier()] = exp
			continue
		}

		actionResponse, ok := runHistory[exp.GetIdentifier()]
		shouldPause := ok && actionResponse.Action == ActionRelease
		if !shouldPause {
			slog.Info("Skipping pause", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince, "reason", actionResponse.Action)
			continue
		}

		if exp.Pause(ctx, brixClient) {
			slog.Info("Experiment paused", "experiment", exp.Experiment, "namespace", exp.Namespace, "GPUs", exp.CurrentGPUCount, "since", exp.InactiveSince, "autoPause", false)
			pausedExperiments[exp.GetIdentifier()] = exp
		}
	}

	slog.Info("Completed pausing experiments", "pausedExperiments", len(pausedExperiments), "totalInactiveExperiments", len(inactiveExperiments))
	return inactiveExperiments, pausedExperiments
}

func AutoPause(
	ctx context.Context,
	cluster *experiment.Cluster,
	brix *brixclientset.Clientset,
	runHistory map[string]RunActionResponse,
) map[string]*experiment.InactiveExperiment {
	time.Sleep(10 * time.Second) // wait before pausing
	pausedExp := make(map[string]*experiment.InactiveExperiment)

	// Get inactive experiments for auto-pause
	experiments, err := cluster.GetInactiveExperiments(ctx, cfg.AutoPausePeriod)
	if err != nil {
		slog.Error("Failed to get inactive experiments", "error", err)
		return nil
	}
	slog.Info(fmt.Sprintf("Found %d inactive experiments for auto-pause", len(experiments)))

	for _, experiment := range experiments {
		decision := experiment.ShouldSkip(brix, cfg.SkipSpecialPool)
		if decision.Skip {
			slog.Info("Skipping auto-pause for experiment", "experiment", experiment.Experiment, "namespace", experiment.Namespace, "reason", decision.Reason)
			continue
		}

		actionResponse, ok := runHistory[experiment.GetIdentifier()]
		if ok && actionResponse.Action == ActionPostpone || actionResponse.Action == ActionNotifyOnly {
			slog.Info("Skipping auto-pause for experiment", "experiment", experiment.Experiment, "namespace", experiment.Namespace, "reason", actionResponse.Action)
			continue
		}

		slog.Info("Auto-pausing inactive experiment", "experiment", experiment.Experiment, "namespace", experiment.Namespace)

		// dry run mode
		if cfg.DryRun {
			slog.Info("Dry run mode enabled, skipping actual auto-pause", "experiment", experiment.Experiment, "namespace", experiment.Namespace)
			continue
		}

		// Pause the experiment
		paused := experiment.AutoPause(ctx, brix)
		if paused {
			slog.Info("Paused experiment", "name", experiment.Experiment, "namespace", experiment.Namespace, "GPUs", experiment.CurrentGPUCount, "since", experiment.InactiveSince, "autoPause", true)
			experiment.Paused = true
			pausedExp[experiment.GetIdentifier()] = experiment
		}
	}

	return pausedExp
}

func NewBrixClient() (*brixclientset.Clientset, error) {
	config, err := rest.InClusterConfig()
	if err != nil {
		return nil, err
	}
	client, err := brixclientset.NewForConfig(config)
	if err != nil {
		return nil, err
	}
	return client, nil
}
