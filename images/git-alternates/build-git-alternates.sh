#!/bin/bash

# build-git-alternates.sh creates an image with torchflow-mirror Git repository snapshot.
# This is adapted from OpenAI's brix-git-alternates.sh for Orange infrastructure.
# Designed to run in Azure DevOps pipelines.

set -e
set -x

PROJECT_ROOT=$(dirname "${BASH_SOURCE[0]}")
cd $PROJECT_ROOT

rm -rf build/repositories
mkdir -p build/repositories && cd build/repositories

# Prepare bare repository snapshot of torchflow-mirror from Azure DevOps.
# Note: We use bare repository as it can be used as git alternates.
# The repository name "openai" matches the Git CRD configuration in brix.tf
repository="openai"

# Use System.AccessToken for authentication (following proven pattern from other Orange pipelines)
if [ -z "$SYSTEM_ACCESSTOKEN" ]; then
    echo "ERROR: SYSTEM_ACCESSTOKEN environment variable not set"
    exit 1
fi

torchflow_repo_url="https://${SYSTEM_ACCESSTOKEN}@dev.azure.com/project-argos/Mimco/_git/torchflow-mirror"

echo "Cloning torchflow-mirror repository from Azure DevOps (orange/main branch)"
git clone --bare --single-branch --branch orange/main --no-tags "$torchflow_repo_url" "$repository.git"
git -C "$repository.git" remote remove origin

cd "$repository.git"
git tag "orange-main-$(git rev-parse orange/main)" orange/main

# Create image with the repository snapshot.
# Target Orange infrastructure ACR.
cd ../../..

image_registry="iridiumsdc.azurecr.io"
image_repository="orange"
image_name="$image_registry/$image_repository/git-alternates"
image_version=$(date +%Y-%m-%d-%H-%M-%S)

# Login to Orange ACR
# Note: In Azure DevOps, this should use service connection or managed identity
az acr login --name iridiumsdc

# Build and push multi-platform image
docker build --platform linux/arm64 -t "$image_name:$image_version-arm64" -f Dockerfile .
docker build --platform linux/amd64 -t "$image_name:$image_version-amd64" -f Dockerfile .
docker push "$image_name:$image_version-arm64"
docker push "$image_name:$image_version-amd64"

# Create and push manifest for multi-platform support
docker manifest rm "$image_name:$image_version" || true
docker manifest create "$image_name:$image_version" "$image_name:$image_version-arm64" "$image_name:$image_version-amd64"
docker manifest annotate "$image_name:$image_version" "$image_name:$image_version-arm64" --arch arm64 --os linux
docker manifest annotate "$image_name:$image_version" "$image_name:$image_version-amd64" --arch amd64 --os linux
docker manifest push "$image_name:$image_version"

# Create and push latest tags
docker tag "$image_name:$image_version-arm64" "$image_name:arm64-latest"
docker tag "$image_name:$image_version-amd64" "$image_name:amd64-latest"
docker push "$image_name:arm64-latest"
docker push "$image_name:amd64-latest"

docker manifest rm "$image_name:latest" || true
docker manifest create "$image_name:latest" "$image_name:arm64-latest" "$image_name:amd64-latest"
docker manifest annotate "$image_name:latest" "$image_name:arm64-latest" --arch arm64 --os linux
docker manifest annotate "$image_name:latest" "$image_name:amd64-latest" --arch amd64 --os linux
docker manifest push "$image_name:latest"

echo "Git alternates image pushed successfully!"
echo "Image: $image_name:$image_version"
echo "Latest: $image_name:latest"

# Show the manifest for verification
az acr manifest show -r iridiumsdc -n "$image_repository/git-alternates:latest"