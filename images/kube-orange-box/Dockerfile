FROM mcr.microsoft.com/oss/go/microsoft/golang:1.24 AS brix

ENV CGO_ENABLED=0

RUN --mount=target=/src/brix,type=bind,source=./code/brix --mount=type=cache,target=/root/.cache/go-build \
    cd /src/brix/brix/cmd/brix && go build -o /usr/local/bin/brix .

FROM mcr.microsoft.com/mirror/nvcr/nvidia/cuda:12.8.1-cudnn-devel-ubuntu24.04

RUN apt-get update
RUN apt-get install -y \
    build-essential \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    tk-dev \
    libffi-dev \
    liblzma-dev \
    libncurses5-dev \
    libunwind-dev \
    libnuma-dev \
    libibverbs-dev \
    libmsgpack-dev \
    ca-certificates \
    unzip \
    wget \
    curl \
    git \
    xz-utils \
    gettext \
    mpich \
    sudo \
    vim \
    dnsutils \
    iputils-ping \
    zstd \
    htop


RUN deluser --remove-home ubuntu

# rust

ENV MONOREPO_RUST_VERSION="1.85.1"
ENV CARGO_HOME="/opt/rust/cargo"
ENV RUSTUP_HOME="/opt/rust/rustup"
ENV PATH="$CARGO_HOME/bin:$PATH"
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | bash -s -- -y --default-toolchain $MONOREPO_RUST_VERSION --profile minimal --component rustfmt --component clippy

# python

ENV PYENV_ROOT="/opt/pyenv"
ENV PYENV="$PYENV_ROOT/bin/pyenv"

RUN curl -fsSL https://pyenv.run | bash 


ENV MONOREPO_PYTHON_VERSION="3.12.9"    

RUN PYTHON_CONFIGURE_OPTS="--enable-optimizations --with-lto --disable-shared" \
    ${PYENV} install --skip-existing $MONOREPO_PYTHON_VERSION && \
    ${PYENV} global $MONOREPO_PYTHON_VERSION

ENV MONOREPO_VENV="/opt/venv/openai"

RUN mkdir -p $MONOREPO_VENV 

RUN "$PYENV_ROOT/versions/$MONOREPO_PYTHON_VERSION/bin/python" -m venv "$MONOREPO_VENV"

ENV PATH="$MONOREPO_VENV/bin:$PATH"

RUN wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq && \
    chmod +x /usr/bin/yq

RUN wget https://github.com/stedolan/jq/releases/latest/download/jq-linux64 -O /usr/bin/jq && \
    chmod +x /usr/bin/jq

RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

ENV GCM_AZREPOS_CREDENTIALTYPE="oauth"
RUN wget -qO gcm.deb https://github.com/git-ecosystem/git-credential-manager/releases/download/v2.6.1/gcm-linux_amd64.2.6.1.deb && \
    apt-get install -y ./gcm.deb && \
    rm gcm.deb
RUN git-credential-manager configure

# kubectl
COPY --from=mcr.microsoft.com/oss/v2/kubernetes/kubectl:v1.33.1 /usr/bin/kubectl /usr/local/bin/kubectl.real
ADD ./kubectl-wrapper.sh /usr/local/bin/kubectl
RUN chmod +x /usr/local/bin/kubectl

RUN wget https://github.com/microsoft/edit/releases/download/v1.2.0/edit-1.2.0-x86_64-linux-gnu.tar.zst -O /tmp/edit.tar.zst && \
    tar --use-compress-program=unzstd -xvf /tmp/edit.tar.zst -C /usr/local/bin && \
    rm /tmp/edit.tar.zst && \
    chmod +x /usr/local/bin/edit
ENV EDITOR="edit"

RUN wget https://aka.ms/downloadazcopy-v10-linux -O /tmp/azcopy.tar.gz && \
    tar -xvf /tmp/azcopy.tar.gz -C /tmp && \
    cp /tmp/azcopy_linux_amd64_*/azcopy /usr/local/bin/azcopy && \
    chmod +x /usr/local/bin/azcopy && \
    rm -rf /tmp/azcopy*

RUN python -m pip install --upgrade pip setuptools wheel pre-commit pyAesCrypt py-spy

ENV OAIPKG_MONOREPO=/root/code/openai
ADD ./code/openai $OAIPKG_MONOREPO

ENV IBVERBS_ARTIFACT_STORAGE_ACCOUNT=orngoaiartifacts
ENV OAIPKG_ARTIFACTS_BASE="az://orngoaiartifacts"
ENV APPLIED_STORAGE_MAP="az://orngoaiartifacts/storage-map/storage-map.json"
ENV OAI_DEPLOYMENTS_TOML_PATH="az://orngoaiartifacts/shared/cacherd/deployments.toml"
ENV CACHE_SVC_DEPLOYMENTS_CONFIG="az://orngoaiartifacts/shared/cacherd/deployments.toml"
ENV OAIPKG_WHEEL_BLOB_ACCOUNT="orngoaiartifacts"

RUN --mount=type=cache,target=/root/.cache pip install $OAIPKG_MONOREPO/oaipackaging 

RUN --mount=type=cache,target=/root/.cache \
    --mount=type=bind,source=./cache,target=/oaiwheelcache/ \
    --mount=type=bind,source=./wheel/connexion2-2020.0.dev1-py2.py3-none-any.whl,target=/tmp/connexion2-2020.0.dev1-py2.py3-none-any.whl \
    export BUILDKITE_AGENT_CACHE_HOST_DIR="/oaiwheelcache" && \
    export OAIPKG_INSTALL_CPU_OPTIMIZER_KERNELS=unsafe_skip && \
    export OAIPKG_INSTALL_TENSORCACHE_RS=unsafe_skip && \
    export OAIPKG_INSTALL_CUPY_WHEEL=unsafe_skip && \
    oaipkg install beam_py_native && \
    oaipkg install torchflow && \
    oaipkg install rcall && \
    oaipkg installoai peashooter qstar && \
    oaipkg installoai bus

ADD ./code/glass /root/code/glass

COPY --from=brix  /usr/local/bin/brix /usr/local/bin/brix


# Optional: Backward compatibility environment variables
ENV SKIP_TORCHFLOW_SETUP_CHECK="1"
ENV BRIX_CLI_DISABLE_TAILSCALE="1"
ENV BRIX_SELF_UPGRADE="0"
ENV AZURE_USE_IDENTITY="1"
ENV USE_STORAGE_MAP="false"


RUN --mount=type=cache,target=/root/.cache pip install jupyter
ADD ./.rcall_config.py /root/.rcall_config.py
RUN echo 'alias b=rcall-brix' > ~/.bash_aliases

