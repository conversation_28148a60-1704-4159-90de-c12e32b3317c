.git

project/actor/actor/electron/main/env-variables.json
project/harmony_sonic/harmony_sonic/webserver/static
.eslintcache
*.jsonl.gz
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
pip-wheel-metadata/

# Emacs temp/private files
.\#*

# C extensions
*.so

# JetBrains
.ijwb

# Distribution / packaging
.Python
build/
build_cmake/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
*.whl
MANIFEST
*.npz
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Virtualenvs
.ve

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb
.ipynb_checkpoints

# IDEA
.idea/

# JetBrains Fleet
.fleet/

# Qt Creator
*.cflags
*.cxxflags
*.creator.user

# VIM
*.swp

# Emacs
*~

# IDEs
.vscode
.cursorignore
.cursorindexingignore

.sublime-project
.sublime-workspace

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

*.tfrecords
*.npy

# SageMath parsed files
*.sage.py

# Environments
.env
.retrieval_env
.env.leave
.envrc
.venv
venv/
env.bak/
venv.bak/
*.rdb

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json

# javascript
node_modules/
.svelte-kit
.vite
.next/

# typescript
tsconfig.tsbuildinfo

# general
local/
*.mp4
*.ogv
.DS_Store

# rl team
.build
.assets
docker_build_staging

# VS Code workspace configuration
*.code-workspace

# Redis
dump.rdb
multi-agent/minecraft/wandb
multi-agent/ci/buildkite/agent_management/buildkite-agent-parametrized.yaml
multi-agent/ci/buildkite/agent_management/buildkite-agent-parametrized-lowpri.yaml

fivesight/fivesight/yoda/basic_data_structures/fast_int_permutation.cpp

# ignoring this since it's copied on the Mac from a fake dir
din/din/nccl.py

# large image file
alignment/neuron_explainer_app/static/logo.png

# world of bits file (rl/wob2)
tmp_wob_files

# Kubernetes certificate during repo setup
certificate.json

# Sapling (https://sapling-scm.com/) is a git-protocol-compatible VCS
# with a a nice stacked diff workflow.  Many tools expect git, so it's
# convenient to work in a hybrid checkout with a single working
# directory shared by both git and sapling.  These two entries ensure
# that sapling is hidden from git, and vice-versa.
.sl/
.git/
# In some configurations .git-under-sapling is a file rather than directory
.git

# For local development, sa-server will store a fake cosmos store
# on disk at this location
fake_cosmos_store

# ctags
TAGS
tags
.sugarjar.local.yaml
.sugarjar.local-overwrite.yaml

# common throw-away files in the root
/0
/1
/2
/3
/4
/5
/6
/7
/8
/9

# bazel
bazel-bin
bazel-openai
bazel-testlogs
bazel-out
# eventually we'll want this one to be checked out but right now it's a conflict nightmare...
MODULE.bazel.lock
# BUILD.bazel are hand-written, BUILD are generated. While we are prototyping bazel support avoid commiting them into the repo to avoid churn in codereviews.
BUILD

# The following packages have editable installs that may generate a build/ directory conflicting
# with Bazel's BUILD files on macos. For these projects specifically, we generate BUILD.bazel files
# instead, and explicitly gitignore them here. We verify these files are gitignored when
# bazel_gen is run. Alternatively you can specify a directory other than "build/" in the project's
# tool.scikit-build.build-dir field in pyproject.toml and you then wouldn't have to add it here..
api/inference-lb-proxy/BUILD.bazel
api/integrity-counter-proxy/BUILD.bazel
api/integrity-gateway/BUILD.bazel
api/libs/integrity/integrity-types/BUILD.bazel
api/rust_to_py/BUILD.bazel
birder/BUILD.bazel
lib/applied/oai_imgsan/BUILD.bazel
lib/applied/py_rust_template/BUILD.bazel
lib/applied/identity/oai_biscuit/BUILD.bazel
lib/applied/integrity/oai_sentinel/BUILD.bazel
lib/applied/integrity/privacy_pass_rs/BUILD.bazel
lib/applied/integrity/redis_counter_client_rs/BUILD.bazel
lib/applied/integrity/integrity_counter_proxy_client/BUILD.bazel
lib/oai_c2pa/BUILD.bazel
lib/oai_grpc_cpp/BUILD.bazel
lib/occl_cpp/BUILD.bazel
lib/robotstxt/BUILD.bazel
lib/applied/oai_index_builder_lib/BUILD.bazel
lib/ekm_client_cpp/BUILD.bazel
multimodal-token/BUILD.bazel
personal/menick/minimal/min_tiktoken/BUILD.bazel
project/enginev3/kv_manager/BUILD.bazel
project/enginev3/inference_contract_cpp/BUILD.bazel
project/enginev3/inference_server_cpp/BUILD.bazel
project/enginev3/oai_cuda_cpp/BUILD.bazel
project/enginev3/oai_gpt_batcher/BUILD.bazel
project/enginev3/oai_opentelemetry_cpp/BUILD.bazel
project/enginev3/pipereplica_load_balancer_cpp/BUILD.bazel
project/runtime/projects/beam/beam-py-native/BUILD.bazel
project/runtime/projects/bus_rs/BUILD.bazel
project/scaling-infra/py_utils_rs/BUILD.bazel
tiktoken/BUILD.bazel
chatgpt/critical-sec/BUILD.bazel
chatgpt/statsig-cython/BUILD.bazel

# Homebrew
Brewfile.lock.json

# Generated input lock files for Applied projects
.applied_manage
.render

# File generated when creating and pushing a docker cluster
project/conversation_classifier/dockerfile/spark.Dockerfile.dockerignore

# Generated python files
project/runtime/projects/horse/horse-pb2/horse_pb2/*
!project/runtime/projects/horse/horse-pb2/horse_pb2/__init__.py

# Java
*.class

# Gradle files
.gradle/
build/
project/cosmos_cdc/gradle_build/

# Package Files #
*.jar
!gradle-wrapper.jar

# chzwiz
*.chzwiz

.usercustomize.py

# Scientist Assistant Build Timestamp (Auto-generated file on build)
project/scientist_assistant/scientist-assistant-vscode-ext/src/buildTimestamp_gen.ts
# Scientist Assistant `vsce package` output
project/scientist_assistant/scientist-assistant-vscode-ext/scientist-assistant-vscode-ext-0.0.1.vsix
.aider*

# Stacked Pull Requests repo-level config, cf. https://github.com/ejoffe/spr?tab=readme-ov-file#configuration #
.spr.yml

# ruff itself adds a gitignore file inside each .ruff_cache folder
# but this is just to help top-level tooling
.ruff_cache/

# These clickhouse local migrations need to be checked in, but are excluded by the broad local/ rule above
!api/primaryapi/primaryapi/clickhouse/migrations/local/

# Buildkite CLI
.bk.yaml

project/scaling-infra/tw_comms_device/tw_comms_device/cache
safesys/safesys/edwin
**/spark_generated.Dockerfile
**/spark_generated.Dockerfile.dockerignore

# allow pyrightconfig.json in all subdirectories except for the root
/pyrightconfig.json
!/*/**/pyrightconfig.json
