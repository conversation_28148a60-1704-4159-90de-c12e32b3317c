#!/bin/bash

# Filter out any --context or --context=...
FILTERED_ARGS=()
skip_next=false
for arg in "$@"; do
  if $skip_next; then
    skip_next=false
    continue
  fi

  case "$arg" in
    --context)
      skip_next=true
      ;;
    --context=*)
      ;;
    *)
      FILTERED_ARGS+=("$arg")
      ;;
  esac
done

# Call the real kubectl with filtered args and forced kubeconfig
exec /usr/local/bin/kubectl.real "${FILTERED_ARGS[@]}"