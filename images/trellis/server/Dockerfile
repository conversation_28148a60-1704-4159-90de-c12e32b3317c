# syntax=docker/dockerfile:1.7-labs
# 2025-01-17: 1.7-labs is required for COPY --parents=true

# This version matches the one used by rcall in rcall/rcall/global_config.py.
# Some newer images have the wrong Python version, so we must be careful to
# match rcall.
ARG PYTHON_BASE_IMAGE="iridiumsdc.azurecr.io/rcall:crow-1147178"

# This target is never built. It can be used to troubleshoot the dockerignore
# Usage:
#   rm -rf /tmp/context && mkdir -p /tmp/context
#   docker --context=default buildx build --platform=linux/amd64 -f project/trellis_server/Dockerfile --target dockerignore -o /tmp/context .
#   du -h -d 3 /tmp/context | sort -h
#
FROM scratch AS dockerignore

FROM ${PYTHON_BASE_IMAGE}
WORKDIR /root

ENV TINI_VERSION v0.19.0
ADD https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini /tini
RUN chmod +x /tini

# Install dependencies via lockfile.
COPY --parents=true \
  oaipackaging/oaipkg/global_constraints.txt \
  oaipackaging/oaipkg/uv.toml \
  project/trellis_server/trellis_server/requirements.txt \
  ./

# Copy the monorepo. The context is controlled by Dockerfile.dockerignore.
COPY . ./
COPY ./copy_wheels.py /root/

ENV UV_BUILD_CONSTRAINT="/root/oaipackaging/oaipkg/global_constraints.txt"
ENV UV_INDEX_URL="https://pypi.org/simple/"

# uv installs packages faster. Specify env explicitly to disable pip fallback.
ENV OAIPKG_INSTALLER_BACKEND=uv

# Pare down what oaipkg installs during bootstrap to the minimum necessary.
ENV OAIPKG_BOOTSTRAP_MINIMAL=1

# Disable oaipkg magic - nothing should get installed outside of pinned
# dependencies. Such installation would probably fail because haven't copied the
# entire repo.
ENV OAIPKG_DISABLE_META_MISSING=1

# Docker Desktop "amd64" CPU type is generic, but we need to download wheels
# which are only built for avx2. This is safe because wheels don't actually run
# in the build process, only when the image is deployed to a cluster.
# https://oai-strawberry-traine.slack.com/archives/C07NBRGCM32/p1744400200292889?thread_ts=1744389416.236609&cid=C07NBRGCM32
ENV OAIPKG_CPU_ISALEVEL=avx2
ENV OAIPKG_INSTALL_CPU_OPTIMIZER_KERNELS=unsafe_skip
ENV OAIPKG_INSTALL_PROTON_WHEEL=unsafe_skip

ENV OAIPKG_VERBOSE=1

# pyenv is avaialbe in the base image but PATH is not set properly.
ENV PYENV_ROOT="/root/.pyenv"
ENV PYENV="$PYENV_ROOT/bin/pyenv"
ENV PATH="$PYENV_ROOT/shims:$PYENV_ROOT/bin:$PYENV_ROOT/versions/3.11.8/bin:$PATH"

# Ensure the specific Python version is installed and set as global.
RUN pyenv install --skip-existing 3.11.8
RUN pyenv global 3.11.8

# install oaipkg
RUN echo "Try to install oaipkg"
RUN python /root/install.py
RUN echo "Installed oaipkg"

# copy wheels to the correct location indicated by:
# $shaHash = sha1($OAIPKG_ARTIFACTS_BASE/wheels/<oai_wheel_file_folder>/<oai_wheel_file_name>)
# target_path = $BUILDKITE_AGENT_CACHE_HOST_DIR/oaipkg_wheels_cache/<sha1/<oai_wheel_file_name>>/
# i.e. from /wheelcache/oaipkg_wheels_cache/<oai_wheel_file_folder>/<oai_wheel_file_name>
# to /oaiwheelcache/oaipkg_wheels_cache/$shaHash/<oai_wheel_file_name>
# For example:
# from /wheelcache/oaipkg_wheels_cache/multimodal-token/multimodal_token-1.0.0+git.taa93a97b6b.os.noble-cp311-cp311-linux_x86_64.whl
# to /oaiwheelcache/oaipkg_wheels_cache/041e8c51fd50ab31/multimodal_token-1.0.0+git.taa93a97b6b.os.noble-cp311-cp311-linux_x86_64.whl
ENV OAIPKG_ARTIFACTS_BASE=az://orngoaiartifacts
ENV BUILDKITE_AGENT_CACHE_HOST_DIR="/oaiwheelcache"
RUN --mount=type=bind,source=./orngoaiartifacts/wheels,target=/wheelcache/oaipkg_wheels_cache/ python /root/copy_wheels.py

# I am not sure if this is really required. But keep it here just in case.
# Refer: https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/_msft/rcall_overrides/orange.py&version=GBorange/main&_a=contents
COPY ./orngoaiartifacts/wheels/public-wheels/connexion2/connexion2-2020.0.dev1-py2.py3-none-any.whl /tmp/connexion2-2020.0.dev1-py2.py3-none-any.whl

# install trellis_server and its dependencies
RUN oaipkg install trellis_server berry_derisking caas_adversarial seedling

# install torchflow and its dependencies. The original openai dockerfile uses torchflow/run-torchflow-setup.py
# to install torchflow, which doesn't work for us without a deep dive into the code. Also, It is unclear how torchflow
# is supposed to be used in the trellis server. We will revisit this later when debugging the trellis_server.
# RUN --mount=type=cache,target=/root/.cache \
#     BRIX_NODE=-cpu- python install.py torchflow oaislack twnode
#  https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/torchflow/run-torchflow-setup.py&version=GBorange/main&_a=contents&line=84&lineStyle=plain&lineEnd=85&lineStartColumn=1&lineEndColumn=20
ENV RCALL_NUM_GPU=0
# Hack for https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/torchflow/run-torchflow-setup.py&version=GBorange/main&_a=contents&line=144&lineStyle=plain&lineEnd=144&lineStartColumn=1&lineEndColumn=36
COPY ./orngoaiartifacts/wheels/nccl/Et8dHKle/libnccl-private_2.18.3-openai+9d4b02d+cuda12.8_amd64.deb /tmp/libnccl-private_2.18.3-openai+9d4b02d+cuda12.8_amd64.deb
# Hack for https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/torchflow/run-torchflow-setup.py&version=GBorange/main&_a=contents&line=66&lineStyle=plain&lineEnd=69&lineStartColumn=1&lineEndColumn=1
RUN mkdir -p /oailib
COPY ./orngoaiartifacts/wheels/cache_getenv/2025-04-04-07-00-33/cache_getenv_x86_64.so /oailib/cache_getenv.so
# Hack for https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/torchflow/run-torchflow-setup.py&version=GBorange/main&_a=contents&line=71&lineStyle=plain&lineEnd=77&lineStartColumn=1&lineEndColumn=1
RUN mkdir -p /root/.cache/openai_clusters
COPY ./orngoaiartifacts/storage-map/storage-map.json /root/.cache/openai_clusters
# Hack for https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/torchflow/run-torchflow-setup.py&version=GBorange/main&line=219&lineEnd=219&lineStartColumn=1&lineEndColumn=183&lineStyle=plain&_a=contents
COPY ./orngoaiartifacts/wheels/manhole/manhole-1.8.0+openai.20220316-py2.py3-none-any.whl /tmp/manhole-1.8.0+openai.20220316-py2.py3-none-any.whl
RUN --mount=type=cache,target=/root/.cache \
    BRIX_NODE=-cpu- python torchflow/run-torchflow-setup.py

# Sets the python output to be unbuffered, so that logs are printed immediately and no information is lost in a crash.
ENV PYTHONUNBUFFERED=1

# enable boostedblob to use azure-identity
ENV AZURE_USE_IDENTITY=1

ENV TIKTOKEN_USE_EXTERNAL_VOCAB=1

ENV SKIP_TORCHFLOW_SETUP_CHECK=1

EXPOSE 8000
ENTRYPOINT ["/tini", "--", "python", "-m", "trellis_server"]