diff --git a/blobfile/VERSION b/blobfile/VERSION
index 4a36342..8dd5c17 100644
--- a/blobfile/VERSION
+++ b/blobfile/VERSION
@@ -1 +1 @@
-3.0.0
+30.0.0
diff --git a/blobfile/_azure.py b/blobfile/_azure.py
index 0986c4c..1ca3144 100644
--- a/blobfile/_azure.py
+++ b/blobfile/_azure.py
@@ -169,6 +169,11 @@ def load_subscription_ids() -> List[str]:
 
 
 def build_url(account: str, template: str, **data: str) -> str:
+    account_mapping = os.environ.get("IRIDIUM_AZURE_STORAGE_ACCOUNT_MAPPING")
+    if account_mapping is not None:
+        mapping = json.loads(account_mapping)
+        if account in mapping:
+            account = mapping[account]
     return common.build_url(f"https://{account}.blob.core.windows.net", template, **data)
 
 
