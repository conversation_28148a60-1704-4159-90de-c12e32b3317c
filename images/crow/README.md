Custom RCall Crow Image
---

We use this image to bootstrap devboxes in Iridium. We base it off OAI [crow image](https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/project/crow_docker&version=GBmsft/staging/20241029&_a=contents). Crow docker itself is a minimal image with enough OAI packages to bootstrap RCall and TorchFlow on each pod startup.

### Building
Image is built using [this pipeline](https://dev.azure.com/project-argos/Mimco/_build?definitionId=1669) which is triggered automatically on changes in `images/crow/*`. Once the image is pushed, make sure you update [rcall_config_templ.py](https://dev.azure.com/project-argos/Mimco/_git/bootstrapping?path=/rcall_config_templ.py) and ask users to update their `~/.rcall_config.py` manually or by running `set-iridium-laptop.sh`.

### Hacks

Please see `Dockerfile` for all the hacks we need to add for Iridium.
