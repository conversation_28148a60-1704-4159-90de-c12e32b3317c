# This is mirrored from OAI. See sebastko.
FROM iridiumsdc.azurecr.io/rcall:crow-f36b49dfabf5f39225bc0efd6e9fe586b32dab57-2404-312

# apply patch to blob to provide stoage account mapping func
# part of the patch is to set version to some arbitrary high versoin
# to stop pip from overwriting it later on.
WORKDIR /root/iridium-hacks
ADD boostedblob.patch .
RUN git clone --branch v0.15.4 https://github.com/hauntsaninja/boostedblob.git && \
    cd boostedblob && \
    patch -p1 < ../boostedblob.patch && \
    /root/.pyenv/versions/3.12.9/bin/python -m uv pip install .
WORKDIR /root/iridium-hacks
ADD blobfile.patch .
RUN git clone --branch v3.0.0 https://github.com/blobfile/blobfile.git && \
    cd blobfile && \
    patch -p1 < ../blobfile.patch && \
    /root/.pyenv/versions/3.12.9/bin/python -m uv pip install .
# make sure that crow requiremnts won't uninstall our versions.
ADD pip-override.txt .
ENV UV_OVERRIDE=/root/iridium-hacks/pip-override.txt

# disable brix auto update from oai artifacts as we don't mirror it.
ENV BRIX_SELF_UPGRADE=0
