diff --git a/boostedblob/path.py b/boostedblob/path.py
index 3a6642e..6a94b20 100644
--- a/boostedblob/path.py
+++ b/boostedblob/path.py
@@ -5,6 +5,7 @@ import base64
 import datetime
 import email.utils
 import functools
+import json
 import os
 import urllib.parse
 from dataclasses import dataclass
@@ -143,6 +144,11 @@ class AzurePath(CloudPath):
         parts = url.split("/", maxsplit=4)
         container = parts[3] if len(parts) >= 4 else ""
         blob = parts[4] if len(parts) >= 5 else ""
+        account_mapping = os.environ.get("IRIDIUM_AZURE_STORAGE_ACCOUNT_MAPPING")
+        if account_mapping is not None:
+            mapping = json.loads(account_mapping)
+            if account in mapping:
+                account = mapping[account]
         return AzurePath(account=account, container=container, blob=blob)
 
     @property
diff --git a/pyproject.toml b/pyproject.toml
index 7b9708a..9231efc 100644
--- a/pyproject.toml
+++ b/pyproject.toml
@@ -41,7 +41,7 @@ ignore = ["E2", "E501", "E741", "C408", "PIE790"]
 
 [tool.poetry]
 name = "boostedblob"
-version = "0.15.4"
+version = "10.15.4"
 description = """
 Command line tool and async library to perform basic file operations on local \
 paths, Google Cloud Storage paths and Azure Blob Storage paths.\
