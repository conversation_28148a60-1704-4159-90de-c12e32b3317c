FROM iridiumsdc.azurecr.io/rcall:crow-1147178

RUN apt-get update && apt-get install -y lsb-release curl gpg
RUN curl -fsSL https://packages.redis.io/gpg | gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg
RUN chmod 644 /usr/share/keyrings/redis-archive-keyring.gpg
RUN echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/redis.list
RUN add-apt-repository universe && apt-get update && apt-get install -y redis-server libclang-dev


ENV TORCHFLOW_MIRROR_VERSION="orange/main"
ENV MONOREPO_BRIX_VERSION="osama/iridium_mods"
ENV MONOREPO_GO_VERSION="1.23.0"
ENV MONOREPO_RUST_VERSION="1.85.1"
ENV MONOREPO_PYTHON_VERSION="3.11.8"
ENV OAIPKG_POLYREPO_SIBLINGS=garden
##ENV OAIPKG_OVERRIDE_WHEEL=unsafe_skip
ENV LEMON_MSFT="True"
ENV USE_AZURE_LOGGING="True"
ENV PERSONAL_NAMESPACE="True"
ENV PATH="/root/.pyenv/versions/3.11.8/bin:$PATH"
ENV OAIPKG_ARTIFACTS_BASE=az://orngoaiartifacts
ENV BUILDKITE_AGENT_CACHE_HOST_DIR="/oaiwheelcache"
ENV OAIPKG_VERBOSE=1


## install lemon dependencies

COPY ./openai /root/code/openai
COPY setup.sh copy_wheels.py /root/
COPY startlemon.py /

RUN chmod +x /startlemon.py

# Run setup script to prepare the environment
RUN --mount=type=bind,source=./wheels/wheels,target=/wheelcache/oaipkg_wheels_cache/ chmod +x /root/setup.sh && /bin/bash /root/setup.sh
ENTRYPOINT ["/startlemon.py"]