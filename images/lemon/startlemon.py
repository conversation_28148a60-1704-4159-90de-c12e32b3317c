#!/usr/bin/env python

import time
import signal
from rapid.classic.supervisor_process import add_supervisor_process
from rapid.utils.supervisor import stop_supervisor_program
from rapid.utils.redis_server import redis_server_command


_CACHE_UPDATER_START = "python -m lemon.cache_updater"
_BACKEND_START = "gunicorn --workers 80 --bind 0.0.0.0:5000 --timeout 180 -k uvicorn.workers.UvicornWorker lemon.main:app"
_FRONTEND_START = "bash -c 'cd /root/code/openai/project/lemon/lemon-client && npm run start'"

def launch_lemon() -> None:
    """Launches the LEMON BACKEND & UI server."""
    add_supervisor_process(
        name="redis_server",
        command=redis_server_command(),
        numprocs=1,
        numprocs_start=6379,
        autorestart=True,
    )

    add_supervisor_process(
        name="cache_updater",
        command=_CACHE_UPDATER_START,
        numprocs=1,
        autorestart=True,
    )

    add_supervisor_process(
        name="frontend_server",
        command=_FRONTEND_START,
        numprocs=1,
        autorestart=True,
    )

    add_supervisor_process(
        name="backend_server",
        command=_BACKEND_START,
        numprocs=1,
        autorestart=True,
    )

def handle_termination(signum, frame):
    print("Termination signal received, shutting down...")
    # Cleanup, stop all supervisor processes
    stop_supervisor_program("redis_server", missing_ok=True)
    stop_supervisor_program("cache_updater", missing_ok=True)
    stop_supervisor_program("frontend_server", missing_ok=True)
    stop_supervisor_program("backend_server", missing_ok=True)
    exit(0)

if __name__ == "__main__":
    signal.signal(signal.SIGTERM, handle_termination)
    signal.signal(signal.SIGINT, handle_termination)
    launch_lemon()
    while True:
        time.sleep(1)