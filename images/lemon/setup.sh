#!/bin/bash
# This script sets up the environment for the Lemon project.
set -euo pipefail

export OPENAI_DIR="$HOME/.openai"
export MONOREPO_ROOT="$HOME/code/openai"
export TORCHFLOW_MIRROR_VERSION="orange/main"
export MONOREPO_BRIX_ROOT="$HOME/code/brix"
export MONOREPO_BRIX_VERSION="osama/iridium_mods"
export MONOREPO_GO_VERSION="1.23.0"
export MONOREPO_RUST_VERSION="1.85.1"
export MONOREPO_PYTHON_VERSION="3.11.8"
export PYENV_ROOT="$HOME/.pyenv"
export PYENV="$PYENV_ROOT/bin/pyenv"
export OAIPKG_POLYREPO_SIBLINGS=garden
export BUILDKITE_AGENT_CACHE_HOST_DIR="/oaiwheelcache"
export OAIPKG_ARTIFACTS_BASE="az://orngoaiartifacts"
export OAIPKG_CPU_ISALEVEL="avx2"
export OAIPKG_INSTALL_BLOBRSCACHE="wheel;az://orngoaiartifacts/wheels/blobrscache/blobrscache-0.1.21+git.t10bcf9499a.os.noble-cp311-cp311-linux_x86_64.whl"
export OAIPKG_INSTALL_TENSORCACHE_RS="wheel;az://orngoaiartifacts/wheels/tensorcache-rs/tensorcache_rs-2.0.0+git.t23363df8cd.os.noble-cp311-cp311-linux_x86_64.whl"

# install the default pyenv which is available as part of crow
$PYENV install --skip-existing $MONOREPO_PYTHON_VERSION

# install oaipkg
echo "Try to install oaipkg"
python $HOME/code/openai/install.py
echo "Installed oaipkg"

echo "Copy wheels to right location"
python /root/copy_wheels.py

# Refer: https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/_msft/rcall_overrides/orange.py&version=GBorange/main&_a=contents
cp /wheelcache/oaipkg_wheels_cache/public-wheels/connexion2/connexion2-2020.0.dev1-py2.py3-none-any.whl /tmp/connexion2-2020.0.dev1-py2.py3-none-any.whl

# install lemon
oaipkg install lemon
# install dependencies for asyncmetrics and go/sb
oaipkg install viz

# install npm dependencies
cd /root/code/openai/project/lemon/lemon-client 
npm install 
npm run build

echo "Setup Complete!"