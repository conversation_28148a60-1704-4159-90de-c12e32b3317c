import os
import hashlib
import shutil
from pathlib import Path

def gethash(path):
    whl_path = os.path.join(*path.parts[-2:])
    baseblob = os.environ.get("OAIPKG_ARTIFACTS_BASE")
    final_path = os.path.join(baseblob, "wheels", whl_path)
    sha = hashlib.sha1(final_path.encode()).hexdigest()[:16]
    print(f"SHA1 hash for {final_path}: {sha}")
    return sha

def copy_wheels(dir):
    oaiwheelcache = os.environ.get("BUILDKITE_AGENT_CACHE_HOST_DIR")
    for file in Path(dir).rglob("*.whl"):
        if file.is_file():
            print(f"Processing {file}...")
            sha = gethash(file)
            newpath = os.path.join(oaiwheelcache, "oaipkg_wheels_cache", sha, file.name)
            os.makedirs(os.path.dirname(newpath), exist_ok=True)
            shutil.copy(str(file), newpath)
            print(f"Moved {file} to {newpath}")


if __name__ == "__main__":
    # Get the current working directory
    dir = "/wheelcache/oaipkg_wheels_cache/"
    # Call the function to copy wheels
    copy_wheels(dir)