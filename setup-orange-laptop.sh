#!/bin/zsh

# Laptop setup script for Orange clusters setup on both macOS and Ubuntu.
# On Mac, it forces x86/Intel (re-executes itself under <PERSON><PERSON> if on Apple Silicon)
#
# Prerequisites:
#
# 1. The laptop must be Intuned to Microsoft CORP.
#
# 2. You must have green <NAME_EMAIL> and it must be functional
#
# 3. You must be logged-in as an admin user with the name matching your microsoft alias.
#
# 4. `torchflow-mirror` has been cloned into ~/code/torchflow-mirror with symlink
#    `openai` and is on branch `orange/main`.
#
# 5. `brix` has been cloned into ~/code/brix.
#
# 6. You must be connected to the Tailnet using green.microsoft.com identity.
#

set -e

##################################################################################
# 0. FORCE ROSETTA x86_64 MODE IF APPLE SILICON
##################################################################################
if [[ "$(uname -s)" == "Darwin" && "$(uname -m)" == "arm64" ]]; then

  # Attempt to install Rosetta if not present
  if /usr/sbin/pkgutil --pkg-info com.apple.pkg.RosettaUpdateAuto > /dev/null 2>&1; then
    echo "Rosetta package is installed"
  else
    echo "Rosetta package is not installed"
    echo "Installing Rosetta 2..."
    sudo /usr/sbin/softwareupdate --install-rosetta --agree-to-license
  fi
fi

##################################################################################
# 1. OS DETECTION
##################################################################################
source ./util.sh
set_os_type_env_var

##################################################################################
# 2. LOGGING
##################################################################################
__LOG_LEVEL=info

__log_error() {
  local red='\033[0;31m'
  local no_color='\033[0m'
  echo -e "${red}$(date +%Y-%m-%dT%H:%M:%S%z) ERROR $*${no_color}" >&2
  exit 1
}

__log_warn() {
  echo -e "$(date +%Y-%m-%dT%H:%M:%S%z) WARN $*" >&2
}

__log_info() {
  local green='\033[1;32m'
  local no_color='\033[0m'
  echo -e "${green}$(date +%Y-%m-%dT%H:%M:%S%z) INFO $*${no_color}" >&2
}

__log_notice() {
  local yellow='\033[1;33m'
  local no_color='\033[0m'
  echo -e "${yellow}$(date +%Y-%m-%dT%H:%M:%S%z) NOTICE $*${no_color}" >&2
}

__log_debug() {
  if [[ "$__LOG_LEVEL" == "debug" ]]; then
    echo "$(date +%Y-%m-%dT%H:%M:%S%z) DEBUG $*" >&2
  fi
}

##################################################################################
# 3. ENVIRONMENT VARIABLES
##################################################################################
BOOTSTRAPPING_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

export OPENAI_USER="$USER"
export OPENAI_DIR="$HOME/.openai"
export MONOREPO_ROOT="$HOME/code/openai"
export TORCHFLOW_MIRROR_VERSION="orange/main"

export MONOREPO_BRIX_ROOT="$HOME/code/brix"
export MONOREPO_BRIX_VERSION="v0.40.0"

export MONOREPO_GO_VERSION="1.24.1"
export MONOREPO_RUST_VERSION="1.85.1"
export MONOREPO_PYTHON_VERSION="3.12.9"

export PYENV_ROOT="$HOME/.pyenv"
export PYENV="$PYENV_ROOT/bin/pyenv"

##################################################################################
# 4. TAILSCALE CHECK
##################################################################################
check_tailscale() {
  __log_info "Checking Tailscale installation/status"

  # Detect the platform (Mac, Linux, or WSL)
  if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    TAILSCALE_CMD="/Applications/Tailscale.app/Contents/MacOS/Tailscale"
  elif grep -qi microsoft /proc/version; then
    # WSL
    if command -v tailscale &>/dev/null; then
      TAILSCALE_CMD="tailscale"
    elif command -v tailscale.exe &>/dev/null; then
      TAILSCALE_CMD="tailscale.exe"
    elif command -v /mnt/c/Program\ Files/Tailscale/tailscale.exe &>/dev/null ; then
      # Fallback to the default Windows Tailscale path
      TAILSCALE_CMD="/mnt/c/Program\ Files/Tailscale/tailscale.exe"
    else
      __log_error "Tailscale is not installed in WSL."
      return 1
    fi
  else
    # Linux (Ubuntu, Debian, Fedora, Arch, etc.)
    TAILSCALE_CMD="tailscale"
  fi

  # Check if Tailscale is installed
  if ! command -v "$TAILSCALE_CMD" &>/dev/null; then
    __log_error "Tailscale is not installed."
    return 1
  fi

  # Check if Tailscale is running and connected to the expected tailnet
  if [ "$("$TAILSCALE_CMD" status --json | jq -r '.CurrentTailnet.Name')" != "green.microsoft.com" ]; then
    __log_error "You must be connected to the Tailnet using your green identity."
    return 1
  fi

  # check that generateResolvConf is there
  if grep -qi microsoft /proc/version; then
    if grep -Eq '^\s*generateResolvConf\s*=\s*false\s*$' /etc/wsl.conf; then
      __log_error "$(cat <<EOF
⚠️  Configuration Issue Detected!
Go through https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4621/WSL-Setup?anchor=configure-networking-for-wsl and
Go through https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4621/WSL-Setup?anchor=configure-wsl-dns
EOF
      )"
    fi
  fi

  __log_info "Tailscale is running and connected to the green.microsoft.com tailnet"
}


##################################################################################
# 5. REPO CHECK
##################################################################################
check_repos() {
  __log_info "Checking code repositories"
  [[ -d "$MONOREPO_ROOT" ]] || __log_error "Repo not found at $MONOREPO_ROOT"
  [[ -d "$MONOREPO_BRIX_ROOT" ]] || __log_error "Repo not found at $MONOREPO_BRIX_ROOT"
}

##################################################################################
# 6. PACKAGE INSTALLATION & CONFIG DOWNLOAD
##################################################################################

install_brew_macos() {
  __log_info "Installing Homebrew"

  if ! command -v brew &> /dev/null; then
    bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    echo >> ~/.zprofile
    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
    eval "$(/opt/homebrew/bin/brew shellenv)"
  fi
}

install_brew_linux() {
  __log_info "Installing Homebrew"

  if ! command -v brew &>/dev/null; then
    __log_info "Homebrew not found. Installing..."
    NONINTERACTIVE=1 /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)" || \
      __log_error "Failed to install Homebrew."
    echo >> ~/.zprofile
    echo 'eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"' >> ~/.zprofile
    eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
  fi
}

install_brew_packages() {
  export HOMEBREW_NO_AUTO_UPDATE=1
  export HOMEBREW_NO_ENV_HINTS=1

  brew install yq gettext jq azure-cli
  if [[ "$OS_TYPE" == "macos" ]]; then
    # Note: Brew's installation of this for Linux runs into some compilation errors.
    # Installed through apt-get instead for Linux.
    brew install mpich
  fi
}

setup_macos_packages() {
  install_brew_macos
  install_brew_packages
}

setup_linux_packages() {
  __log_info "Setting up apt packages on Linux"

  sudo apt-get update
  sudo apt-get install -y \
    build-essential \
    curl \
    git \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    wget \
    ca-certificates \
    xz-utils \
    tk-dev \
    libffi-dev \
    liblzma-dev \
    mpich

  install_brew_linux
  install_brew_packages
}

setup_packages() {
  if [[ "$OS_TYPE" == "macos" ]]; then
    setup_macos_packages
  else
    setup_linux_packages
  fi
}

az_login() {
  __log_info "Logging in to az cli"

  if ! az ad signed-in-user show --query userPrincipalName -o tsv | grep green.microsoft.com &>/dev/null; then
    __log_info "When prompted, please log in to az cli with your GREEN TENANT account..."
    sleep 3
    az login --tenant green.microsoft.com
  fi
}

download_users_map() {
  __log_info "Downloading users map from Blob Storage"
  local users_map_storage_account="orngoaiartifacts"
  local users_map_storage_container="storage-map"
  local users_map_storage_blob="users.json"
  mkdir -p "$OPENAI_DIR"
  az storage blob download \
    --account-name "$users_map_storage_account" \
    --container-name "$users_map_storage_container" \
    --name "$users_map_storage_blob" \
    --file "$OPENAI_DIR/users.json" \
    --auth-mode login \
    >/dev/null || __log_error "Failed to download users.json, are you onboarded and logged in to your green tenant account?"
  if [[ $(jq --arg user "$OPENAI_USER" 'has($user)' "$OPENAI_DIR/users.json") == "false" ]]; then
    __log_error "Failed to find current user in users.json, are you onboarded to orange?"
  fi
}

##################################################################################
# 7. PYTHON (PYENV) SETUP
##################################################################################
setup_python() {
  __log_info "Setting up PyEnv and Python $MONOREPO_PYTHON_VERSION"

  if [[ ! -d "$PYENV_ROOT" ]]; then
    git clone https://github.com/pyenv/pyenv.git "$PYENV_ROOT"
  else
    __log_info "Updating existing PyEnv installation"
    pushd "$PYENV_ROOT" >/dev/null
      git pull
    popd >/dev/null
  fi

  export PYTHON_CONFIGURE_OPTS="--enable-optimizations --with-lto --disable-shared"
  "$PYENV" install --skip-existing "$MONOREPO_PYTHON_VERSION" || \
    __log_error "pyenv failed to install Python $MONOREPO_PYTHON_VERSION"

  if ! "$PYENV" global | grep -q "$MONOREPO_PYTHON_VERSION"; then
    "$PYENV" global "$MONOREPO_PYTHON_VERSION"
  fi

  if ! "$PYENV_ROOT/versions/$MONOREPO_PYTHON_VERSION/bin/python" -c 'import ssl, lzma, hashlib' &>/dev/null; then
    __log_error "Python built via pyenv missing required modules"
  fi

  export MONOREPO_VENV="$HOME/.virtualenvs/openai"

  # Check if venv exists and compare Python versions
  if [[ -d "$MONOREPO_VENV" ]]; then
    local venv_python_version
    venv_python_version=$("$MONOREPO_VENV/bin/python" -V 2>&1 | cut -d' ' -f2 || echo "")

    if [[ "$venv_python_version" != "$MONOREPO_PYTHON_VERSION" ]]; then
      __log_info "Python version changed from $venv_python_version to $MONOREPO_PYTHON_VERSION. Recreating virtual environment."
      rm -fr "$MONOREPO_VENV"

      # Clear OAIPKG cache when changing Python versions
      __log_info "Clearing OAIPKG cache /tmp/oaipkg/cache due to Python version change."
      rm -fr "/tmp/oaipkg/cache"
    fi
  fi

  mkdir -p "$MONOREPO_VENV"

  "$PYENV_ROOT/versions/$MONOREPO_PYTHON_VERSION/bin/python" -m venv "$MONOREPO_VENV"
  "$MONOREPO_VENV/bin/python" -m pip install --upgrade pip setuptools wheel pre-commit
}

##################################################################################
# 8. GO SETUP (FORCE X86 ON MACOS)
##################################################################################
setup_go() {
  __log_info "Installing Go $MONOREPO_GO_VERSION"

  local marker_file="$OPENAI_DIR/.go_version"
  if [[ -f "$marker_file" ]] && grep -q "$MONOREPO_GO_VERSION" "$marker_file"; then
    __log_info "Go $MONOREPO_GO_VERSION already installed."
    return
  fi

  mkdir -p "$OPENAI_DIR/bin"
  rm -rf "$OPENAI_DIR/go" "$OPENAI_DIR/bin/go" "$OPENAI_DIR/bin/gofmt"

  local go_file
  if [[ "$OS_TYPE" == "macos" ]]; then
    go_file="go${MONOREPO_GO_VERSION}.darwin-amd64.tar.gz"
  else
    go_file="go${MONOREPO_GO_VERSION}.linux-amd64.tar.gz"
  fi

  curl -sSL "https://go.dev/dl/${go_file}" | tar -xz -C "$OPENAI_DIR"

  ln -s "$OPENAI_DIR/go/bin/go" "$OPENAI_DIR/bin/go"
  ln -s "$OPENAI_DIR/go/bin/gofmt" "$OPENAI_DIR/bin/gofmt"

  echo "$MONOREPO_GO_VERSION" > "$marker_file"
}

##################################################################################
# 9. RUST SETUP (X86)
##################################################################################
setup_rust() {
  __log_info "Setting up Rust toolchain >= $MONOREPO_RUST_VERSION"

  if ! command -v cargo &>/dev/null; then
    if [[ "$OS_TYPE" == "macos" && "$(uname -m)" == "arm64" ]]; then
      arch -x86_64 curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs \
        | arch -x86_64 bash -s -- -y --default-toolchain "$MONOREPO_RUST_VERSION"
    else
      curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs \
        | bash -s -- -y --default-toolchain "$MONOREPO_RUST_VERSION"
    fi
    source "$HOME/.cargo/env"
  fi

  if ! command -v rustup &>/dev/null; then
    __log_error "Found cargo but not rustup."
  fi

  export MONOREPO_VENV="$HOME/.virtualenvs/openai"

  local actual_rustc_version
  actual_rustc_version="$(rustc --version || true)"
  if ! "$MONOREPO_VENV/bin/python" -c "import sys; v=lambda s: tuple(map(int,s.split('.'))); \
    assert v('$actual_rustc_version'.split()[1]) >= v('$MONOREPO_RUST_VERSION')" 2>/dev/null; then
    rustup update
    source "$HOME/.cargo/env"
  fi

  actual_rustc_version="$(rustc --version || true)"
  if ! "$MONOREPO_VENV/bin/python" -c "import sys; v=lambda s: tuple(map(int,s.split('.'))); \
    assert v('$actual_rustc_version'.split()[1]) >= v('$MONOREPO_RUST_VERSION')" 2>/dev/null; then
    __log_error "rustc version < $MONOREPO_RUST_VERSION."
  fi
}

##################################################################################
# 10. SHELL PROFILE
##################################################################################
setup_shell_profile() {
  __log_info "Setting up .orange_profile"

  local cluster_access="$(jq -r --arg user "$OPENAI_USER" \
    '.[$user].cluster_access' "$OPENAI_DIR/users.json")" # List of clusters that this user has access to

  local clusters_json
  clusters_json="$(yq -o json -e '.clusters[] | { "name":.name,"region":.region }' "$BOOTSTRAPPING_DIR/orange-clusters.yaml" \
    | jq --compact-output -M -j --slurp --argjson access "$cluster_access" '[.[] | select(.name as $name | $access | index($name))]')"

  cat <<EOF > "$HOME/.orange_profile"
export OPENAI_USER=\$USER
export OPENAI_DIR=\$HOME/.openai
export MONOREPO_ROOT=\$HOME/code/openai
export MONOREPO_DIR=\$HOME/.openai
export MONOREPO_VENV=\$HOME/.virtualenvs/openai
export PYENV_ROOT="\$HOME/.pyenv"
export PYENV="\$PYENV_ROOT/bin/pyenv"
export PATH="\$PYENV_ROOT/bin:\$OPENAI_DIR/bin:\$MONOREPO_DIR/bin:\$HOME/.cargo/bin:\$PATH"

eval "\$(pyenv init --path)"
source "\$MONOREPO_VENV/bin/activate"
alias b="rcall-brix"

# orange variables
export AZURE_USE_IDENTITY=1
export BRIX_SELF_UPGRADE=0
export USE_STORAGE_MAP=false
export OPENAI_STORAGE_SET=oaiorange\$OPENAI_USER
export EXTRA_CLUSTERS_JSON='$clusters_json'
export APPLIED_STORAGE_MAP="az://orngoaiartifacts/storage-map/storage-map.json"

# TODO: remove OAI_DEPLOYMENTS_TOML_PATH after a while (left here for now for backward compatibility with code up to 3/11).
export OAI_DEPLOYMENTS_TOML_PATH=az://orngoaiartifacts/shared/cacherd/deployments.toml
export CACHE_SVC_DEPLOYMENTS_CONFIG=az://orngoaiartifacts/shared/cacherd/deployments.toml

# TODO: remove OAI_ARTIFACTS_BASE after a while (left here for now for backward compatibility with code up to 6/3/2025).
export OAIPKG_ARTIFACTS_BASE=az://orngoaiartifacts
export OAIPKG_WHEEL_BLOB_ACCOUNT=orngoaiartifacts
export OAIPKG_WHEELS_DOWNLOAD_TIMEOUT_SEC=600
EOF

    if [[ "$OS_TYPE" == "linux" ]]; then
      echo "export BRIX_CLI_DISABLE_TAILSCALE=1" >> "$HOME/.orange_profile"
      echo "export SKIP_TORCHFLOW_SETUP_CHECK=1" >> "$HOME/.orange_profile"
    fi

    if grep -q "source .*\.iridium_profile" "$HOME/.zprofile"; then
      __log_error "Manual upgrade step: Edit $HOME/.zprofile to remove: source \$HOME/.iridium_profile"
      return 1
    fi

    if ! grep -Fxq "source $HOME/.orange_profile" "$HOME/.zprofile"; then
      echo -e "source $HOME/.orange_profile\n$(cat $HOME/.zprofile)" > ~/.zprofile
      echo "$HOME/.orange_profile has been added to the top of $HOME/.zprofile"
    else
      echo "$HOME/.orange_profile is already sourced in $HOME/.zprofile"
    fi
}

##################################################################################
# 11. KUBECTL, LOGIN, CLUSTER IMPORT
##################################################################################
setup_kubectl() {
  if [[ ! -f "$OPENAI_DIR/bin/kubectl" ]]; then
    __log_info "Installing kubectl in $OPENAI_DIR/bin"
    mkdir -p "$OPENAI_DIR/bin"
    if [[ "$OS_TYPE" == "macos" ]]; then
      curl -sSL "https://storage.googleapis.com/kubernetes-release/release/v1.22.0/bin/darwin/amd64/kubectl" \
        -o "$OPENAI_DIR/bin/kubectl"
    else
      curl -sSL "https://dl.k8s.io/release/v1.22.0/bin/linux/amd64/kubectl" \
        -o "$OPENAI_DIR/bin/kubectl"
    fi
    chmod +x "$OPENAI_DIR/bin/kubectl"
    "$OPENAI_DIR/bin/kubectl" version --client
  fi

  if ! command -v kubelogin &>/dev/null; then
    brew install Azure/kubelogin/kubelogin || true
  fi
  if ! command -v oidc-kubelogin &>/dev/null; then
    brew tap int128/kubelogin
    brew install int128/kubelogin/oidc-kubelogin || true
  fi
}

import_clusters() {
  setup_kubectl

  __log_info "Importing clusters from orange-clusters.yaml"
  local clusters
  clusters="$(jq -r --arg user "$OPENAI_USER" \
    '.[$user].cluster_access | .[]' "$OPENAI_DIR/users.json")" # Use list of clusters user has access to

  local default_cluster=""
  local cluster_name

  while IFS= read -r cluster_name; do
    __log_info "Importing context for $cluster_name"

    if ! grep -Fq "$cluster_name" "$BOOTSTRAPPING_DIR/orange-clusters.yaml"; then
      echo "$cluster_name is not configured in the orange-clusters.yaml file."
      continue
    fi

    local oidc_url oidc_ca oidc_app_id oidc_issuer_url is_default
    oidc_url="$(yq -e ".clusters[] | select(.name == \"${cluster_name}\") | .oidcProxy.url" "$BOOTSTRAPPING_DIR/orange-clusters.yaml")"
    oidc_ca="$(yq -e ".clusters[] | select(.name == \"${cluster_name}\") | .oidcProxy.caCertificate" "$BOOTSTRAPPING_DIR/orange-clusters.yaml")"
    oidc_app_id="$(yq -e ".clusters[] | select(.name == \"${cluster_name}\") | .oidcProxy.appID" "$BOOTSTRAPPING_DIR/orange-clusters.yaml")"
    oidc_issuer_url="$(yq -e ".clusters[] | select(.name == \"${cluster_name}\") | .oidcProxy.issuerURL" "$BOOTSTRAPPING_DIR/orange-clusters.yaml")"
    is_default="$(yq -e ".clusters[] | select(.name == \"${cluster_name}\") | .default_cluster // \"false\"" "$BOOTSTRAPPING_DIR/orange-clusters.yaml")"

    [[ "$is_default" == "true" ]] && default_cluster="$cluster_name"

    mkdir -p "$OPENAI_DIR/personal"
    local ca_cert_file="$OPENAI_DIR/personal/ca-${cluster_name}.pem"
    echo -n "$oidc_ca" | base64 -d > "$ca_cert_file"

    kubectl config set-cluster "${cluster_name}" \
      --server="${oidc_url}" \
      --certificate-authority="${ca_cert_file}" || \
      __log_error "Failed to set cluster: $?"

    kubectl config set-context "${cluster_name}" \
      --cluster="${cluster_name}" \
      --user="${cluster_name}" \
      --namespace="${OPENAI_USER}" || \
      __log_error "Failed to set context"

    kubectl config use-context "${cluster_name}" || \
      __log_error "Failed to use context"

    kubectl config set-credentials "${cluster_name}" \
      --exec-api-version="client.authentication.k8s.io/v1beta1" \
      --exec-command="kubectl" \
      --exec-arg="oidc-login" \
      --exec-arg="get-token" \
      --exec-arg="--oidc-issuer-url=${oidc_issuer_url}" \
      --exec-arg="--oidc-client-id=${oidc_app_id}" \
      --exec-arg="--oidc-extra-scope=profile" \
      --exec-arg="--oidc-extra-scope=offline_access" || \
      __log_error "Failed to set credentials"
  done <<< "$clusters"

  if [[ -n "$default_cluster" ]]; then
    __log_info "Setting $default_cluster as default context"
    kubectl config use-context "$default_cluster" || \
      __log_error "Failed to switch to default context"
  fi
}

##################################################################################
# 12. RCALL
##################################################################################
setup_rcall() {
  __log_info "Setting up rcall"

  if ! command -v mpirun &>/dev/null; then
    __log_warn "mpich not found. Check logs?"
  fi

  local cluster_access="$(jq -r --arg user "$OPENAI_USER" \
    '.[$user].cluster_access' "$OPENAI_DIR/users.json")" # List of clusters that this user has access to

  local cluster_names_json
  cluster_names_json="$(yq -o json -e '.clusters[] | .name' "$BOOTSTRAPPING_DIR/orange-clusters.yaml" \
    | jq --compact-output -M -j --slurp --argjson access "$cluster_access" '[.[] | select(. as $name | $access | index($name))]')"

  local multi_island_cluster_names_json
  multi_island_cluster_names_json="$(yq -e -o json '.clusters[] | select(.multi_island == true) | .name' \
    "$BOOTSTRAPPING_DIR/orange-clusters.yaml" | jq --compact-output -M -j --slurp \
    --argjson access "$cluster_access" '[.[] | select(. as $name | $access | index($name))]')"

  "$MONOREPO_VENV/bin/python" "$MONOREPO_ROOT/install.py" rcall

  cat "$BOOTSTRAPPING_DIR/rcall_config_templ.py" \
  | CLUSTER_NAMES_JSON="$cluster_names_json" \
    MULTI_ISLAND_CLUSTER_NAMES_JSON="$multi_island_cluster_names_json" \
    envsubst > "$HOME/.rcall_config.py"
}

##################################################################################
# 13. BRIX
##################################################################################
setup_brix() {
  __log_info "Installing brix $MONOREPO_BRIX_VERSION"

  mkdir -p $MONOREPO_BRIX_ROOT/brix
  pushd "$MONOREPO_BRIX_ROOT/brix" >/dev/null
    git fetch --all
    git checkout "$MONOREPO_BRIX_VERSION"
    make install-brix || __log_warn "'make install-brix' may have issues."
  popd >/dev/null

  mkdir -p "$OPENAI_DIR/bin"
  if [[ -f "$HOME/go/bin/brix" ]]; then
    mv "$HOME/go/bin/brix" "$OPENAI_DIR/bin/" || __log_warn "Couldn't move brix from go/bin."
  else
    __log_warn "No brix found in $HOME/go/bin."
  fi

  local brix_namespace="$OPENAI_USER"
  local brix_available_clusters
  brix_available_clusters="$(jq -r --arg user "$OPENAI_USER" \
    '.[$user].cluster_access | join(",")' "$OPENAI_DIR/users.json")" # List of clusters that this user has access to
  local brix_project_root
  brix_project_root="$(dirname "$MONOREPO_ROOT")"

  mkdir -p "$HOME/.brix"
  cat <<EOF > "$HOME/.brix/profile"
export BRIX_NAMESPACE=$brix_namespace
export BRIX_AVAILABLE_CLUSTERS=$brix_available_clusters
export BRIX_GIT_PROJECT_ROOT=$brix_project_root
export BRIX_USE_KUBE_CONFIG_AUTH=1
EOF

    if ! grep -Fxq "source \$HOME/.brix/profile" "$HOME/.zprofile"; then
      echo "source \$HOME/.brix/profile" >> "$HOME/.zprofile"
      echo "\$HOME/.brix/profile has been added to "$HOME/.zprofile""
    else
      echo "\$HOME/.brix/profile is already sourced in $HOME/.zprofile"
    fi

}

##################################################################################
# 14. LAPTOP UTILS
##################################################################################
setup_laptop_utils() {
  __log_info "Copying laptop utils to $OPENAI_DIR/bin"
  mkdir -p "$OPENAI_DIR/bin"
  if [[ -d "$BOOTSTRAPPING_DIR/laptop-utils" ]]; then
    cp -a "$BOOTSTRAPPING_DIR/laptop-utils/"* "$OPENAI_DIR/bin" || true
  fi
}

##################################################################################
# 15. TORCHFLOW
##################################################################################
setup_torchflow() {
  __log_info "setting up torchflow from version $TORCHFLOW_MIRROR_VERSION"

  pushd $MONOREPO_ROOT
  git checkout $TORCHFLOW_MIRROR_VERSION

  # __log_info "installing beam"
  # this must be done after setup completes successfully so it moved out of this script.
  # oaipkg install beam beam_py_native

  __log_info "installing genai requirements [ pyAesCrypt ]"
  "$MONOREPO_VENV/bin/python" -m pip install pyAesCrypt

  __log_info "installing torchflow"
  OAIPKG_INSTALL_APPLIED_MODELS_CRYPTO=editable OAIPKG_OVERRIDE_WHEEL=unsafe_skip oaipkg install torchflow
  OAIPKG_INSTALL_BIRDER=editable oaipkg install birder

  popd
}

setup_orangectl() {
  __log_info "Setting up orangectl"
  "$MONOREPO_VENV/bin/python" -m pip install -e $( dirname "${BASH_SOURCE[0]}" )/src
}

##################################################################################
# 16. MAIN
##################################################################################
main() {
  check_repos

  setup_packages

  # Requires jq, so we check after setup_packages
  check_tailscale

  az_login
  download_users_map

  setup_python
  setup_go
  setup_rust
  setup_shell_profile

  source "$HOME/.orange_profile"

  import_clusters
  setup_brix
  setup_rcall
  setup_laptop_utils
  setup_torchflow

  setup_orangectl

  __log_info "setup completed successfully!

next steps:
* restart current shell session to load new environment.
* login to azure using az login --tenant green.microsoft.com.
* initialize local storage map using orange_pull_storagemap.

* please review the user guide at https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4583/Getting-Started-Orange
"
}

main
