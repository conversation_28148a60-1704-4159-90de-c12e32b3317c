# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
# DO NOT MAKE CHANGES IN YOUR ~/.rcall_config.py AS THEY WILL BE OVERWRITTEN
# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
#
# rcall overrides template.
# this file serves two purposes:
# 1. As it its intention: provide RCall configs.
# 2. Hack and override internal variables and functions to adapt it
#    to iridium clusters.
#
# bootstrapping laptop script uses this file and fills in the
# missing details and finally placing it in ~/.rcall_config.py.
#

CODE_INCLUDE_PATHS = [
  # put directories with projects that you want to upload, e.g. api or model-runner-api
  # orange: oai code hard-codes openai repo and therefore we should either clone
  #          torchflow-mirror in openai dir or create a symlink.
]

EXTRA_SETUP = """
    # put setup commands here
    # these run on each instance when it starts up
    # e.g. pip install -e ~/code/api/engineapi

    # Enable cluster caching on orange clusters, without encryption.
    export TWDEV_LAUNCH_CACHE_SVC_ENGINE=1
    export CACHE_SVC_ENABLE_ENCRYPTION=0
    export CACHE_SVC_NAMESPACE="orange"
""" + EXTRA_SETUP

ENVIRONMENT.update(
  {
    # put additional environment variables here
    # e.g. "PYTHONTRACEMALLOC": "10"
    # "SNOWFLAKE_USERNAME": ""
    # "SNOWFLAKE_PASSWORD": ""
    #
    # iridium: TODO: add env variables to disable snowflake by default.
    # iridium does not yet have in-cluster pip proxies.
    "UV_INDEX_URL": "",
    "PIP_INDEX_URL": "",
    # Encodings settings that work in iridium. TODO(sebastko): double-check differences between
    # different storages on the OAI side and try to match our set-up more closely, or at least
    # make sure we are using correct encodings for the newest/most important models (like Scallion).
    "TIKTOKEN_ENCODINGS_BASE": "az://orngoaiartifacts/data-gym/encodings",
    # This one seems to be set in the container to https://iridiumdata.blob.core.windows.net/encodings/applied-encodings
    # but that storage path does not seem to be correctly populated.
    "OPENAI_ENCODINGS_BASE": "az://orngoaiartifacts/data-gym/encodings",
    "OPENAI_ENCODER_GYM_BASE": "az://orngoaiartifacts/data-gym/encodings",

    # TODO: remove OAI_DEPLOYMENTS_TOML_PATH after a while (left here for now for backward compatibility with code up to 3/11).
    "OAI_DEPLOYMENTS_TOML_PATH": "az://orngoaiartifacts/shared/cacherd/deployments.toml",
    "CACHE_SVC_DEPLOYMENTS_CONFIG": "az://orngoaiartifacts/shared/cacherd/deployments.toml",
    
    "OTEL_DISABLE_TRACES": "1",  # Disable OTel traces - Orange clusters don't have trace collector configured
  }
)

KUBE_CLUSTERS = $CLUSTER_NAMES_JSON

# add our multi-island clusters to the hard-coded list.
# note this will move to Cluster crds and this won't be needed:
# https://dev.azure.com/project-argos/Mimco/_git/brix?path=/pkg/apis/brix/v1alpha1/types_cluster.go&version=GBmaster&line=42&lineEnd=46&lineStartColumn=1&lineEndColumn=41&lineStyle=plain&_a=contents
# and also the note at the constant definition:
# https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/rcall/rcall/constants.py&version=GBopenai/msft/rlhf-master&line=103&lineEnd=104&lineStartColumn=1&lineEndColumn=1&lineStyle=plain&_a=contents

import rcall
# TODO: this is causing issues with code from 3/11/2025 or newer, as rcall has changed. Figure out the long-term fix.
# rcall.constants.KUBE_MULTI_ISLAND_CLUSTERS.update($MULTI_ISLAND_CLUSTER_NAMES_JSON)

# Code version-specific configuration overrides.
MSFT_SKIP_IMAGE_CHECK = False
ENV_CONFIG_PATH = "_msft/rcall_overrides/orange.py"

############################################################################################
# end of user-configurable section
# begin orange hooks/hacks and validations
############################################################################################

if os.path.exists(os.path.join(MONOREPO_ROOT, ENV_CONFIG_PATH)):
    exec_monorepo_config(ENV_CONFIG_PATH)
else:
    print(f"WARNING: could not find rcall config overrides at '{os.path.join(MONOREPO_ROOT, ENV_CONFIG_PATH)}', falling back to DOCKER_IMAGE from 1/14/2025...")
    EXPECTED_CROW_DOCKER_IMAGE = "openai.azurecr.io/rcall:crow-52d381aac2901d92a8709d3667c1fafef4e5ab8e-2404-311"
    DOCKER_IMAGE = "iridiumsdc.azurecr.io/rcall:crow-1147178"

# this is a dummy image for the infra init container - just reuse the same image
INFRA_IMAGE = DOCKER_IMAGE

assert MSFT_SKIP_IMAGE_CHECK or EXPECTED_CROW_DOCKER_IMAGE == CROW_DOCKER_IMAGE, \
(
    f"{EXPECTED_CROW_DOCKER_IMAGE} != {CROW_DOCKER_IMAGE}. "
    + f"The config overrides are inconsistent with rcall/rcall/global_config.py. "
    + "Chances are the base image has changed and our image needs to be rebuilt. "
    + "You can skip this check by setting MSFT_SKIP_IMAGE_CHECK=True in your ~/.rcall_config.py."
)



# override kubernetes api server authentication.
# oai uses client certificates assigned to each user to authenticate against api server.
# iridium uses azure aad auth using kubelogin.
#
# this bit of override replaces cluster info function with one that uses kubelogin token
# and proivded ca certificate.
#
# since python requests package does not use in-lined ca certificate, laptop bootstrapping
# script should extract it from kubeconfig and save it in $OPENAI_DIR/personal/ca-<cluster_name>.pem.
from typing import Any
import subprocess
import rcall.kube_requests
def _request_info_override(cluster: str | None, force_cluster: bool = False) -> tuple[str, dict[str, Any]]:
    config = rcall.kube_requests.config_info()
    clusters = {c["name"]: c["cluster"] for c in config["clusters"]}
    contexts = {c["name"]: c["context"] for c in config["contexts"]}
    users = {c["name"]: c["user"] for c in config["users"]}

    if cluster is None:
        # Cluster not specified, defaulting to current context.
        cluster = contexts[config["current-context"]]["cluster"]

    if cluster in clusters:
        server = clusters[cluster]["server"]
        user_exec = users[contexts[cluster]["user"]]["exec"]
        token_exec = user_exec["command"] + " " + " ".join(user_exec["args"])
        token = subprocess.check_output(token_exec + " | jq -j '.status.token'", shell=True).decode("utf-8")
        return (
            server,
            {
                "headers": {"Authorization": "Bearer " + token},
                # iridium: TODO: replace this in laptop bootstrapping step
                "verify": f"${OPENAI_DIR}/personal/ca-{cluster}.pem",
            },
        )

    raise RuntimeError(f"Invalid cluster name {cluster}")
rcall.kube_requests._request_info = _request_info_override

# get rid of vpn warning as it is hard-coded to openai networks
# for now we just make sure that there is at least one `iridium`
# network connected.
import rcall.util
import platform
import os
def ensure_vpn_override():
    """
    Print an error and exit if the user is not on the VPN
    """
    # only check on mac machines so that we can run rcall on the clusters
    if platform.system() != "Darwin":
        return

    def print_vpn_help():
        print()
        print(colorize("VPN connection seems to be missing!", "red"))
        print("rcall will not work")
        print()
        print("See the VPN setup docs for help:")
        print("http://go/tailscale")
        print()

    proc = subprocess.run(["ifconfig"], check=True, stdout=subprocess.PIPE)
    proc_netstat = subprocess.run(["netstat", "-rn"], check=True, stdout=subprocess.PIPE)
    netstat = proc_netstat.stdout.decode("utf8")
    ifconfig = proc.stdout.decode("utf8")

    sci_tailscale_exists = False
    tailscale_location = "/Applications/Tailscale.app/Contents/MacOS/Tailscale"
    if os.path.exists(tailscale_location):
        try:
            proc_tailscale = subprocess.run(
                [tailscale_location, "status"],
                check=True,
                stdout=subprocess.PIPE,
            )
            sci_tailscale_exists = "azml-prod-uksouth-azhub-7-router" in proc_tailscale.stdout.decode("utf8")
        except subprocess.CalledProcessError:
            print_vpn_help()
rcall.util.ensure_vpn = ensure_vpn_override

## access overrides
import rcall.access
rcall.access.ACCOUNT_DOMAIN = "green.microsoft.com"