Iridium Architecture
---

## Current setup

The following digram outlines the architecture. The directory `terraform/` contains terraform configurations that brings up clusters according to the below setup.

![Architecture](docs/DR%20Arch.png)

#### Networking
Cluster resources are created in AME tenant (right). Access to the VNet is controlled by TailScale VPN which connects to a TailScale Exit Node VMs within the VNet.

Users must authenticate and use TailScale clients to have network accesss to Azure resources. Authentication is done using MSIT tenant credenetails.

NOTE: Today storage accounts do not have private links and we allow direct access from laptop (more on that later). Final setup will put them behind private links accessible only from VPN.

#### VPN authorization
MSIT tenant users must be authorized to access TailScale VPN. This must be performed over two steps:
* Merge a PR to add the user to [this file](https://dev.azure.com/project-argos/Mimco/_git/microsoft-tailscale?path=/policy.hujson&version=GBmain&line=129&lineEnd=130&lineStartColumn=1&lineEndColumn=1&lineStyle=plain&_a=contents) in group `iridium-access`. [Sample PR](https://dev.azure.com/project-argos/Mimco/_git/microsoft-tailscale/pullrequest/26858).
* A TailScale admin must approve both user and machine access from [TailScale admin portal](https://login.tailscale.com/admin). Currently only `osama` and `amrh` can perform this action.

#### Kubernetes API server access
Since Kubernetes is in AME tenant and users are authenticated in MSIT tenant, an OIDC proxy is deployed in the cluster that validates users MSIT tokens and proxy the request to the API server with [User Impersonation](https://kubernetes.io/docs/reference/access-authn-authz/authentication/#user-impersonation).

Impersonated users are then authorized to access Kubernetes resources using Kubernetes native RBAC.

See [this onboarding script](clusterops/onboard-user-corp.sh) for more details.

#### Kubernetes resources access
Each cluster user has a dedicated namespace. Users are granted full access to that namespace only and are not allowed to access anything else.

See [this onboarding script](clusterops/onboard-user-corp.sh) for more details.

#### Azure resources access
In OAI, users are allowed direct access to Azure resources such as their personal storage accounts. This is currently not possible since the resources are in AME tenant and it does not allow pinned authentication any more. This is an ongoing challenge.

See [this onboarding script](clusterops/onboard-user-corp.sh) for more details.

## Admin access
All cluster resources are created in subscription `64467a16-0cdd-4a44-ad9b-83b5540828ac`. Access is restricted to group `AME\genai-resilience-access`.
