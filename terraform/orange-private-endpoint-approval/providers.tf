terraform {

  required_providers {
    azurerm = "4.19.0"
    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
    azresourcegraph = {
      source  = "tiwood/azresourcegraph"
      version = "0.3.0"
    }
  }
}

provider "azurerm" {
  alias                           = "orange-users-provider"
  storage_use_azuread             = true
  subscription_id                 = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS https://microsoftservicetree.com/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8
  resource_provider_registrations = "none"


  features {
    storage {
      # We create storage accounts with azurerm_storage_account that are private access only,
      # but we don't create a corresponding private endpoint + dns on the pipeline builder's vnet.
      # This setting prevents that resource from trying to query the inaccessible data-plane
      # (*.blob.core.windows.net) endpoint for e.g. static website settings.
      # Note that changing this setting might be problematic w.r.t. needing to recreate resources.
      data_plane_available = false
    }
  }
}

# default infra provider
provider "azurerm" {
  storage_use_azuread             = true
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA https://microsoftservicetree.com/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a
  resource_provider_registrations = "none"

  features {
    storage {
      data_plane_available = false
    }
  }
}

data "azurerm_client_config" "current" {}

provider "azapi" {
  subscription_id = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS https://microsoftservicetree.com/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8
}
