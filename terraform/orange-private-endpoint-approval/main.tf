data "azurerm_subscription" "orange-users" {
  provider = azurerm.orange-users-provider
}

# Retrieve all user storage account private endpoint connections, extracting only the connection ID and status.
data "azresourcegraph_query" "pending_storage_private_endpoint_ids" {
  query = <<-EOT
    resources
    | where subscriptionId == "${data.azurerm_subscription.orange-users.subscription_id}"
    | where resourceGroup == "${var.user-resources-group}"
    | where type =~ "microsoft.storage/storageAccounts"
    | extend privateEndpoints = properties.privateEndpointConnections
    | mv-expand privateEndpoints
    | project connection_id = privateEndpoints.id, status = privateEndpoints.properties.privateLinkServiceConnectionState.status
  EOT
}

locals {
  # Filter only the IDs of connections with a "Pending" status.
  pending_storage_private_endpoint_ids = [
    for endpoint in jsondecode(data.azresourcegraph_query.pending_storage_private_endpoint_ids.result) :
    endpoint.connection_id
    if endpoint.status == "Pending"
  ]
}

resource "null_resource" "endpoint_approval" {
  triggers = {
    # Re-run this resource if the list of pending connection IDs changes.
    pending_connections = jsonencode(local.pending_storage_private_endpoint_ids)
  }

  provisioner "local-exec" {
    command = <<-EOT
      echo "Approving ${length(local.pending_storage_private_endpoint_ids)} pending private endpoint connections"
      pending_ids='${jsonencode(local.pending_storage_private_endpoint_ids)}'
      # Loop over each connection ID from the JSON array using jq.
      echo "$pending_ids" | jq -r '.[]' | while read -r id; do
         echo "Approving connection with id: $id"
         az network private-endpoint-connection approve --id "$id" --description "Approved by Terraform"
         sleep 10 # Reduce risk of throttling
      done
    EOT
  }
}
