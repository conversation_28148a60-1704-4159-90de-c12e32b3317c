variable "storage_account_prefix" {
  type        = string
  description = "Prefix for the storage account name."
}

variable "oidc_token_file" {
  type        = string
  description = "Path to the OIDC token file for the federated credentials."
  default     = "/tmp/oidc_token"
}

variable "user_sync_keyvault" {
  type = object({
    id                = string
    subscription_name = string
    subscription_id   = string
    tenant_id         = string
  })
}

variable "user_app_sync_sp_client_id" {
  type        = string
  description = "Client ID of the service principal used to sync user apps."
}

variable "user_storage_resource_group_id" {
  type        = string
  description = "The user storage resource group ID."
}

variable "team-quota-file-location" {
  type        = string
  description = "Location of the file containing the team and quota information (originally named output.json, provided by the orange-onboarding ADO pipeline)."
}
