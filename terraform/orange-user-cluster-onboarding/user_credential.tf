data "azurerm_key_vault_secret" "user-sp-clientid" {
  provider = azurerm.user-secret-reader-sp

  for_each     = toset(keys(local.users))
  name         = "${each.key}-clientid"
  key_vault_id = var.user_sync_keyvault.id
}

data "azurerm_key_vault_secret" "user-sp-secret" {
  provider = azurerm.user-secret-reader-sp

  for_each     = toset(keys(local.users))
  name         = "${each.key}-secret"
  key_vault_id = var.user_sync_keyvault.id
}
