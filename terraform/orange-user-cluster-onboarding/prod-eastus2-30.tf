# BEGIN: Providers for cluster prod-eastus2-30

locals {
  prod-eastus2-30 = "prod-eastus2-30"
}

provider "kubernetes" {
  alias                  = "prod-eastus2-30"
  host                   = local.clusters[local.prod-eastus2-30].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.cluster_ca_certificate)

  #
  # For client-id/server-id values see the following link:
  # https://learn.microsoft.com/en-us/azure/aks/kubelogin-authentication
  #
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

provider "kubectl" {
  alias                  = "prod-eastus2-30"
  host                   = local.clusters[local.prod-eastus2-30].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.prod-eastus2-30].cluster_aks.kube_config.0.cluster_ca_certificate)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

# BEGIN: prod-eastus2-30 onboarding

module "cluster-onboarding-prod-eastus2-30" {
  providers = {
    kubernetes = kubernetes.prod-eastus2-30
    kubectl    = kubectl.prod-eastus2-30
    azurerm    = azurerm.aisc-prod-ame-dp-ipp-orange-eastus2-01 # Use the subscription-specific provider
  }

  source = "../modules/cluster-onboarding"
  # select all the users that have prod-eastus2-30 in their list of clusters
  for_each = { for user, settings in local.users : user => settings if contains(settings.cluster_access, "prod-eastus2-30") }
  subscription = {
    id   = var.user_sync_keyvault.subscription_id
    name = var.user_sync_keyvault.subscription_name
  }

  tenant_id = var.user_sync_keyvault.tenant_id
  user = {
    alias                = each.key
    object_id            = each.value.object_id
    storage_account_name = each.value.storage_account_name
    team_name            = each.value.team_name
    auth_identifier      = each.value.auth_identifier

    secret = data.azurerm_key_vault_secret.user-sp-secret[each.key].value
    app = {
      client_id = data.azurerm_key_vault_secret.user-sp-clientid[each.key].value
    }
  }

  cluster = local.prod-eastus2-30
}

module "users-brix-cluster-access-prod-eastus2-30" {
  providers = {
    kubernetes = kubernetes.prod-eastus2-30
    kubectl    = kubectl.prod-eastus2-30
  }
  source = "../modules/users-brix-cluster-access"
  users  = local.cluster_users["prod-eastus2-30"]
}

module "quota-prod-eastus2-30" {
  source           = "../modules/orange-cluster-guest-quota"
  providers = {
    kubernetes = kubernetes.prod-eastus2-30
    kubectl = kubectl.prod-eastus2-30
  }

  cluster_name     = "prod-eastus2-30"
  all_teams        = local.all_teams
}

# Moonfire team admin RBAC for prod-eastus2-30
module "moonfire-admin-rbac-prod-eastus2-30" {
  providers = {
    kubernetes = kubernetes.prod-eastus2-30
  }

  source                     = "../modules/moonfire-admin-rbac"
  moonfire_team_admin_groups = local.cluster_moonfire_admin_groups["prod-eastus2-30"]
}

# END: cluster prod-eastus2-30 onboarding
