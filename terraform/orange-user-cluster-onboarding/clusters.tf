# Define modules for each AME cluster subscription
# One module instance will be created per cluster
# Consumes clusters_by_subscription from clusters_providers.tf and endpoints_by_subscription from private_endpoints.tf

module "clusters_aisc-prod-ame-dp-ipp-orange-uksouth-01" {
  for_each          = local.clusters_by_subscription.aisc-prod-ame-dp-ipp-orange-uksouth-01
  source            = "./cluster-azure"
  providers         = { azurerm = azurerm.aisc-prod-ame-dp-ipp-orange-uksouth-01 }
  cluster           = each.value
  private_endpoints = local.endpoints_by_subscription.aisc-prod-ame-dp-ipp-orange-uksouth-01[each.key]
}
module "clusters_azml-nexus-hpe-prod-ame-southcentralus-01" {
  for_each          = local.clusters_by_subscription.azml-nexus-hpe-prod-ame-southcentralus-01
  source            = "./cluster-azure"
  providers         = { azurerm = azurerm.azml-nexus-hpe-prod-ame-southcentralus-01 }
  cluster           = each.value
  private_endpoints = local.endpoints_by_subscription.azml-nexus-hpe-prod-ame-southcentralus-01[each.key]
}
module "clusters_aisc-prod-ame-dp-ipp-orange-westus2-01" {
  for_each          = local.clusters_by_subscription.aisc-prod-ame-dp-ipp-orange-westus2-01
  source            = "./cluster-azure"
  providers         = { azurerm = azurerm.aisc-prod-ame-dp-ipp-orange-westus2-01 }
  cluster           = each.value
  private_endpoints = local.endpoints_by_subscription.aisc-prod-ame-dp-ipp-orange-westus2-01[each.key]
}
module "clusters_aisc-prod-ame-dp-ipp-orange-eastus2-01" {
  for_each          = local.clusters_by_subscription.aisc-prod-ame-dp-ipp-orange-eastus2-01
  source            = "./cluster-azure"
  providers         = { azurerm = azurerm.aisc-prod-ame-dp-ipp-orange-eastus2-01 }
  cluster           = each.value
  private_endpoints = local.endpoints_by_subscription.aisc-prod-ame-dp-ipp-orange-eastus2-01[each.key]
}
module "clusters_aisc-stage-ame-dp-ipp-orange-01" {
  for_each          = local.clusters_by_subscription.aisc-stage-ame-dp-ipp-orange-01
  source            = "./cluster-azure"
  providers         = { azurerm = azurerm.aisc-stage-ame-dp-ipp-orange-01 }
  cluster           = each.value
  private_endpoints = local.endpoints_by_subscription.aisc-stage-ame-dp-ipp-orange-01[each.key]
}
# Add new subscriptions here when needed

# Create helper to unify access across all subscriptions
locals {
  clusters_data = merge(
    module.clusters_aisc-prod-ame-dp-ipp-orange-uksouth-01,
    module.clusters_azml-nexus-hpe-prod-ame-southcentralus-01,
    module.clusters_aisc-prod-ame-dp-ipp-orange-westus2-01,
    module.clusters_aisc-prod-ame-dp-ipp-orange-eastus2-01,
    module.clusters_aisc-stage-ame-dp-ipp-orange-01
    # Add new subscriptions here when needed
  )
}
