locals {
  private_endpoints = {
    for cluster_name, cluster in local.clusters : cluster_name => {
      for username in keys(local.cluster_users[cluster_name]) : "pe-${cluster_name}-${username}" => {
        cluster            = cluster_name
        username           = username
        storage_account_id = format("%s/providers/Microsoft.Storage/storageAccounts/%s", var.user_storage_resource_group_id, local.users[username].storage_account_name)
      }
    }
  }

  # Group cluster endpoints by subscription for easier resource creation
  # Maps subscription ID -> cluster name -> private endpoint name -> private endpoint details
  endpoints_by_subscription = {
    for sub in keys(local.subscriptions) : sub => {
      for cluster_name, cluster_endpoints in local.private_endpoints : cluster_name => cluster_endpoints
      if local.clusters[cluster_name].subscription == sub
    }
  }
}
