# Adding a New Subscription & Cluster: Step-by-Step Guide

This guide explains how to add a new subscription and clusters within that subscription to the Terraform configuration.

**If you're adding a second cluster to an existing subscription, skip to Step 4!**

## 1. Add the Provider

In `providers.tf`, add a new provider entry for your subscription:

```terraform
provider "azurerm" {
  alias                      = "new_sub_name"         # Use subscription name
  skip_provider_registration = true
  storage_use_azuread        = true
  subscription_id            = "new-subscription-id"  # Your new subscription ID

  features {}
}
```

## 2. Register the Subscription in Configuration

In `clusters_providers.tf`, add the new subscription to the `subscriptions` map:

```terraform
locals {
  subscriptions = {
    # Existing subscriptions...
    "new_sub_name" = {                          # Use subscription name
      id = "new-subscription-id"
    }
  }
}
```

## 3. Add Subscription clusters module definition

In `clusters.tf`, add a module instantiation for the new subscription:

```terraform
module "clusters_new-sub-name" {                                               # Use sub name
  for_each          = local.clusters_by_subscription.new-sub-name              # Use sub name
  source            = "./clusters"
  providers         = { azurerm = azurerm.new-sub-name }                       # Use sub name
  cluster           = each.value
  private_endpoints = local.endpoints_by_subscription.new-sub-name[each.key]   # Use sub name
}
```

Then update the `clusters_data` local:

```terraform
locals {
  clusters_data = merge(
    # Existing entries...
    module.clusters_new-sub-name,   # Use sub name
  )
}
```

## 4. Add Clusters to Configuration

Now you can add your new clusters to the `clusters` map in `clusters_providers.tf`:

```terraform
locals {
  clusters = {
    # Existing clusters...
    "new-cluster-name" = {
      host           = "https://your-cluster-host.api.federation.singularity.azure.net:443"
      resource_group = "your-resource-group"
      vnet           = "aks-vnet"
      subnet         = "AKS"
      aks_name       = "aks"
      subscription   = "new_sub_name"  # Must match the subscription name
    }
  }
}
```

## 5. Create Cluster File

Create a new file for your cluster (e.g., `new-cluster-name.tf`) using the cluster file template. You can copy an existing cluster file and update:

1. Find-and-replace the cluster name with the new one
2. Find the azurerm provider and update the alias to match the subscription of the new cluster

The template automatically adapts to use the right data sources based on the cluster's subscription.
