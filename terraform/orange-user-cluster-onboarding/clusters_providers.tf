# In terraform we can't create providers dynamically, so we have to define them in the root module.
# So here we will define all the clusters we want to onboard users to, and then we will create a provider for each of them.

locals {
  # Define all subscriptions with their IDs and provider aliases
  subscriptions = {
    "aisc-prod-ame-dp-ipp-orange-uksouth-01" = {
      id = "ab0dad62-9fe9-4302-89f4-0264610c7447"
    }
    "azml-nexus-hpe-prod-ame-southcentralus-01" = {
      id = "61a4c7c0-68e0-4675-a760-d7feed155495"
    }
    "aisc-prod-ame-dp-ipp-orange-westus2-01" = {
      id = "b82dbd21-ecad-49df-a5b4-9554082bc3a1"
    }
    "aisc-prod-ame-dp-ipp-orange-eastus2-01" = {
      id = "1b830cc3-4aaa-498f-a5d1-686346a70afe"
    }
    "aisc-stage-ame-dp-ipp-orange-01" = {
      id = "4acd2d20-f2c0-4b5b-834c-e10aef6c2443" # New subscription for stage clusters
    }
    # Add new subscriptions here when needed
  }

  # Enhanced clusters configuration with subscription information
  clusters = {
    "prod-uksouth-7" = {
      host           = "https://azmlproduksazhub7.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-uksouth-azhub-7_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS"
      aks_name       = "aks"
      subscription   = "aisc-prod-ame-dp-ipp-orange-uksouth-01"
      region         = "uksouth"
    }
    "prod-uksouth-8" = {
      host           = "https://azmlproduksazhub8.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-uksouth-azhub-8_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS"
      aks_name       = "aks"
      subscription   = "aisc-prod-ame-dp-ipp-orange-uksouth-01"
      region         = "uksouth"
    }
    "prod-uksouth-9" = {
      host           = "https://azmlproduksazhub9.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-uksouth-azhub-9_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS"
      aks_name       = "aks"
      subscription   = "aisc-prod-ame-dp-ipp-orange-uksouth-01"
      region         = "uksouth"
    }
    "stage-southcentralus-hpe-1" = {
      host           = "https://azmlstagescushpe1.api.federation.singularity-stage.azure.net:443"
      resource_group = "azml-stage-southcentralus-hpe-1_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS"
      aks_name       = "aks"
      subscription   = "azml-nexus-hpe-prod-ame-southcentralus-01"
      region         = "southcentralus"
    }
    "prod-uksouth-15" = {
      host           = "https://azmlproduksazhub15.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-uksouth-azhub-15_rg"
      vnet           = "aks-vnet"
      subnet         = "SATVNET" # Cluster was built with small AKS subnet, use larger subnet for user storage endpoints
      aks_name       = "aks"
      subscription   = "aisc-prod-ame-dp-ipp-orange-uksouth-01"
      region         = "uksouth"
    }
    "prod-southcentralus-hpe-2" = {
      host           = "https://azmlprodscushpe2.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-southcentralus-hpe-2_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS" # user storage traffic is cross-region, cannot use storage appliance
      aks_name       = "aks"
      subscription   = "azml-nexus-hpe-prod-ame-southcentralus-01"
      region         = "southcentralus"
    }
    "prod-southcentralus-hpe-5" = {
      host           = "https://azmlprodscushpe5.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-southcentralus-hpe-5_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS" # user storage traffic is cross-region, cannot use storage appliance
      aks_name       = "aks"
      subscription   = "azml-nexus-hpe-prod-ame-southcentralus-01"
      region         = "southcentralus"
    }
    "prod-southcentralus-hpe-3" = {
      host           = "https://azmlprodscushpe3.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-southcentralus-hpe-3_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS" # user storage traffic is cross-region, cannot use storage appliance
      aks_name       = "aks"
      subscription   = "azml-nexus-hpe-prod-ame-southcentralus-01"
      region         = "southcentralus"
    }
    "prod-westus2-19" = {
      host           = "https://azmlprodwus2azhub19.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-westus2-azhub-19_rg"
      vnet           = "aks-vnet"
      subnet         = "SATVNET" # Cluster was built with small AKS subnet, use larger subnet for user storage endpoints
      aks_name       = "aks"
      subscription   = "aisc-prod-ame-dp-ipp-orange-westus2-01"
      region         = "westus2"
    }
    "prod-southcentralus-hpe-4" = {
      host           = "https://azmlprodscushpe4.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-southcentralus-hpe-4_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS" # user storage traffic is cross-region, cannot use storage appliance
      aks_name       = "aks"
      subscription   = "azml-nexus-hpe-prod-ame-southcentralus-01"
      region         = "southcentralus"
    }
    "prod-eastus2-30" = {
      host           = "https://azmlprodeus2azhub30.api.federation.singularity.azure.net:443"
      resource_group = "azml-prod-eastus2-azhub-30_rg"
      vnet           = "aks-vnet"
      subnet         = "SATVNET" # user storage traffic is cross-region, cannot use storage appliance
      aks_name       = "aks"
      subscription   = "aisc-prod-ame-dp-ipp-orange-eastus2-01"
      region         = "eastus2"
    }
    "stage-southcentralus-2" = {
      host           = "https://azmlstagescusazhub2.api.federation.singularity-stage.azure.net:443"
      resource_group = "azml-stage-southcentralus-azhub-2_rg"
      vnet           = "aks-vnet"
      subnet         = "AKS"
      aks_name       = "aks"
      subscription   = "aisc-stage-ame-dp-ipp-orange-01" # New subscription for stage clusters
      region         = "southcentralus"
    }
    # Add new clusters here when needed
  }

  # Group clusters by subscription for easier data source creation
  # Maps subscription id -> cluster name -> cluster details
  clusters_by_subscription = {
    for sub in keys(local.subscriptions) : sub => {
      for cluster_key, cluster_value in local.clusters : cluster_key => cluster_value
      if cluster_value.subscription == sub
    }
  }
}

