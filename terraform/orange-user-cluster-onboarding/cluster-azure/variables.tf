variable "cluster" {
  type = object({
    aks_name       = string
    resource_group = string
    vnet           = string
    subnet         = string
  })
  description = "Cluster description object containing AKS name, resource group name, VNet name, and subnet name"
}

variable "private_endpoints" {
  description = "Set of private endpoints to create"
  type = map(object({
    cluster            = string
    username           = string
    storage_account_id = string
  }))
}
