resource "azurerm_private_endpoint" "user_storage_endpoints" {
  for_each = var.private_endpoints

  name                = each.key
  location            = data.azurerm_virtual_network.cluster_vnet.location
  resource_group_name = var.cluster.resource_group
  subnet_id           = data.azurerm_subnet.cluster_k8s_subnet.id

  private_service_connection {
    name                           = "conn-${each.value.cluster}-${each.value.username}"
    private_connection_resource_id = each.value.storage_account_id
    subresource_names              = ["blob"]
    is_manual_connection           = true
    request_message                = "Orange terraform endpoint request"
  }

  private_dns_zone_group {
    name                 = "conn-${each.value.cluster}-${each.value.username}"
    private_dns_zone_ids = [data.azurerm_private_dns_zone.storage_blob_private_dns_zone.id]
  }
}
