data "azurerm_kubernetes_cluster" "cluster_aks" {
  name                = var.cluster.aks_name
  resource_group_name = var.cluster.resource_group
}

data "azurerm_virtual_network" "cluster_vnet" {
  name                = var.cluster.vnet
  resource_group_name = var.cluster.resource_group
}

data "azurerm_subnet" "cluster_k8s_subnet" {
  name                 = var.cluster.subnet
  virtual_network_name = var.cluster.vnet
  resource_group_name  = var.cluster.resource_group
}

data "azurerm_private_dns_zone" "storage_blob_private_dns_zone" {
  name                = "privatelink.blob.core.windows.net"
  resource_group_name = var.cluster.resource_group
}
