# BEGIN: Providers for cluster prod-southcentralus-hpe-4

locals {
  prod-southcentralus-hpe-4 = "prod-southcentralus-hpe-4"
}

provider "kubernetes" {
  alias                  = "prod-southcentralus-hpe-4"
  host                   = local.clusters[local.prod-southcentralus-hpe-4].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.cluster_ca_certificate)

  #
  # For client-id/server-id values see the following link:
  # https://learn.microsoft.com/en-us/azure/aks/kubelogin-authentication
  #
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

provider "kubectl" {
  alias                  = "prod-southcentralus-hpe-4"
  host                   = local.clusters[local.prod-southcentralus-hpe-4].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.prod-southcentralus-hpe-4].cluster_aks.kube_config.0.cluster_ca_certificate)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

# BEGIN: prod-southcentralus-hpe-4 onboarding

module "cluster-onboarding-prod-southcentralus-hpe-4" {
  providers = {
    kubernetes = kubernetes.prod-southcentralus-hpe-4
    kubectl    = kubectl.prod-southcentralus-hpe-4
    azurerm    = azurerm.azml-nexus-hpe-prod-ame-southcentralus-01 # Use the subscription-specific provider
  }

  source = "../modules/cluster-onboarding"
  # select all the users that have prod-southcentralus-hpe-4 in their list of clusters
  for_each = { for user, settings in local.users : user => settings if contains(settings.cluster_access, "prod-southcentralus-hpe-4") }
  subscription = {
    id   = var.user_sync_keyvault.subscription_id
    name = var.user_sync_keyvault.subscription_name
  }

  tenant_id = var.user_sync_keyvault.tenant_id
  user = {
    alias                = each.key
    object_id            = each.value.object_id
    auth_identifier      = each.value.auth_identifier
    storage_account_name = each.value.storage_account_name
    team_name            = each.value.team_name

    secret = data.azurerm_key_vault_secret.user-sp-secret[each.key].value
    app = {
      client_id = data.azurerm_key_vault_secret.user-sp-clientid[each.key].value
    }
  }

  cluster = local.prod-southcentralus-hpe-4
}

module "users-brix-cluster-access-prod-southcentralus-hpe-4" {
  providers = {
    kubernetes = kubernetes.prod-southcentralus-hpe-4
    kubectl    = kubectl.prod-southcentralus-hpe-4
  }

  source = "../modules/users-brix-cluster-access"
  users  = local.cluster_users["prod-southcentralus-hpe-4"]
}

module "quota-prod-southcentralus-hpe-4" {
  source           = "../modules/orange-cluster-guest-quota"
  providers = {
    kubernetes = kubernetes.prod-southcentralus-hpe-4
    kubectl = kubectl.prod-southcentralus-hpe-4
  }

  cluster_name     = "prod-southcentralus-hpe-4"
  all_teams        = local.all_teams
}

# Moonfire team admin RBAC for prod-southcentralus-hpe-4
module "moonfire-admin-rbac-prod-southcentralus-hpe-4" {
  providers = {
    kubernetes = kubernetes.prod-southcentralus-hpe-4
  }

  source                     = "../modules/moonfire-admin-rbac"
  moonfire_team_admin_groups = local.cluster_moonfire_admin_groups["prod-southcentralus-hpe-4"]
}

# END: cluster prod-southcentralus-hpe-4 onboarding
