# BEGIN: Providers for cluster stage-southcentralus-hpe-1

locals {
  stage-southcentralus-hpe-1 = "stage-southcentralus-hpe-1"
}

provider "kubernetes" {
  alias                  = "stage-southcentralus-hpe-1"
  host                   = local.clusters[local.stage-southcentralus-hpe-1].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.cluster_ca_certificate)

  #
  # For client-id/server-id values see the following link:
  # https://learn.microsoft.com/en-us/azure/aks/kubelogin-authentication
  #
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

provider "kubectl" {
  alias                  = "stage-southcentralus-hpe-1"
  host                   = local.clusters[local.stage-southcentralus-hpe-1].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.stage-southcentralus-hpe-1].cluster_aks.kube_config.0.cluster_ca_certificate)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

# BEGIN: stage-southcentralus-hpe-1 onboarding

module "cluster-onboarding-stage-southcentralus-hpe-1" {
  providers = {
    kubernetes = kubernetes.stage-southcentralus-hpe-1
    kubectl    = kubectl.stage-southcentralus-hpe-1
    azurerm    = azurerm.azml-nexus-hpe-prod-ame-southcentralus-01 # Use the subscription-specific provider
  }

  source = "../modules/cluster-onboarding"
  # select all the users that have stage-southcentralus-hpe-1 in their list of clusters
  for_each = { for user, settings in local.users : user => settings if contains(settings.cluster_access, "stage-southcentralus-hpe-1") }
  subscription = {
    id   = var.user_sync_keyvault.subscription_id
    name = var.user_sync_keyvault.subscription_name
  }

  tenant_id = var.user_sync_keyvault.tenant_id
  user = {
    alias                = each.key
    object_id            = each.value.object_id
    storage_account_name = each.value.storage_account_name
    team_name            = each.value.team_name
    auth_identifier      = each.value.auth_identifier

    secret = data.azurerm_key_vault_secret.user-sp-secret[each.key].value
    app = {
      client_id = data.azurerm_key_vault_secret.user-sp-clientid[each.key].value
    }
  }

  cluster = local.stage-southcentralus-hpe-1
}

module "users-brix-cluster-access-stage-southcentralus-hpe-1" {
  providers = {
    kubernetes = kubernetes.stage-southcentralus-hpe-1
    kubectl    = kubectl.stage-southcentralus-hpe-1
  }
  source = "../modules/users-brix-cluster-access"
  users  = local.cluster_users["stage-southcentralus-hpe-1"]
}

module "quota-stage-southcentralus-hpe-1" {
  source           = "../modules/orange-cluster-guest-quota"
  providers = {
    kubernetes = kubernetes.stage-southcentralus-hpe-1
    kubectl = kubectl.stage-southcentralus-hpe-1
  }

  cluster_name     = "stage-southcentralus-hpe-1"
  all_teams        = local.all_teams
}

# Moonfire team admin RBAC for stage-southcentralus-hpe-1
module "moonfire-admin-rbac-stage-southcentralus-hpe-1" {
  providers = {
    kubernetes = kubernetes.stage-southcentralus-hpe-1
  }

  source                     = "../modules/moonfire-admin-rbac"
  moonfire_team_admin_groups = local.cluster_moonfire_admin_groups["stage-southcentralus-hpe-1"]
}

# END: cluster stage-southcentralus-hpe-1 onboarding
