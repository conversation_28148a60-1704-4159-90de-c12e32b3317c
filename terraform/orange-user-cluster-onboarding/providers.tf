terraform {

  required_providers {
    azuread = "~> 3.0.2"
    azurerm = "4.19.0"

    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }

    kubernetes = {
      version = "2.31.0"
    }

    kubectl = {
      source  = "alekc/kubectl"
      version = ">= 2.0.4"
    }
  }
}

# default infra provider, unused but azurerm requires one
provider "azurerm" {
  resource_provider_registrations = "none"
  storage_use_azuread             = true
  subscription_id                 = "64467a16-0cdd-4a44-ad9b-83b5540828ac" # GenAI Dev AME

  features {}
}

provider "azurerm" {
  alias                = "user-secret-reader-sp"
  client_id            = var.user_app_sync_sp_client_id
  tenant_id            = var.user_sync_keyvault.tenant_id
  use_oidc             = true
  oidc_token_file_path = var.oidc_token_file

  resource_provider_registrations = "none"
  subscription_id                 = var.user_sync_keyvault.subscription_id
  features {
  }
}

# CLUSTER AME SUBSCRIPTION PROVIDERS

provider "azurerm" {
  alias               = "aisc-prod-ame-dp-ipp-orange-uksouth-01"
  storage_use_azuread = true
  subscription_id     = "ab0dad62-9fe9-4302-89f4-0264610c7447" # aisc-prod-ame-dp-ipp-orange-uksouth-01

  resource_provider_registrations = "none"
  features {}
}

provider "azurerm" {
  alias               = "azml-nexus-hpe-prod-ame-southcentralus-01"
  storage_use_azuread = true
  subscription_id     = "61a4c7c0-68e0-4675-a760-d7feed155495" # azml-nexus-hpe-prod-ame-southcentralus-01

  resource_provider_registrations = "none"
  features {}
}

provider "azurerm" {
  alias               = "aisc-prod-ame-dp-ipp-orange-westus2-01"
  storage_use_azuread = true
  subscription_id     = "b82dbd21-ecad-49df-a5b4-9554082bc3a1" # aisc-prod-ame-dp-ipp-orange-westus2-01

  resource_provider_registrations = "none"
  features {}
}

provider "azurerm" {
  alias               = "aisc-prod-ame-dp-ipp-orange-eastus2-01"
  storage_use_azuread = true
  subscription_id     = "1b830cc3-4aaa-498f-a5d1-686346a70afe" # aisc-prod-ame-dp-ipp-orange-eastus2-01

  resource_provider_registrations = "none"
  features {}
}

provider "azurerm" {
  alias               = "aisc-stage-ame-dp-ipp-orange-01"
  storage_use_azuread = true
  subscription_id     = "4acd2d20-f2c0-4b5b-834c-e10aef6c2443" # aisc-stage-ame-dp-ipp-orange-01

  resource_provider_registrations = "none"
  features {}
}
# Add new subscriptions here when needed
