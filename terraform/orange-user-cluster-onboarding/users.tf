locals { /*
    Expected JSON structure (file specified by var.team-quota-file-location):
    {
      "users": {
        "value": {
          "<username>": {
            "teams":        List(String) -- list of teams for the user e.g. ["team-moonfire-genai", "team-moonfire-dri"]
            "cluster_access": List(String) -- list of clusters the user can access. e.g. ["prod-uksouth-7", "prod-uksouth-8"]
            "storage_access": List(String) -- list of storage systems the user can access. e.g. ["base", "cresco"]
            "is_robot":     Boolean (optional) -- true if this is a robot/service account user
          },
          ...
        }
      },
      "team_allocations": {
        "value": {
          "cluster-name": [
            {
              "team": "Orange users",
              "allocations": [
                {
                  "subteam": "team-name",
                  "count": number,
                  "priority": number,
                  "resourceCount": number,
                  "selector": string
                }
              ]
            }
          ]
        }
      },
      "moonfire_admin_groups_for_clusters": {
        "value": {
          "TEAM-MOONFIRE-M365-ADMIN": "********-1234-1234-1234-********9012",
          "TEAM-MOONFIRE-GENAICORE-ADMIN": "********-4321-4321-4321-************",
          ...
        }
      },
      "moonfire_team_admin_cluster_access": {
        "value": {
          "TEAM-MOONFIRE-M365-ADMIN": ["prod-uksouth-7", "prod-westus2-19"],
          "TEAM-MOONFIRE-GENAICORE-ADMIN": ["prod-uksouth-7", "prod-uksouth-8"],
          ...
        }
      }
    }

    Admin groups are hardcoded in orange-team-onboarding and enable team administrators 
    to manage their teams' Brix jobs in specific clusters.
  */
  team_quota_json = jsondecode(file(var.team-quota-file-location))
  users = {
    for username, user in local.team_quota_json.users.value : username => {
      team_name = try(
        length(user.teams) > 0 ? sort(user.teams)[0] : local.default_team,
        local.default_team
      )
      cluster_access       = user.cluster_access,
      storage_access       = user.storage_access,
      is_robot             = user.is_robot,
      storage_account_name = user.storage_account_name,
      object_id            = user.object_id,
      # Use object_id for robots, email for humans
      auth_identifier = user.is_robot ? user.object_id : "${username}@green.microsoft.com"
    }
  }
  cluster_users = {
    for cluster in keys(local.clusters) : cluster => {
      for username, user in local.users : username => user
      if contains(user.cluster_access, cluster)
    }
  }

  default_team     = "iridium-pilot"
  team_allocations = try(local.team_quota_json.team_allocations.value, {})
  # team-bus is the pseudo-team used by Bus Shooter: https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9291/Autoscaling-Bus-Shooter-on-Idle-GPUs
  # We want a Quota resource for it to let Perhonen report the capacity usage.
  all_teams        = distinct(concat(flatten([local.team_quota_json.teams_with_quotas.value, local.team_quota_json.teams_without_quotas.value]), ["team-bus"]))

  # Admin groups data from orange-user-onboarding
  moonfire_admin_groups_for_clusters = try(local.team_quota_json.moonfire_admin_groups_for_clusters.value, {})
  moonfire_team_admin_cluster_access = try(local.team_quota_json.moonfire_team_admin_cluster_access.value, {})

  # Map admin groups to clusters they have access to
  cluster_moonfire_admin_groups = {
    for cluster in keys(local.clusters) : cluster => {
      for admin_group_name, object_id in local.moonfire_admin_groups_for_clusters :
      admin_group_name => object_id
      if contains(try(local.moonfire_team_admin_cluster_access[admin_group_name], []), cluster)
    }
  }
}
