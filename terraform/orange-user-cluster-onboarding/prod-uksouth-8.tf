# BEGIN: Providers for cluster prod-uksouth-8

locals {
  prod-uksouth-8 = "prod-uksouth-8"
}

provider "kubernetes" {
  alias                  = "prod-uksouth-8"
  host                   = local.clusters[local.prod-uksouth-8].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.cluster_ca_certificate)

  #
  # For client-id/server-id values see the following link:
  # https://learn.microsoft.com/en-us/azure/aks/kubelogin-authentication
  #
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

provider "kubectl" {
  alias                  = "prod-uksouth-8"
  host                   = local.clusters[local.prod-uksouth-8].host
  tls_server_name        = trimsuffix(trimprefix(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(local.clusters_data[local.prod-uksouth-8].cluster_aks.kube_config.0.cluster_ca_certificate)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

# BEGIN: prod-uksouth-8 onboarding

module "cluster-onboarding-prod-uksouth-8" {
  providers = {
    kubernetes = kubernetes.prod-uksouth-8
    kubectl    = kubectl.prod-uksouth-8
    azurerm    = azurerm.aisc-prod-ame-dp-ipp-orange-uksouth-01
  }

  source = "../modules/cluster-onboarding"
  # select all the users that have prod-uksouth-8 in their list of clusters
  for_each = { for user, settings in local.users : user => settings if contains(settings.cluster_access, "prod-uksouth-8") }
  subscription = {
    id   = var.user_sync_keyvault.subscription_id
    name = var.user_sync_keyvault.subscription_name
  }

  tenant_id = var.user_sync_keyvault.tenant_id

  user = {
    alias                = each.key
    object_id            = each.value.object_id
    storage_account_name = each.value.storage_account_name
    auth_identifier      = each.value.auth_identifier
    team_name            = each.value.team_name

    secret = data.azurerm_key_vault_secret.user-sp-secret[each.key].value
    app = {
      client_id = data.azurerm_key_vault_secret.user-sp-clientid[each.key].value
    }
  }

  cluster = local.prod-uksouth-8
}

module "users-brix-cluster-access-prod-uksouth-8" {
  providers = {
    kubernetes = kubernetes.prod-uksouth-8
    kubectl    = kubectl.prod-uksouth-8
  }
  source = "../modules/users-brix-cluster-access"
  users  = local.cluster_users["prod-uksouth-8"]
}

module "quota-prod-uksouth-8" {
  source = "../modules/orange-cluster-guest-quota"
  providers = {
    kubernetes = kubernetes.prod-uksouth-8
    kubectl = kubectl.prod-uksouth-8
  }

  cluster_name     = "prod-uksouth-8"
  all_teams        = local.all_teams
}

# Moonfire team admin RBAC for prod-uksouth-8
module "moonfire-admin-rbac-prod-uksouth-8" {
  providers = {
    kubernetes = kubernetes.prod-uksouth-8
  }

  source                     = "../modules/moonfire-admin-rbac"
  moonfire_team_admin_groups = local.cluster_moonfire_admin_groups["prod-uksouth-8"]
}

# END: cluster prod-uksouth-8 onboarding

# END: Providers for cluster prod-uksouth-8
