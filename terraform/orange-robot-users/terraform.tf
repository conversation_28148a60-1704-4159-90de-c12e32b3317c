terraform {
  backend "azurerm" {
    resource_group_name  = "orange-terraform"
    storage_account_name = "orangetfstate"
    container_name       = "tfstate"
    subscription_id      = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
    use_azuread_auth     = true
    key                  = "orange-robot-users"
  }

  required_providers {
    azurerm = "4.19.0"
    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
  }
}

# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
  storage_use_azuread             = true
  resource_provider_registrations = "none"
  features {}
}

provider "azapi" {
  subscription_id = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
}


