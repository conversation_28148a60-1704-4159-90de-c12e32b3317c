# Robot Users

Alongside human users, we create a few 'robot' users for purposes such as E2E testing. Robot users are supposed to be nearly the same as human users, but they happen to be MSI identities. They connect to clusters and storage over Tailscale, are onboarded to particular clusters by the same user onboarding pipeline, etc.

This directory handles some preliminary setup for these robot users:

- Creating an MSI
- Creating an associated 1ES build pool to run pipelines as the robot
- Arranging for the MSI to be able to connect to Tailscale, etc.
  (but most other access comes from the normal user onboarding process)

One wrinkle for robot users (currently) is that they must use a different oidc endpoint (`oidc-oid`) which passes through the `oid` rather than `upn` (email alias; they don't have one). This also results in some special handling in the user onboarding pipeline, to generate role bindings against those `oid`s.