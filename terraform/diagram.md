// Generated with Copilot :-)

:::mermaid
flowchart TB
    %% Main modules
    root[bootstrapping/terraform]
    
    %% Core infrastructure modules
    clusters[clusters/*]
    clusters_green[clusters-green-ad-ops/*]
    modules[modules/*]
    monitoring[monitoring/*]
    
    %% Orange services modules
    orange_builder[orange-builder]
    orange_infra_apps[orange-infra-apps]
    orange_observability[orange-observability]
    orange_storage[orange-storage-bootstrapping]
    
    %% User management modules
    orange_user_cluster[orange-user-cluster-onboarding]
    orange_user_onboarding[orange-user-onboarding]
    orange_team_onboarding[orange-team-onboarding]
    orange_users_membership[orange-users-membership]
    orange_users_vnet[orange-users-vnet]
    orange_robot_users[orange-robot-users]
    
    %% Core module relationships
    root --> clusters
    root --> clusters_green
    root --> modules
    root --> monitoring
    
    %% Shared modules relationships
    modules --> global_settings[global_settings]
    modules --> orange_builder_access[orange-builder-access]
    modules --> storage_base[storage-base]
    modules --> orange_remote_state[orange-remote-state]
    modules --> orange_user_team_quota[orange-user-team-quota]
    modules --> brix[brix]
    modules --> stage_conn[stage-conn]
    
    %% Orange services relationships
    root --> orange_builder
    root --> orange_infra_apps
    root --> orange_observability
    root --> orange_storage
    
    %% User management relationships
    root --> orange_user_cluster
    root --> orange_user_onboarding
    root --> orange_team_onboarding
    root --> orange_users_membership
    root --> orange_users_vnet
    root --> orange_robot_users
    
    %% Key dependencies
    orange_user_onboarding --> orange_remote_state
    orange_team_onboarding --> orange_user_team_quota
    orange_users_membership --> orange_remote_state
    orange_infra_apps --> orange_builder_access
    orange_infra_apps --> global_settings
    orange_observability --> global_settings
    orange_storage --> storage_base
    orange_user_cluster --> orange_remote_state
    
    %% Special relationships
    orange_user_cluster -.-> clusters
    clusters_green -.-> clusters
    
    %% Resource types
    classDef infra fill:#f96,stroke:#333
    classDef user fill:#9cf,stroke:#333
    classDef shared fill:#fcf,stroke:#333
    
    class clusters,clusters_green,orange_builder,orange_infra_apps,orange_observability,orange_storage infra
    class orange_user_cluster,orange_user_onboarding,orange_team_onboarding,orange_users_membership,orange_users_vnet,orange_robot_users user
    class modules,global_settings,orange_builder_access,storage_base,orange_remote_state,orange_user_team_quota,brix,stage_conn shared
:::    