# Orange Infra Apps Module

This module creates service principals for infra-apps to use for authenticatation with azure blob storage to save required data.

We follow a similar bootstrapping flow as user-apps, we create an `infra-app` for app registration, `infra-sp` & `infra-password` and store them in `orange-infra-sp-sync-kv` to be read by dual-homed service principal and ingested into the infra app K8s namespace.

### The changes include:

1. `orange-infra-apps`  to define the infra apps and create key vault to store client_id/password, and give required access to dual-home SP.
2. `orange-infra-app` module that does app reg, creates SP & Password and stores them in Key Vault and gives
3. Pipeline to setup infra-apps in `terraform/orange-infra-apps/terraform.tfvars`  with `orange-builder` and terraform.

### Why separate orange-infra-app module?
We decided to have a separate `orange-infra-app` module and not reuse the existing `orange-user-app` to decouple the logic that is user specific e.g. (User email, Groups for User identity & SP)

### Are we going to reimplement user-cluster-onboarding for infra?
No, we will defer the responsbility of reading client_id/password, creating namespace/config map/etc.. to each app to do its required unique setup and pull from green using the dual-homed SP. The reasons are:

1. Infra apps differ in the required access and functionality (i.e. some require access to storage account others don't)
2. Infra apps might go into one shared namespace, `scaling` ns in case of OAI, or separate namespaces
3. Infra don't need same brix access, team-config maps, secrets that are setup in the user-cluster-onboarding module

### Are we going to have a separate infra storage account or reuse the existing regional one e.g. `orangeukscresco`?
We decided to create new infra-specific storage accounts, to limit the attack surface for our regional storage accounts as they contain models/data.

### Should we have a single SP for all infra-apps or per single app and should we have it per cluster?
We create a single SP per infra app, to limit access. A next step is to explore if we would have a SP per app per tent to limit access to tented resources.

### Should the infra resource group be regional?
No, since we only create a Key Vault/SP per app, and we don't create separate storage accounts for infra-apps.

