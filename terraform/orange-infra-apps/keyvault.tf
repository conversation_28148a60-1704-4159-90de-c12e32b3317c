# We store the secrets for the per-infra-app SPs into a keyvault.
# An AME-side pipeline stage will copy these into k8s secrets.

resource "azurerm_key_vault" "orange-infra-sp-sync-kv" {
  name                      = "orange-infra-sp-sync-kv"
  resource_group_name       = azurerm_resource_group.orange-infra-global.name
  location                  = "uksouth"
  enable_rbac_authorization = true

  sku_name  = "standard"
  tenant_id = data.azurerm_client_config.current.tenant_id

  soft_delete_retention_days    = 90
  purge_protection_enabled      = true
  public_network_access_enabled = false
}

module "orange_infra_sp_sync_kv_secmon_audit" {
  source = "../modules/aoai-secmon-audit"
  providers = {
    azurerm              = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  target_resource_id = azurerm_key_vault.orange-infra-sp-sync-kv.id
}

# AME side needs to read the secrets
resource "azurerm_role_assignment" "orange-infra-sp-sync-keyvault-secret-user" {
  principal_id         = var.orange-infra-sp-sync.object_id
  role_definition_name = "Key Vault Secrets User"
  scope                = azurerm_key_vault.orange-infra-sp-sync-kv.id
}

# AME side also needs metadata access for some reason
resource "azurerm_role_assignment" "orange-infra-sp-sync-keyvault-reader" {
  principal_id         = var.orange-infra-sp-sync.object_id
  role_definition_name = "Key Vault Reader"
  scope                = azurerm_key_vault.orange-infra-sp-sync-kv.id
}

# Create a PE from the builder to the keyvault and secrets reader
module "builder-access" {
  source = "../modules/orange-builder-access"

  name             = azurerm_key_vault.orange-infra-sp-sync-kv.name
  dns_zone         = module.global.private_dns_zones["vault"]
  resource_id      = azurerm_key_vault.orange-infra-sp-sync-kv.id
  role_assignments = ["Key Vault Secrets Officer"]
}
