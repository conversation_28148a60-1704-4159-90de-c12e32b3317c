resource "azurerm_resource_group" "orange-infra-resources" {
  name     = var.rg_name
  location = var.region
}

# Get builder msi object id from global settings module
module "global" {
  source = "../../modules/global_settings"
}

# Adding builder role assignment to the resource group
resource "azurerm_role_assignment" "builder-role-assignment" {
  principal_id         = module.global.builder.identity.principal_id
  role_definition_name = "Contributor"
  scope                = azurerm_resource_group.orange-infra-resources.id
}

# Create a regional account for infra
module "regional-storage" {
  providers = {
    azurerm.infra        = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
  }
  source = "../../modules/storage-base"

  name                = var.storage_account_name # eg, ornginfrauks
  resource_group_name = azurerm_resource_group.orange-infra-resources.name
  location            = azurerm_resource_group.orange-infra-resources.location

  builder_access   = true
  role_assignments = var.role_assignments

  tags = {
    "purpose"         = "infra"
    "oai-sensitivity" = "critical"
  }
}

# Create kubecache storage container in each regional storage account
resource "azurerm_storage_container" "kubecache_store" {
  # Only create if kubecache is in the apps list
  count                 = contains(var.apps, "scaling-infra") ? 1 : 0
  name                  = "kubecache-store"
  storage_account_id    = module.regional-storage.storage_account.id
  container_access_type = "private"

  depends_on = [
    module.regional-storage
  ]
}
