variable "region" {
  description = "Azure region where resources should be created"
  type        = string
}

variable "rg_name" {
  description = "Name of the resource group to create"
  type        = string
}

variable "region_abbrev" {
  description = "Short name for the Azure region"
  type        = string
}

variable "storage_account_name" {
  description = "Name of the regional storage account to create"
  type        = string
}

variable "role_assignments" {
  type        = map(set(string))
  description = "role assignment definition name as the key and a set of user/service principal object ids"
  default     = {}
}

variable "apps" {
  type        = set(string)
  description = "List of infr apps"
}
