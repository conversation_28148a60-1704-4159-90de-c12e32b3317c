variable "onboarding-sp" {
  type = object({
    client_id                    = string
    tenant_id                    = string
    keyvault_name                = string
    keyvault_resource_group_name = string
    certificate_name             = string
  })
}

variable "apps" {
  type = set(string)
}

variable "orange-infra-sp-sync" {
  type = object({
    client_id = string
    object_id = string
  })
}

variable "builder_msi_principal_id" {
  type        = string
  description = "The principal ID of the managed identity running the automation."
}
