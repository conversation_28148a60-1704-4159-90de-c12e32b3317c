module "infra-app" {
  providers = {
    azurerm = azurerm
    azuread = azuread.onboarding-sp
  }
  source   = "../modules/orange-infra-app"
  for_each = var.apps

  infra_alias  = each.key
  key_vault_id = azurerm_key_vault.orange-infra-sp-sync-kv.id

  # Force creation AFTER KeyVault + roles are assigned:
  depends_on = [
    module.builder-access,
    azurerm_key_vault.orange-infra-sp-sync-kv,
    azurerm_role_assignment.orange-infra-sp-sync-keyvault-secret-user,
    azurerm_role_assignment.orange-infra-sp-sync-keyvault-reader,
  ]
}
