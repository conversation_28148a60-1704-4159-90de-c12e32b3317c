terraform {

  required_providers {
    azuread = "~> 3.0.2"
    azurerm = "4.19.0"
  }
}

data "azurerm_key_vault" "onboarding-sp-keyvault" {
  name                = var.onboarding-sp.keyvault_name
  resource_group_name = var.onboarding-sp.keyvault_resource_group_name
}

data "azurerm_key_vault_secret" "onboarding-sp-cert" {
  name         = var.onboarding-sp.certificate_name
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}

provider "azuread" {
  alias              = "onboarding-sp"
  client_id          = var.onboarding-sp.client_id
  tenant_id          = var.onboarding-sp.tenant_id
  client_certificate = data.azurerm_key_vault_secret.onboarding-sp-cert.value
}

# default infra provider
provider "azurerm" {
  storage_use_azuread             = true
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA https://microsoftservicetree.com/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a
  resource_provider_registrations = "none"

  features {
    storage {
      data_plane_available = false
    }
  }
}

provider "azurerm" {
  alias             = "auditing-sub"
  subscription_id   = "f91c4126-3a46-4ddc-b984-6e9e4c1625ee" # AIPLATFORM-MUMFORD-SECMON-GREEN-DEV
  resource_provider_registrations = "none"

  features {}
}

data "azurerm_client_config" "current" {}
