apps = [
  "scaling-infra", # Single app for infra apps in scaling namespace
  "joiner",
  "lemon-infra",
]

# This is the service principal that has the extra permissions to
# create apps/serivce principals for users.
onboarding-sp = {
  client_id                    = "17eb5cd6-98da-4e7a-bab6-1adefcafa5de"
  tenant_id                    = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green tenant
  keyvault_name                = "orange-onboarding-app-kv"
  keyvault_resource_group_name = "orange-onboarding"
  certificate_name             = "orange-onboarding-app-auth"
}

# This is the multi-tenant app (homed in AME) used for syncing user SP secrets into clusters.
# We give its Green tenant service principal access to a keyvault.
orange-infra-sp-sync = {
  client_id = "79f57bc6-dd99-4067-b1f1-f6abb241be4b"
  # SP OID in Green tenant
  object_id = "6a76b261-89e4-4280-a599-695e6b22e6d5"
}

builder_msi_principal_id = "82cb6bbd-45fc-4139-9980-bf074453ab80"
