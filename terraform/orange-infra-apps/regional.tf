# Create infra resources for each region
module "regional" {
  providers = {
    azurerm              = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
  }
  source   = "./regional"
  for_each = local.regions

  region               = each.key
  region_abbrev        = each.value.region_abbrev
  rg_name              = each.value.resource_group_name
  storage_account_name = each.value.storage_account_name
  apps                 = var.apps

  role_assignments = {
    "Storage Blob Data Contributor" = [
      for app in var.apps : module.infra-app[app].app.object_id
      if app != "joiner" && app != "lemon-infra" # Filter out apps that don't need storage
    ]
  }
}

