# terraform

The `terraform` directory contains Terraform configurations for the genai infra team clusters

## Terraform Clusters

Clusters are defined under `clusters/`. This is the root terraform module that defines the cluster components.

To add / test changes follow the following routine:

1. `terraform plan` in relevant cluster directory.
2. If needed, inspect the output to figure out which targets you want to focus changes on. `terraform plan -target=<target name>` to see the relevant subset of `terraform plan` changes.
3. `terraform apply` the changes.

## Monitoring

We use a an Azure monitoring workspace per regions, and hence its state is not tied to a cluster. 

Each region has a separate monitoring config under `terraform/monitoring`. The module deployed a managed grafana instance, Azure log analytics, and an Azure monitor workspace for managed prometheus.

## Shared modules

Shared modules are under `terraform/modules`, these should be generic modules that can be referenced from any cluster. 

## Terraform state

The terraform state for each cluster in stored in azure blob storage. The key is defined in each cluster's `terraform.tf` each cluster MUST have a unique key.

## New cluster buildout steps

### AME Tenant: Apply `clusters/common` once
This step will build out common components such as TailScale and its exit nodes. All clusters share the same TailScale exit nodes.

### Green Tenant: Apply `clusters-green-ad-ops/<cluster>`
This step creates green tenant objects needed for OIDC based authentication. Once done, grab the OIDC application ID for next step.

### AME Tenant: Apply `clusters/<clusters>`
This steps builds out the cluster. It will prompt you for the OIDC application ID from previous step.

## Developer Setup
- Install `terraform` version `1.1.3`:

## Detailed Guide for Adding New Clusters
For a comprehensive, step-by-step guide on adding new clusters, including network planning and OIDC configuration, please see [Adding New Clusters Guide](clusters/README.md).

### Linux

```
curl -L https://raw.githubusercontent.com/warrensbox/terraform-switcher/release/install.sh | sudo bash
tfswitch 1.1.3
```
### Mac

```
brew install warrensbox/tap/tfswitch
tfswitch 1.1.3
```

### VS Code extenstion

Install the following extensions:
1. [Terraform](https://marketplace.visualstudio.com/items?itemName=4ops.terraform)
2. [Azure Terraform](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureterraform)

### Learning

- https://developer.hashicorp.com/terraform/intro
- https://developer.hashicorp.com/terraform/tutorials/azure-get-started

### End to end example:

Lets first create a test cluster:
1. Navigate to [test-cluster](lusters/test-cluster)
2. Run `terraform init` to initalize modules
3. Run `terraform plan` to create the state plan.
4. Inspect the output to get yourself fimiliar with how the plan looks like, what it plans to add, change, and remove.
5. When ready run `terraform apply` which will run plan and prompt you for approval, at which point you should enter `yes`. If you are very confident with the plan you can run `terraform apply -auto-approve` to skip the manual approval step.
6. Now that your cluster has been created, let do a modification to it, under [main.tf](clusters/test-cluster/main.tf) change `enable_volcano` to `false`, then start from step #3. This should remove the volcano resources from the cluster.

Other changes, like changing the cluster location will be more distruptive, since it requires deleting the current cluster and recreating it in another region.

NOTE: The test-cluster module is a local only module, i.e. we don't store it state in azure since it is for testing purposes only.

## Usage with MSI
To use terraform with MSI you need to set the following environment variables:

```
ARM_CLIENT_ID=<the user assigned identity client id>
ARM_TENANT_ID=33e01921-4d64-4f8c-a055-5bdaffd5e33d # The AME tenant ID
ARM_USE_MSI=true
ARM_USE_AZUREAD=true
ARM_STORAGE_USE_AZUREAD=true
```

## Iridium setup
1. You need to have tailscale up and running on your machine in order for terraform to be able to talk to the cluster.
2. Currently the MSI identities do not have access to the AAD graph API, until we resolve this issue, the object Ids for groups and users must be hardcoded in the terraform where needed.

## Identities

### User onboarding service principal

**Application ID**: `581ce198-4fcd-4d9d-95e3-abd5d7580bd0`
**Application Name**: `iridium-onboarding`

We have a special service principal that is used to onboard users to the cluster. This service principal is created in the AME tenant and has the following permissions:

- `User.Read.All` - Read all users' full profiles
- `GroupMember.ReadWrite.All` - Read and write all group memberships
- `Application.ReadWrite.OwnedBy` - Read and write applications that the app owns

This service principal is used to create an application per user in the AME tenant, and generate a certificate for the user to use to authenticate to the cluster.

The process to create an applications is a 2 step process:
1- Manually create the application in the AME tenant.
2- Open a PR to get the application granted the necessary permissions. https://msazure.visualstudio.com/One/_git/TenantSec-IsoEng-CTAC/pullrequest/11259737

Required RBAC permissions:
| Role                         | Scope                                                                                                              | Description                                          |
|------------------------------|--------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|
| Key Vault Certificates Officer | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-onboarding/providers/Microsoft.KeyVault/vaults/iridium-user-onboarding` | To generate certificates for the applications.      |
| Key Vault Certificate User        | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-onboarding/providers/Microsoft.KeyVault/vaults/iridium-user-onboarding` | To read the certificate and set it on the application. |


### iridium-builder msi

**MSI ID**: `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourcegroups/iridium-onboarding/providers/Microsoft.ManagedIdentity/userAssignedIdentities/iridium-onboarding-mi`

This is the MSI that is used on the 1ES agent pool [iridium-builder](https://dev.azure.com/project-argos/Mimco/_settings/agentqueues?queueId=860) to run the automation pipelines. This MSI has the following permissions:

| Role                                      | Scope                                                                                                    | Description                                              |
|-------------------------------------------|----------------------------------------------------------------------------------------------------------|----------------------------------------------------------|
| Contributor                               | Subscription-wide                                                                                       | Grants full access to manage all resources within the subscription to create and managed resources |
| Role Based Access Control Administrator   | Subscription-wide                                                                                       | Manages access control for resources within the subscription. |
| Storage Blob Data Contributor             | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-terraform/providers/Microsoft.Storage/storageAccounts/iridiumtfstate`                                                                      | To manage the terraform state. |
| Key Vault Certificate User                    | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-onboarding/providers/Microsoft.KeyVault/vaults/iridium-onboarding-app` | Allows to read the onboarding service principal (iridium-onbaording) certificate so that terraform can use the SP to execute certain operations related to managing user applications. |
| Storage Blob Data Contributor             | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-resources/providers/Microsoft.Storage/storageAccounts/iridiumoaiartifactsame` account                                                                             | To manage the gloval storage map. |
| Azure Kubernetes Service RBAC Cluster Admin | iridium clusters                                                              | Provides full admin access to manage AKS clusters to manage namespaces / configmaps / RBACs etc.     |
