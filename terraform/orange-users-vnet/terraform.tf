terraform {

  backend "azurerm" {
    resource_group_name  = "orange-terraform"
    storage_account_name = "orangetfstate"
    container_name       = "tfstate"
    key                  = "users-vnet"
    subscription_id      = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
    use_azuread_auth     = true
  }

  required_providers {
    azuread = "~> 2.37.0"
    azurerm = "4.19.0"
  }
}

# PROVIDER SETUP

provider "azurerm" {
  storage_use_azuread             = true
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA https://microsoftservicetree.com/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a
  resource_provider_registrations = "none"

  features {
    storage {
      data_plane_available = false
    }
  }
}

provider "azurerm" {
  alias               = "oai-assets"
  subscription_id     = "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e" # AIPLATFORM-ORANGE-OAI-ASSESTS
  storage_use_azuread = true
  features {
    storage {
      data_plane_available = true
    }
  }
}

provider "azurerm" {
  alias               = "users"
  subscription_id     = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS
  storage_use_azuread = true
  features {
    storage {
      data_plane_available = true
    }
  }
}
