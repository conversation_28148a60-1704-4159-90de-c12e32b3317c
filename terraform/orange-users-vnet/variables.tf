variable "user_storage_resource_group_name" {
  description = "Resource group name for user storage accounts."
  type        = string
  default     = "orange-users-resources"
}

variable "vnet_range" {
  description = "Address ranges for the user resources vnet."
  type        = list(string)
  default     = [
    "***********/22",
    "***********/27",
    "***********/24",
  ]
}

variable "tailscale_subnet_range" {
  description = "Address ranges for the tailscale router subnet."
  type        = list(string)
  default     = ["***********/27"]
}

variable "dns_in_range" {
  description = "Address ranges for the DNS resolver subnet."
  type        = list(string)
  default     = ["***********/27"]
}

variable "dns_in_ip" {
  description = "Address for the private DNS resolver."
  type        = string
  default     = "************"
}


variable "dns_out_range" {
  description = "Address ranges for the tailscale router subnet."
  type        = list(string)
  default     = ["***********/24"]
}


variable "resources_subnet_range" {
  description = "Address ranges for the resources subnet."
  type        = list(string)
  default = [ # "************/22", skip first 32 for subnet router
    "************/27",
    "************/26",
    "*************/25",
    "***********/24",
    "***********/23",
  ]
}

variable "cluster_oidc_oid_ips" {
  description = "IP addresses of cluster oidc-proxy services (the oidc-oid one) (Look in kube-oidc-proxy-oid namespace)"
  type        = map(string)
  default = {
    "prod-uksouth-7"             = "************"
    "prod-uksouth-8"             = "**********"
    "prod-uksouth-9"             = "***********"
    "prod-uksouth-15"            = "************"
    "stage-southcentralus-hpe-1" = "***********"
    "prod-southcentralus-hpe-2"  = "************"
    "prod-southcentralus-hpe-3"  = "************"
    "prod-southcentralus-hpe-5"  = "***********"
    "prod-westus2-19"            = "***********"
    "prod-southcentralus-hpe-4"  = "***********"
    "prod-eastus2-30"            = "***********"
  }
}

variable "team-quota-file-location" {
  type        = string
  description = "Location of the user file."
}
