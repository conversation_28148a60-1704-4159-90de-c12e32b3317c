data "azurerm_key_vault" "tailscale" {
  name                = "orangetailscalekeys"
  resource_group_name = "orange-tailscale"
}

module "tailscale" {
  source = "../modules/tailscale-subnet-router"

  resource_group_name       = azurerm_resource_group.orange_users_rg.name
  location                  = azurerm_resource_group.orange_users_rg.location
  subnet_id                 = azurerm_subnet.tailscale_subnet.id
  subnet-router-name-prefix = "orange-users-access"
  tags                      = ["orange"]
  # "************/22", but skip first 32 for subnet router
  advertise_subnets = concat(azurerm_subnet.resources_subnet.address_prefixes, ["${var.dns_in_ip}/32"])
  secret_name       = "tailscale"
  keyvault_id       = data.azurerm_key_vault.tailscale.id

  advertise_azure_dns = false


  tailsnail_rate_mbit = 2
}

