# Private DNS Resolver

module "orange-clusters-config" {
  source = "../modules/orange-clusters-config"
}

resource "azurerm_private_dns_resolver" "dns_resolver" {
  name                = "orange-dns-resolver"
  virtual_network_id  = azurerm_virtual_network.vnet.id
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name
}

resource "azurerm_private_dns_resolver_inbound_endpoint" "dns_resolver_inbound" {
  name                    = "inbound-endpoint"
  location                = azurerm_resource_group.orange_users_rg.location
  private_dns_resolver_id = azurerm_private_dns_resolver.dns_resolver.id

  ip_configurations {
    subnet_id                    = azurerm_subnet.dns_in_subnet.id
    private_ip_address           = var.dns_in_ip
    private_ip_allocation_method = "Static"
  }
}

resource "azurerm_private_dns_resolver_outbound_endpoint" "dns_resolver_outbound" {
  name                    = "outbound-endpoint"
  location                = azurerm_resource_group.orange_users_rg.location
  private_dns_resolver_id = azurerm_private_dns_resolver.dns_resolver.id
  subnet_id               = azurerm_subnet.dns_out_subnet.id
}

resource "azurerm_private_dns_resolver_dns_forwarding_ruleset" "forwarding_ruleset" {
  name                = "orange-forwarding-ruleset"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name

  private_dns_resolver_outbound_endpoint_ids = [
    azurerm_private_dns_resolver_outbound_endpoint.dns_resolver_outbound.id
  ]
}

resource "azurerm_private_dns_resolver_virtual_network_link" "forwarding_ruleset_vnet_link" {
  name                      = "orange-users-vnet-link"
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.forwarding_ruleset.id
  virtual_network_id        = azurerm_virtual_network.vnet.id
}


resource "azurerm_private_endpoint" "coredns_pe" {
  for_each = module.orange-clusters-config.clusters

  name                = "coredns-pe-${each.key}"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name

  subnet_id = azurerm_subnet.resources_subnet.id

  private_service_connection {
    name                           = "coredns-psc-${each.key}"
    private_connection_resource_id = "/subscriptions/${each.value.cluster_subscription}/resourceGroups/${each.value.resource_group_name}/providers/Microsoft.Network/privateLinkServices/coredns"
    is_manual_connection           = true
    request_message                = "central connection for CoreDNS"
  }
}

resource "azurerm_private_dns_resolver_forwarding_rule" "coredns_forwarding_rule_svc" {
  for_each = azurerm_private_endpoint.coredns_pe

  name                      = each.key
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.forwarding_ruleset.id
  domain_name               = "${each.key}.dev.openai.org."
  target_dns_servers {
    ip_address = each.value.private_service_connection[0].private_ip_address
    port       = 53
  }
  enabled = true
}

resource "azurerm_private_dns_resolver_forwarding_rule" "coredns_forwarding_rule_k8s" {
  for_each = azurerm_private_endpoint.coredns_pe

  name                      = "${each.key}-k8s"
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.forwarding_ruleset.id
  domain_name               = "${each.key}.k8s.dev.openai.org."
  target_dns_servers {
    ip_address = each.value.private_service_connection[0].private_ip_address
    port       = 53
  }
  enabled = true
}

// Manually approve the private endpoint connection for CoreDNS
# resource "azurerm_private_endpoint_connection" "coredns_pe_approval" {
#   provider = azurerm
#   for_each = module.orange-clusters-config.clusters

#   private_endpoint_id = azurerm_private_endpoint.coredns_pe[each.key].id
#   private_link_service_connection {
#     name                    = "coredns-psc-${each.key}"
#     private_link_service_id = "/subscriptions/${each.value.cluster_subscription}/resourceGroups/${each.value.resource_group_name}/providers/Microsoft.Network/privateLinkServices/coredns"
#     request_message         = "Approve CoreDNS Private Endpoint"
#     actions_required        = "None"
#     status                  = "Approved"
#     description             = "Approved by Terraform"
#   }
# }
