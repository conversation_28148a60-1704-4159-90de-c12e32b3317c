resource "azurerm_virtual_network" "vnet" {
  name                = "orange-users-vnet"
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  location            = azurerm_resource_group.orange_users_rg.location
  address_space       = var.vnet_range
}

resource "azurerm_subnet" "resources_subnet" {
  name                 = "resources-subnet"
  resource_group_name  = azurerm_resource_group.orange_users_rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = var.resources_subnet_range
}

resource "azurerm_subnet" "tailscale_subnet" {
  name                 = "tailscale-subnet"
  resource_group_name  = azurerm_resource_group.orange_users_rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = var.tailscale_subnet_range
}

resource "azurerm_subnet" "dns_in_subnet" {
  name                 = "dns-subnet"
  resource_group_name  = azurerm_resource_group.orange_users_rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = var.dns_in_range
  delegation {
    name = "Microsoft.Network/dnszones"
    service_delegation {
      name = "Microsoft.Network/dnsResolvers"
      actions = [
        "Microsoft.Network/virtualNetworks/subnets/join/action",
      ]
    }
  }
}

resource "azurerm_subnet" "dns_out_subnet" {
  name                 = "dns-out-subnet"
  resource_group_name  = azurerm_resource_group.orange_users_rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = var.dns_out_range
  delegation {
    name = "Microsoft.Network/dnszones"
    service_delegation {
      name = "Microsoft.Network/dnsResolvers"
      actions = [
        "Microsoft.Network/virtualNetworks/subnets/join/action",
      ]
    }
  }
}