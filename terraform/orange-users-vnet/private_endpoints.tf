locals {
  team_quota_json = jsondecode(file(var.team-quota-file-location))

  # Extract the users map from the output (includes both regular users and robots)
  users                          = local.team_quota_json.users.value
  model_storage_accounts         = { for key, value in module.model-settings.all_storage_accounts : key => value if !try(value.create_only, false) }
  observability_storage_accounts = module.observability-storage.active_regions
}

# Link user personal storage accounts

module "user-settings" {
  source = "../modules/orange-user-settings"
}

data "azurerm_subscription" "user-storage" {
  provider = azurerm.users
}

data "azurerm_storage_account" "user-storage" {
  for_each = local.users
  provider = azurerm.users

  name                = each.value.storage_account_name
  resource_group_name = var.user_storage_resource_group_name
}

resource "azurerm_private_endpoint" "pe-user-access" {
  for_each            = local.users
  name                = "pe-user-access-${each.key}"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  subnet_id           = azurerm_subnet.resources_subnet.id

  private_service_connection {
    name                           = "conn-user-access-${each.key}"
    private_connection_resource_id = data.azurerm_storage_account.user-storage[each.key].id
    is_manual_connection           = false
    subresource_names              = ["blob"]
  }

  private_dns_zone_group {
    name                 = "conn-user-access-${each.key}"
    private_dns_zone_ids = [azurerm_private_dns_zone.blob.id]
  }
}

# Link model storage accounts

module "model-settings" {
  source = "../modules/orange-storage-config"
}

data "azurerm_subscription" "model-storage" {
  provider = azurerm.oai-assets
}

data "azurerm_storage_account" "model-storage" {
  for_each = local.model_storage_accounts
  provider = azurerm.oai-assets

  name                = each.key
  resource_group_name = each.value.resource_group_name
}

resource "azurerm_private_endpoint" "pe-model-access" {
  for_each            = local.model_storage_accounts
  name                = "pe-model-access-${each.key}"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  subnet_id           = azurerm_subnet.resources_subnet.id

  private_service_connection {
    name                           = "conn-model-access-${each.key}"
    private_connection_resource_id = data.azurerm_storage_account.model-storage[each.key].id
    is_manual_connection           = false
    subresource_names              = ["blob"]
  }

  private_dns_zone_group {
    name                 = "conn-user-access-${each.key}"
    private_dns_zone_ids = [azurerm_private_dns_zone.blob.id]
  }
}

# Link observability storage accounts

module "observability-storage" {
  source = "../modules/orange-observability-config"
}

data "azurerm_storage_account" "observability-storage" {
  for_each = local.observability_storage_accounts

  name                = each.value.storage_account_name
  resource_group_name = each.value.resource_group_name
}

resource "azurerm_private_endpoint" "pe-observability-access" {
  for_each            = local.observability_storage_accounts
  name                = "pe-observability-access-${each.value.storage_account_name}"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  subnet_id           = azurerm_subnet.resources_subnet.id

  private_service_connection {
    name                           = "conn-observability-access-${each.value.storage_account_name}"
    private_connection_resource_id = data.azurerm_storage_account.observability-storage[each.key].id
    is_manual_connection           = false
    subresource_names              = ["blob"]
  }

  private_dns_zone_group {
    name                 = "conn-observability-access-${each.value.storage_account_name}"
    private_dns_zone_ids = [azurerm_private_dns_zone.blob.id]
  }
}


resource "azurerm_private_endpoint" "pe-snowflake-access" {
  name                = "pe-snowflake-tailscale"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  subnet_id           = azurerm_subnet.resources_subnet.id

  private_service_connection {
    name                              = "conn-snowflake-access"
    private_connection_resource_alias = "sf-pvlinksvc-azuksouth.e2f9fa41-cf7e-4e13-8a45-e6f23913990f.uksouth.azure.privatelinkservice"
    is_manual_connection              = true
    request_message                   = "Private-endpoint connection request"
  }

  private_dns_zone_group {
    name                 = "dns-snowflake-access"
    private_dns_zone_ids = [azurerm_private_dns_zone.snowflake.id]
  }
}

resource "azurerm_private_endpoint" "pe-wandb-access" {
  name                = "pe-wandb-tailscale"
  location            = azurerm_resource_group.orange_users_rg.location
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  subnet_id           = azurerm_subnet.resources_subnet.id

  private_service_connection {
    name                           = "conn-wandb-access"
    private_connection_resource_id = module.global.private_dns_zones["wandb"].private_link_resource_id
    is_manual_connection           = true
    subresource_names              = [module.global.private_dns_zones["wandb"].subresource_name]
    request_message                = "Requesting Private Link connection for Application Gateway"
  }

    private_dns_zone_group {
    name                 = "dns-wandb-access"
    private_dns_zone_ids = [azurerm_private_dns_zone.wandb_dns.id]
  }

}