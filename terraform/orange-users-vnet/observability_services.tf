module "observability-config" {
  source = "../modules/orange-observability-config"
}

resource "azurerm_private_dns_a_record" "observability_legacy" {
  for_each            = module.observability-config.services

  name                = "${each.key}"
  zone_name           = azurerm_private_dns_zone.genai.name
  resource_group_name = azurerm_private_dns_zone.genai.resource_group_name
  ttl                 = 300
  records             = [each.value.cluster_ip]
}

resource "azurerm_private_dns_a_record" "observability" {
  for_each            = module.observability-config.services

  name                = "${each.key}"
  zone_name           = azurerm_private_dns_zone.orange.name
  resource_group_name = azurerm_private_dns_zone.orange.resource_group_name
  ttl                 = 300
  records             = [each.value.cluster_ip]
}
