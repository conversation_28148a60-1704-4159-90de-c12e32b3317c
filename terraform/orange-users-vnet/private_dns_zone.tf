# blob storage dns

resource "azurerm_private_dns_zone" "blob" {
  name                = "privatelink.blob.core.windows.net"
  resource_group_name = azurerm_resource_group.orange_users_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_blob_link" {
  name                  = "link-blob-vnet"
  resource_group_name   = azurerm_resource_group.orange_users_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.blob.name
  virtual_network_id    = azurerm_virtual_network.vnet.id
  registration_enabled  = false
}

# cluster oidc dns

resource "azurerm_private_dns_zone" "orange" {
  name                = "orange.internal"
  resource_group_name = azurerm_resource_group.orange_users_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_orange_link" {
  name                  = "link-orange-vnet"
  resource_group_name   = azurerm_resource_group.orange_users_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.orange.name
  virtual_network_id    = azurerm_virtual_network.vnet.id
  registration_enabled  = false
}

# legacy oidc dns

resource "azurerm_private_dns_zone" "genai" {
  name                = "internal.genai.ms"
  resource_group_name = azurerm_resource_group.orange_users_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_genai_link" {
  name                  = "link-genai-vnet"
  resource_group_name   = azurerm_resource_group.orange_users_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.genai.name
  virtual_network_id    = azurerm_virtual_network.vnet.id
  registration_enabled  = false
}

# snowflake dns

resource "azurerm_private_dns_zone" "snowflake" {
  name                = module.global.private_dns_zones["snowflake"].name
  resource_group_name = azurerm_resource_group.orange_users_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_snowflake_link" {
  depends_on = [azurerm_private_dns_zone.snowflake]

  name                  = "link-snowflake-vnet"
  resource_group_name   = azurerm_resource_group.orange_users_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.snowflake.name
  virtual_network_id    = azurerm_virtual_network.vnet.id
  registration_enabled  = false
}


resource "azurerm_private_dns_a_record" "snowflake_records" {
  for_each   = toset(values(module.global.private_dns_zones["snowflake"].snowflake_a_records))
  depends_on = [azurerm_private_dns_zone.snowflake]

  name                = each.key
  zone_name           = azurerm_private_dns_zone.snowflake.name
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  ttl                 = 300
  records             = [azurerm_private_endpoint.pe-snowflake-access.private_service_connection[0].private_ip_address]
}

resource "azurerm_private_dns_zone" "wandb_dns" {
  name                = module.global.private_dns_zones["wandb"].name
  resource_group_name = azurerm_resource_group.orange_users_rg.name
}

resource "azurerm_private_dns_a_record" "wandb_a_record" {
  name                = module.global.private_dns_zones["wandb"].dns_name_a_record
  zone_name           = module.global.private_dns_zones["wandb"].name
  resource_group_name = azurerm_resource_group.orange_users_rg.name
  ttl                 = 300
  records             = [azurerm_private_endpoint.pe-wandb-access.private_service_connection[0].private_ip_address]

  depends_on = [azurerm_private_dns_zone.wandb_dns]
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_wandb_link" {
  depends_on = [azurerm_private_dns_zone.wandb_dns]

  name                  = "msaip-private-terraform"
  resource_group_name   = azurerm_resource_group.orange_users_rg.name
  private_dns_zone_name = module.global.private_dns_zones["wandb"].name
  virtual_network_id    = azurerm_virtual_network.vnet.id
}
