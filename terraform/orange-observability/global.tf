# Global resources (that aren't region-specific)
resource "azurerm_resource_group" "orange-observability-global" {
  name     = "orange-observability-global"
  location = "uksouth" # Default location for global resources
}


# Adding builder role assignment to the resource group
resource "azurerm_role_assignment" "builder-role-assignment" {
  principal_id         = module.global.builder.identity.principal_id
  role_definition_name = "Contributor"
  scope                = azurerm_resource_group.orange-observability-global.id
}


# Any global observability resources would go here
