terraform {

  backend "azurerm" {
    resource_group_name  = "orange-terraform"
    storage_account_name = "orangetfstate"
    container_name       = "tfstate"
    key                  = "observability"
    subscription_id      = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
    use_azuread_auth     = true
  }

  required_providers {
    azuread = "~> 2.37.0"
    azurerm = "4.19.0"
  }
}

# Default provider, needed so terraform doesn't complain
provider "azurerm" {
  subscription_id = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
  features {
    storage {
      data_plane_available = true
    }
  }
}

# PROVIDER SETUP
provider "azurerm" {
  alias               = "infra"
  subscription_id     = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
  storage_use_azuread = true
  features {
    storage {
      data_plane_available = true
    }
  }
}

provider "azurerm" {
  alias                           = "onboarding-sp"
  storage_use_azuread             = true
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
  resource_provider_registrations = "none"

  features {
    storage {
      data_plane_available = false
    }
  }
}

provider "azurerm" {
  alias                           = "auditing-sub"
  subscription_id                 = "f91c4126-3a46-4ddc-b984-6e9e4c1625ee" # AIPLATFORM-MUMFORD-SECMON-GREEN-DEV
  resource_provider_registrations = "none"

  features {}
}

provider "azuread" {
  tenant_id = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # green tenant
}

provider "azuread" {
  alias              = "onboarding-sp"
  client_id          = var.onboarding-sp.client_id
  tenant_id          = var.onboarding-sp.tenant_id
  client_certificate = data.azurerm_key_vault_secret.onboarding-sp-cert.value
}

data "azurerm_key_vault" "onboarding-sp-keyvault" {
  provider            = azurerm.onboarding-sp
  name                = var.onboarding-sp.keyvault_name
  resource_group_name = var.onboarding-sp.keyvault_resource_group_name
}

data "azurerm_key_vault_secret" "onboarding-sp-cert" {
  provider     = azurerm.onboarding-sp
  name         = var.onboarding-sp.certificate_name
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}
