# Create resources for each region
module "regional" {
  providers = {
    azurerm.infra = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
    azuread              = azuread.onboarding-sp
  }
  source   = "./regional"
  for_each = local.regions

  region               = each.key
  region_abbrev        = each.value.region_abbrev
  rg_name              = each.value.resource_group_name
  groups               = data.azuread_group.groups
  storage_account_name = each.value.storage_account_name
  events_dev_storage_account_name = each.value.events_dev_storage_account_name
}
