resource "azurerm_resource_group" "orange-observability-resources" {
  name     = var.rg_name
  location = var.region
}

# Get builder msi object id from global settings module
module "global" {
  source = "../../modules/global_settings"
}

# Adding builder role assignment to the resource group
resource "azurerm_role_assignment" "builder-role-assignment" {
  principal_id         = module.global.builder.identity.principal_id
  role_definition_name = "Contributor"
  scope                = azurerm_resource_group.orange-observability-resources.id
}