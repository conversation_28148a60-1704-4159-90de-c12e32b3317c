variable "region" {
  description = "Azure region where resources should be created"
  type        = string
}

variable "rg_name" {
  description = "Name of the resource group to create"
  type        = string
}

variable "groups" {
  description = "Map of Azure AD groups objects with tent name as key"
  type = map(object({
    object_id = string
  }))
}

variable "region_abbrev" {
  description = "Short name for the Azure region"
  type        = string
}

variable "storage_account_name" {
  description = "Name of the regional storage account to create"
  type        = string
}

variable "events_dev_storage_account_name" {
  description = "Name of the dev regional storage account needed for joiner"
  type        = string
}

variable "joiner_service_principal_name" {
  type        = string
  description = "Name of service principal to assign roles to"
  default     = "joiner-orange-app"
}

# TODO: Add events storage account for prod