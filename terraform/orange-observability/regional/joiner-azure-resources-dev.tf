module "observability-joiner-dev-storage" {
  source = "../../modules/storage-base"
  providers = {
    azurerm.infra        = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  name                = var.events_dev_storage_account_name # eg, orngeventsdevuks
  resource_group_name = azurerm_resource_group.orange-observability-resources.name
  location            = azurerm_resource_group.orange-observability-resources.location

  builder_access         = true
  builder_access_service = "queue"

  builder_role_assignments = [
    "Storage Blob Data Contributor",
    "Storage Queue Data Contributor"
  ]

  # TODO: Check whether we need this as blob storage for joiners are used only by joiner components and not accessed through Q* code
  group_role_assignments = {
    "Storage Blob Data Contributor" = [
      var.groups["cresco"].object_id
    ]
  }

  tags = {
    "Purpose"         = "Observability"
    "oai-sensitivity" = "critical"
  }
}

# Adding delay to allow for Azure's Data plane RBAC to propagate
# as "depends_on" only ensures the role assignment is deployed in Azure control plane
resource "time_sleep" "wait_for_role" {
  depends_on = [
    module.observability-joiner-dev-storage
  ]

  create_duration = "60s"
}

resource "azurerm_storage_queue" "joiner-experiment-schedule-queue" {
  depends_on           = [time_sleep.wait_for_role]
  name                 = "joiner-experiment-schedule-queue"
  storage_account_name = module.observability-joiner-dev-storage.storage_account.name
}

resource "azurerm_storage_queue" "joiner-experiment-schedule-failed-queue" {
  depends_on           = [time_sleep.wait_for_role]
  name                 = "joiner-experiment-schedule-failed-queue"
  storage_account_name = module.observability-joiner-dev-storage.storage_account.name
}

# Assign RBAC to Service Principal so that these resources can be accessed through joiner pods using RBAC
data "azuread_service_principal" "joiner_infra_app" {
  display_name = var.joiner_service_principal_name
}

resource "azurerm_role_assignment" "storage_data_contributor" {
  scope                = module.observability-joiner-dev-storage.storage_account.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = data.azuread_service_principal.joiner_infra_app.object_id
}

resource "azurerm_role_assignment" "storage_queue_data_contributor" {
  scope                = module.observability-joiner-dev-storage.storage_account.id
  role_definition_name = "Storage Queue Data Contributor"
  principal_id         = data.azuread_service_principal.joiner_infra_app.object_id
}
