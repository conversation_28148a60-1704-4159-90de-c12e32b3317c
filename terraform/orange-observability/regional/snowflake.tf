module "observability-snowflake-storage" {
  source    = "../../modules/storage-base"
  providers = {
    azurerm.infra = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  name                          = var.storage_account_name # eg, orngsnowflakeuks
  resource_group_name           = azurerm_resource_group.orange-observability-resources.name
  location                      = azurerm_resource_group.orange-observability-resources.location
  public_network_access_enabled = true

  builder_access         = true
  builder_access_service = "queue"

  builder_role_assignments = [
    "Storage Blob Data Contributor",
    "Storage Queue Data Contributor",
    "EventGrid EventSubscription Contributor"
  ]

  group_role_assignments = {
    "Storage Blob Data Contributor" = [
      var.groups["cresco"].object_id
    ]
  }

  tags = {
    "Purpose"         = "Observability"
    "oai-sensitivity" = "critical"
  }
}

// Queue to listen events of Blob Storage
resource "azurerm_storage_queue" "strawberry_queue" {
  depends_on = [module.observability-snowflake-storage]

  name                 = "strawberry-queue"
  storage_account_name = module.observability-snowflake-storage.storage_account.name
}

// Subscribes to events from the snowflake storage account and sends them to the queue
resource "azurerm_eventgrid_event_subscription" "event_subscription" {
  depends_on = [module.observability-snowflake-storage]

  name  = "orngsnowflake${var.region_abbrev}-event-subscription"
  scope = module.observability-snowflake-storage.storage_account.id

  storage_queue_endpoint {
    storage_account_id = module.observability-snowflake-storage.storage_account.id
    queue_name         = azurerm_storage_queue.strawberry_queue.name
  }

  // Only listens for blob created events
  included_event_types = [
    "Microsoft.Storage.BlobCreated"
  ]
}

# Assign RBAC to Joiner Service Principal. This is needed for two reasons:
# 1. Joiner reads samples folder to figure out experiment names
# 2. Post Join operation, it writes completed samples to completed_samples folder which eventually will be used by Snowpipe to populated completed samples table
resource "azurerm_role_assignment" "snowflake_storage_data_contributor_for_joiner" {
  scope                = module.observability-snowflake-storage.storage_account.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = data.azuread_service_principal.joiner_infra_app.object_id
}