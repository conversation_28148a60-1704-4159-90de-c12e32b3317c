module "teams-onboarding" {
  source = "../modules/orange-user-team-quota"
  providers = {
    azuread = azuread
    azurerm = azurerm
  }
  moonfire_teams         = local.moonfire-teams
  cluster_access_groups  = local.cluster_access_groups
}

module "orange-team-observability-azure" {
  for_each = toset(local.moonfire-teams)
  source = "../modules/orange-team-observability-azure"
  providers = {
    azurerm.infra         = azurerm.infra
    azurerm.auditing-sub  = azurerm.auditing-sub
    azuread               = azuread
    azuread.onboarding-sp = azuread
  }
  team_name               = "${trimprefix(each.key, "team-moonfire-")}"
}

data "azuread_groups" "moonfire-admin-teams" {
  display_names = [
    for k, _ in local.moonfire_admin_team_cluster_access : upper("${k}-admin")
  ]
}

# MARK: Quota
output "orange-teams" {
  value = module.teams-onboarding.orange-teams
}

output "team_allocations" {
  value = module.teams-onboarding.team_allocations
}

output "team_stats" {
  value = module.teams-onboarding.team_stats
}

output "cluster_stats" {
  value = module.teams-onboarding.cluster_stats
}

output "team_total_resources" {
  value = module.teams-onboarding.team_total_resources
}

output "teams_with_quotas" {
  value = module.teams-onboarding.teams_with_quotas
}

output "teams_without_quotas" {
  value = module.teams-onboarding.teams_without_quotas
}

output "moonfire_admin_team_cluster_access" {
  # Validate admin teams actually exist
  depends_on = [data.azuread_groups.moonfire-admin-teams]
  value = local.moonfire_admin_team_cluster_access
}
