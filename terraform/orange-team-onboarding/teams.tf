locals {
  // The team names must be lowercase as they're used as labels in Kubernetes.
  // And Kubernetes labels must be lowercase
  // https://kubernetes.io/docs/concepts/overview/working-with-objects/names/
  moonfire-teams = [
    "team-moonfire-bic",
    "team-moonfire-cap",
    "team-moonfire-dri",
    "team-moonfire-ed",
    "team-moonfire-genai",
    "team-moonfire-genaicore",
    "team-moonfire-genaid1",
    "team-moonfire-github",
    "team-moonfire-hls",
    "team-moonfire-infrasecurity",
    "team-moonfire-m365",
    "team-moonfire-mai",
    "team-moonfire-msr",
    "team-moonfire-oairt",
    "team-moonfire-octo",
    "team-moonfire-opg",
    "team-moonfire-playground",
    "team-moonfire-rai",
    "team-moonfire-security",
  ]
  security-teams = [
    "orange-genaid1",
    "orange-genaid2"
  ]
  moonfire_admin_team_cluster_access = {
    "team-moonfire-m365" = ["prod-uksouth-7", "prod-westus2-19", "prod-southcentralus-hpe-4"]
    "team-moonfire-genaicore" = ["prod-uksouth-7", "prod-uksouth-8", "prod-southcentralus-hpe-2", "prod-southcentralus-hpe-3", "prod-southcentralus-hpe-5"]
    "team-moonfire-bic" = ["prod-southcentralus-hpe-2"]
    "team-moonfire-security" = ["prod-southcentralus-hpe-2"]
    "team-moonfire-mai" = ["prod-southcentralus-hpe-5"]
  }

  # Define regular cluster access groups first
  regular_cluster_access_groups = {
    TEAM-ORANGE-ACCESS-UKSOUTH7 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-uksouth-7"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    }
    TEAM-ORANGE-ACCESS-UKSOUTH8 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-uksouth-8"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-UKSOUTH9 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-uksouth-9"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-UKSOUTH15 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-uksouth-15"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-STG-SCUS-1 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["stage-southcentralus-hpe-1"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-PROD-SOUTHCENTRALUS-HPE-2 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-southcentralus-hpe-2"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-PROD-SOUTHCENTRALUS-HPE-5 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-southcentralus-hpe-5"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-PROD-SOUTHCENTRALUS-HPE-3 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-southcentralus-hpe-3"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    },
    TEAM-ORANGE-ACCESS-PROD-WESTUS2-19 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-westus2-19"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    }
    TEAM-ORANGE-ACCESS-PROD-SOUTHCENTRALUS-HPE-4 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-southcentralus-hpe-4"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    }
    TEAM-ORANGE-ACCESS-PROD-EASTUS2-30 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["prod-eastus2-30"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    }
    TEAM-ORANGE-ACCESS-STG-SCUS-2 : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : ["stage-southcentralus-2"],
      storage_access : ["base", "cresco"],
      parent_security_groups : concat(local.moonfire-teams, local.security-teams)
    }
  }

  # Collect all available clusters from regular groups
  all_available_clusters = distinct(flatten([
    for team_data in local.regular_cluster_access_groups : team_data.cluster_access
  ]))
}

locals {
  # Merge regular teams with the special all-clusters team
  cluster_access_groups = merge(local.regular_cluster_access_groups, {
    TEAM-ORANGE-ACCESS-ALL-CLUSTERS : {
      owners : ["vipulm", "royoung", "ragarg", "bolian", "liuming"],
      cluster_access : local.all_available_clusters,
      storage_access : ["base", "cresco"],
      parent_security_groups : ["team-moonfire-dri"]
    }
  })
}
