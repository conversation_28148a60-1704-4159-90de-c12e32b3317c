module "manual-users" {
  source = "../modules/orange-user-settings/"
}

module "orange-team-onboarding-remote-state" {
  source = "../modules/orange-remote-state"
  providers = {
    azurerm = azurerm
  }
  state-key = "orange-team-onboarding"
}

#TODO: [aupadhyay] Remove this once we stop using the manual list of users
locals {

  teams          = module.orange-team-onboarding-remote-state.remote-state-outputs.orange-teams
  imported_users = module.manual-users.users
  allowlist      = ["aupadhyay", "pandeyn", "noabani", "ragarg"]

  users = {
    for username, user in local.imported_users : username => user
    if contains(local.allowlist, username)
  }



  team_cluster_map = {
    for team_name, team in local.teams : team.cluster_access[0] => team_name
    // Ignore teams that spans multiple clusters
    if team_name != "TEAM-ORANGE-ACCESS-ALL-CLUSTERS"
  }

  users_to_teams_map = toset(flatten([
    for username, user in local.users : [
      for cluster_access in user.cluster_access : {
        user = username
        team = local.team_cluster_map[cluster_access]
      }
    ]
  ]))
}


data "azuread_user" "user" {
  for_each            = toset(keys(local.users))
  user_principal_name = "${each.key}@${var.tenant_domain}"
}


data "azuread_group" "group" {
  for_each = toset(keys(local.teams))

  display_name = each.value
}


resource "azuread_group_member" "group_access" {
  for_each = { for item in local.users_to_teams_map : "${item.user}-${item.team}" => item }

  group_object_id  = data.azuread_group.group[each.value.team].object_id
  member_object_id = data.azuread_user.user[each.value.user].object_id
}
