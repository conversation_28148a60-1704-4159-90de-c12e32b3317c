terraform {
  backend "azurerm" {
    resource_group_name  = "orange-terraform"
    storage_account_name = "orangetfstate"
    container_name       = "tfstate"
    key                  = "trellis-global"
    subscription_id      = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA
    use_azuread_auth     = true
  }

  required_providers {
    azuread = "~> 2.37.0"
    azurerm = "~> 4.30.0"
    random  = {
      source  = "hashicorp/random"
      version = ">= 3.0.0"
    }
  }
}

# Set the default azurerm provider to use the specified subscription and tenant
provider "azurerm" {
  subscription_id = local.trellis_config.subscription_id
  tenant_id       = local.trellis_config.tenant_id
  features {
    key_vault {
      purge_soft_delete_on_destroy = true
    }
  }
}

# Set the default azuread provider to be authenticated using the onboarding service principal in the green tenant
## Define the azurerm.infra provider to use the AIPLATFORM-ORANGE-INFRA subscription
provider "azurerm" {
  alias                           = "infra"
  storage_use_azuread             = true
  subscription_id                 = local.infra_subscription_id
  tenant_id                       = local.green_tenant_id
  resource_provider_registrations = "none"
  features {
    key_vault {
      purge_soft_delete_on_destroy = true
    }
  }
}
## Get the onboarding key vault from the infra subscription
data "azurerm_key_vault" "onboarding-sp-keyvault" {
  provider            = azurerm.infra
  name                = var.onboarding-sp.keyvault_name
  resource_group_name = var.onboarding-sp.keyvault_resource_group_name
}
## Get the onboarding service principal certificate from the key vault
data "azurerm_key_vault_secret" "onboarding-sp-cert" {
  provider     = azurerm.infra
  name         = var.onboarding-sp.certificate_name
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}
## Set the default azuread provider for the onboarding service principal in the green tenant
provider "azuread" {
  client_id          = var.onboarding-sp.client_id
  tenant_id          = var.onboarding-sp.tenant_id
  client_certificate = data.azurerm_key_vault_secret.onboarding-sp-cert.value
}
