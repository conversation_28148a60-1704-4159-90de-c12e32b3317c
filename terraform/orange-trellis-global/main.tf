# This file sets up all the global resources for trellis.
module "global" {
  source = "../modules/global_settings"
}

# Read security groups from the global settings module
# This can be used to grant access to common orange security groups. e.g. orange-base-access, orange-cresco-access
data "azuread_group" "orange_security_groups" {
  for_each     = module.global.security_groups_compat
  display_name = each.value
}

# Create the trellis security groups
## Create the admin group
resource "azuread_group" "data_tool_admin_group" {
  display_name     = local.trellis_config.admin_group.name
  description      = "Orange Data Tool Admin Group"
  security_enabled = true
  owners           = local.trellis_config.owners
}
## Add members to the admin group
resource "azuread_group_member" "data_tool_admin_group_members" {
  for_each = toset(local.trellis_config.admin_group.members)
  group_object_id = azuread_group.data_tool_admin_group.id
  member_object_id = each.value
}

# Create the application registration for the trellis application
resource "azuread_application" "trellis_application" {
  display_name     = "trellis-application-${local.trellis_config.environment}"
  owners           = local.trellis_config.owners
  sign_in_audience = "AzureADMyOrg"
}
## Create the service principal for the trellis application
resource "azuread_service_principal" "trellis_sp" {
  application_id               = azuread_application.trellis_application.application_id
  app_role_assignment_required = false
}
## Create the client secret only if the service principal is (re)created
resource "azuread_application_password" "trellis_sp_password" {
  application_object_id = azuread_application.trellis_application.id
  display_name          = "trellis-sp-secret"
  end_date              = "2099-12-31T23:59:59Z"
  depends_on            = [azuread_service_principal.trellis_sp]
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = false
  }
}

# Create the trellis resource group and assign owners
## Create the resource group for the trellis global resources
resource "azurerm_resource_group" "trellis_resource_group" {
  name     = local.trellis_config.resource_group
  location = local.trellis_config.region
}
## Assign owners to the resource group
resource "azurerm_role_assignment" "trellis_resource_group_owners" {
  for_each             = toset(local.trellis_config.owners)
  scope                = azurerm_resource_group.trellis_resource_group.id
  role_definition_name = "Owner"
  principal_id         = each.value
  depends_on = [azurerm_resource_group.trellis_resource_group]
}

# Create the key vault for the trellis application with rbac access policies
## Create the key vault
resource "azurerm_key_vault" "trellis_key_vault" {
  name                          = local.trellis_config.keyvault.name
  location                      = azurerm_resource_group.trellis_resource_group.location
  resource_group_name           = azurerm_resource_group.trellis_resource_group.name
  tenant_id                     = local.trellis_config.tenant_id
  sku_name                      = "standard"
  enable_rbac_authorization     = true
  public_network_access_enabled = false
}
## Grant access on the key vault to the builder service principal
module "builder-access" {
  providers = {
    azurerm = azurerm.infra
  }
  source   = "../modules/orange-builder-access"
  name        = azurerm_key_vault.trellis_key_vault.name
  dns_zone    = module.global.private_dns_zones["vault"]
  resource_id = azurerm_key_vault.trellis_key_vault.id
  role_assignments = [
    "Key Vault Secrets Officer"
  ]
  depends_on = [azurerm_key_vault.trellis_key_vault]
}
## Grant the Secrets Officer role on the key vault to the admin group
resource "azurerm_role_assignment" "data_tool_admin_group_kv_secret_officer_role" {
  principal_id         = azuread_group.data_tool_admin_group.id
  role_definition_name = "Key Vault Secrets Officer"
  scope                = azurerm_key_vault.trellis_key_vault.id
}
## Grant the Secrets User role on the key vault to the trellis service principal
resource "azurerm_role_assignment" "trellis_sp_kv_secret_user_role" {
  principal_id         = azuread_service_principal.trellis_sp.id
  role_definition_name = "Key Vault Secrets User"
  scope                = azurerm_key_vault.trellis_key_vault.id
}
## Create a secret in the key vault for the trellis service principal ID
resource "azurerm_key_vault_secret" "trellis_sp_id" {
  name         = local.trellis_config.keyvault.secret_names.trellis_sp_id
  value        = azuread_service_principal.trellis_sp.id
  key_vault_id = azurerm_key_vault.trellis_key_vault.id
  depends_on   = [azuread_service_principal.trellis_sp]
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = false
  }
}
## Create a secret for the trellis service principal password
resource "azurerm_key_vault_secret" "trellis_sp_password" {
  name         = local.trellis_config.keyvault.secret_names.trellis_sp_password
  value        = azuread_application_password.trellis_sp_password.value
  key_vault_id = azurerm_key_vault.trellis_key_vault.id
  depends_on   = [azuread_application_password.trellis_sp_password]
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = false
  }
}

# Create a PostgreSQL Flexible Server with private access
## Generate a random password for the PostgreSQL server
resource "random_password" "postgres_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}
## Create the PostgreSQL server
resource "azurerm_postgresql_flexible_server" "trellis_postgresql" {
  name                          = local.trellis_config.postgressql.server_name
  resource_group_name           = azurerm_resource_group.trellis_resource_group.name
  location                      = azurerm_resource_group.trellis_resource_group.location
  sku_name                      = "B_Standard_B1ms" # Burstable, 1 vCore
  storage_mb                    = 524288 # 512 GB
  backup_retention_days         = 7
  administrator_login           = local.trellis_config.postgressql.admin_login
  administrator_password        = random_password.postgres_password.result
  version                       = "13"
  tags                          = {}
  public_network_access_enabled = false # Ensure public network access is disabled
  depends_on = [azurerm_resource_group.trellis_resource_group]
  lifecycle {
    ignore_changes = [zone]
  }
}
## Create the PostgreSQL database
resource "azurerm_postgresql_flexible_server_database" "postgresql_db" {
  name       = local.trellis_config.postgressql.database_name
  server_id  = azurerm_postgresql_flexible_server.trellis_postgresql.id
  charset    = "UTF8"
  collation  = "en_US.utf8"
  depends_on = [azurerm_postgresql_flexible_server.trellis_postgresql]
}
## Store the PostgreSQL server password in the key vault
resource "azurerm_key_vault_secret" "postgresql_password" {
  name = local.trellis_config.keyvault.secret_names.postgresql_admin_password
  value = random_password.postgres_password.result
  key_vault_id = azurerm_key_vault.trellis_key_vault.id
  # Only update the secret if the PostgreSQL server is recreated (i.e., its password changes)
  lifecycle {
    ignore_changes = [value]
    replace_triggered_by = [azurerm_postgresql_flexible_server.trellis_postgresql]
  }
  depends_on = [azurerm_postgresql_flexible_server.trellis_postgresql]
}
## Generate a random password for the Redis server
resource "random_password" "redis_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}
## Store the Redis server password in the key vault
resource "azurerm_key_vault_secret" "redis_password" {
  name = local.trellis_config.keyvault.secret_names.redis_password
  value = random_password.redis_password.result
  key_vault_id = azurerm_key_vault.trellis_key_vault.id
  depends_on = [random_password.redis_password]
  lifecycle {
    ignore_changes = [value]
  }
}

# Read the secrets and copy them to the infra_sync key vault so that they can be read by the AME cluster builder
resource "azurerm_key_vault_secret" "postgresql_password_sync" {
  provider     = azurerm.infra
  name         = local.trellis_config.keyvault.secret_names.postgresql_admin_password
  value        = random_password.postgres_password.result
  key_vault_id = module.global.infra_sync.keyvault.id
  depends_on   = [azurerm_key_vault_secret.postgresql_password]
}

resource "azurerm_key_vault_secret" "redis_password_sync" {
  provider     = azurerm.infra
  name         = local.trellis_config.keyvault.secret_names.redis_password
  value        = random_password.redis_password.result
  key_vault_id = module.global.infra_sync.keyvault.id
  depends_on   = [azurerm_key_vault_secret.redis_password]
}

resource "azurerm_key_vault_secret" "trellis_sp_id_sync" {
  provider     = azurerm.infra
  name         = local.trellis_config.keyvault.secret_names.trellis_sp_id
  value        = azuread_service_principal.trellis_sp.id
  key_vault_id = module.global.infra_sync.keyvault.id
  depends_on   = [azurerm_key_vault_secret.trellis_sp_id]
}

resource "azurerm_key_vault_secret" "trellis_sp_password_sync" {
  provider     = azurerm.infra
  name         = local.trellis_config.keyvault.secret_names.trellis_sp_password
  value        = azuread_application_password.trellis_sp_password.value
  key_vault_id = module.global.infra_sync.keyvault.id
  depends_on   = [azurerm_key_vault_secret.trellis_sp_password]
}