terraform {
  backend "azurerm" {
    resource_group_name  = "iridium-terraform"
    storage_account_name = "iridiumtfstate"
    container_name       = "tfstate"
    key                  = "1es-iridium-builder-eastus2"
    subscription_id      = "64467a16-0cdd-4a44-ad9b-83b5540828ac"
  }

  required_providers {
    azurerm = "4.19.0"
    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
  }
}

# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = "64467a16-0cdd-4a44-ad9b-83b5540828ac"
  resource_provider_registrations = "none"
  features {}
}

provider "azapi" {
}


