terraform {
  backend "azurerm" {
    resource_group_name  = "orange-terraform-ame"
    storage_account_name = "orangeametfstate"
    container_name       = "tfstate"
    key                  = "1es-orange-user-cluster-onboarding-builder-eastus2"
    subscription_id      = "57ef2365-3a4a-4150-ac28-1ec2563c43c4" # aisc-orange-ame-prod-03
  }

  required_providers {
    azurerm = "4.19.0"
    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
  }
}

# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = "57ef2365-3a4a-4150-ac28-1ec2563c43c4" # aisc-orange-ame-prod-03
  resource_provider_registrations = "none"
  features {}
}

provider "azapi" {
}


