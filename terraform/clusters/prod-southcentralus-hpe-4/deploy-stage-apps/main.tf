module "config" {
  source = "../../../modules/orange-clusters-config"
}

locals {
  cluster_name = basename(dirname(abspath(path.module)))
  cluster_config = module.config.clusters[local.cluster_name]
}

module "data" {
  source = "../../../modules/manifold-cluster-data"
  providers = {
    azurerm = azurerm
  }
  cluster_config = local.cluster_config
}

module "stage-apps" {
  source = "../../../modules/stage-apps"

  providers = {
    azurerm                     = azurerm
    azurerm.ame-infra           = azurerm.ame-infra
    azurerm.common              = azurerm.common
    azurerm.infra-secret-reader = azurerm.infra-secret-reader
    kubernetes                  = kubernetes
    kubectl                     = kubectl
    helm                        = helm
  }

  config = merge(local.cluster_config, module.data.manifold_cluster_data)
}

import {
  to = module.stage-apps.kubernetes_storage_class_v1.managed-premium
  id = "managed-premium"
}
