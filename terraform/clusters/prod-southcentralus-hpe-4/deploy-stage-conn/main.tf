module "config" {
  source = "../../../modules/orange-clusters-config"
}

locals {
  cluster_name = basename(dirname(abspath(path.module)))
  cluster_config = module.config.clusters[local.cluster_name]
}

module "data" {
  source = "../../../modules/manifold-cluster-data"
  providers = {
    azurerm = azurerm
  }
  cluster_config = local.cluster_config
}

module "stage-conn" {
  source = "../../../modules/stage-conn"

  providers = {
    azurerm        = azurerm
    azurerm.common = azurerm.common
  }

  config = merge(local.cluster_config, module.data.manifold_cluster_data)
}

import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/caas.net"
  to = module.stage-conn.azurerm_private_dns_zone.caas_private_dns_zone
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/caas.net/virtualNetworkLinks/caas-pls-westus2-01-link"
  to = module.stage-conn.azurerm_private_dns_zone_virtual_network_link.caas_private_dns_zone_vnet_link
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com"
  to = module.stage-conn.azurerm_private_dns_zone.caas_azure_private_dns_zone
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com/virtualNetworkLinks/caas-pls-a5d12135554b842eda1135c9e87b21b1-link"
  to = module.stage-conn.azurerm_private_dns_zone_virtual_network_link.caas_azure_private_dns_zone_vnet_link
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.vaultcore.azure.net"
  to = module.stage-conn.azurerm_private_dns_zone.keyvault_private_dns_zone
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.vaultcore.azure.net/virtualNetworkLinks/kv-ojskq-southcentralus-1-link"
  to = module.stage-conn.azurerm_private_dns_zone_virtual_network_link.keyvault_private_dns_zone_vnet_link
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com/A/southcentralus-03"
  to = module.stage-conn.module.custom_services["southcentralus-03"].azurerm_private_dns_a_record.service_record[0]
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com/A/eastus2-07"
  to = module.stage-conn.module.custom_services["eastus2-07"].azurerm_private_dns_a_record.service_record[0]
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com/A/westus2-02"
  to = module.stage-conn.module.custom_services["westus2-02"].azurerm_private_dns_a_record.service_record[0]
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com/A/southcentralus-02"
  to = module.stage-conn.module.custom_services["southcentralus-02"].azurerm_private_dns_a_record.service_record[0]
}
import {
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.caas.azure.com/A/eastus2-08"
  to = module.stage-conn.module.custom_services["eastus2-08"].azurerm_private_dns_a_record.service_record[0]
}
import {
  to = module.stage-conn.azurerm_private_dns_zone.redis_private_dns_zone
  id = "/subscriptions/61a4c7c0-68e0-4675-a760-d7feed155495/resourceGroups/azml-prod-southcentralus-hpe-4_rg/providers/Microsoft.Network/privateDnsZones/privatelink.redis.cache.windows.net"
}
