terraform {
  required_version = ">= 1.9.0, < 1.12.0"
  backend "azurerm" {
    resource_group_name  = "iridium-terraform"
    storage_account_name = "iridiumtfstate"
    container_name       = "tfstate"
    key                  = "prod-southcentralus-hpe-2.stage-apps"
    subscription_id      = "64467a16-0cdd-4a44-ad9b-83b5540828ac" # GenAI Dev AME
  }

  required_providers {
    azuread = "~> 2.23.0"
    azurerm = "4.19.0"
    kubernetes = {
      version = "2.31.0"
    }

    kubectl = {
      source  = "alekc/kubectl"
      version = ">= 2.0.4"
    }
  }
}

# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = local.cluster_config.cluster_subscription
  resource_provider_registrations = "none"
  resource_providers_to_register  = ["Microsoft.Web", "Microsoft.CognitiveServices"]
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azurerm" {
  subscription_id                 = "64467a16-0cdd-4a44-ad9b-83b5540828ac" # GenAI Dev AME
  resource_provider_registrations = "none"
  alias                           = "common"
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azurerm" {
  alias                = "infra-secret-reader"
  client_id            = module.global_settings.infra_sync.app_sp_client_id
  tenant_id            = module.global_settings.infra_sync.keyvault.tenant_id
  use_oidc             = true
  oidc_token_file_path = var.oidc_token_file

  resource_provider_registrations = "none"
  subscription_id                 = module.global_settings.infra_sync.keyvault.subscription_id
  features {
  }
}

provider "azurerm" {
  subscription_id                 = "57ef2365-3a4a-4150-ac28-1ec2563c43c4" # aisc-orange-ame-infra
  resource_provider_registrations = "none"
  alias                           = "ame-infra"
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azuread" {
}

module "global_settings" {
  source = "../../../modules/global_settings"
}

# Cluster-specific provider configurations

provider "kubernetes" {
  # set to fqdn and remove trailing dot
  host                   = "https://${trimsuffix(local.cluster_config.cluster_fqdn, ".")}:443"
  tls_server_name        = trimsuffix(trimprefix(module.data.manifold_cluster_data.cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.cluster_ca_certificate)

  #
  # For client-id/server-id values see the following link:
  # https://learn.microsoft.com/en-us/azure/aks/kubelogin-authentication
  #
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

provider "kubectl" {
  host                   = "https://${trimsuffix(local.cluster_config.cluster_fqdn, ".")}:443"
  tls_server_name        = trimsuffix(trimprefix(module.data.manifold_cluster_data.cluster_aks.kube_config.0.host, "https://"), ":443")
  client_certificate     = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.client_certificate)
  client_key             = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.client_key)
  cluster_ca_certificate = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.cluster_ca_certificate)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "kubelogin"
    args = [
      "get-token",
      "--login",
      "azurecli",
      "--server-id",
      "6dae42f8-4368-4678-94ff-3960e28e3630",
      "--client-id",
      "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
    ]
  }
}

provider "helm" {
  kubernetes {
    host                   = "https://${trimsuffix(local.cluster_config.cluster_fqdn, ".")}:443"
    tls_server_name        = trimsuffix(trimprefix(module.data.manifold_cluster_data.cluster_aks.kube_config.0.host, "https://"), ":443")
    client_certificate     = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.client_certificate)
    client_key             = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.client_key)
    cluster_ca_certificate = base64decode(module.data.manifold_cluster_data.cluster_aks.kube_config.0.cluster_ca_certificate)

    #
    # For client-id/server-id values see the following link:
    # https://learn.microsoft.com/en-us/azure/aks/kubelogin-authentication
    #
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "kubelogin"
      args = [
        "get-token",
        "--login",
        "azurecli",
        "--server-id",
        "6dae42f8-4368-4678-94ff-3960e28e3630",
        "--client-id",
        "80faf920-1908-4b52-b5ef-a8e7bedfc67a",
      ]
    }
  }
}
