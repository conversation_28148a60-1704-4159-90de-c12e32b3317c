module "config" {
  source = "../../../modules/orange-clusters-config"
}

locals {
  cluster_name = basename(dirname(abspath(path.module)))
  cluster_config = module.config.clusters[local.cluster_name]
}

module "data" {
  source = "../../../modules/manifold-cluster-data"
  providers = {
    azurerm = azurerm
  }
  cluster_config = local.cluster_config
}

module "stage-conn" {
  source = "../../../modules/stage-conn"

  providers = {
    azurerm        = azurerm
    azurerm.common = azurerm.common
  }

  config = merge(local.cluster_config, module.data.manifold_cluster_data)
}

import {
  to = module.stage-conn.module.custom_services["orng-trellis-sql-prod-prod-westus2-19"].azurerm_private_endpoint.service_private_endpoint
  id = "/subscriptions/b82dbd21-ecad-49df-a5b4-9554082bc3a1/resourceGroups/azml-prod-westus2-azhub-19_rg/providers/Microsoft.Network/privateEndpoints/pe-orng-trellis-sql-prod-prod-westus2-19"
}
