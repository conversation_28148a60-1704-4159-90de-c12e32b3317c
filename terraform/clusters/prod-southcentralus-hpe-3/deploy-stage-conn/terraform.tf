terraform {
  required_version = ">= 1.9.0, < 1.12.0"
  backend "azurerm" {
    resource_group_name  = "iridium-terraform"
    storage_account_name = "iridiumtfstate"
    container_name       = "tfstate"
    key                  = "prod-southcentralus-hpe-3.stage-conn"
    subscription_id      = "64467a16-0cdd-4a44-ad9b-83b5540828ac" # GenAI Dev AME
  }

  required_providers {
    azuread = "~> 2.23.0"
    azurerm = "4.19.0"
    kubernetes = {
      version = "2.31.0"
    }

    kubectl = {
      source  = "alekc/kubectl"
      version = ">= 2.0.4"
    }
  }
}

# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = local.cluster_config.cluster_subscription
  resource_provider_registrations = "none"
  resource_providers_to_register  = ["Microsoft.Web", "Microsoft.CognitiveServices"]
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azurerm" {
  subscription_id                 = "64467a16-0cdd-4a44-ad9b-83b5540828ac" # GenAI Dev AME
  resource_provider_registrations = "none"
  alias                           = "common"
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azurerm" {
  alias                = "infra-secret-reader"
  client_id            = module.global_settings.infra_sync.app_sp_client_id
  tenant_id            = module.global_settings.infra_sync.keyvault.tenant_id
  use_oidc             = true
  oidc_token_file_path = var.oidc_token_file

  resource_provider_registrations = "none"
  subscription_id                 = module.global_settings.infra_sync.keyvault.subscription_id
  features {
  }
}

provider "azuread" {
}

module "global_settings" {
  source = "../../../modules/global_settings"
}
