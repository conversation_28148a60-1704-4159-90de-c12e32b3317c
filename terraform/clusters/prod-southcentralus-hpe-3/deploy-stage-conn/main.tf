module "config" {
  source = "../../../modules/orange-clusters-config"
}

locals {
  cluster_name   = basename(dirname(abspath(path.module)))
  cluster_config = module.config.clusters[local.cluster_name]
}

module "data" {
  source = "../../../modules/manifold-cluster-data"
  providers = {
    azurerm = azurerm
  }
  cluster_config = local.cluster_config
}

module "stage-conn" {
  source = "../../../modules/stage-conn"

  providers = {
    azurerm        = azurerm
    azurerm.common = azurerm.common
  }

  config = merge(local.cluster_config, module.data.manifold_cluster_data)
}
