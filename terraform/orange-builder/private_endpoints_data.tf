locals {
  keyvaults = [
    {
      name           = "orange-onboarding-app-kv"
      resource_group = "orange-onboarding"
    },
    {
      name           = "orangetailscalekeys"
      resource_group = "orange-tailscale"
    }
  ]

  storage_accounts = [
    {
      name           = "orangetfstate"
      resource_group = "orange-terraform"
    }
  ]
}

data "azurerm_storage_account" "storage_accounts" {
  for_each            = { for sa in local.storage_accounts : sa.name => sa }
  name                = each.value.name
  resource_group_name = each.value.resource_group
}

data "azurerm_key_vault" "keyvaults" {
  for_each            = { for kv in local.keyvaults : kv.name => kv }
  name                = each.value.name
  resource_group_name = each.value.resource_group
}
