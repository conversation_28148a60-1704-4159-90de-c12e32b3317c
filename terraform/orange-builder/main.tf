module "orange-builder" {
  source = "../modules/1es-build-pool"

  pool_name     = "orange-builder"
  pool_location = "eastus2"

  max_pool_size = 20
  base_image    = "/canonical/ubuntu-24_04-lts/server/latest"
  pool_sku      = "Standard_D8ds_v4"

  organization       = "https://dev.azure.com/project-argos"
  projects           = ["Mimco"]
  contacts           = ["<EMAIL>"]
  enable_nat_gateway = false

  # Pass in Key Vault private endpoints
  keyvault_private_endpoints = [
    for kv in data.azurerm_key_vault.keyvaults : {
      name                 = kv.name
      keyvault_id          = kv.id
      is_manual_connection = false
    }
  ]

  # Pass in Storage Account private endpoints
  storage_private_endpoints = [
    for sa in data.azurerm_storage_account.storage_accounts : {
      name                 = sa.name
      storage_account_id   = sa.id
      is_manual_connection = false
    }
  ]

  # Pass in Snowflake private endpoint
  snowflake_private_endpoint = {
    name                 = "pe-snowflake-pool"
    snowflake_id         = "sf-pvlinksvc-azuksouth.e2f9fa41-cf7e-4e13-8a45-e6f23913990f.uksouth.azure.privatelinkservice"
    is_manual_connection = true 
  }

  wandb_private_endpoint = {
    name                 = "pe-wandb-pool"
    private_link_resource_id = "/subscriptions/c213eb8e-d0e7-4bbb-985a-2f8deac5c1c5/resourceGroups/wandb-msaip/providers/Microsoft.Network/applicationGateways/wandb-msaip-ag"
    is_manual_connection = true
  }

  roles_assignments = local.flattened_role_assignments
}
