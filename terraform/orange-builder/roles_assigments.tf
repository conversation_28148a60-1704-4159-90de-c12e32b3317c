locals {
  assets_role_assignments = [
    {
      resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-uksouth-resources"
      roles       = ["Contributor"]
    },
    {
      resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources"
      roles       = ["Contributor"]
    }
  ]

  users_role_assignments = [
    {
      resource_id = "/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8/resourceGroups/orange-users-resources"
      roles       = ["Contributor"]
    }
  ]

  infra_role_assignments = [
    {
      resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-tailscale/providers/Microsoft.KeyVault/vaults/orangetailscalekeys"
      roles       = ["Key Vault Secrets User"]
    },
    {
      resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-onboarding/providers/Microsoft.KeyVault/vaults/orange-onboarding-app-kv"
      roles       = ["Key Vault Secrets User"]
    },
    {
      resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-terraform/providers/Microsoft.Storage/storageAccounts/orangetfstate"
      roles       = ["Storage Blob Data Contributor"]
    }
  ]
}

locals {
  # Flatten the resource/roles list into individual assignments:
  flattened_role_assignments = flatten([
    for rr in concat(local.assets_role_assignments, local.infra_role_assignments, local.users_role_assignments) : [
      for r in rr.roles : {
        scope = rr.resource_id
        role  = r
      }
    ]
  ])
}

