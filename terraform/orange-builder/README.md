orange builder
---

The orange builder is a managed 1ES Hosted pool that is used for running all iridium automation pipelines. For more info on how the builder is setup refer to [1es-build-pool](../modules/1es-build-pool/README.md)

## Deploy or update

1. You must have access to the [AIPLATFORM-ORANGE-INFRA](https://microsoftservicetree.com/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a) subscription in the Green tenant. The subscription id is `57cd9edb-cae4-478d-9c6a-81ac38d30c4a`.
2. Go to https://aka.ms/pim to activate your role.
3. Login to the green tenant `az login`
4. Terraform plan and apply the changes in the `terraform/orange-builder` directory