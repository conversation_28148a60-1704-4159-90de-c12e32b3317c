#!/bin/bash
set -euo pipefail

# We are always non-interactive
export DEBIAN_FRONTEND=noninteractive

# Logging functions
function __info()
{
    echo >&2 "--- $${1}"
}

function __note()
{
    echo >&2 "!!! NOTE: $${1}"
}

function __progress()
{
    echo >&2 "===== $${1}"
}

function __error()
{
    echo >&2 "**** $${1}"
}

function __warn()
{
    echo >&2 "### WARNING: $${1}"
}

# Environment-configurable variables
INTERVAL="$${TAILSNAIL_INTERVAL:-60}"
INTERFACE="$${TAILSNAIL_INTERFACE:-tailscale0}"
RATE_MBIT="$${TAILSNAIL_RATE_MBIT:-${rate_mbit}}"
MIN_CLASS_ID="$${TAILSNAIL_MIN_CLASS_ID:-10}"
MAX_CLASS_ID="$${TAILSNAIL_MAX_CLASS_ID:-65535}"

# Check for required commands
function check_dependencies() {
    for cmd in jq tailscale tc; do
        if ! command -v "$cmd" &>/dev/null; then
            __error "Required command '$cmd' is not installed or not in PATH."
            exit 1
        fi
    done
}

# Grab the Tailscale status JSON. 
# If the command fails, return non-zero so the caller can skip applying rules.
function get_tailscale_json() {
    local ts_json
    if ! ts_json="$(tailscale status --json 2>/dev/null)"; then
        __warn "Could not retrieve Tailscale status. Tailscale may not be running."
        return 1
    fi
    echo "$ts_json"
}

# Check the backend state from a given JSON string, ensuring it's "Running".
# If not "Running", return non-zero so the caller can skip applying rules.
function check_backend_is_running() {
    local ts_json="$1"
    local backend_state
    backend_state="$(echo "$ts_json" | jq -r '.BackendState // "unknown"')"
    if [[ "$backend_state" != "Running" ]]; then
        __warn "Tailscale BackendState is '$backend_state' (not 'Running')."
        return 1
    fi
    return 0
}

# Extract connected device IPs (IPv4 only) from the Tailscale JSON.
function get_connected_ips_from_json() {
    local ts_json="$1"
    echo "$ts_json" | jq -r '
        .Peer[]
        | select(.Online == true 
            or ((now - (.LastSeen | sub("\\.[0-9]+Z$"; "Z") | fromdateiso8601)) < 3600))
        | .TailscaleIPs[]
        | select(test("^[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+$"))
    '
}

# ------------------------------------------------------------------------------
# FREE CLASS ID MANAGEMENT
# ------------------------------------------------------------------------------
FREE_LIST=()
for ((i=MIN_CLASS_ID; i<=MAX_CLASS_ID; i++)); do
    FREE_LIST+=("$i")
done

NEXT_ID=""

function pop_free_list() {
    if [ $${#FREE_LIST[@]} -eq 0 ]; then
        return 1
    fi
    NEXT_ID="$${FREE_LIST[0]}"
    FREE_LIST=("$${FREE_LIST[@]:1}")
    return 0
}

function push_free_list() {
    local cid="$1"
    FREE_LIST+=("$cid")
}

# ------------------------------------------------------------------------------
# TC RULE MANAGEMENT
# ------------------------------------------------------------------------------

# Declare mapping as an associative array: IP -> class id.
declare -A IP_CLASSID_MAP=()

# Clear any existing tc configuration and set up root qdisc and default class.
function setup_root_qdisc() {
    __info "Clearing existing tc configuration on $${INTERFACE}..."
    tc qdisc del dev "$${INTERFACE}" root 2>/dev/null || true

    __progress "Setting up root qdisc and default class..."
    tc qdisc add dev "$${INTERFACE}" root handle 1: htb default 999
    tc class add dev "$${INTERFACE}" parent 1: classid 1:1 htb rate 1gbit

    # Default class for non-matching traffic - use same rate as per-device limit
    tc class add dev "$${INTERFACE}" parent 1:1 classid 1:999 htb \
        rate "$${RATE_MBIT}"mbit \
        ceil "$${RATE_MBIT}"mbit \
        burst 15k
    tc qdisc add dev "$${INTERFACE}" parent 1:999 handle 999: sfq perturb 10
}

# Add a tc rule for a given IP with a unique class id.
function add_tc_rule() {
    local ip="$1"
    local class_id="$2"

    __info "Adding tc rule for $${ip} with class id $${class_id}"

    tc class add dev "$${INTERFACE}" parent 1:1 classid 1:"$${class_id}" htb \
        rate "$${RATE_MBIT}"mbit \
        ceil "$${RATE_MBIT}"mbit \
        burst 15k

    tc qdisc add dev "$${INTERFACE}" parent 1:"$${class_id}" handle "$${class_id}": sfq perturb 10

    # Outgoing traffic to destination IP
    tc filter add dev "$${INTERFACE}" parent 1: protocol ip prio "$${class_id}" u32 \
        match ip dst "$${ip}/32" flowid 1:"$${class_id}"
}

# Remove a tc rule for a given IP with its class id.
function remove_tc_rule() {
    local ip="$1"
    local class_id="$2"

    __info "Removing tc rule for $${ip} with class id $${class_id}"

    # Remove filters
    tc filter del dev "$${INTERFACE}" parent 1: protocol ip prio "$${class_id}" u32 \
        match ip dst "$${ip}/32" flowid 1:"$${class_id}" 2>/dev/null || true

    # Remove qdisc and class
    tc qdisc del dev "$${INTERFACE}" parent 1:"$${class_id}" handle "$${class_id}:" sfq 2>/dev/null || true
    tc class del dev "$${INTERFACE}" classid 1:"$${class_id}" 2>/dev/null || true

    # Return the class id to the free list
    push_free_list "$class_id"
}

# Update tc rules based on currently connected IPs.
function update_tc_rules() {
    local ts_json="$1"

    # Retrieve current IPs from the JSON
    local -a current_ips=()
    while IFS= read -r ip; do
        current_ips+=("$ip")
    done < <(get_connected_ips_from_json "$ts_json")

    __info "Number of connected IPs: $${#current_ips[@]}"

    # Remove rules for IPs no longer connected
    for ip in "$${!IP_CLASSID_MAP[@]}"; do
        # If this IP is not in the new list, remove its rule
        if [[ ! " $${current_ips[*]} " =~ " $${ip} " ]]; then
            remove_tc_rule "$${ip}" "$${IP_CLASSID_MAP[$${ip}]}"
            unset "IP_CLASSID_MAP[$${ip}]"
        fi
    done

    # Add rules for new IPs
    for ip in "$${current_ips[@]}"; do
        if [[ -z "$${IP_CLASSID_MAP[$${ip}]:-}" ]]; then
            if ! pop_free_list; then
                __error "No more free class IDs available."
                exit 1
            fi
            add_tc_rule "$${ip}" "$${NEXT_ID}"
            IP_CLASSID_MAP["$${ip}"]="$${NEXT_ID}"
        fi
    done

    __progress "TC configuration updated."
}

# ------------------------------------------------------------------------------
# MAIN LOGIC
# ------------------------------------------------------------------------------
function reconcile_devices() {
    while true; do
        # Grab Tailscale JSON once per iteration.
        local ts_json
        if ! ts_json="$(get_tailscale_json)"; then
            # Could not get tailscale status at all
            __warn "Skipping applying any traffic shaping rules this interval."
            sleep "$INTERVAL" || exit 0
            continue
        fi

        # Check the backend state from this single JSON
        if ! check_backend_is_running "$ts_json"; then
            __warn "Skipping applying any traffic shaping rules this interval."
            sleep "$INTERVAL" || exit 0
            continue
        fi

        # If the Tailscale backend is Running, do the shaping steps with the same JSON
        update_tc_rules "$ts_json"

        sleep "$${INTERVAL}" || exit 0
    done
}

setup_root_qdisc
check_dependencies
reconcile_devices
