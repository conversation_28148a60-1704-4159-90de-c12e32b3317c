terraform {
  required_providers {
    kubectl = {
      source = "alekc/kubectl"
    }
  }
}

resource "helm_release" "blobcache" {
  name       = "blobcache"
  namespace  = var.namespace

  repository = "${path.module}/charts"
  chart      = "blobcache"

  set {
    name  = "imageRepository"
    value = var.image_repository
  }

  set {
    name  = "imageTag"
    value = var.image_tag
  }

  set {
    name  = "imagePullSecret"
    value = ""
  }

  set {
    name  = "cluster_name"
    value = var.cluster_name
  }

  set {
    name  = "cluster_region"
    value = var.cluster_region
  }
}
