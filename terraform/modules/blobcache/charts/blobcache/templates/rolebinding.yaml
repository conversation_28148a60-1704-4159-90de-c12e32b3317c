apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "blobcache" . }}
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  namespace: scaling
  name: {{ include "blobcache" . }}
  labels:
    app: {{ include "blobcache" . }}
subjects:
- kind: ServiceAccount
  name: blobcache
roleRef:
  kind: Role
  name: {{ include "blobcache" . }}
  apiGroup: rbac.authorization.k8s.io