apiVersion: v1
kind: Service
metadata:
  name: {{ include "blobcache" . }}
  labels:
    app: {{ include "blobcache" . }}
    prometheus-target: "true"
spec:
  selector:
    app: {{ include "blobcache" . }}
  clusterIP: None
  type: ClusterIP
  ports:
  - name: http
    protocol: TCP
    port: 80
    targetPort: http
  - name: grpc
    protocol: TCP
    port: 9000
    targetPort: grpc
  - name: metrics
    protocol: TCP
    port: 9001
    targetPort: metrics


