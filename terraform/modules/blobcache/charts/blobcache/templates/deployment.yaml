apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "blobcache" . }}
  labels:
    app: {{ include "blobcache" . }}
spec:
  replicas: {{ get .Values.replica_count .Values.cluster_name | default 1  }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  minReadySeconds: 180
  selector:
    matchLabels:
      app: {{ include "blobcache" . }}
  template:
    metadata:
      labels:
        app: {{ include "blobcache" . }}
        pod.isolate.openai.com/logs: "drop"
    spec:
      serviceAccountName: blobcache
      priorityClassName: team-critical
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            app: {{ include "blobcache" . }}
      terminationGracePeriodSeconds: 20
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.avoidSelector | quote }}
                operator: DoesNotExist
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            preference:
              matchExpressions:
              - key: openai.com/team
                operator: In
                values:
                - platform-services

      containers:
      - name: blobcacheinst
        {{- if hasKey .Values "imageTag" }}
        image: {{ printf "%s:%s" .Values.imageRepository .Values.imageTag }}
        {{- else }}
        image: {{ printf "%s@%s" .Values.imageRepository .Values.imageSHA }}
        {{- end }}
        imagePullPolicy: Always
        command: [
            "blobcache-server",
            "--grpc-port",
            "9000",
            "--http-port",
            "8080",
            "--metrics-port",
            "9001",
            "--max-file-size-mb",
            "1024",
            "--blobcache-service-cluster",
            {{ include "blobcache" . | quote}},
            ]
        env:
          - name: MY_POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: MY_HOST_IP
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: MY_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: MY_POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: MY_CLUSTER_NAME
            value: {{ .Values.cluster_name }}
          {{- if hasKey .Values "imageTag" }}
          - name: MY_IMAGE_TAG
            value: {{ .Values.imageTag | quote }}
          {{- else }}
          - name: MY_IMAGE_TAG
            value: {{ .Values.imageSHA | quote }}
          {{- end }}
              
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9000
          name: grpc
        - containerPort: 9001
          name: metrics
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        lifecycle:
          postStart:
            exec:
              command: ["/bin/bash", "-c", "service cron start"]
        resources:
          limits:
            cpu: {{ get .Values.cpu .Values.cluster_name | default 4 | quote }}
            memory: {{ get .Values.memory .Values.cluster_name | default "24G" | quote }}
            ephemeral-storage: {{ .Values.disk | quote }}
          requests:
            cpu: {{ get .Values.cpu .Values.cluster_name | default 4 | quote }}
            memory: {{ get .Values.memory .Values.cluster_name | default "24G" | quote }}
            ephemeral-storage: {{ .Values.disk | quote }}
      tolerations:
      - key: openai.com/team
        operator: Equal
        value: platform-services
        effect: NoSchedule
      {{- if .Values.imagePullSecret }}
      imagePullSecrets:
      - name: {{ .Values.imagePullSecret | quote }}
      {{- end }}
