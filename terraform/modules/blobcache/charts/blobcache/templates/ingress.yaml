apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "blobcache" . }}
  labels:
    app: {{ include "blobcache" . }}
  annotations:
    nginx.ingress.kubernetes.io/auth-cache-key: "$oauth_cookie_cache_key$http_authorization"
    nginx.ingress.kubernetes.io/auth-response-headers: "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
    nginx.ingress.kubernetes.io/auth-url: {{ printf "https://oai-azure-auth-proxy.int.%s.%s.openai.org/oauth2/auth" .Values.cluster_name .Values.env }}
    nginx.ingress.kubernetes.io/auth-signin: {{ printf "https://oauth2-proxy-core-group.aad-auth.int.%s.%s.openai.org/oauth2/start?rd=https://$http_host$request_uri" .Values.cluster_name .Values.env }}
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      proxy_set_header X-User-Email $upstream_http_x_auth_request_email;
      add_header Cache-Control "no-store";
    nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "128k"
    nginx.ingress.kubernetes.io/proxy-busy-buffers-size: "512k"
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    kubernetes.io/ingress.global-static-ip-name: {{ printf "%s-kubernetes-ingress" .Values.cluster_name }}
spec:
  ingressClassName: internal-nginx
  rules:
  - host: {{ printf "%s.int.%s.%s.openai.org" (include "blobcache" .) .Values.cluster_name .Values.env }}
    http:
      paths:
      - path: /blobcache.blobcache_service.BlobcacheService/GetEncryptionKeyUri
        pathType: Exact
        backend:
          service:
            name: {{ include "blobcache" . }}
            port:
              name: grpc
  tls:
  - hosts:
    - {{ printf "%s.int.%s.%s.openai.org" (include "blobcache" .) .Values.cluster_name .Values.env }}