variable "namespace" {
  description = "The namespace to create and deploy the Kubecache Helm chart into"
  type        = string
}

variable "cluster_name" {
  description = "The name of the AKS cluster"
  type        = string
}

variable "cluster_region" {
  description = "The region name (e.g. uksouth) that should be used for naming storage paths."
  type        = string
}

variable "image_repository" {
  description = "The repository to pull the image from"
  type        = string
  default     = "iridiumsdc.azurecr.io/infra/blobcache"
}

variable "image_tag" {
  description = "The tag of the image to pull"
  type        = string
  default     = "0.1"
}
