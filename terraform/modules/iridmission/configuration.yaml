objectConfigs:
  pod:
    - matchLabels:
        brix.openai.com/pod: ".*"
      matchAnnotations: {}
      ops:
%{ if workload_auth_type == "certificates" ~}
      - op: add
        path: /spec/volumes/-
        value: |
          {
            "name":"sp-cert",
            "secret":{
              "secretName":"azure-service-principal-cert"
            }
          }
      - op: add
        path: /spec/containers/0/volumeMounts/-
        value: |
          {
            "name":"sp-cert",
            "mountPath":"/etc/iridium",
            "readOnly":true
          }
%{ endif ~}
# Only inject fluent-bit config, mounts, and sidecar if enabled
%{ if enable_file_logs_collector }
%{ for volume_def in shared_volume_definitions ~}
      - op: ${volume_def.op}
        path: ${volume_def.path}
        value: |
          ${volume_def.value}
%{ endfor ~}
      - op: add
        path: /spec/volumes/-
        value: |
          ${fluent_bit_config_volume.value}
%{ for mount_def in main_container_volume_mounts ~}
      - op: ${mount_def.op}
        path: ${mount_def.path}
        value: |
          ${mount_def.value}
%{ endfor ~}
      - op: add
        path: /spec/containers/-
        value: |
          ${jsonencode(fluent_bit_container)}
%{ endif }
%{ if override_cpu_isalevel != "" }
      # Some clusters have Sapphire Rapids CPUs with Intel's AMX extension. oaipkg will detect that and try to find certain wheels like:
      #   cpu_optimizer_kernels-0.1.0+tree.tdfdf455250.cpu.amx.torch.241.os.noble-cp311-cp311-linux_x86_64.whl
      # (note the "amx"), but we don't have those binaries currently. For those cases we can set e.g. OAIPKG_CPU_ISALEVEL=avx2
      # (all of our GPU nodes support at least AVX2, and we have those binaries).
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name":"OAIPKG_CPU_ISALEVEL",
            "value":"${override_cpu_isalevel}"
          }
%{ endif }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name":"EXTRA_CLUSTERS_JSON",
            "value":"[{\"name\":\"${cluster}\",\"region\":\"${cluster_region}\",\"env\":\"dev\"}]"
          }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name":"APPLIED_STORAGE_MAP",
            "value":"${applied_storage_map}"
          }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name": "OPENAI_ENCODINGS_BASE_DATA_GYM",
            "value": "${openai_encodings_base_data_gym}"
          }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name": "DATA_GYM_BACKENDS",
            "value": "${data_gym_backends}"
          }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name": "IRIDIUM_AZURE_STORAGE_ACCOUNT_MAPPING",
            "value": "{\"oaiartifacts\":\"${remap_oai_artifact_storage_account}\", \"oaimichael\":\"${remap_oai_data_storage_account}\", \"oaiphx1\":\"${remap_oai_data_storage_account}\"}"
          }
# Conditionally add these entries if ${nccl_host_topology_path} is set
%{ if nccl_host_topology_path != "" }
    - matchLabels: {}
      matchAnnotations:
        brix.openai.com/infiniband: enabled
        brix.openai.com/target-skus: (${gpu_skus}).*
      ops:
      - op: add
        path: /spec/volumes/-
        value: |
          {
            "name": "topo-volume",
            "hostPath": {
              "path": "${nccl_host_topology_path}",
              "type": "File"
            }
          }
      - op: add
        path: /spec/containers/0/volumeMounts/-
        value: |
          {
            "name": "topo-volume",
            "mountPath": "/tmp/nccl-topo.xml",
            "readOnly": true
          }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name": "NCCL_TOPO_FILE",
            "value": "/tmp/nccl-topo.xml"
          }
%{ endif }
%{ if tailsnail_enabled }
    - matchLabels:
        tailscale.orange.internal/subnet-router: "true"
      matchAnnotations: {}
      ops:
      - op: add
        path: /spec/volumes/-
        value: |
          {
            "name": "tailscale-socket",
            "emptyDir": {}
          }
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name": "TS_SOCKET",
            "value": "/var/run/tailscale/tailscaled.sock"
          }
      - op: add
        path: /spec/containers/0/volumeMounts/-
        value: |
          {
            "name": "tailscale-socket",
            "mountPath": "/var/run/tailscale"
          }
      - op: add
        path: /spec/volumes/-
        value: |
          {
            "name": "tailsnail-script",
            "configMap": {
              "name": "tailsnail-script",
              "defaultMode": ${yamldecode("0o755")}
            }
          }
      - op: add
        path: /spec/containers/-
        value: |
          {
            "name": "tailsnail",
            "image": "iridiumsdc.azurecr.io/infra/tailimit:latest",
            "imagePullPolicy": "Always",
            "env": [
              {
                "name": "TS_SOCKET",
                "value": "/var/run/tailscale/tailscaled.sock"
              }
            ],
            "volumeMounts": [
              {
                "name": "tailscale-socket",
                "mountPath": "/var/run/tailscale"
              },
              {
                "name": "tailsnail-script",
                "mountPath": "/usr/local/bin/tailsnail.sh",
                "subPath": "tailsnail.sh"
              }
            ],
            "securityContext": {
              "privileged": true
            },
            "command": [
              "/usr/local/bin/tailsnail.sh"
            ]
          }
%{ endif }
    - matchLabels:
        orange-devbox: ".*"
      matchAnnotations: {}
      ops:
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name":"EXTRA_CLUSTERS_JSON",
            "value":"[{\"name\":\"${cluster}\",\"region\":\"${cluster_region}\",\"env\":\"dev\"}]"
          }
# Only inject fluent-bit config, mounts, and sidecar if enabled
%{ if enable_file_logs_collector }
%{ for volume_def in shared_volume_definitions ~}
      - op: ${volume_def.op}
        path: ${volume_def.path}
        value: |
          ${volume_def.value}
%{ endfor ~}
      - op: add
        path: /spec/volumes/-
        value: |
          ${fluent_bit_config_volume.value}
%{ for mount_def in main_container_volume_mounts ~}
      - op: ${mount_def.op}
        path: ${mount_def.path}
        value: |
          ${mount_def.value}
%{ endfor ~}
      - op: add
        path: /spec/containers/-
        value: |
          ${jsonencode(fluent_bit_container)}
%{ endif }