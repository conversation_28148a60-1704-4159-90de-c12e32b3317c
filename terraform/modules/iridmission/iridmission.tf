resource "kubernetes_service_account_v1" "iridmission-service-account" {
  depends_on = [kubernetes_namespace.iridmission]

  metadata {
    name      = "iridmission"
    namespace = "iridmission"
    labels    = { app = "iridmission" }
  }
}

resource "kubernetes_cluster_role_v1" "iridmission-cluster-role" {
  depends_on = [kubernetes_namespace.iridmission]

  metadata {
    name = "iridmission"
  }

  rule {
    verbs      = ["get", "list", "watch", "update", "patch"]
    api_groups = [""]
    resources  = ["pods"]
  }
}

resource "kubernetes_cluster_role_binding_v1" "iridmission-cluster-role-binding" {
  depends_on = [
    kubernetes_service_account_v1.iridmission-service-account,
    kubernetes_cluster_role_v1.iridmission-cluster-role,
  ]
  metadata {
    name = "iridmission"
  }
  subject {
    kind      = "ServiceAccount"
    name      = "iridmission"
    namespace = "iridmission"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "iridmission"
  }
}

data "kubernetes_resources" "skus" {
  api_version = "brix.openai.com/v1alpha1"
  kind        = "SKU"
}

locals {
  gpu_skus = [for sku in data.kubernetes_resources.skus.objects : sku.metadata.name if sku.spec.type == "gpu"]
}

resource "kubernetes_config_map" "iridmission-config-map" {
  depends_on = [
    kubernetes_namespace.iridmission
  ]
  metadata {
    name      = "iridmission-configuration"
    namespace = "iridmission"
    labels = {
      app = "iridmission"
    }
  }
  data = {
    "configuration.yaml" = templatefile("${path.module}/configuration.yaml", {
      cluster                            = var.cluster_name,
      cluster_region                     = var.cluster_region,
      nccl_host_topology_path            = var.nccl_host_topology_path,
      data_gym_backends                  = var.data_gym_backends
      openai_encodings_base_data_gym     = var.openai_encodings_base_data_gym
      remap_oai_artifact_storage_account = var.remap_oai_artifact_storage_account
      remap_oai_data_storage_account     = var.remap_oai_data_storage_account
      applied_storage_map                = var.applied_storage_map
      workload_auth_type                 = var.workload_auth_type
      tailsnail_enabled                  = var.tailsnail_enabled
      override_cpu_isalevel              = var.override_cpu_isalevel
      gpu_skus                           = join("|", local.gpu_skus)
      fluent_bit_version                 = var.fluent_bit_version
      # Pass local values directly as template variables
      shared_volume_definitions          = local.shared_volume_definitions
      main_container_volume_mounts       = local.main_container_volume_mounts
      fluent_bit_config_volume           = local.fluent_bit_config_volume
      fluent_bit_container               = local.fluent_bit_container
      enable_file_logs_collector         = var.enable_file_logs_collector
    })
  }
}

resource "kubernetes_service_v1" "iridmission-service" {
  depends_on = [
    kubernetes_deployment_v1.iridmission-deployment
  ]
  metadata {
    name      = "iridmission"
    namespace = "iridmission"
    labels = {
      app = "iridmission"
    }
  }
  spec {
    port {
      name = "https"
      port = 443
    }
    port {
      name = "http"
      port = 8080
    }
    selector = { app = "iridmission" }
  }
}

resource "kubectl_manifest" "iridmission-issuer" {
  yaml_body = file("${path.module}/certificate-issuer.yaml")
}

resource "kubectl_manifest" "iridmission-certificate" {
  depends_on = [
    kubectl_manifest.iridmission-issuer
  ]
  yaml_body = file("${path.module}/certificate.yaml")
}

resource "kubernetes_mutating_webhook_configuration" "iridmission-mutating-webhook" {
  metadata {
    name        = "iridmission"
    labels      = { app = "iridmission" }
    annotations = { "cert-manager.io/inject-ca-from" = "iridmission/iridmission" }
  }

  lifecycle {
    ignore_changes = [
      webhook[0].client_config[0].ca_bundle,
      webhook[1].client_config[0].ca_bundle,
      webhook[2].client_config[0].ca_bundle,
    ]
  }

  webhook {
    name                      = "pods.iridmission.genai.ms"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "iridmission"
        name      = "iridmission"
        path      = "/mutate"
      }
    }
    rule {
      api_groups   = [""]
      api_versions = ["*"]
      operations   = ["CREATE"]
      resources    = ["pods"]
    }
    object_selector {
      match_expressions {
        key      = "brix.openai.com/pod"
        operator = "Exists"
      }
    }
    namespace_selector {
      match_expressions {
        key      = "iridmission"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
    }
  }

  dynamic "webhook" {
    for_each = var.tailsnail_enabled ? [1] : []
    content {
      name                      = "pods.tailscale.genai.ms"
      admission_review_versions = ["v1"]
      side_effects              = "None"
      client_config {
        service {
          namespace = "iridmission"
          name      = "iridmission"
          path      = "/mutate"
        }
      }
      rule {
        api_groups   = [""]
        api_versions = ["*"]
        operations   = ["CREATE"]
        resources    = ["pods"]
      }
      object_selector {
        match_expressions {
          key      = "tailscale.orange.internal/subnet-router"
          operator = "In"
          values   = ["true"]
        }
      }
      namespace_selector {
        match_expressions {
          key      = "kubernetes.io/metadata.name"
          operator = "In"
          values   = ["tailscale"]
        }
        match_expressions {
          key      = "kubernetes.azure.com/managedby"
          operator = "NotIn"
          values   = ["aks"]
        }
        match_expressions {
          key      = "control-plane"
          operator = "NotIn"
          values   = ["true"]
        }
      }
    }
  }

  webhook {
    name                      = "orange-devbox.iridmission.genai.ms"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "iridmission"
        name      = "iridmission"
        path      = "/mutate"
      }
    }
    rule {
      api_groups   = [""]
      api_versions = ["*"]
      operations   = ["CREATE"]
      resources    = ["pods"]
    }
    object_selector {
      match_expressions {
        key      = "orange-devbox"
        operator = "Exists"
      }
    }
    namespace_selector {
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
    }
  }
}

resource "kubernetes_deployment_v1" "iridmission-deployment" {
  depends_on = [
    kubernetes_cluster_role_v1.iridmission-cluster-role,
    kubernetes_cluster_role_binding_v1.iridmission-cluster-role-binding,
    kubernetes_service_account_v1.iridmission-service-account,
    kubernetes_config_map.iridmission-config-map,
  ]
  metadata {
    name      = "iridmission"
    namespace = "iridmission"
    labels    = { app = "iridmission" }
  }
  spec {
    replicas          = 3
    min_ready_seconds = 10
    selector {
      match_labels = { app = "iridmission" }
    }
    template {
      metadata {
        labels = { app = "iridmission" }
        annotations = {
          "iridmission-configuration-hash" = sha1(jsonencode(kubernetes_config_map.iridmission-config-map.data))
        }
      }
      spec {
        volume {
          name = "iridmission-configuration"
          config_map {
            name = "iridmission-configuration"
          }
        }
        volume {
          name = "iridmission-certificate"
          secret {
            secret_name = "iridmission-tls"
          }
        }
        container {
          name  = "main"
          image = "${local.repository}:${local.version}"
          args = [
            "--port=443",
            "--config-path=/etc/iridmission/config/configuration.yaml",
            "--tls-key-path=/etc/iridmission/tls/tls.key",
            "--tls-cert-path=/etc/iridmission/tls/tls.crt",
          ]
          port {
            name           = "https"
            container_port = "443"
          }
          volume_mount {
            name       = "iridmission-configuration"
            mount_path = "/etc/iridmission/config"
          }
          volume_mount {
            name       = "iridmission-certificate"
            mount_path = "/etc/iridmission/tls"
            read_only  = true
          }
          readiness_probe {
            http_get {
              path   = "/live"
              port   = 443
              scheme = "HTTPS"
            }
          }
          liveness_probe {
            http_get {
              path   = "/live"
              port   = 443
              scheme = "HTTPS"
            }
            initial_delay_seconds = 30      # Give pod time to start
            period_seconds        = 300     # Check every 5 minutes
            timeout_seconds       = 10      # Allow 10 seconds for response
            success_threshold     = 1       # One success is enough
            failure_threshold     = 2       # Two failures before restart
          }
        }
        service_account_name = "iridmission"
        affinity {
          pod_anti_affinity {
            required_during_scheduling_ignored_during_execution {
              label_selector {
                match_expressions {
                  key      = "app"
                  operator = "In"
                  values   = ["iridmission"]
                }
              }
              topology_key = "kubernetes.io/hostname"
            }
          }
        }
        priority_class_name = "system-cluster-critical"
      }
    }
    strategy {
      type = "Recreate"
    }
  }
}
