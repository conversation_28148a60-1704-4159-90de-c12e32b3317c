locals {
  # Generate volume definitions
  shared_volume_definitions = [
    for volume_name, config in var.shared_volumes : {
      op   = "add"
      path = "/spec/volumes/-"
      value = jsonencode({
        name     = volume_name
        emptyDir = {}
      })
    }
  ]

  # Generate main container volume mounts
  main_container_volume_mounts = [
    for volume_name, config in var.shared_volumes : {
      op   = "add"
      path = "/spec/containers/0/volumeMounts/-"
      value = jsonencode({
        name      = volume_name
        mountPath = config.mount_path
        readOnly  = config.read_only_main
      })
    }
  ]

  # Generate fluent-bit sidecar volume mounts
  fluent_bit_volume_mounts = [
    for volume_name, config in var.shared_volumes : {
      name      = volume_name
      mountPath = config.mount_path
      readOnly  = config.read_only_sidecar
    }
  ]

  # Fluent-bit container definition
  fluent_bit_container = {
    name  = "file-logs-collector"
    image = "nexusstaticacr.azurecr.io/fluent/fluent-bit:${var.fluent_bit_version}"
    args = [
      "--config=/fluent-bit/etc/fluent-bit.yaml"
    ]
    volumeMounts = concat(
      local.fluent_bit_volume_mounts,
      [
        {
          name      = "fluent-bit-config"
          mountPath = "/fluent-bit/etc"
          readOnly  = true
        }
      ]
    )
  }

  # ConfigMap volume for fluent-bit config
  fluent_bit_config_volume = {
    op   = "add"
    path = "/spec/volumes/-"
    value = jsonencode({
      name = "fluent-bit-config"
      configMap = {
        name = "fluent-bit-config"
      }
    })
  }
}
