variable "cluster_name" {
  description = "Deployment cluster name"
  type        = string
}

variable "cluster_region" {
  description = "Deployment region"
  type        = string
}

variable "nccl_host_topology_path" {
  description = "Path to the NCCL host topology file on the host"
  type        = string
  default     = ""
}

variable "data_gym_backends" {
  description = "Value for DATA_GYM_BACKENDS"
  type        = string
  default     = "azure"
}

variable "openai_encodings_base_data_gym" {
  description = "Value for OPENAI_ENCODINGS_BASE_DATA_GYM"
  type        = string
}

variable "remap_oai_artifact_storage_account" {
  description = "Account to redirect az://oaiartifacts to"
  type        = string
}

variable "remap_oai_data_storage_account" {
  description = "Account to redirect az://oai<PERSON><PERSON><PERSON> and az://oaiphx1 to"
  type        = string
}

variable "applied_storage_map" {
  description = "value for APPLIED_STORAGE_MAP"
  type        = string
}

variable "workload_auth_type" {
  description = "Whether to use certificates or secrets for workload to user sp authentication"
  type        = string
  validation {
    condition     = contains(["certificates", "secrets"], var.workload_auth_type)
    error_message = "workload_auth_type must be either 'certificates' or 'secrets'"
  }
}

variable "tailsnail_enabled" {
  description = "Whether to enable Tailsnail rate limiting on Tailscale subnet routers"
  type        = bool
}

variable "override_cpu_isalevel" {
  description = "Sets OAIPKG_CPU_ISALEVEL to the specified value (e.g. avx2, avx512)"
  type        = string
  default     = ""
}

variable "enable_file_logs_collector" {
  description = "Enable fluent-bit sidecar for file logs"
  type        = bool
  default     = true
}

variable "shared_volumes" {
  description = "Configuration for shared volumes between main container and fluent-bit"
  type = map(object({
    mount_path = string
    read_only_main = optional(bool, false)
    read_only_sidecar = optional(bool, true)
  }))
  default = {
    "shared-var-logs" = {
      mount_path = "/var/log"
      read_only_main = false
      read_only_sidecar = true
    }
    "shared-tw-logs" = {
      mount_path = "/tmp/tw"
      read_only_main = false
      read_only_sidecar = true
    }
    "shared-ray-logs" = {
      mount_path = "/tmp/ray"
      read_only_main = false
      read_only_sidecar = true
    }
    "shared-bus-logs" = {
      mount_path = "/tmp/bus-shooter"
      read_only_main = false
      read_only_sidecar = true
    }
    "shared-rlogs" = {
      mount_path = "/tmp/rlogs"
      read_only_main = false
      read_only_sidecar = true
    }
  }
}

variable "fluent_bit_version" {
  description = "Version tag for the fluent-bit container image"
  type        = string
  default     = "4.0.3"
}
