variable "name" {
  type        = string
  description = "Azure keyvault name"
}

variable "resource_group_name" {
  type        = string
  description = "Azure resource group name"
}

variable "location" {
  type        = string
  description = "Azure location"
}

variable "tenant_id" {
  type        = string
  description = "Azure tenant id"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resource"
  default     = {}
}

variable "private_dns_zones" {
  description = "A map of private DNS zones with their configurations"
  default     = {}
  type = map(object({
    short_name       = string
    resource_group   = string
    subresource_name = string
  }))
}

variable "virtual_networks" {
  description = "A list of virtual networks with their configurations"
  default     = {}
  type = map(object({
    virtual_network_id   = string
    virtual_network_name = string
    subnet_id            = string
    subnet_name          = string
    resource_group_name  = string
    location             = string
  }))
}

variable "role_assignments" {
  type        = map(set(string))
  description = "role assignment definition name as the key and a set of user/service principal object ids"
  default     = {}
}

variable "group_role_assignments" {
  type        = map(set(string))
  description = "role assignment definition name as the key and a set of group object ids"
  default     = {}
}

variable "builder_access" {
  type        = bool
  description = "Whether to allow builder access to the key vault"
  default     = false
}

variable "builder_access_service" {
  type        = string
  description = "Service to grant builder access to"
  default     = "vault"
}

variable "builder_role_assignments" {
  type        = set(string)
  description = "Role assignments to grant to the builder identity"
  default     = ["Key Vault Secrets User"]
}