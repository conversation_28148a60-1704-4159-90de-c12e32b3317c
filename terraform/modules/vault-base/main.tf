module "global" {
  source = "../global_settings"
}
resource "azurerm_key_vault" "vault" {
  name                = var.name
  resource_group_name = var.resource_group_name
  location            = var.location
  tenant_id           = var.tenant_id
  sku_name            = "premium"

  tags = var.tags

  enable_rbac_authorization = true

  soft_delete_retention_days = 90
  purge_protection_enabled   = true

  public_network_access_enabled = false

  lifecycle {
    prevent_destroy = true
  }
}

locals {
  role_assignments = merge([
    for role, object_ids in var.role_assignments : {
      for object_id in object_ids : "${role}-${object_id}" => {
        object_id            = object_id
        role_definition_name = role
      }
    }
  ]...)

  group_role_assignments = merge([
    for role, object_ids in var.group_role_assignments : {
      for object_id in object_ids : "${role}-${object_id}" => {
        object_id            = object_id
        role_definition_name = role
      }
    }
  ]...)
}

resource "azurerm_role_assignment" "permissions" {
  for_each = local.role_assignments

  scope                = azurerm_key_vault.vault.id
  role_definition_name = each.value.role_definition_name
  principal_id         = each.value.object_id
}

resource "azurerm_role_assignment" "group_permissions" {
  for_each = local.group_role_assignments

  scope                = azurerm_key_vault.vault.id
  role_definition_name = each.value.role_definition_name
  principal_id         = each.value.object_id
  principal_type       = "Group"
}

module "vault_secmon_audit" {
  source = "../aoai-secmon-audit"
  providers = {
    azurerm = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  target_resource_id = azurerm_key_vault.vault.id
}

module "builder-access" {
  providers = {
    azurerm = azurerm.infra
  }
  count  = var.builder_access ? 1 : 0
  source = "../orange-builder-access"

  name             = azurerm_key_vault.vault.name
  dns_zone         = module.global.private_dns_zones[var.builder_access_service]
  resource_id      = azurerm_key_vault.vault.id
  role_assignments = var.builder_role_assignments
}
