

data "azuread_client_config" "current" {}

# App Registration
resource "azuread_application" "infra_app" {
  display_name                 = "${var.infra_alias}-${var.app_name_suffix}"
  owners                       = [data.azuread_client_config.current.object_id]
  service_management_reference = var.service_tree_id
}

# Service Principal
resource "azuread_service_principal" "infra_sp" {
  depends_on = [azuread_application.infra_app]
  client_id  = azuread_application.infra_app.client_id
  owners     = [data.azuread_client_config.current.object_id]
}

resource "azuread_application_password" "infra_sp_password" {
  depends_on     = [azuread_application.infra_app, azuread_service_principal.infra_sp]
  application_id = azuread_application.infra_app.id
}

# We store the app ID and secret into a keyvault; separate automation
# copies them into cluster secrets

resource "azurerm_key_vault_secret" "infra_kv_clientid" {
  name         = "${var.infra_alias}-clientid"
  value        = azuread_application.infra_app.client_id
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "infra_kv_secret" {
  name         = "${var.infra_alias}-secret"
  value        = azuread_application_password.infra_sp_password.value
  key_vault_id = var.key_vault_id
}
