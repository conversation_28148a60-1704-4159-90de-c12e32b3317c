terraform {
  required_providers {
    azuread = "~> 2.37.0"
    azurerm = "4.19.0"
  }
}

data "azuread_client_config" "current" {}

module "global_settings" {
  source = "../global_settings"
}

resource "azuread_application" "auth" {

  display_name     = "Orange LOGIN"
  sign_in_audience = "AzureADMyOrg"

  service_management_reference = var.service_tree_id

  web {
    redirect_uris = toset(
      concat(
        [for name in keys(module.global_settings.cluster_aks_issuer_url) : "https://oai-azure-auth-proxy.int.${name}.dev.openai.org/oauth2/callback"],
        [for name in keys(module.global_settings.cluster_aks_issuer_url) : "https://oauth2-proxy-core-group.aad-auth.int.${name}.dev.openai.org/oauth2/callback"],
      )
    )
  }

  fallback_public_client_enabled = true

  public_client {
    redirect_uris = ["http://localhost"]
  }

  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000"

    resource_access {
      id   = "37f7f235-527c-4136-accd-4a02d197296e"
      type = "Scope"
    }

    resource_access {
      id   = "14dad69e-099b-42c9-810b-d002981feec1"
      type = "Scope"
    }
  }

  group_membership_claims = ["SecurityGroup"]

  optional_claims {
    id_token {
      name = "groups"
    }
  }

  owners = [
    data.azuread_client_config.current.object_id,
  ]
}

output "application_id" {
  description = "login application id"
  value       = azuread_application.auth.application_id

  depends_on = [azuread_application.auth]
}

resource "azuread_application_federated_identity_credential" "fedcred" {
  for_each              = module.global_settings.cluster_aks_issuer_url
  application_object_id = azuread_application.auth.object_id
  display_name          = "oauth2-proxy-${each.key}"
  description           = "workload identity for oauth2-proxy of ${each.key}"
  audiences             = ["api://AzureADTokenExchange"]
  issuer                = each.value
  subject               = "system:serviceaccount:oauth2-proxy:oauth2-proxy" # set proper NS and SA name
}
