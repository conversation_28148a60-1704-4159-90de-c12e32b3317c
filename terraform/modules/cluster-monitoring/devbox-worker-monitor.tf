resource "kubernetes_manifest" "devbox-worker-monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-devbox-worker-monitor"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "any" = true
      }
      "selector" = {
        "matchExpressions" = [
          {
            "key"      = "torchflow.openai.com/component"
            "operator" = "In"
            "values"   = ["ray-devbox"]
          }
        ]
      }
      "podMetricsEndpoints" = [
        {
          "targetPort" = "2112"
        }
      ]
    }
  }
}
