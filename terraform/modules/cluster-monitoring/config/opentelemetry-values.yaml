
mode: "daemonset"

config:
  processors:
    batch: {}
    # Default memory limiter configuration for the collector based on k8s resource limits.
    memory_limiter:
      # check_interval is the time between measurements of memory usage.
      check_interval: 5s
      # By default limit_mib is set to 80% of ".Values.resources.limits.memory"
      limit_percentage: 80
      # By default spike_limit_mib is set to 25% of ".Values.resources.limits.memory"
      spike_limit_percentage: 25
  exporters:
    prometheus:
      endpoint: ${env:MY_POD_IP}:8889
      namespace: default
    debug:
      verbosity: basic
  receivers:
    jaeger: null
    prometheus: null
    zipkin: null
    otlp:
      protocols:
        grpc:
          endpoint: "0.0.0.0:18306"
        http:
          endpoint: "0.0.0.0:4318"
  service:
    extensions:
      - health_check
    pipelines:
      metrics:
        exporters:
          - prometheus
        processors:
          - memory_limiter
          - batch
        receivers:
          - otlp
      logs: 
        exporters:
          - debug
        processors:
          - memory_limiter
          - batch
        receivers:
          - otlp
      traces: null
ports:
  jaeger-compact:
    enabled: false
  jaeger-thrift:
    enabled: false
  jaeger-grpc:
    enabled: false
  zipkin:
    enabled: false
  otlp:
    enabled: true
    containerPort: 18306
    hostPort: 18306
    protocol: TCP
    # nodePort: 30317
    appProtocol: grpc
  otlp-http:
    enabled: true
    containerPort: 4318
    hostPort: 4318
    protocol: TCP
  prometheus:
    enabled: true
    containerPort: 8889
    protocol: TCP
image:
  # If you want to use the core image `otel/opentelemetry-collector`, you also need to change `command.name` value to `otelcol`.
  repository: "nexusstaticacr.azurecr.io/otel/opentelemetry-collector"
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "0.122.1"
  # When digest is set to a non-empty value, images will be pulled by digest (regardless of tag value).
  digest: ""
imagePullSecrets: []

# OpenTelemetry Collector executable
command:
  name: "otelcol"
  extraArgs: []

tolerations:
  - key: "nvidia.com/gpu"
    operator: "Exists"

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8889"
  prometheus.io/path: "/metrics"