resource "kubernetes_manifest" "brix-state-metrics-monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-brix-state-metrics"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "matchNames" = ["system"]
      }
      "selector" = {
        "matchLabels" = {
          "app"       = "brix"
          "component" = "state-metrics"
        }
      }
      "podMetricsEndpoints" = [
        {
          "port" = "http"
          "path" = "/metrics"
        }
      ]
    }
  }
}

resource "kubernetes_manifest" "brix-operator-monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-brix-operator"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "matchNames" = ["system"]
      }
      "selector" = {
        "matchLabels" = {
          "app"       = "brix"
          "component" = "operator"
        }
      }
      "podMetricsEndpoints" = [
        {
          "port" = "metrics"
          "path"       = "/metrics"
        }
      ]
    }
  }
}