resource "kubernetes_service_account_v1" "redis-exporter-service-account" {
  count = var.enable_redis_exporter ? 1 : 0
  metadata {
    name      = "redis-exporter"
    namespace = var.monitoring_namespace.metadata[0].name
    labels    = { app = "redis-exporter" }
  }
}

resource "kubernetes_service" "redis-exporter" {
  count = var.enable_redis_exporter ? 1 : 0
  metadata {
    name      = "redis-exporter"
    namespace = var.monitoring_namespace.metadata[0].name
    labels    = { app = "redis-exporter" }
  }

  spec {
    selector = {
      app = "redis-exporter"
    }

    port {
      name        = "metrics"
      port        = 9121
      target_port = 9121
    }
  }
}

resource "kubernetes_deployment_v1" "redis-exporter-deployment" {
  count = var.enable_redis_exporter ? 1 : 0
  depends_on = [
    kubernetes_service_account_v1.redis-exporter-service-account,
  ]
  metadata {
    name      = "redis-exporter"
    namespace = var.monitoring_namespace.metadata[0].name
    labels    = { app = "redis-exporter" }
  }
  spec {
    replicas = 1
    selector {
      match_labels = { app = "redis-exporter" }
    }
    template {
      metadata {
        labels = {
          app                            = "redis-exporter"
          "openai.com/prometheus-target" = "true"
        }
      }
      spec {
        container {
          name  = "redis-exporter"
          image = "iridiumsdc.azurecr.io/infra/redis-exporter:v1.67.0-alpine"
          
          port {
            name           = "metrics"
            container_port = 9121
          }

          env {
            name  = "REDIS_EXPORTER_IS_CLUSTER"
            value = "false"
          }
          
          env {
            name  = "REDIS_EXPORTER_REDIS_ONLY_METRICS"
            value = "true" # Only export Redis metrics, exclude Go runtime metrics
          }
          
          env {
            name = "REDIS_ADDR"
            value_from {
              secret_key_ref {
                name = kubernetes_secret.redis-connection[0].metadata[0].name
                key  = "redis-addr"
              }
            }
          }

          liveness_probe {
            http_get {
              path = "/health"
              port = "metrics"
            }
            initial_delay_seconds = 30
            period_seconds        = 30
          }

          readiness_probe {
            http_get {
              path = "/health"
              port = "metrics"
            }
            initial_delay_seconds = 5
            period_seconds        = 10
          }
        }

        service_account_name = "redis-exporter"
        
        toleration {
          key      = "openai.com/team"
          operator = "Equal"
          value    = "infra"
          effect   = "NoSchedule"
        }
        
        priority_class_name = "system-cluster-critical"
      }
    }
  }
}

# Data source to read Redis connection from harmony Key Vault
data "azurerm_key_vault_secret" "harmony_redis_secret" {
  count        = var.enable_redis_exporter ? 1 : 0
  name         = "redis-oaiharmony"
  key_vault_id = var.harmony_vault.id
  provider     = azurerm.infra-secret-reader
}

# Parse the Redis connection string from Key Vault
locals {
  # Only parse if redis exporter is enabled and secret exists
  redis_secret_parts = var.enable_redis_exporter && length(data.azurerm_key_vault_secret.harmony_redis_secret) > 0 ? split(":", data.azurerm_key_vault_secret.harmony_redis_secret[0].value) : []
  
  # Extract components with validation
  redis_host = length(local.redis_secret_parts) >= 1 ? local.redis_secret_parts[0] : ""
  redis_port = length(local.redis_secret_parts) >= 2 ? local.redis_secret_parts[1] : ""
  redis_key  = length(local.redis_secret_parts) >= 3 ? local.redis_secret_parts[2] : ""
  
  # Construct the Redis exporter connection string
  # Format: rediss://default:password@hostname:port (Redis Enterprise requires default username)
  redis_connection_string = local.redis_host != "" && local.redis_port != "" && local.redis_key != "" ? "rediss://default:${local.redis_key}@${local.redis_host}:${local.redis_port}" : ""
}

# Secret for Redis connection using Azure Key Vault secret
resource "kubernetes_secret" "redis-connection" {
  count = var.enable_redis_exporter ? 1 : 0
  
  # Add validation to ensure connection string is properly formatted
  lifecycle {
    precondition {
      condition     = local.redis_connection_string != ""
      error_message = "Redis connection string could not be parsed from Key Vault secret. Expected format: hostname:port:access_key"
    }
  }
  
  metadata {
    namespace = var.monitoring_namespace.metadata[0].name
    name      = "redis-connection"
  }
  type = "Opaque"
  data = {
    # Format the connection string for Redis exporter
    # Key Vault secret format: hostname:port:access_key
    # Redis exporter format: rediss://default:password@hostname:port
    redis-addr = local.redis_connection_string
  }
}