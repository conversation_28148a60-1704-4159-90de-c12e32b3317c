resource "helm_release" "opentelemetry-collector" {
  name      = "open-telemetry-agent"
  namespace = var.monitoring_namespace.metadata[0].name

  repository = "https://open-telemetry.github.io/opentelemetry-helm-charts"
  chart      = "opentelemetry-collector"
  version    = "0.120.2"

  values = [ 
    file("${path.module}/config/opentelemetry-values.yaml") 
  ]

  wait = false # TODO disable once image pull is reliable
}
