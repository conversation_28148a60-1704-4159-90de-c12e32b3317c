resource "kubernetes_manifest" "perhonen_pod_monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-perhonen-monitor"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "matchNames" = ["system"]
      }
      "selector" = {
        "matchLabels" = {
          "app" = "perhonen"
        }
      }
      "podMetricsEndpoints" = [
        {
          "port"     = "metrics"
          "path"     = "/metrics"
          "interval" = "30s"
        }
      ]
    }
  }
}