resource "kubernetes_manifest" "rapid-pool-monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-rapidpool-monitor"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "any" = true
      }
      "selector" = {
        "matchExpressions" = [
          {
            "key"      = "rapid_pool"
            "operator" = "In"
            "values"   = ["rollout-worker"]
          }
        ]
      }
      "podMetricsEndpoints" = [
        {
          "targetPort" = "2112"
        }
      ]
    }
  }
}
