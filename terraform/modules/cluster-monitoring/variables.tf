variable "monitoring_namespace" {
  type        = any
  description = "Data or Resource for cluster monitoring namespace"
}

variable "harmony_vault" {
  type        = any
  description = "Harmony Key Vault resource containing Redis connection details"
}

variable "enable_redis_exporter" {
  type        = bool
  description = "Enable Redis exporter deployment. Set to false initially if Key Vault access not yet granted."
  default     = true
}
