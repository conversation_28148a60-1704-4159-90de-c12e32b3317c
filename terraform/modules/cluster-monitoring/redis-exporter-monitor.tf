resource "kubernetes_manifest" "redis-exporter-monitor" {
  count = var.enable_redis_exporter ? 1 : 0
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-redis-exporter-monitor"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "any" = true
      }
      "selector" = {
        "matchLabels" = {
          "app" = "redis-exporter"
        }
      }
      "podMetricsEndpoints" = [
        {
          "targetPort" = "9121"
          "path"       = "/metrics"
        }
      ]
    }
  }
}