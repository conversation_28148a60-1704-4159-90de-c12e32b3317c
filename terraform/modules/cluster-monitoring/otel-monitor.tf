resource "kubernetes_manifest" "otel-monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-otel-monitor"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "selector" = {
        "matchLabels" = {
          "app.kubernetes.io/instance" = "open-telemetry-agent"
          "app.kubernetes.io/name"     = "opentelemetry-collector"
          "component"                  = "agent-collector"
        }
      }
      "namespaceSelector" = {
        "matchNames" = [
          var.monitoring_namespace.metadata[0].name
        ]
      }
      "podMetricsEndpoints" = [
        {
          "targetPort" = "8889"
        }
      ]
    }
  }
}
