resource "kubernetes_manifest" "kubecache_pod_monitor" {
  manifest = {
    "apiVersion" = "azmonitoring.coreos.com/v1"
    "kind"       = "PodMonitor"
    "metadata" = {
      "name"      = "managed-prometheus-kubecache-monitor"
      "namespace" = var.monitoring_namespace.metadata[0].name
    }
    "spec" = {
      "namespaceSelector" = {
        "matchNames" = ["scaling"]
      }
      "selector" = {
        "matchLabels" = {
          "app" = "kubecache"
        }
      }
      "podMetricsEndpoints" = [
        {
          "port" = "metrics"
          "path" = "/metrics"
          "interval" = "30s"
        }
      ]
    }
  }
}