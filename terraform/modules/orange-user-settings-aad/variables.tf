variable "orange-teams" {
  type = map(object({
    cluster_access         = list(string)
    storage_access         = list(string)
    parent_security_groups = list(string)
  }))
  description = "Map of team names to their access configurations"
}

variable "moonfire_admin_team_cluster_access" {
  type        = map(list(string))
  description = "Map of moonfire team names to their admin cluster access"
  default     = {}
}
