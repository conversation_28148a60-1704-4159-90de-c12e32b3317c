output "users" {
  value       = local.users
  description = "Map of users with their cluster access, storage access, and teams"
}

output "users_not_added" {
  value       = local.users_not_added
  description = "Map of users who are in Orange access teams but not in their parent security groups"
}

output "moonfire_admin_groups_for_clusters" {
  value       = local.moonfire_admin_groups_for_clusters
  description = "Map of moonfire team admin Azure AD group names to their object IDs (only for groups that have cluster access)"
}

output "moonfire_team_admin_cluster_access" {
  value       = local.moonfire_team_admin_cluster_access
  description = "Map of moonfire team admin groups to the clusters they should have access to"
}
