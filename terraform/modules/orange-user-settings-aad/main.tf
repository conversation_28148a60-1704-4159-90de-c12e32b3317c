module "user-settings" {
  source = "../orange-user-settings"
}

data "azuread_group" "orange-access-team-sg" {
  provider = azuread
  for_each = local.orange_access_teams

  display_name = each.key
}

data "azuread_group" "parent_security_groups" {
  provider = azuread
  for_each = toset(flatten([for team_data in var.orange-teams : team_data.parent_security_groups]))

  display_name = each.key
}

# Moonfire team admin groups based on hardcoded mapping
locals {
  moonfire_team_admin_group_names = toset([
    for team in keys(var.moonfire_admin_team_cluster_access) :
    upper("${team}-admin")
  ])
}

data "azuread_group" "moonfire_team_admin_groups" {
  provider = azuread
  for_each = local.moonfire_team_admin_group_names

  display_name = each.key
}

data "azuread_user" "all_orange_access_users" {
  provider = azuread
  for_each = toset(flatten([
    for team_name in local.orange_access_teams :
    data.azuread_group.orange-access-team-sg[team_name].members
  ]))

  object_id = each.key
}

locals {
  manually_entered_users = module.user-settings.users

  orange_access_teams = toset(keys(var.orange-teams))

  # get the list of all users in the orange access teams
  # one user can be in multiple teams. eg user1 in uksouth8 and uksouth7
  userIds_with_teams = flatten([
    for team_name, team in var.orange-teams : [
      for user_id in data.azuread_group.orange-access-team-sg[team_name].members : {
        user_id                = user_id
        cluster_access         = team.cluster_access
        storage_access         = team.storage_access
        parent_security_groups = team.parent_security_groups
      }
    ]
  ])

  users_present_in_parent_security_groups = [
    for user in local.userIds_with_teams : merge(user, {
      matching_parent_groups = [
        for parent_sg in user.parent_security_groups :
        parent_sg if contains(data.azuread_group.parent_security_groups[parent_sg].members, user.user_id)
      ]
    })
  ]

  #  get all the users who are also in parent_security_groups
  userIds_with_parent_security_groups = [
    for user in local.users_present_in_parent_security_groups :
    user if length(user.matching_parent_groups) > 0
  ]



  #  get all the users who are not in their parent_security_groups
  userIds_not_in_parent_security_groups = [
    for user in local.users_present_in_parent_security_groups :
    user if length(user.matching_parent_groups) == 0
  ]

  # for users present in the parent security groups, map their alias to user objects
  user_alias_mapping = {
    for user in local.userIds_with_parent_security_groups :
    split("@", data.azuread_user.all_orange_access_users[user.user_id].user_principal_name)[0] => user...
  }

  # for users not present in the parent security groups, map their alias to user objects
  users_not_added = {
    for user in local.userIds_not_in_parent_security_groups :
    split("@", data.azuread_user.all_orange_access_users[user.user_id].user_principal_name)[0] => user...
  }

  # for each alias, combine the user object. This will combine their cluster_access, storage_access and teams
  orange_access_users = {
    for alias, user_entries in local.user_alias_mapping : alias => {
      cluster_access = distinct(flatten([for entry in user_entries : entry.cluster_access]))
      storage_access = distinct(flatten([for entry in user_entries : entry.storage_access]))
      teams          = distinct(flatten([for entry in user_entries : entry.matching_parent_groups]))
    }
  }

  manual_users_with_empty_teams = {
    for alias, user_data in local.manually_entered_users : alias => merge(user_data, {
      teams = []
    })
  }

  users = merge(
    local.manual_users_with_empty_teams,
    {
      for alias, user_data in local.orange_access_users : alias => {
        cluster_access = distinct(concat(
          user_data.cluster_access,
          try(lookup(local.manual_users_with_empty_teams, alias, {}).cluster_access, [])
        ))
        storage_access = distinct(concat(
          user_data.storage_access,
          try(lookup(local.manual_users_with_empty_teams, alias, {}).storage_access, [])
        ))
        teams = distinct(concat(
          user_data.teams,
          try(lookup(local.manual_users_with_empty_teams, alias, {}).teams, [])
        ))
      }    }
  )

  # Convert team names to admin group names with their cluster access
  moonfire_team_admin_cluster_access = {
    for team, clusters in var.moonfire_admin_team_cluster_access :
    upper("${team}-admin") => clusters
  }

  # Map admin groups to their object IDs for existing groups only
  moonfire_admin_groups_for_clusters = {
    for admin_group in local.moonfire_team_admin_group_names :
    admin_group => try(data.azuread_group.moonfire_team_admin_groups[admin_group].object_id, null)
    if try(data.azuread_group.moonfire_team_admin_groups[admin_group].object_id, null) != null
  }
}

