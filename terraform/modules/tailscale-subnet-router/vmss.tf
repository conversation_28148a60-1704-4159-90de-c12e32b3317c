locals {
  tags = join(",", [for tag in var.tags : "tag:${tag}"])
  # add azure dns server to subnets for tailscale to resolve azure private dns
  subnets = join(",", var.advertise_azure_dns ? concat(var.advertise_subnets, ["*************/32"]) : var.advertise_subnets)

  tailscale_setup_script = templatefile("${path.module}/cloud-init/scripts/tailscale-startup.sh.tpl", {
    tags          = local.tags
    subnets       = local.subnets
    client-id     = data.azurerm_key_vault_secret.client-id.value
    client-secret = data.azurerm_key_vault_secret.client-secret.value
  })

  tailsnail_script = templatefile("${path.module}/../tailsnail/tailsnail.sh.tpl", {
    rate_mbit = var.tailsnail_rate_mbit
  })

  tailsnail_service = file("${path.module}/cloud-init/systemd/tailsnail.service")
}

data "azurerm_key_vault_secret" "client-id" {
  key_vault_id = var.keyvault_id
  name         = "${var.secret_name}-client-id"
}

data "azurerm_key_vault_secret" "client-secret" {
  key_vault_id = var.keyvault_id
  name         = "${var.secret_name}-client-secret"
}

resource "azurerm_public_ip_prefix" "router" {
  name                = "${var.subnet-router-name-prefix}-pip-prefix"
  location            = var.location
  resource_group_name = var.resource_group_name

  prefix_length = 30 # up to 4 IPs
}

resource "azurerm_linux_virtual_machine_scale_set" "tailscale-router" {
  name                   = var.subnet-router-name-prefix
  location               = var.location
  resource_group_name    = var.resource_group_name
  sku                    = var.machine_type
  instances              = 2
  upgrade_mode           = "Manual"
  single_placement_group = false
  computer_name_prefix   = var.subnet-router-name-prefix
  admin_username         = "azureuser"

  custom_data = base64encode(templatefile(
    "${path.module}/cloud-init/cloud-init.yaml.tpl",
    {
      tailscale_setup_script = local.tailscale_setup_script
      tailsnail_script       = local.tailsnail_script
      tailsnail_service      = local.tailsnail_service
    }
  ))

  overprovision = false

  admin_ssh_key {
    username   = "azureuser"
    public_key = tls_private_key.host_key.public_key_openssh
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
  }


  source_image_reference {
    publisher = "Canonical"
    offer     = "0001-com-ubuntu-server-jammy"
    sku       = "22_04-lts"
    version   = "latest"
  }

  network_interface {
    name                          = "nic"
    primary                       = true
    enable_accelerated_networking = true
    enable_ip_forwarding          = true
    network_security_group_id     = azurerm_network_security_group.tailscale-router.id

    ip_configuration {
      name      = "primary"
      primary   = true
      subnet_id = var.subnet_id
      public_ip_address {
        name                = "${var.subnet-router-name-prefix}-pip"
        public_ip_prefix_id = azurerm_public_ip_prefix.router.id
      }
    }
  }

  identity {
    type = "SystemAssigned"
  }
}
