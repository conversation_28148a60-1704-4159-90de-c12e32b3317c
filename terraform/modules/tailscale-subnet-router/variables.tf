variable "resource_group_name" {
  type        = string
  description = "Resource Group to place router in"
}

variable "location" {
  type = string
}

variable "subnet-router-name-prefix" {
  type        = string
  description = "Prefix for the subnet router name"
}

variable "machine_type" {
  type        = string
  description = "Machine type for VMSS"
  default     = "Standard_D14_v2"
}

variable "tags" {
  type        = list(string)
  description = "List of tags to register the routers with"
}

variable "advertise_subnets" {
  type        = list(string)
  description = "List of routes to advertise with router"
}

variable "keyvault_id" {
  type        = string
  description = "ID for keyvault that holds authkey for router"
}

variable "secret_name" {
  type        = string
  description = "Authkey identifier in KeyVault for router"
  default     = "tailscale"
}

variable "subnet_id" {
  type        = string
  description = "Subnet ID for router"
}

variable "advertise_azure_dns" {
  type        = bool
  description = "Advertise Azure DNS server to tailscale"
  default     = false
}

variable "tailsnail_rate_mbit" {
  type        = number
  description = "Rate in Mbit for tailsnail"
  default     = 1
}