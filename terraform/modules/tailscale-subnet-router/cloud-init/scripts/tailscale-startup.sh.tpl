#!/bin/bash
# shellcheck disable=SC2154,SC2086,SC2006

export DEBIAN_FRONTEND=noninteractive

function apt-waitlock() {
  while fuser /var/lib/dpkg/lock >/dev/null 2>&1 ; do sleep 5; done
}

# Install Tailscale
apt-waitlock && sudo apt-get update -y -o DPkg::Lock::Timeout=60
apt-waitlock && sudo apt-get install -y -o DPkg::Lock::Timeout=60 ca-certificates curl apt-transport-https lsb-release gnupg software-properties-common

curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/jammy.noarmor.gpg | sudo tee /usr/share/keyrings/tailscale-archive-keyring.gpg >/dev/null
curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/jammy.tailscale-keyring.list | sudo tee /etc/apt/sources.list.d/tailscale.list

echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
# echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf

# Disabling IPv6 forwarding to prevent Tailscale from using it
# With kernel > kernel 6.8.0-1025-azure it broke tailscale masquerade rules

# See https://www.reddit.com/r/Tailscale/comments/1jqcu8x/ubuntu_2404_kernel_68_tailscale_broken_ip6tables/
# And https://github.com/tailscale/tailscale/issues/13863#issuecomment-2772675668
echo 'net.ipv6.conf.all.disable_ipv6 = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p /etc/sysctl.conf


apt-waitlock && sudo apt-get update -y -o DPkg::Lock::Timeout=60
apt-waitlock && sudo apt-get install -y -o DPkg::Lock::Timeout=60 tailscale jq

ACCESS_TOKEN=`curl -s -d "client_id=${client-id}" -d "client_secret=${client-secret}" "https://api.tailscale.com/api/v2/oauth/token" | jq -r .access_token`
AUTHKEY=`curl -s "https://api.tailscale.com/api/v2/tailnet/-/keys" \
  -u "$${ACCESS_TOKEN}:" \
  -H "Content-Type: application/json" \
  --data-binary '
{
  "capabilities": {
    "devices": {
      "create": {
        "reusable": false,
        "ephemeral": true,
        "preauthorized": true,
        "tags": [ "${tags}" ]
      }
    }
  }
}' | jq -r .key`

sudo tailscale up  --advertise-routes=${subnets} --accept-dns=false --authkey $AUTHKEY
