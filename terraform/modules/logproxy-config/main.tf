locals {
  config = file("${path.module}/config.json")
}

resource "azurerm_storage_container" "logproxy" {
  name               = "logproxyclient"
  storage_account_id = var.storage_account_id
}

resource "azurerm_storage_blob" "config" {
  name                   = "config.json"
  storage_account_name   = var.storage_account_name
  storage_container_name = azurerm_storage_container.logproxy.name
  content_type           = "application/json"
  type                   = "Block"
  source_content         = local.config

  depends_on = [
    azurerm_storage_container.logproxy
  ]
}
