# This assumes there is already a storage account and container are already created

module "orange_storage_config" {
  source = "../orange-storage-config"
}

locals {
  regions = keys(module.orange_storage_config.regions)

  # Per-user storage accounts are all created in one region (uksouth historically), but currently
  # we pretend that each one is in every supported region. Otherwise, job submission will try to
  # call sciclone/scapi to instantiate the user's 'storage set' in the target cluster's region.
  # TODO: This might be the wrong arrangement for user storage accounts?
  user_map = {
    for username, user_attrs in var.users : user_attrs.storage_acount_name => {
      home    = "az://${user_attrs.storage_acount_name}"
      homes   = { for region in local.regions : region => "az://${user_attrs.storage_acount_name}" }
      regions = { for region in local.regions : region => "az://${user_attrs.storage_acount_name}" }
      type    = "storage-set"
    }
  }

  template = jsondecode(file("${path.module}/template/storage-map.json"))

  # Merge user_map into the 'sources' key of the template
  storage_map = merge(
    local.template,
    {
      sources = merge(
        local.template.sources, // Original sources from the template
        local.user_map          // Add user_map to sources
      )
    }
  )
}

data "azurerm_storage_account" "storage_account" {
  name                = var.storage_map.storage_account_name
  resource_group_name = var.storage_map.storage_resource_group
}

data "azurerm_storage_container" "storage_map" {
  name                 = "storage-map"
  storage_account_name = data.azurerm_storage_account.storage_account.name
}


resource "azurerm_role_assignment" "read-storage-map-app" {
  for_each = var.users

  scope                = data.azurerm_storage_account.storage_account.id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = each.value.app_object_id

}

resource "azurerm_role_assignment" "read-storage-map-user" {
  for_each = var.users

  scope                = data.azurerm_storage_account.storage_account.id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = each.value.user_object_id

}

data "json-formatter_format_json" "storage_map" {
  json = jsonencode(local.storage_map)
}

# Transition storage map to manually-managed
removed {
  from = azurerm_storage_blob.storage_map
  lifecycle {
    destroy = false
  }
}

locals {
  storage_map_file = "/tmp/storage_map.json"
}

resource "local_file" "storage_map_file" {
  filename = local.storage_map_file
  content  = data.json-formatter_format_json.storage_map.result
}

resource "null_resource" "storage_map" {
  depends_on = [local_file.storage_map_file]
  triggers = {
    storage_map = data.json-formatter_format_json.storage_map.result
  }
  provisioner "local-exec" {
    command = <<-EOT
      az storage blob upload \
        --auth-mode login \
        --account-name "${data.azurerm_storage_account.storage_account.name}" \
        --container-name "${data.azurerm_storage_container.storage_map.name}" \
        --name "storage-map.json" \
        --content-type "application/json" \
        --type "block" \
        --file "${local.storage_map_file}" \
        --overwrite
    EOT
  }
}
