resource "kubernetes_cron_job_v1" "git_gc" {
  metadata {
    name      = "git-gc"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  spec {
    schedule                      = "0 10 * * *"
    successful_jobs_history_limit = 1
    failed_jobs_history_limit     = 1
    starting_deadline_seconds     = 6000

    job_template {
      metadata {
        name = "git-gc"
      }
      spec {
        template {
          metadata {
            name = "git-gc"
          }
          spec {
            affinity {
              node_affinity {
                preferred_during_scheduling_ignored_during_execution {
                  weight = 100
                  preference {
                    match_expressions {
                      key      = "singularity.azure.com/processing-unit"
                      operator = "In"
                      values   = ["cpu", "system"]
                    }
                  }
                }
              }
            }
            container {
              name  = "main"
              image = "mcr.microsoft.com/oss/v2/kubernetes/kubectl:v1.33.1"
              command = [
                "/bin/bash",
                "-cxe",
                join("\n", [
                  "echo random sleep for git gc",
                  "sleep $((RANDOM % 60 + 1))m",
                  "kubectl exec brix-git -c main -- git -C /root/code/openai gc"
                ])
              ]
            }
            restart_policy = "OnFailure"
          }
        }
      }
    }
  }
}