
# these should be moved to azure rbac.
resource "kubernetes_role_binding" "namespace_admin" {
  metadata {
    name      = "namespace-admin"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-admin"
  }

  subject {
    kind      = "User"
    name      = var.user.auth_identifier
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }
}

resource "kubernetes_role_binding" "namespace_admin_oid" {
  metadata {
    name      = "namespace-admin-oid"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-admin"
  }

  subject {
    kind      = "User"
    name      = var.user.object_id
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }
}

resource "kubernetes_role_binding" "namespace_default_admin" {
  metadata {
    name      = "namespace-default-admin"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-admin"
  }

  subject {
    kind      = "ServiceAccount"
    name      = "default"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }
}

resource "kubectl_manifest" "git" {
  yaml_body = <<YAML
apiVersion: brix.openai.com/v1alpha1
kind: Git
metadata:
  name: brix-git
  namespace: ${kubernetes_namespace.usernamespace.metadata.0.name}
spec:
    projectRoot: /root/code
    repositories:
        openai:
          bare: true
YAML
}
