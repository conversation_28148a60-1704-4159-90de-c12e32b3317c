# Exactly one of `certificate` or `secret` must be set.
variable "user" {
  type = object({
    alias                = string
    object_id            = string
    auth_identifier      = string
    storage_account_name = string
    team_name            = string
    certificate = optional(object({
      name        = string
      keyvault_id = string
    }))
    secret = optional(string)
    app = object({
      client_id = string
    })
  })
}

variable "tenant_id" {
  type = string
}

variable "subscription" {
  type = object({
    id   = string
    name = string
  })
}

variable "cluster" {
  type = string
}

variable "enable_file_logs_collector" {
  type        = bool
  default     = true
  description = "Enable fluent-bit sidecar for file logs collection"
}