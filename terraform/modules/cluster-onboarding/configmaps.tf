resource "kubernetes_config_map" "userconfigmap" {
  metadata {
    name      = "openai-team"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  data = {
    team = var.user.team_name
    type = "user"
  }
}

resource "kubernetes_config_map" "storageconfigmap" {
  metadata {
    name      = "azure-storage-account"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  data = {
    AZURE_STORAGE_ACCOUNT = var.user.storage_account_name
  }
}

resource "kubernetes_config_map" "fluent_bit_config" {
  count = var.enable_file_logs_collector ? 1 : 0
  
  metadata {
    name      = "fluent-bit-config"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  data = {
    "fluent-bit.yaml" = <<-EOT
      service:
        flush: 1
        log_level: info
        daemon: off
        storage.metrics: on

      pipeline:
        inputs:
          - name: tail
            path: /tmp/ray/**/**/*.log,/tmp/**/bus.log,/var/log/**/*.log,/var/log/**/**/*.log,/var/log/api-services.log,/tmp/tw/*.log,/tmp/rlogs/peashooter_rollout_*/*.log.*,/var/log/**/**/*.log.*
            tag: file.logs
            refresh_interval: 5
            read_from_head: false
            path_key: filename
            storage.type: memory
            exit_on_eof: false

        outputs:
          - name: stdout
            match: file.logs
            format: json_lines
    EOT
  }
}
