# cluster-onboarding modules supports two authentication methods:
# 1. Client certificate, used by iridium clusters
#    The cert is read from a keyvault and mounted in workload pods by iridmission.
# 2. Client secret, used by orange clusters starting with prod-uksouth-7
#    The secret is stored in the cluster and exposed as an environment variable in workload pods by iridmission.
# This module will create the appropriate k8s secret(s) for the desired method,
# and configure environment variables for the workload to (if using azure python sdk) automatically authenticate with.

check "cluster-authentication-method" {
  assert {
    condition     = (var.user.certificate != null) != (var.user.secret != null) # exclusive or
    error_message = <<EOT
    User ${var.user.alias}:
    Exactly one of `certificate` or `secret` must be set.
    Got certificate: ${nonsensitive(var.user.certificate != null)}
    Got secret: ${nonsensitive(var.user.secret != null)}
    EOT
  }
}

resource "kubernetes_secret" "azure-service-principal" {
  metadata {
    name      = "azure-service-principal"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  data = {
    # only one of these two will be set
    AZURE_CLIENT_CERTIFICATE_PATH = var.user.certificate != null ? "/etc/iridium/sp-cert.pem" : null
    AZURE_CLIENT_SECRET           = var.user.secret

    AZURE_CLIENT_ID                 = var.user.app.client_id
    AZURE_TENANT_ID                 = var.tenant_id
    AZURE_SUBSCRIPTION_ID           = var.subscription.id
    AZURE_USE_IDENTITY              = "1"
    BRIX_DEFAULT_SUBSCRIPTION       = var.subscription.name
    BRIX_AZURE_DEFAULT_SUBSCRIPTION = var.subscription.name
  }
}

data "azurerm_key_vault_certificate_data" "user-cert" {
  count = var.user.certificate != null ? 1 : 0

  name         = var.user.certificate.name
  key_vault_id = var.user.certificate.keyvault_id
}


module "pem-strip-intermediates" {
  count = var.user.certificate != null ? 1 : 0

  source = "../pem-strip-intermediates"
  pem    = data.azurerm_key_vault_certificate_data.user-cert[0].pem
}

resource "kubernetes_secret" "azure-service-principal-cert" {
  count = var.user.certificate != null ? 1 : 0

  metadata {
    name      = "azure-service-principal-cert"
    namespace = kubernetes_namespace.usernamespace.metadata.0.name
  }

  data = {
    "sp-cert.pem" = join("", [
      data.azurerm_key_vault_certificate_data.user-cert[0].key,
      module.pem-strip-intermediates[0].pem,
    ])
  }
}
