locals {
  jupyter_enabled  = true
  jupyter_username = kubernetes_namespace.usernamespace.metadata.0.name
}

resource "kubernetes_deployment" "orange_devbox" {
  count            = local.jupyter_enabled ? 1 : 0
  wait_for_rollout = false
  metadata {
    name      = "orange-devbox"
    namespace = local.jupyter_username
    annotations = {
      nginx-hash = sha256(local.nginxconf)
    }
    labels = {
      app = "orange-devbox"
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        app = "orange-devbox"
      }
    }

    template {
      metadata {
        labels = {
          app           = "orange-devbox"
          orange-devbox = "true"
        }
      }

      spec {
        priority_class_name = "low-priority"
        affinity {
          node_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 100
              preference {
                match_expressions {
                  key      = "singularity.azure.com/processing-unit"
                  operator = "In"
                  values   = ["cpu"]
                }
              }
            }
          }
        }

        container {
          name              = "main"
          image             = "iridiumsdc.azurecr.io/kube-orange-box:1288785"
          image_pull_policy = "Always"
          command = [
            "jupyter",
            "lab",
            "--ip=127.0.0.1",
            "--port=8080",
            "--no-browser",
            "--allow-root",
            "--NotebookApp.token=''",
            "--notebook-dir=/root/code",
            "--ServerApp.allow_remote_access=True",
            "--NotebookApp.terminado_settings={\"shell_command\":[\"/bin/bash\", '-c', 'cd /root/code && exec bash -l']}"
          ]
          resources {
            requests = {
              cpu    = "0"
              memory = "0"
            }
            limits = {
              cpu    = "0"
              memory = "0"
            }
          }
          env {
            name = "OPENAI_USER"
            value_from {
              field_ref {
                api_version = "v1"
                field_path  = "metadata.namespace"
              }
            }
          }
          env {
            name  = "BRIX_USE_KUBE_CONFIG_AUTH"
            value = "true"
          }
          env {
            name  = "SKIP_TORCHFLOW_SETUP_CHECK"
            value = "1"
          }
          env {
            name  = "BRIX_CLI_DISABLE_TAILSCALE"
            value = "1"
          }
          env {
            name  = "BRIX_SELF_UPGRADE"
            value = "0"
          }
          env {
            name  = "AZURE_USE_IDENTITY"
            value = "1"
          }
          env {
            name  = "USE_STORAGE_MAP"
            value = "false"
          }
          env {
            name  = "BRIX_CLUSTER"
            value = var.cluster
          }
          env {
            name = "BRIX_NAMESPACE"
            value_from {
              field_ref {
                api_version = "v1"
                field_path  = "metadata.namespace"
              }
            }
          }
          env {
            name  = "OPENAI_STORAGE_SET"
            value = "oaiorange$(OPENAI_USER)"
          }
          env {
            name  = "APPLIED_STORAGE_MAP"
            value = "az://orngoaiartifacts/storage-map/storage-map.json"
          }
          env {
            name  = "OPENAI_ENCODINGS_BASE_DATA_GYM"
            value = "az://orngoaiartifacts/data-gym/encodings"
          }
          env {
            name  = "DATA_GYM_BACKENDS"
            value = "azure"
          }
          env {
            name  = "BRIX_TELEMETRY"
            value = "off"
          }
          env {
            name = "AZCOPY_SPA_CLIENT_SECRET"
            value_from {
              secret_key_ref {
                name     = "azure-service-principal"
                key      = "AZURE_CLIENT_SECRET"
                optional = false
              }
            }
          }
          env {
            name = "AZCOPY_SPA_APPLICATION_ID"
            value_from {
              secret_key_ref {
                name     = "azure-service-principal"
                key      = "AZURE_CLIENT_ID"
                optional = false
              }
            }
          }
          env {
            name = "AZCOPY_TENANT_ID"
            value_from {
              secret_key_ref {
                name     = "azure-service-principal"
                key      = "AZURE_TENANT_ID"
                optional = false
              }
            }
          }
          env {
            name  = "AZCOPY_AUTO_LOGIN_TYPE"
            value = "spn"
          }
          env_from {
            secret_ref {
              name = "azure-service-principal"
            }
          }
          env_from {
            config_map_ref {
              name = "azure-storage-account"
            }
          }
          volume_mount {
            name       = "brix-ssh"
            mount_path = "/etc/brix/ssh"
            read_only  = true
          }
          volume_mount {
            name       = "brix-tls"
            mount_path = "/etc/brix/tls"
            read_only  = true
          }
          volume_mount {
            name       = "share-config"
            mount_path = "/opt/share"
          }
        }

        container {
          name    = "jupyter-nginx-auth-proxy"
          image   = "mcr.microsoft.com/oss/kubernetes/ingress/nginx-ingress-controller:v1.11.0"
          command = ["nginx", "-g", "daemon off;"]
          env {
            name = "OWNER"
            value_from {
              field_ref {
                api_version = "v1"
                field_path  = "metadata.namespace"
              }
            }
          }
          env {
            name  = "ALLOWED_EMAILS"
            value = "$(OWNER)@green.microsoft.com"
          }
          resources {
            requests = {
              cpu    = "0"
              memory = "0"
            }
            limits = {
              cpu    = "0"
              memory = "0"
            }
          }
          security_context {
            run_as_user  = 0
            run_as_group = 0
          }
          port {
            container_port = 8443
          }
          volume_mount {
            name       = "jupyter-nginx-auth-proxy-conf"
            mount_path = "/etc/nginx/nginx.conf"
            sub_path   = "nginx.conf"
          }
          volume_mount {
            name       = "brix-tls"
            mount_path = "/etc/nginx/ssl"
            read_only  = true
          }
          volume_mount {
            name       = "share-config"
            mount_path = "/opt/share"
            read_only  = true
          }
        }

        volume {
          name = "brix-ssh"
          secret {
            secret_name  = "brix-ssh"
            default_mode = "0420"
            items {
              key  = "ssh-privatekey"
              path = "id_rsa"
              mode = "0600"
            }
            items {
              key  = "ssh-publickey"
              path = "id_rsa.pub"
              mode = "0644"
            }
          }
        }

        volume {
          name = "brix-tls"
          secret {
            secret_name  = "brix-tls"
            default_mode = "0420"
            items {
              key  = "ca.crt"
              path = "ca.crt"
              mode = "0644"
            }
            items {
              key  = "tls.crt"
              path = "tls.crt"
              mode = "0644"
            }
            items {
              key  = "tls.key"
              path = "tls.key"
              mode = "0600"
            }
          }
        }

        volume {
          name = "jupyter-nginx-auth-proxy-conf"
          config_map {
            name = "jupyter-nginx-auth-proxy"
          }
        }

        volume {
          name = "share-config"
          empty_dir {}
        }
      }
    }
  }
}

resource "kubernetes_service" "devbox_jupyter_server" {
  count = local.jupyter_enabled ? 1 : 0
  metadata {
    name      = "orange-devbox-jupyter-server"
    namespace = local.jupyter_username
  }

  spec {
    selector = {
      app = "orange-devbox"
    }

    port {
      protocol    = "TCP"
      port        = 8443
      target_port = 8443
    }
  }
}

locals {
  nginxconf = <<-EOT
      env ALLOWED_EMAILS;
      events {
      }
      http {
        server {
          listen 8443 ssl;

          error_log /dev/stderr warn;
          access_log /dev/stdout;

          ssl_certificate     /etc/nginx/ssl/tls.crt;
          ssl_certificate_key /etc/nginx/ssl/tls.key;

          ssl_client_certificate /etc/nginx/ssl/ca.crt;
          ssl_verify_client on;

          location / {

            access_by_lua_block {
              local allowed_emails_str = os.getenv("ALLOWED_EMAILS") or ""
              local allowed_emails = {}
              for email in string.gmatch(allowed_emails_str, '([^,]+)') do
                allowed_emails[email] = true
              end

              -- Load from file
              local file_path = "/opt/share/allowed_emails"
              local file, err = io.open(file_path, "r")
              if file then
                for line in file:lines() do
                    local trimmed = line:match("^%s*(.-)%s*$")
                    if trimmed ~= "" then
                        allowed_emails[trimmed] = true
                        allowed_emails_str = allowed_emails_str .. ", " .. trimmed
                    end
                end
                file:close()
              end


              local email = ngx.req.get_headers()["X-Auth-Request-Email"]
              if not email or not allowed_emails[email] then
                ngx.status = ngx.HTTP_FORBIDDEN
                ngx.say("Forbidden, your email is not allowed: " .. (email or "nil") .. ", allowed emails: " .. allowed_emails_str)
                return ngx.exit(ngx.HTTP_FORBIDDEN)
              end

              local ck = require "resty.cookie"
              local cookie, err = ck:new()
              if not cookie then
                ngx.log(ngx.ERR, "Failed to create cookie: ", err)
                return ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
              end

              local confirmed = cookie:get("confirmed")
              if not confirmed or confirmed ~= "yes" then
                return ngx.redirect("/confirm", 302)
              end
            }

            proxy_pass http://127.0.0.1:8080;

            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          }

          location = /confirm {
              content_by_lua_block {
                  local method = ngx.req.get_method()
                  local ck = require "resty.cookie"
                  local cookie, err = ck:new()

                  if method == "POST" then
                      cookie:set({
                          key = "confirmed",
                          value = "yes",
                          path = "/",
                          max_age = 365 * 24 * 60 * 60 -- 1 years
                      })
                      return ngx.redirect("/", 302)
                  else
                      ngx.header.content_type = "text/html"
                      ngx.say([[
                        <html>
                          <head>
                            <style>
                              body {
                                margin: 0;
                                height: 100vh;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                font-family: sans-serif;
                                background-color: #f7f7f7;
                              }
                              .card {
                                background: white;
                                padding: 2em 3em;
                                border-radius: 10px;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                                text-align: center;
                                max-width: 600px;
                              }
                              .warning {
                                background-color: #fff3cd;
                                color: #856404;
                                padding: 12px 20px;
                                font-size: 14px;
                                border: 1px solid #ffeeba;
                                border-radius: 4px;
                                margin-bottom: 20px;
                                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                                text-align: left;
                              }
                              button {
                                padding: 0.5em 1.5em;
                                font-size: 1em;
                                background: #007bff;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                              }
                              button:hover {
                                background: #0056b3;
                              }
                            </style>
                          </head>
                          <body>
                            <div class="card">
                              <div class="warning">
                                <h2><strong>Warning:</strong> JupyterLab for <strong>Orange</strong> is currently under active development and testing.
                                Please do not store important data, everything may be deleted at any time.</h2>
                              </div>
                              <p>Please confirm to continue.</p>
                              <form method="POST" action="/confirm">
                                <button type="submit">I am 18+ and I Agree</button>
                              </form>
                            </div>
                          </body>
                        </html>
                      ]])
                  end
              }
          }
        }
      }
    EOT
}

resource "kubernetes_config_map" "nginx_auth_proxy" {
  count = local.jupyter_enabled ? 1 : 0
  metadata {
    name      = "jupyter-nginx-auth-proxy"
    namespace = local.jupyter_username
  }

  data = {
    "nginx.conf" = local.nginxconf
  }
}

resource "kubernetes_ingress_v1" "jupyter_ingress" {
  count = local.jupyter_enabled ? 1 : 0
  metadata {
    name      = "jupyter-ingress"
    namespace = local.jupyter_username
    annotations = {
      "nginx.ingress.kubernetes.io/auth-cache-key"          = "$oauth_cookie_cache_key$http_authorization"
      "nginx.ingress.kubernetes.io/auth-response-headers"   = "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
      "nginx.ingress.kubernetes.io/auth-url"                = "https://oai-azure-auth-proxy.int.${var.cluster}.dev.openai.org/oauth2/auth"
      "nginx.ingress.kubernetes.io/auth-signin"             = "https://oai-azure-auth-proxy.int.${var.cluster}.dev.openai.org/oauth2/start"
      "nginx.ingress.kubernetes.io/proxy-buffer-size"       = "128k"
      "nginx.ingress.kubernetes.io/proxy-busy-buffers-size" = "512k"
      "nginx.ingress.kubernetes.io/proxy-buffers-number"    = "4"
      "nginx.ingress.kubernetes.io/configuration-snippet"   = <<-EOT
        proxy_set_header X-User-Email $upstream_http_x_auth_request_email;
      EOT
      "nginx.ingress.kubernetes.io/backend-protocol"        = "HTTPS"
      "nginx.ingress.kubernetes.io/proxy-ssl-secret"        = "${local.jupyter_username}/brix-tls"
    }
  }

  spec {
    ingress_class_name = "internal-nginx"
    rule {
      host = "devbox-${local.jupyter_username}.int.${var.cluster}.dev.openai.org"
      http {
        path {
          path      = "/"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service.devbox_jupyter_server.0.metadata[0].name
              port {
                number = 8443
              }
            }
          }
        }
      }
    }
    tls {
      hosts = [
        "devbox-${local.jupyter_username}.int.${var.cluster}.dev.openai.org"
      ]
    }
  }
}
