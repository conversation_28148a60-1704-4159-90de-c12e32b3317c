variable "cluster_name" {
  type = string
}

variable "operator_cpu" {
  type = string
  # default = "16"
  default = "2"
}

variable "operator_memory" {
  type = string
  # default = "64Gi"
  default = "4Gi"
}

variable "preload_image" {
  type    = bool
  default = true
}

variable "cluster_aks" {
  type        = any
  description = "Data or Resource for Azure AKS cluster"
}

variable "image_tag" {
  type        = string
  description = "Image tag for the Brix operator"
}

variable "state_metrics_cpu" {
  type        = string
  description = "CPU limit/request for brix-state-metrics"
  default     = "1"
}

variable "state_metrics_memory" {
  type        = string
  description = "Memory limit/request for brix-state-metrics"
  default     = "1Gi"
}

variable "state_metrics_image_tag" {
  type        = string
  description = "Image tag for brix-state-metrics (defaults to main brix image tag if not specified)"
  default     = "v0.16.4-8d84c9c3-dirty-cloudtest-amd64"
}
