# Infra Mirror
# https://dev.azure.com/project-argos/Mimco/_git/infra-mirror?path=/src/services/brix/brix-operator/brix-operator.tf&version=GBmain&_a=contents
resource "kubernetes_service_account_v1" "brix-operator-service-account" {
  metadata {
    name      = "brix-operator"
    namespace = "system"
    labels    = { app = "brix", component = "operator" }
  }
}

resource "kubernetes_cluster_role_v1" "brix-operator-cluster-role" {
  metadata {
    name = "brix-operator"
  }

  # Required to manage its own custom resources
  rule {
    verbs      = ["list", "watch", "update", "create", "patch", "delete"]
    api_groups = ["apiextensions.k8s.io"]
    resources  = ["customresourcedefinitions"]
  }
  # Required to manage brix resources.
  rule {
    verbs      = ["*"]
    api_groups = ["brix.openai.com"]
    resources  = ["*"]
  }
  # Basic resources required to run the operator.
  rule {
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
    api_groups = [""]
    resources  = ["pods", "secrets", "configmaps", "services", "namespaces"]
  }
  # Required to observe Quota and SKU availability.
  # TODO: infra-mirror doesn't have update or patch. Should we remove those?
  rule {
    verbs      = ["get", "list", "watch", "update", "patch"]
    api_groups = [""]
    resources  = ["nodes"]
  }
  # Required to configure access permissions for users.
  rule {
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
    api_groups = ["rbac.authorization.k8s.io"]
    resources  = ["rolebindings", "clusterrolebindings"]
  }
  rule {
    verbs          = ["bind"]
    api_groups     = ["rbac.authorization.k8s.io"]
    resources      = ["clusterroles"]
    resource_names = ["admin", "edit", "view"]
  }
  # Required to queue tasks based on pod priority and validate pod templates.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["scheduling.k8s.io"]
    resources  = ["priorityclasses"]
  }
  # Required for InternalIngress controller.
  rule {
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
    api_groups = ["networking.k8s.io"]
    resources  = ["ingresses"]
  }
  # Required to to validate pod templates.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["node.k8s.io"]
    resources  = ["runtimeclasses"]
  }
  # Required to check if there is collision with StatefulSet.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["apps"]
    resources  = ["statefulsets"]
  }
  # Required for leader election.
  rule {
    verbs      = ["get", "create", "update"]
    api_groups = [""]
    resources  = ["endpoints"]
  }
  rule {
    verbs      = ["get", "create", "update"]
    api_groups = ["coordination.k8s.io"]
    resources  = ["leases"]
  }
  # Required for event recording.
  rule {
    verbs      = ["create", "patch"]
    api_groups = [""]
    resources  = ["events"]
  }
  # Required for supporting PVC creation in the operator.
  rule {
    verbs      = ["get", "list", "watch", "create"]
    api_groups = [""]
    resources  = ["persistentvolumeclaims"]
  }
  # TODO: infra-mirror doesn't have podgroup permissions. should we remove this?
  rule {
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
    api_groups = ["scheduling.sigs.k8s.io"]
    resources  = ["podgroups"]
  }
  # Required to manage brix TLS certificates.
  rule {
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
    api_groups = ["cert-manager.io"]
    resources  = ["certificates"]
  }
}

resource "kubernetes_cluster_role_binding_v1" "brix-operator-cluster-role-binding" {
  depends_on = [
    kubernetes_service_account_v1.brix-operator-service-account,
    kubernetes_cluster_role_v1.brix-operator-cluster-role,
  ]
  metadata {
    name = "brix-operator"
  }
  subject {
    kind      = "ServiceAccount"
    name      = "brix-operator"
    namespace = "system"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-operator"
  }
}

locals {
  k8s_minor_version = parseint(split(".", var.cluster_aks.kubernetes_version)[1], 10)
  flow_schema_version = (
    (local.k8s_minor_version < 26) ? "flowcontrol.apiserver.k8s.io/v1beta2" :
    (local.k8s_minor_version < 29) ? "flowcontrol.apiserver.k8s.io/v1beta3" :
    "flowcontrol.apiserver.k8s.io/v1"
  )
}

# This flow schema is based on flow schema used for the kube-controller-manager,
# because brix-operator manages resources similar to resources managed by kube-controller-manager.
resource "kubectl_manifest" "brix-operator-flow-schema" {
  depends_on = [
    kubernetes_service_account_v1.brix-operator-service-account
  ]
  yaml_body = templatefile("${path.module}/brix-operator/brix-operator-flow-schema.yaml", {
    api_version = local.flow_schema_version
  })
}

resource "kubernetes_config_map" "brix-operator-config-map" {
  metadata {
    name      = "brix-operator"
    namespace = "system"
    labels = {
      app       = "brix"
      component = "operator"
    }
  }
  data = {
    "brix-operator.yaml" = templatefile("${path.module}/brix-operator/brix-operator-configuration.yaml", {
      cluster             = var.cluster_name
      installer_image_tag = var.image_tag
      acr_base            = local.repository
    })
  }
}

resource "kubernetes_service_v1" "brix-operator-service" {
  depends_on = [
    kubernetes_deployment_v1.brix-operator-deployment
  ]
  metadata {
    name      = "brix-operator"
    namespace = "system"
    labels = {
      app       = "brix"
      component = "operator"
    }
  }
  spec {
    port {
      name = "https"
      port = 443
    }
    port {
      name = "http"
      port = 8080
    }
    port {
      name = "metrics"
      port = 8081
    }
    selector = { app = "brix", component = "operator" }
  }
}

resource "kubectl_manifest" "brix-operator-issuer" {
  yaml_body = file("${path.module}/brix-operator/brix-operator-issuer.yaml")
}

resource "kubectl_manifest" "brix-operator-cluster-issuer" {
  yaml_body = file("${path.module}/brix-operator/brix-operator-cluster-issuer.yaml")
}

resource "kubectl_manifest" "brix-operator-certificate" {
  depends_on = [
    kubectl_manifest.brix-operator-issuer
  ]
  yaml_body = file("${path.module}/brix-operator/brix-operator-certificate.yaml")
}

resource "kubernetes_mutating_webhook_configuration" "brix-operator-mutating-webhook" {
  metadata {
    name        = "brix-operator"
    labels      = { app = "brix", component = "operator" }
    annotations = { "cert-manager.io/inject-ca-from" = "system/brix-operator" }
  }

  lifecycle {
    ignore_changes = [
      webhook[0].client_config[0].ca_bundle,
      webhook[1].client_config[0].ca_bundle,
      webhook[2].client_config[0].ca_bundle,
      webhook[3].client_config[0].ca_bundle,
      webhook[4].client_config[0].ca_bundle,
      webhook[5].client_config[0].ca_bundle,
      webhook[6].client_config[0].ca_bundle,
      webhook[7].client_config[0].ca_bundle,
    ]
  }

  webhook {
    name                      = "clusters.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-cluster"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["cluster"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "gits.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-git"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["gits"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "pods.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate--v1-pod"
      }
    }
    rule {
      api_groups   = [""]
      api_versions = ["*"]
      operations   = ["CREATE"]
      resources    = ["pods"]
    }
    object_selector {
      match_expressions {
        key      = "brix.openai.com/pod"
        operator = "Exists"
      }
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "plans.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-plan"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["plans"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "pools.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-pool"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["pools"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "queues.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-queue"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["queues"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "revisions.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-revision"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["revisions"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "skus.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/mutate-brix-openai-com-v1alpha1-sku"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["skus"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }
}

resource "kubernetes_validating_webhook_configuration" "brix-operator-validating-webhook" {
  metadata {
    name        = "brix-operator"
    labels      = { app = "brix", component = "operator" }
    annotations = { "cert-manager.io/inject-ca-from" = "system/brix-operator" }
  }

  lifecycle {
    ignore_changes = [
      webhook[0].client_config[0].ca_bundle,
      webhook[1].client_config[0].ca_bundle,
      webhook[2].client_config[0].ca_bundle,
      webhook[3].client_config[0].ca_bundle,
      webhook[4].client_config[0].ca_bundle,
      webhook[5].client_config[0].ca_bundle,
      webhook[6].client_config[0].ca_bundle,
      webhook[7].client_config[0].ca_bundle,
    ]
  }

  webhook {
    name                      = "clusters.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-cluster"
      }
    }
    rule {
      api_groups   = ["brix.openaic.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["clusters"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "gits.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-git"
      }
    }
    rule {
      api_groups   = ["brixl.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["gits"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "pods.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate--v1-pod"
      }
    }
    rule {
      api_groups   = [""]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["pods"]
    }
    object_selector {
      match_expressions {
        key      = "brix.openai.com/pod"
        operator = "Exists"
      }
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "plans.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-plan"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["plans"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "pools.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-pool"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["pools"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "queues.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-queue"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["queues"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "revisions.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-revision"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["revisions"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }

  webhook {
    name                      = "skus.brix.openai.com"
    admission_review_versions = ["v1"]
    side_effects              = "None"
    client_config {
      service {
        namespace = "system"
        name      = "brix-operator"
        path      = "/validate-brix-openai-com-v1alpha1-sku"
      }
    }
    rule {
      api_groups   = ["brix.openai.com"]
      api_versions = ["*"]
      operations   = ["CREATE", "UPDATE"]
      resources    = ["skus"]
    }
    namespace_selector {
      match_expressions {
        key      = "control-plane"
        operator = "NotIn"
        values   = ["true"]
      }
      match_expressions {
        key      = "kubernetes.azure.com/managedby"
        operator = "NotIn"
        values   = ["aks"]
      }
    }
  }
}

resource "kubernetes_deployment_v1" "brix-operator-deployment" {
  depends_on = [
    kubernetes_config_map.brix-operator-config-map,
    kubernetes_cluster_role_v1.brix-operator-cluster-role,
    kubernetes_cluster_role_binding_v1.brix-operator-cluster-role-binding,
    kubernetes_service_account_v1.brix-operator-service-account,
  ]
  metadata {
    name      = "brix-operator"
    namespace = "system"
    labels    = { app = "brix", component = "operator" }
  }
  spec {
    replicas          = 3
    min_ready_seconds = 10
    selector {
      match_labels = { app = "brix", component = "operator" }
    }
    template {
      metadata {
        labels = { app = "brix", component = "operator" }
        annotations = {
          // Force redeploy on config-map change
          "brix-operator-configuration-hash" = sha1(jsonencode(kubernetes_config_map.brix-operator-config-map.data))
        }
      }
      spec {
        volume {
          name = "brix-operator-configuration"
          config_map {
            name = "brix-operator"
          }
        }
        volume {
          name = "brix-operator-certificate"
          secret {
            secret_name = "brix-operator"
          }
        }
        container {
          name = "main"
          // TODO
          image = "${local.repository}/brix-operator:${var.image_tag}"
          command = [
            "/opt/brix-operator",
            "--config",
            "/etc/brix-operator/brix-operator.yaml",
            "-v",
            "4",
          ]
          port {
            name           = "https"
            container_port = "443"
          }
          port {
            name           = "http"
            container_port = 8080
          }
          port {
            name           = "metrics"
            container_port = 8081
          }
          resources {
            limits = { cpu = var.operator_cpu, memory = var.operator_memory }
          }
          volume_mount {
            name       = "brix-operator-configuration"
            mount_path = "/etc/brix-operator"
          }
          volume_mount {
            name       = "brix-operator-certificate"
            mount_path = "/etc/certificate"
            read_only  = true
          }
          startup_probe {
            http_get {
              path = "/livez"
              port = "http"
            }
          }
          liveness_probe {
            http_get {
              path = "/livez"
              port = "http"
            }
          }
          readiness_probe {
            http_get {
              path = "/readyz"
              port = "http"
            }
          }
        }
        service_account_name = "brix-operator"
        toleration {
          key      = "openai.com/maintenance"
          operator = "Exists"
          effect   = "NoSchedule"
        }
        toleration {
          key      = "node.kubernetes.io/role"
          operator = "Equal"
          value    = "control-plane"
          effect   = "NoSchedule"
        }
        toleration {
          key      = "openai.com/team"
          operator = "Equal"
          value    = "infra"
          effect   = "NoSchedule"
        }
        affinity {
          node_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 100
              preference {
                match_expressions {
                  key      = "opernai.com/maintenance-status"
                  operator = "NotIn"
                  values   = ["requested"]
                }
              }
            }
          }
          pod_anti_affinity {
            required_during_scheduling_ignored_during_execution {
              label_selector {
                match_expressions {
                  key      = "app"
                  operator = "In"
                  values   = ["brix"]
                }
                match_expressions {
                  key      = "component"
                  operator = "In"
                  values   = ["operator"]
                }
              }
              topology_key = "kubernetes.io/hostname"
            }
          }
        }
        priority_class_name = "system-cluster-critical"
      }
    }
    strategy {
      type = "Recreate"
    }
  }
}
