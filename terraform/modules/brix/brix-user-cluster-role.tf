# give users access to brix clusters and node listing.
resource "kubernetes_cluster_role" "brix_user_cluster_role" {
  metadata {
    name = "brix-cluster-access"
  }
  rule {
    api_groups = ["brix.openai.com"]
    resources  = ["clusters", "quotas", "skus", "pools", "workloads" ]
    verbs      = ["get", "list", "watch"]
  }
  # brix agent running as default service account needs to list nodes.
  rule {
    api_groups = [""]
    resources  = ["nodes"]
    verbs      = ["get", "list", "watch"]
  }
}
