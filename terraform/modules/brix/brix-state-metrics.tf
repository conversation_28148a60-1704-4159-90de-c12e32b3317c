# Brix State Metrics deployment
# Based on infra-mirror pattern from OpenAI

resource "kubernetes_service_account_v1" "brix-state-metrics-service-account" {
  metadata {
    name      = "brix-state-metrics"
    namespace = "system"
    labels    = { app = "brix", component = "state-metrics" }
  }
}

resource "kubernetes_cluster_role_binding_v1" "brix-state-metrics-cluster-role-binding" {
  depends_on = [
    kubernetes_service_account_v1.brix-state-metrics-service-account,
  ]
  metadata {
    name = "brix-state-metrics"
  }
  subject {
    kind      = "ServiceAccount"
    name      = "brix-state-metrics"
    namespace = "system"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-view"  # Read-only access to Brix resources
  }
}

resource "kubernetes_deployment_v1" "brix-state-metrics-deployment" {
  depends_on = [
    kubernetes_service_account_v1.brix-state-metrics-service-account,
    kubernetes_cluster_role_binding_v1.brix-state-metrics-cluster-role-binding,
    kubernetes_deployment_v1.brix-operator-deployment, # Ensure CRDs exist
  ]
  metadata {
    name      = "brix-state-metrics"
    namespace = "system"
    labels    = { app = "brix", component = "state-metrics" }
  }
  spec {
    replicas = 1
    selector {
      match_labels = { app = "brix", component = "state-metrics" }
    }
    template {
      metadata {
        labels = { app = "brix", component = "state-metrics" }
      }
      spec {
        container {
          name  = "main"
          image = "${local.repository}/brix-state-metrics:${var.state_metrics_image_tag != "" ? var.state_metrics_image_tag : var.image_tag}"
          args = [
            "--port", "8080",
          ]
          port {
            name           = "metrics"
            container_port = 8080
          }
          port {
            name           = "http"
            container_port = 8080
          }
          resources {
            limits = { 
              cpu    = var.state_metrics_cpu 
              memory = var.state_metrics_memory 
            }
            requests = {
              cpu    = var.state_metrics_cpu
              memory = var.state_metrics_memory
            }
          }
          startup_probe {
            http_get {
              path = "/livez"
              port = "http"
            }
            initial_delay_seconds = 5
            period_seconds        = 10
            failure_threshold     = 3
          }
          liveness_probe {
            http_get {
              path = "/livez"
              port = "http"
            }
            initial_delay_seconds = 10
            period_seconds        = 30
          }
          readiness_probe {
            http_get {
              path = "/readyz"
              port = "http"
            }
            initial_delay_seconds = 5
            period_seconds        = 10
          }
        }
        service_account_name = "brix-state-metrics"
        node_selector = {
          "singularity.azure.com/processing-unit" = "cpu"
        }
        toleration {
          key      = "openai.com/team"
          operator = "Equal"
          value    = "infra"
          effect   = "NoSchedule"
        }
        affinity {
          pod_anti_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 100
              pod_affinity_term {
                label_selector {
                  match_expressions {
                    key      = "app"
                    operator = "In"
                    values   = ["brix"]
                  }
                  match_expressions {
                    key      = "component"
                    operator = "In"
                    values   = ["state-metrics"]
                  }
                }
                topology_key = "kubernetes.io/hostname"
              }
            }
          }
        }
        priority_class_name = "system-cluster-critical"
      }
    }
  }
}

# Note: No service is created as metrics are scraped directly from pods by OpenTelemetry collectors