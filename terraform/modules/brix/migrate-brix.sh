#!/bin/bash

# migrate brix terraform state where brix is responsible for managing the CRDs.
# this should only be run once when brix is updated. otherwise it is a noop.

set -e

echo "deleting state module.aks-cluster.module.brix.kubernetes_manifest.brix_crds*"
terraform state list | grep module.aks-cluster.module.brix.kubernetes_manifest.brix_crds | sed 's/"/\\"/g' | xargs -I{} bash -c "echo {}; terraform state rm '{}'"

echo "deleting state module.aks-cluster.module.brix.kubernetes_manifest.skus*"
terraform state list | grep module.aks-cluster.module.brix.kubernetes_manifest.skus | sed 's/"/\\"/g' | xargs -I{} bash -c "echo {}; terraform state rm '{}'"

echo "deleting state module.aks-cluster.module.brix.kubernetes_manifest.clusters"
terraform state list | grep module.aks-cluster.module.brix.kubernetes_manifest.clusters | xargs -I{} bash -c "echo {}; terraform state rm '{}'"

