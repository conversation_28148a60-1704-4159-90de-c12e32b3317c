apiVersion: components.brix.openai.com/v1alpha1
kind: BrixOperatorConfiguration
cluster: ${cluster}
domain: dev.openai.org
leaderElection:
  resourceName: brix-operator-lock
  resourceNamespace: system
clientConnection:
  qps: 50
  burst: 200
serving:
  port: 8080
  securePort: 443
  metricsPort: 8081
agent:
  installerImageName: "${acr_base}/brix-installer"
  installerImageTag: "${installer_image_tag}"
  defaultImageName: "${acr_base}/brix-base"
  defaultImageTag: "${installer_image_tag}"
tls:
  certDir: /etc/certificate
  certName: tls.crt
  keyName: tls.key
  issuerRef:
    name: selfsigned-issure
    kind: ClusterIssuer
    group: cert-manager.io
gitController:
  # Git alternates image with torchflow-mirror repository snapshot for faster git operations
  defaultAlternatesImage: "iridiumsdc.azurecr.io/orange/git-alternates:2025-07-10-12-17-32"
  server:
    podTemplate:
      metadata:
        annotations:
          brix.openai.com/git-max-requests: "8"
          brix.openai.com/git-request-timeout: "60m"
          brix.openai.com/git-alternates: "enabled"
      spec:
        # brix-git pods have PVs, and so we need to land them on the cpu node pool which has the csi controller
        # There are other node pools which would otherwise be schedulable, but PVs would fail to attach.
        nodeSelector:
          singularity.azure.com/processing-unit: "cpu"
        tolerations:
          - key: openai.com/team
            effect: NoSchedule
            operator: Exists
        priorityClassName: "team-critical"
        restartPolicy: Never
        containers:
          - name: main
    persistentVolumeClaimTemplate:
      spec:
        storageClassName: managed-premium
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 16Gi
poolController:
  concurrentSyncs: 16
  syncParallelism: 50
queueController:
  concurrentSyncs: 16
