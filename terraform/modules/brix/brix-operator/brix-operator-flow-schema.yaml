apiVersion: ${api_version}
kind: FlowSchema
metadata:
  name: brix-operator
  labels:
    app: brix
    component: operator
spec:
  distinguisherMethod:
    type: ByNamespace
  matchingPrecedence: 800
  priorityLevelConfiguration:
    name: workload-high
  rules:
    - nonResourceRules:
        - nonResourceURLs:
            - "*"
          verbs:
            - "*"
      resourceRules:
        - apiGroups:
            - "*"
          clusterScope: true
          namespaces:
            - "*"
          resources:
            - "*"
          verbs:
            - "*"
      subjects:
        - kind: ServiceAccount
          serviceAccount:
            namespace: system
            name: brix-operator
