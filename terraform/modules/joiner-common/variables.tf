variable "namespace" {
  type = string
}

variable "env" {
  type    = string
  default = "dev"
}

variable "experiment_schedule_queue_url" {
  type    = string
  default = "https://orngeventsdevuks.queue.core.windows.net/joiner-experiment-schedule-queue"
}

variable "experiment_schedule_failed_queue_url" {
  type    = string
  default = "https://orngeventsdevuks.queue.core.windows.net/joiner-experiment-schedule-failed-queue"
}

variable "experiment_input_dir" {
  type    = string
  default = "az://orngsnowflakeuks/strawberry/upload/v1/sample_events"
}

variable "seen_experiments_dir" {
  type    = string
  default = "az://orngeventsdevuks/joiner/seen_experiments"
}

variable "snowpipe_output_dir" {
  type    = string
  default = "az://orngsnowflakeuks/strawberry/eventjoiner/upload/v1/joined_sample_events"
}

variable "joiner_storage_container" {
  type    = string
  default = "az://orngeventsdevuks/joiner"
}

variable "remoteconfig_base_dir" {
  type    = string
  default = "az://orngeventsdevuks/configs"
}

variable "failure_count_warning_threshold" {
  type    = number
  default = 1
}

variable "failure_count_dead_letter_threshold" {
  type    = number
  default = 2
}

variable "failure_max_backoff_minutes" {
  type    = number
  default = 1
}

variable "inactive_grace_period_days" {
  type    = number
  default = 0
}

variable "kubernetes_sp_secret_name" {
  type        = string
  description = "Name of the kubernetes secret"
}

variable "sp_tenant_id" {
  type        = string
  description = "Tenant ID of the service principal" # Green tenant
}

variable "client_id_name" {
  description = "Name of the secret containing the client id"
  type        = string
  default     = "joiner-clientid"
}

variable "client_secret_name" {
  description = "Name of the secret containing the client password"
  type        = string
  default     = "joiner-secret"
}

variable "infra_sync_keyvault" {
  type = object({
    id                = string
    subscription_name = string
    subscription_id   = string
    tenant_id         = string
  })
}