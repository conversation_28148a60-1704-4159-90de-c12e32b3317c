data "azurerm_key_vault_secret" "client_id" {
  provider     = azurerm.infra-secret-reader
  name         = var.client_id_name
  key_vault_id = var.infra_sync_keyvault.id
}


data "azurerm_key_vault_secret" "client_secret" {
  provider     = azurerm.infra-secret-reader
  name         = var.client_secret_name
  key_vault_id = var.infra_sync_keyvault.id
}

resource "kubernetes_secret" "azure-service-principal" {
  metadata {
    name      = var.kubernetes_sp_secret_name
    namespace = var.namespace
  }

  data = {
    AZURE_CLIENT_ID     = data.azurerm_key_vault_secret.client_id.value
    AZURE_TENANT_ID     = var.sp_tenant_id
    AZURE_CLIENT_SECRET = data.azurerm_key_vault_secret.client_secret.value
  }
}