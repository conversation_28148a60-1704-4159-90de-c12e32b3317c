locals {
  configmap_name = var.env == "dev" ? "joiner-dev-configmap" : ""
}

resource "kubernetes_config_map" "configmap" {
  metadata {
    name      = local.configmap_name
    namespace = var.namespace
  }

  data = {
    JOINER_ENV                                  = var.env
    JOINER_EXPERIMENT_SCHEDULE_QUEUE_URL        = var.experiment_schedule_queue_url
    JOINER_EXPERIMENT_SCHEDULE_FAILED_QUEUE_URL = var.experiment_schedule_failed_queue_url
    JOINER_EXPERIMENT_INPUT_DIR                 = var.experiment_input_dir
    JOINER_EVENTS_MIN_LOOKBACK_SECS             = 3600
    JOINER_EVENTS_MAX_ROLLOUT_WINDOW_HOURS      = 36
    JOINER_SEEN_EXPERIMENTS_DIR                 = var.seen_experiments_dir
    JOINER_SNOWPIPE_OUTPUT_DIR                  = var.snowpipe_output_dir
    JOINER_STORAGE_CONTAINER                    = var.joiner_storage_container
    REMOTECONFIG_BASE_DIR                       = var.remoteconfig_base_dir
    JOINER_LOG_LEVEL                            = "DEBUG"
    # Generally set to the number of cores on the machine.
    JOINER_STORE_NUM_SERVER_WORKERS  = "8"
    JOINER_STORE_ROCKSDB_PARALLELISM = "8"
    # Needs to be set in tandem with the amount of CPUs and RAM available on the machine.
    JOINER_STORE_WRITE_BUFFER_MANAGER_SIZE = 17179869184 # 16GiB
    JOINER_STORE_BLOCK_CACHE_SIZE          = 17179869184 # 16GiB
    # Delete data once the join is done.
    JOINER_DELETE_EXPERIMENT_DATA_AFTER_DONE   = "true"
    JOINER_DELETE_EVENTS_OUTSIDE_WINDOW        = "true"
    JOINER_ABANDON_OLD_STRAGGLERS              = "true"
    JOINER_FAILURE_COUNT_WARNING_THRESHOLD     = var.failure_count_warning_threshold
    JOINER_FAILURE_COUNT_DEAD_LETTER_THRESHOLD = var.failure_count_dead_letter_threshold
    JOINER_FAILURE_MAX_BACKOFF_MINUTES         = var.failure_max_backoff_minutes
    JOINER_INACTIVE_GRACE_PERIOD_DAYS          = var.inactive_grace_period_days
    # Whether to use workload identity to get credential and create the azure queue client using it
    USE_WORKLOAD_IDENTITY = "true"
  }
}
