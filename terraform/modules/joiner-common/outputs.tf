output "kubernetes_configmap_name" {
  value = kubernetes_config_map.configmap.metadata.0.name
}

locals {
  configmap_store = {
    for k, v in kubernetes_config_map.configmap.data : k => v if startswith(k, "JOINER_STORE_")
  }
  configmap_non_store = {
    for k, v in kubernetes_config_map.configmap.data : k => v if !startswith(k, "JOINER_STORE_")
  }
}

output "kubernetes_configmap_hash" {
  description = "Hash of configmap (non-JOINER_STORE_* env vars) to trigger redeployment on change"
  value       = sha1(jsonencode(local.configmap_non_store))
}

output "kubernetes_store_configmap_hash" {
  description = "Hash of JOINER_STORE_* configmap env vars to trigger redeployment on change"
  value       = sha1(jsonencode(local.configmap_store))
}
