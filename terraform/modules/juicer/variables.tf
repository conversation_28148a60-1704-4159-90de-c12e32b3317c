variable "location" {
  type        = string
  description = "Azure location for the monitoring resources, e.g. uksouth"
}

variable "cluster_name" {
  type        = string
  description = "Name of the AKS cluster"
}

variable "cluster_rg" {
  type        = any
  description = "Data or Resource for Azure resource group for the AKS cluster"
}

variable "oidc_issuer_url" {
  type        = string
  description = "OIDC issuer url for federated identity credential"
}

variable "prometheus_cluster_name" {
  type        = string
  description = "cluster name for the monitoring resources, e.g. azml_prod_uksouth_azhub_7"
}

variable "prometheus_env" {
  type        = string
  description = "The environment for prometheus"
}

variable "prometheus_url" {
  type        = string
  description = "The url used when reading data from prometheus"
}

variable "acr" {
  type    = string
  default = "iridiumsdc.azurecr.io"
}

variable "image_tag" {
  type        = string
  description = "The image tag for the juicer container"
  default     = "juicer-v0.1.0-f90c71da"
}

variable "namespace" {
  type    = string
  default = "system"
}