resource "kubernetes_cron_job_v1" "juicer" {
  depends_on = [
    kubernetes_cluster_role_binding_v1.juicer,
    kubernetes_service_account.juicer,
    kubernetes_config_map.juicer-configmap
  ]
  metadata {
    name      = "juicer"
    namespace = var.namespace
  }

  spec {
    schedule                      = "0 */2 * * *" # Every 2 hours. Also modify juicer/main.go
    timezone                      = "America/Los_Angeles"
    successful_jobs_history_limit = 1
    failed_jobs_history_limit     = 1
    job_template {
      metadata {
        name = "juicer"
      }
      spec {
        template {
          metadata {
            labels = {
              app                                 = "juicer"
              "azure.workload.identity/use"       = "true"
              "azure.workload.identity/client-id" = local.identity_client_id
            }
          }

          spec {
            container {
              name  = "juicer"
              image = "${var.acr}/infra/juicer:${var.image_tag}"
              env {
                name  = "REGION"
                value = var.location
              }
              env {
                name  = "CLUSTER_NAME"
                value = var.prometheus_cluster_name
              }
              env {
                name  = "PROMETHEUS_URL"
                value = var.prometheus_url
              }
              env_from {
                config_map_ref {
                  name = kubernetes_config_map.juicer-configmap.metadata[0].name
                }
              }
            }
            restart_policy                  = "Never"
            service_account_name            = kubernetes_service_account.juicer.metadata[0].name
            automount_service_account_token = true
            affinity {
              node_affinity {
                preferred_during_scheduling_ignored_during_execution {
                  weight = 100
                  preference {
                    match_expressions {
                      key      = "singularity.azure.com/processing-unit"
                      operator = "In"
                      values   = ["cpu", "system"]
                    }
                  }
                }
              }
            }
          }
        }
        backoff_limit = 3
      }
    }
  }
}

resource "kubernetes_service_account" "juicer" {
  metadata {
    name      = "juicer"
    namespace = var.namespace
    annotations = {
      "azure.workload.identity/client-id" = local.identity_client_id
    }
  }
}

resource "kubernetes_cluster_role_binding_v1" "juicer" {
  depends_on = [
    kubernetes_service_account.juicer
  ]

  metadata {
    name = "juicer"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-admin"
  }

  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.juicer.metadata[0].name
    namespace = var.namespace
  }
}

resource "kubernetes_config_map" "juicer-configmap" {
  metadata {
    name      = "juicer-configmap"
    namespace = var.namespace
  }

  data = {
    DRY_RUN              = "false",
    NOTIFICATION_PERIOD  = "8h",
    AUTO_PAUSE_PERIOD    = "12h",
    SKIP_SPECIAL_POOL    = "false",
    SKIP_NAMESPACES      = "^$",
    SKIP_CRITICAL_PRIORITY = "true",
    LOGIC_APP_SUBSCRIPTION_ID : "57ef2365-3a4a-4150-ac28-1ec2563c43c4",
    LOGIC_APP_RESOURCE_GROUP : "orange-juicer-ame",
    LOGIC_APP_WORKFLOW_NAME : "juicer-notification-logicapp",
    LOGIC_APP_TRIGGER_NAME : "When_a_HTTP_request_is_received",
    LOGIC_APP_API_VERSION : "2016-10-01"
  }
}
