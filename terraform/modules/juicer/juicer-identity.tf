resource "azurerm_role_assignment" "juicer_monitoring_reader" {
  scope                = var.cluster_rg.id
  role_definition_name = "Monitoring Reader"
  principal_id         = local.identity_principal_id
}

resource "azurerm_federated_identity_credential" "juicer_federated" {
  provider            = azurerm.ame-infra
  name                = var.cluster_name
  resource_group_name = local.resource_group_name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.oidc_issuer_url
  parent_id           = "/subscriptions/57ef2365-3a4a-4150-ac28-1ec2563c43c4/resourceGroups/orange-juicer-ame/providers/Microsoft.ManagedIdentity/userAssignedIdentities/orange-juicer-identity"
  subject             = "system:serviceaccount:${var.namespace}:${kubernetes_service_account.juicer.metadata[0].name}"
}

locals {
  resource_group_name   = "orange-juicer-ame"
  identity_name         = "orange-juicer-identity"
  identity_principal_id = "bbc529b5-f8cf-4bbb-bc97-378dba584661"
  identity_client_id    = "a52b5c3d-066b-49ef-aaad-759dfe9282b9"
}