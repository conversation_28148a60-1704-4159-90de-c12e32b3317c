output "server_id" {
  description = "The ID of the PostgreSQL Flexible Server"
  value       = azurerm_postgresql_flexible_server.postgres.id
}

output "server_fqdn" {
  description = "The FQDN of the PostgreSQL Flexible Server"
  value       = azurerm_postgresql_flexible_server.postgres.fqdn
}

output "admin_username" {
  description = "The administrator username of the PostgreSQL Flexible Server"
  value       = azurerm_postgresql_flexible_server.postgres.administrator_login
}

output "admin_password" {
  description = "The administrator password of the PostgreSQL Flexible Server"
  value       = random_password.postgres_password.result
  sensitive   = true
}

output "database_name" {
  description = "The name of the default database (always 'postgres' for PostgreSQL)"
  value       = "postgres"
}
