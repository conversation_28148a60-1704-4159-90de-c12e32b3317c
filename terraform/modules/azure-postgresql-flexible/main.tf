resource "random_password" "postgres_password" {
  length           = var.password_length
  special          = true
  override_special = var.password_special_chars
}

resource "azurerm_postgresql_flexible_server" "postgres" {
  name                          = var.database_name
  resource_group_name           = var.resource_group_name
  location                      = var.location
  version                       = var.database_version
  administrator_login           = var.administrator_login
  administrator_password        = random_password.postgres_password.result
  storage_mb                    = var.storage_mb
  sku_name                      = var.sku_name
  backup_retention_days         = var.backup_retention_days
  tags                          = var.tags
  zone                          = var.zone
  delegated_subnet_id           = var.delegated_subnet_id
  private_dns_zone_id           = var.private_dns_zone_id
  public_network_access_enabled = var.public_network_access_enabled
}
