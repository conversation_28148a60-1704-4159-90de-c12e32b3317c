variable "database_name" {
  description = "The name of the PostgreSQL flexible server"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The Azure region to deploy to"
  type        = string
}

variable "database_version" {
  description = "PostgreSQL version for the database"
  type        = string
  default     = "16"
}

variable "storage_mb" {
  description = "Storage size for the PostgreSQL database in MB"
  type        = number
}

variable "sku_name" {
  description = "The SKU name for the PostgreSQL server"
  type        = string
}

variable "backup_retention_days" {
  description = "Backup retention days for the server"
  type        = number
  default     = 30
}

variable "tags" {
  description = "Tags to be applied to resources"
  type        = map(string)
  default     = {}
}

variable "administrator_login" {
  description = "The administrator login name for the PostgreSQL server"
  type        = string
  default     = "psqladmin"
}

variable "password_length" {
  description = "Length of the generated password for PostgreSQL administrator"
  type        = number
  default     = 16
}

variable "password_special_chars" {
  description = "Special characters to use in the PostgreSQL administrator password"
  type        = string
  default     = "!#$%&*()-_=+[]{}<>:?"
}

variable "zone" {
  description = "Availability zone for the PostgreSQL server"
  type        = string
  default     = null
}

variable "delegated_subnet_id" {
  description = "The ID of the subnet delegated to the PostgreSQL flexible server"
  type        = string
  default     = null
}

variable "private_dns_zone_id" {
  description = "The ID of the private DNS zone for PostgreSQL flexible server"
  type        = string
  default     = null
}

variable "public_network_access_enabled" {
  description = "Enable or disable public network access for the PostgreSQL flexible server"
  type        = bool
  default     = false
}