variable "namespace" {
  type = string
}

variable "name_suffix" {
  type = string
}

variable "image" {
  type = string
}

variable "configmap_name" {
  type = string
}

variable "config_hash" {
  type = string
}

variable "replicas" {
  type = number
}

variable "resource_limit_cpu" {
  type    = string
  default = "12000m"
}

variable "resource_limit_memory" {
  type    = string
  default = "48Gi"
}

variable "resource_request_cpu" {
  type    = string
  default = "8000m"
}

variable "resource_request_memory" {
  type    = string
  default = "32Gi"
}

variable "store_statefulset_name" {
  type = string
}

variable "store_service_name" {
  type = string
}

variable "override_quota_team" {
  type        = string
  default     = ""
  description = <<-EOT
  Set to use a specific team's quota instead of namespace default. Value should match the
  `openai.com/team` taint on the desired nodes.
EOT
}

variable "priority_class_name" {
  type        = string
  description = "Name of the priority class to use for the pods"
  default     = "team-infra-high"
}

variable "kubernetes_sp_secret_name" {
  type        = string
  description = "Name of the kubernetes secret"
}