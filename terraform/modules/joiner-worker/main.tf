locals {
  namespace = var.namespace
  name      = "joiner-worker${var.name_suffix}"
}

resource "kubernetes_deployment" "joiner_worker" {
  metadata {
    name      = local.name
    namespace = var.namespace
  }

  spec {
    replicas = var.replicas

    selector {
      match_labels = {
        app = local.name
      }
    }

    template {
      metadata {
        labels = {
          app = local.name
        }
        # Use annotation to trigger redeployment on secret or configmap change
        annotations = { config_hash = var.config_hash }
      }

      spec {
        termination_grace_period_seconds = 30

        priority_class_name = var.priority_class_name

        node_selector = {
          "kubernetes.azure.com/mode"             = "user"
          "singularity.azure.com/processing-unit" = "cpu"
        }

        container {
          name  = local.name
          image = var.image


          env_from {
            config_map_ref {
              name = var.configmap_name
            }
          }

          env_from {
            secret_ref {
              name = var.kubernetes_sp_secret_name
            }
          }

          env {
            name  = "JOINER_STORE_STATEFULSET_NAME"
            value = var.store_statefulset_name
          }
          env {
            name  = "JOINER_STORE_SERVICE_NAME"
            value = var.store_service_name
          }
          # Disable the boostedblob token cache in case it's the source of our invalid token errors.
          # Context: https://oai-post-training-col.slack.com/archives/CV4HMFDQR/p1735936157154669?thread_ts=1735000947.003889&cid=CV4HMFDQR
          # TODO(nf-openai): Re-enable once we've synced in the os.rename() fix, or if we keep it
          #   disabled, co-locate with AZURE_USE_IDENTITY.
          env {
            name  = "BBB_DISABLE_CACHE"
            value = "1"
          }

          port {
            container_port = 9090
            name           = "metrics"
          }

          liveness_probe {
            http_get {
              path = "/"
              port = 9090
            }

            period_seconds    = 10
            timeout_seconds   = 10
            failure_threshold = 12 # 2 minutes
          }
          resources {
            limits = {
              cpu    = var.resource_limit_cpu
              memory = var.resource_limit_memory
            }
            requests = {
              cpu    = var.resource_request_cpu
              memory = var.resource_request_memory
            }
          }

          # Allows to profile with py-spy and similar tools.
          security_context {
            capabilities {
              add = ["SYS_PTRACE"]
            }
          }
        }
      }
    }
  }
}
