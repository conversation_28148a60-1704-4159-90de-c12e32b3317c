resource "azurerm_user_assigned_identity" "lemon_identity" {
  name                = "lemon_identity"
  resource_group_name = var.cluster_rg.name
  location            = var.cluster_rg.location
}

resource "azurerm_role_assignment" "lemon_monitoring_reader" {
  scope                = var.cluster_rg.id
  role_definition_name = "Monitoring Reader"
  principal_id         = azurerm_user_assigned_identity.lemon_identity.principal_id
}


resource "azurerm_role_assignment" "lemon_monitoring_data_reader" {
  scope                = "/subscriptions/fbbd0f8a-b594-4fee-b381-3713acf07e7e/resourceGroups/orange-${var.prometheus_env}-azure-monitor-workspace_rg/providers/Microsoft.Monitor/accounts/orange-${var.prometheus_env}-amw"
  role_definition_name = "Monitoring Data Reader"
  principal_id         = azurerm_user_assigned_identity.lemon_identity.principal_id

}

resource "azurerm_federated_identity_credential" "lemon_federated" {
  name                = "lemon_federated"
  resource_group_name = var.cluster_rg.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.lemon_identity.id
  subject             = "system:serviceaccount:lemon:grafana-account"
}

resource "azurerm_federated_identity_credential" "lemon_federated_adx" {
  name                = "lemon_federated_adx"
  resource_group_name = var.cluster_rg.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.lemon_identity.id
  subject             = "system:serviceaccount:${kubernetes_namespace.lemon.metadata[0].name}:${kubernetes_service_account.adx-account.metadata[0].name}"
}

resource "kubernetes_service_account" "adx-account" {
  metadata {
    name = "adx-account"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    annotations = {
      "azure.workload.identity/client-id": azurerm_user_assigned_identity.lemon_identity.client_id
    }
  }
}
