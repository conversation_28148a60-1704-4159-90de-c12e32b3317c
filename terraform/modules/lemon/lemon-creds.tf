data "azurerm_key_vault_secret" "lemon_secret" {
  name         = var.lemon_secret_name
  key_vault_id = var.orng_observability_keyvault.id
  provider     = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "lemon_app_client_id" {
  name         = "${var.lemon_app_name}-clientid"
  key_vault_id = var.orng_observability_keyvault.id
  provider     = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "lemon_app_secret" {
  name         = "${var.lemon_app_name}-secret"
  key_vault_id = var.orng_observability_keyvault.id
  provider     = azurerm.infra-secret-reader
}

resource "kubernetes_secret" "lemon-service-principal" {
  metadata {
    name      = "lemon-service-principal"
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }

  data = {
    AZURE_CLIENT_ID     = data.azurerm_key_vault_secret.lemon_app_client_id.value
    AZURE_TENANT_ID     = var.orng_observability_keyvault.tenant_id
    AZURE_CLIENT_SECRET = data.azurerm_key_vault_secret.lemon_app_secret.value
  }
}

data "azurerm_key_vault_secret" "snowflake_password" {
  name         = var.lemon_snowflake_secret_name
  key_vault_id = var.orng_observability_keyvault.id
  provider = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "snowflake_user" {
  name         = var.lemon_snowflake_user_name
  key_vault_id = var.orng_observability_keyvault.id
  provider = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "wandb_secret" {
  name         = var.wandb_secret_name
  key_vault_id = var.orng_observability_keyvault.id
  provider = azurerm.infra-secret-reader
}