locals {
  configmap_name = "lemon-configmap"
  configmap_sha = sha256(yamlencode(kubernetes_config_map.lemon-configmap.data))
}

locals {
  lemon_adx_token_dir  = "/var/run/secrets/azure/adx-account-tokens"
  lemon_adx_token_file = "lemon-adx-token"
  lemon_adx_token_name = "lemon-adx-token"
  use_single_lemon_env = contains(["prod-uksouth-7", "prod-southcentralus-hpe-3", "prod-southcentralus-hpe-5", "stage-southcentralus-hpe-1"], var.cluster_name) ? [1] : []
}

resource "kubernetes_config_map" "lemon-configmap" {
  metadata {
    name      = local.configmap_name
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }

  data = {
      SNOWFLAKE_WAREHOUSE      = "COMPUTE_WH"
      LEMON_SNOWFLAKE_USERNAME = "WEBSERVICEUSER"
      SNOWFLAKE_USERNAME       = "WEBSERVICEUSER"
      WANDB_ORGANIZATION       = "https://msaip.wandb.io"
      PERSONAL_NAMESPACE       = "True"
      USE_AZURE_LOGGING        = "True"
      SB_METRICS_BASE_URL      = "http://asyncmetrics.orange-7.internal.genai.ms/"
      LEMON_MSFT               = "True"
      NOTEBOOKS_AZ_DIR         = "az://iridiumsnowflakeuks/strawberry/upload/v1/notebooks"
      OPENAI_USER              = "lemon"
  }
}

# Lemon requires credentials for both GREEN and AME accounts. GREEN credentials will be set as default for storage 
# access, similar to OAI. For ADX in AME, we use federated credentials and set LEMON_ADX_* environment 
# variables for auth, to avoid overwriting default GREEN credentials with the use_workload_identity pod annotation.
resource "kubernetes_deployment" "lemon" {
  metadata {
    name = "lemon"
    labels = {
      app = "lemon"
    }
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }

  spec {
    replicas = 2
    selector {
      match_labels = {
        app = "lemon"
      }
    }

    template {
      metadata {
        labels = {
          app = "lemon"
        }
        annotations = {
          "checksum/config" = local.configmap_sha
        }
      }

      spec {
        service_account_name = kubernetes_service_account.adx-account.metadata[0].name

        volume {
          name = local.lemon_adx_token_name
          projected {
            sources {
              service_account_token {
                path     = local.lemon_adx_token_file
                audience = "api://AzureADTokenExchange"
              }
            }
          }
        }

        container {
            name  = "lemon"
            image = "iridiumsdc.azurecr.io/infra/lemon:1287738"
            port {
              name           = "http"
              container_port = 3000
            }

            volume_mount {
              name       = local.lemon_adx_token_name
              mount_path = local.lemon_adx_token_dir
              read_only  = true
            }

            env_from {
              config_map_ref {
                name = local.configmap_name
              }
            }
            env_from {
              secret_ref {
                name = kubernetes_secret.lemon-service-principal.metadata[0].name
              }
            }
            env {
              name  = "LEMON_SECRET_KEY"
              value = data.azurerm_key_vault_secret.lemon_secret.value
            }
            env {
              name  = "WANDB_API_KEY"
              value = data.azurerm_key_vault_secret.wandb_secret.value
            }
            env {
              name  = "LEMON_SNOWFLAKE_PASSWORD"
              value = data.azurerm_key_vault_secret.snowflake_password.value
            }
            env {
              name = "SNOWFLAKE_PASSWORD"
              value = data.azurerm_key_vault_secret.snowflake_password.value
            }
            env {
              name  = "SNOWFLAKE_ACCOUNT"
              value = data.azurerm_key_vault_secret.snowflake_user.value
            }
            env {
              name  = "LEMON_GRAFANA_URL"
              value = var.lemon_grafana_url
            }
            env {
              name  = "LEMON_ADX_CLIENT_ID"
              value = azurerm_user_assigned_identity.lemon_identity.client_id
            }
            env {
              name  = "LEMON_ADX_TENANT_ID"
              value = azurerm_user_assigned_identity.lemon_identity.tenant_id
            }
            env {
              name  = "LEMON_ADX_TOKEN_FILE_PATH"          
              value = "${local.lemon_adx_token_dir}/${local.lemon_adx_token_file}"
            }
            env {
              name  = "LEMON_ADX_KUSTO_CLUSTER_EU"
              value = var.lemon_adx_kusto_cluster_eu

            }
            env {
              name  = "LEMON_ADX_KUSTO_CLUSTER_US"
              value = var.lemon_adx_kusto_cluster_us
            }
            env {
              name  = "LEMON_ADX_KUSTO_DATABASE"
              value = var.lemon_adx_kusto_database
            }
            dynamic "env" {
              for_each = local.use_single_lemon_env
              content {
                name  = "USE_SINGLE_LEMON"
                value = "True"
              }
            }
        }
      }
    }
  }
}

resource "kubernetes_service" "lemon_service" {
  metadata {
    name = "lemon-service"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    annotations = {
      "service.beta.kubernetes.io/azure-load-balancer-internal": "true"
      "config.hash": sha256(jsonencode(kubernetes_deployment.lemon.spec))
    }
  }
  spec {
    selector = {
      app = "lemon"
    }
    port {
      protocol    = "TCP"
      port        = 80
      target_port = 3000
    }
    type = "LoadBalancer"
  }
}

# Nginx reverse proxy ConfigMap for Tailscale-based service
resource "kubernetes_config_map" "nginx_reverse_proxy_config" {
  metadata {
    name      = "nginx-reverse-proxy-config"
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }
  data = {
    "nginx.conf" = <<-EOT
      worker_processes 1;
      events { worker_connections 1024; }
      http {
        resolver kube-dns.kube-system.svc.cluster.local valid=10s;
        server {
          listen 5000;
          location / {
            return 200 'nginx-reverse-proxy healthy';
            add_header Content-Type text/plain;
          }
          location ~ ^/api/([^/]+)/([^/]+)/([^/]+)/([^/]+)$ {
            set $route $1;
            set $job_id $2;
            set $rapid_id $3;
            set $user_id $4;
            proxy_pass http://$rapid_id-controller-w0-0.rcall.$user_id.svc.cluster.local:5000/api/$route?$args;
            proxy_set_header Host $host;
          }
        }
      }
    EOT
  }
}

# Nginx reverse proxy Deployment
resource "kubernetes_deployment" "nginx_reverse_proxy" {
  metadata {
    name      = "nginx-reverse-proxy"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    labels = {
      app = "nginx-reverse-proxy"
    }
  }
  spec {
    replicas = 2
    selector {
      match_labels = {
        app = "nginx-reverse-proxy"
      }
    }
    template {
      metadata {
        labels = {
          app = "nginx-reverse-proxy"
        }
      }
      spec {
        container {
          name  = "nginx"
          image = "mcr.microsoft.com/cbl-mariner/base/nginx:1.22"
          port {
            container_port = 5000
          }
          volume_mount {
            name       = "nginx-conf"
            mount_path = "/etc/nginx/nginx.conf"
            sub_path   = "nginx.conf"
            read_only  = true
          }
          liveness_probe {
            http_get {
              path = "/"
              port = 5000
            }
            initial_delay_seconds = 30
            period_seconds        = 10
          }
          readiness_probe {
            http_get {
              path = "/"
              port = 5000
            }
            initial_delay_seconds = 5
            period_seconds        = 10
          }
        }
        volume {
          name = "nginx-conf"
          config_map {
            name = kubernetes_config_map.nginx_reverse_proxy_config.metadata[0].name
          }
        }
      }
    }
  }
}

# Nginx reverse proxy Service
resource "kubernetes_service" "nginx_reverse_proxy" {
  metadata {
    name      = "nginx-reverse-proxy-${var.cluster_name}"
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }
  spec {
    type = "LoadBalancer"
    # Use Tailscale for load balancing to create magicdns address which will be passed on to lemonclient externalName service. 
    # magicdns address foramt is <k8s-namespace>-<k8s-servicename>, counter would be appeneded if there is already a service with same magicdns address in the tailnet.
    # refer https://tailscale.com/kb/1439/kubernetes-operator-cluster-ingress#exposing-a-cluster-workload-via-a-tailscale-load-balancer-service for more details.
    # magicdns address generated for this service will be lemon-nginx-reverse-proxy-${var.cluster_name}.tailefd4cb.ts.net
    load_balancer_class = "tailscale"
    selector = {
      app = "nginx-reverse-proxy"
    }
    port {
      port        = 80
      target_port = 5000
      protocol    = "TCP"
    }
  }
}

# Get cluster names from orange-clusters-config module (only in prod-uksouth-7)
module "orange_clusters_config" {
  source = "../orange-clusters-config"
}

locals {
  # Always produce a list of cluster names, even if not prod-uksouth-7, prod-southcentralus-hpe-3, or prod-southcentralus-hpe-5
  lemon_clusters = contains(["prod-uksouth-7", "prod-southcentralus-hpe-3", "prod-southcentralus-hpe-5", "stage-southcentralus-hpe-1"], var.cluster_name) ? keys(module.orange_clusters_config.clusters) : []
}

resource "kubernetes_service" "lemonclient" {
  for_each = toset(local.lemon_clusters)
  metadata {
    name = "lemonclient-${each.key}"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    # We access the reverse proxy service of other clusters through Tailscale.
    # This is achieved by setting the FQDN annotation to the MagicDNS address.
    # For more information, see: https://tailscale.com/kb/1438/kubernetes-operator-cluster
    annotations = {
      "tailscale.com/tailnet-fqdn" = "lemon-nginx-reverse-proxy-${each.key}.tailefd4cb.ts.net"
    }
  }
  spec {
    type         = "ExternalName"
    external_name = "unused"
  }
  lifecycle {
    ignore_changes = [spec[0].external_name]
  }
}

# Add a new variable for lemon ingress cluster name
variable "lemon_ingress_cluster_name" {
  description = "Cluster name for lemon ingress hostname. Only used for prod-uksouth-7 and stage-southcentralus-hpe-1. If not set, defaults to var.cluster_name."
  type        = string
  default     = null
}

# Local to resolve the effective cluster name for Ingress
locals {
  lemon_ingress_effective_cluster_name = var.lemon_ingress_cluster_name != null && var.lemon_ingress_cluster_name != "" ? var.lemon_ingress_cluster_name : var.cluster_name
}

# Lemon Ingress for internal-nginx
resource "kubernetes_ingress_v1" "lemon_internal" {
  count = contains(["prod-uksouth-7", "stage-southcentralus-hpe-1", "prod-southcentralus-hpe-3", "prod-southcentralus-hpe-5"], local.lemon_ingress_effective_cluster_name) ? 1 : 0
  metadata {
    name = "lemon-internal"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    annotations = {
      "kubernetes.io/ingress.global-static-ip-name" = "${local.lemon_ingress_effective_cluster_name}-kubernetes-ingress"
      "nginx.ingress.kubernetes.io/ssl-redirect" = "true"
      "nginx.ingress.kubernetes.io/force-ssl-redirect" = "true"
      "nginx.ingress.kubernetes.io/auth-cache-key" = "$oauth_cookie_cache_key$http_authorization"
      "nginx.ingress.kubernetes.io/auth-response-headers" = "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
      "nginx.ingress.kubernetes.io/auth-url" = "https://oai-azure-auth-proxy.int.${local.lemon_ingress_effective_cluster_name}.dev.openai.org/oauth2/auth?allowed_groups="
      "nginx.ingress.kubernetes.io/auth-signin" = "https://oauth2-proxy-core-group.aad-auth.int.${local.lemon_ingress_effective_cluster_name}.dev.openai.org/oauth2/start"
      "nginx.ingress.kubernetes.io/proxy-buffer-size" = "128k"
      "nginx.ingress.kubernetes.io/proxy-busy-buffers-size" = "512k"
      "nginx.ingress.kubernetes.io/proxy-buffers-number" = "4"
      "nginx.ingress.kubernetes.io/configuration-snippet" = <<-EOT
        proxy_set_header X-User-Email $upstream_http_x_auth_request_email;
        add_header Cache-Control "no-store";
      EOT
    }
  }
  spec {
    ingress_class_name = "internal-nginx"
    rule {
      host = "lemon.int.${local.lemon_ingress_effective_cluster_name}.dev.openai.org"
      http {
        path {
          path = "/"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service.lemon_service.metadata[0].name
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    tls {
      hosts = ["lemon.int.${local.lemon_ingress_effective_cluster_name}.dev.openai.org"]
    }
  }
  depends_on = [
    kubernetes_service.lemon_service,
    kubernetes_namespace.lemon
  ]
}