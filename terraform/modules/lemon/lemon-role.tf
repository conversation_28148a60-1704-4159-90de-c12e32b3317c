resource "kubernetes_role" "lemon_admin" {
  metadata {
    name      = "lemon-admin"
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }
 
  # Core API group (pods, services, secrets, configmaps, etc.)
  rule {
    api_groups = [""]
    resources  = ["*"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }
 
  # Commonly used namespaced API groups
  rule {
    api_groups = [
      "apps",
      "batch",
      "extensions",
      "autoscaling",
      "networking.k8s.io",
      "policy",
      "rbac.authorization.k8s.io"
    ]
    resources  = ["*"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  # Allow pod exec
  rule {
    api_groups = [""]
    resources  = ["pods/exec"]
    verbs      = ["create"]
  }

  # Allow configmap updates
  rule {
    api_groups = [""]
    resources  = ["configmaps"]
    verbs      = ["create", "update", "patch", "delete"]
  }


  # Allow pod delete for restart after configmap update
  rule {
    api_groups = [""]
    resources  = ["pods"]
    verbs      = ["delete"]
  }
}