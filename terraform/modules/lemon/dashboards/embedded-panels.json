{"__elements": {}, "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "11.5.2"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "For showing embedded panels in lemon dashboard as a workaround until iframe is figured out", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 0}, "id": 7, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": true, "expr": "avg(dcgm_fi_dev_gpu_util{exported_namespace=~\"$namespace\"}) by (pod)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "GPU Utilization", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 0}, "id": 11, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{namespace=~\"$namespace\",container=~\".+\"}) by (pod)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "Main Memory", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Eligible chunks waiting in queue for sutighosh-peashooter-small2"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "code", "expr": "default_peashooter_queue_waiting_eligible{exported_job=~\"$rapid_id.*\", exported_namespace=\"$namespace\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Eligible chunks waiting in queue for {{rapid_id}}", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "code", "expr": "default_peashooter_queue_waiting_total{exported_job=~\"$rapid_id.*\", exported_namespace=\"$namespace\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Total chunks waiting in queue for {{rapid_id}}", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "rate(default_peashooter_queue_stalled_batches_total{exported_job=~\"$rapid_id.*\", exported_namespace=\"$namespace\"}[$__interval])", "hide": false, "instant": false, "legendFormat": "Stalled chunks {{rapid_id}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "rate(default_peashooter_queue_lost_batches_total{exported_job=~\"$rapid_id.*\", exported_namespace=\"$namespace\"}[$__interval])", "hide": false, "instant": false, "legendFormat": "Lost chunks {{rapid_id}}", "range": true, "refId": "D"}], "title": "Peashooter queue size", "type": "timeseries"}, {"datasource": {"default": false, "type": "datasource", "uid": "-- Mixed --"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 28, "interval": "30s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(brix_job) (label_replace(rate(engineapi__controller__batcher__sampled_tokens_per_step_sum{namespace=~\"$namespace\", pod=~\"$pod\"}[$__rate_interval]), \"brix_job\", \"$1\", \"pod\", \"(.+)-[0-9]+$\"))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "(rate(enginev3_inference_pipereplica_gpt_sampled_tokens_total{exported_namespace=~\"$namespace\",pod=~\"$pod\"})[$__rate_interval])", "hide": false, "legendFormat": "{{pod}}[{{replica_idx}}]", "range": true, "refId": "B"}], "title": "Aggregate Sampled <PERSON><PERSON><PERSON> Per Second", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${datasource}"}, "description": "Tracks the number of requests in each state across the peashooter job", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 45, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": false, "sizing": "auto"}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by (state) (default_peashooter_request_state_state_total{exported_namespace=\"$namespace\",exported_job=~\"$rapid_id.*\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "sum by (state) (default_peashooter_request_state_state{exported_namespace=\"$namespace\",exported_job=~\"$rapid_id.*\"})", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Request state", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "active kv / total gpu blocks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["sebastko-pea-feb-0304-o3mini-3-rollout-worker-w0-0"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 37, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "engineapi__controller__memory_controller__unique_active_blocks_gauge{namespace=~\"$namespace\"} * 100 /engineapi__controller__memory_controller__kv_cache_total_gpu_alldps_gauge{namespace=~\"$namespace\"}", "instant": true, "key": "Q-e2b5da96-1a27-4ebd-83fa-fd9eb097c547-2", "legendFormat": "{{pod}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "enginev3_inference_pipereplica_gpt_gpu_kv_util_percent{namespace=~\"$namespace\"}", "hide": false, "instant": false, "legendFormat": "{{pod}}[{{replica_idx}}]", "range": true, "refId": "A"}], "title": "Active GPU KV Utilization (%)", "type": "timeseries"}], "refresh": "", "schemaVersion": 40, "tags": ["observability"], "templating": {"list": [{"current": {}, "includeAll": false, "label": "datasource", "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(container_memory_working_set_bytes,namespace)", "includeAll": false, "label": "namespace", "name": "namespace", "options": [], "query": {"qryType": 1, "query": "label_values(container_memory_working_set_bytes,namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(engineapi__controller__batcher__in_flight_sum{namespace=\"$namespace\"},pod)", "includeAll": false, "label": "pod", "multi": true, "name": "pod", "options": [], "query": {"qryType": 1, "query": "label_values(engineapi__controller__batcher__in_flight_sum{namespace=\"$namespace\"},pod)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "1m", "value": "1m"}, "label": "interval", "name": "interval", "options": [{"selected": true, "text": "1m", "value": "1m"}], "query": "1m", "type": "textbox"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(default_peashooter_queue_main_waiting_eligible{namespace=\"$namespace\"},rapid_id)", "includeAll": false, "label": "rapid_id", "name": "rapid_id", "options": [], "query": {"qryType": 1, "query": "label_values(default_peashooter_queue_main_waiting_eligible{namespace=\"$namespace\"},rapid_id)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Embedded Panels", "uid": "eeex3l26oifpce", "version": 1, "weekStart": ""}