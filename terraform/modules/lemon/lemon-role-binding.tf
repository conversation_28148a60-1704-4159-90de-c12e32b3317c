resource "kubernetes_role_binding" "lemon_admin" {
  metadata {
    name      = "lemon-admin"
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = "lemon-admin"
  }

  # Group PIMSG-ORANGE-OBSERVABILITY-CONTRIBUTOR
  subject {
    kind      = "Group"
    name      = "036e5e42-ba84-46a0-a5cc-0fe17e8892a7"
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }
}