variable "location" {
  type        = string
  description = "Azure location for the monitoring resources"
}

variable "oidc_issuer_url" {
  type        = string
  description = "OIDC issuer url for federated identity credential"
}

variable "cluster_rg" {
  type        = any
  description = "Data or Resource for Azure resource group for the AKS cluster"
}

variable "prometheus_env" {
  type        = string
  description = "The environment for prometheus"
}

variable "prometheus_url" {
  type        = string
  description = "The url used when reading data from prometheus"
}

variable "lemon_grafana_url" {
  type        = string
  description = "The url used by lemon to embed Grafana charts"
}

variable "lemon_adx_kusto_cluster_eu" {
  type        = string
  description = "The ADX Kusto cluster for experiments running in EU clusters"
}

variable "lemon_adx_kusto_cluster_us" {
  type        = string
  description = "The ADX Kusto cluster for experiments running in US clusters"
}

variable "lemon_adx_kusto_database" {
  type        = string
  description = "The ADX Kusto database name for both US and EU Kusto clusters"
}

variable "orng_observability_keyvault" {
  type = object({
    id                = string
    subscription_name = string
    subscription_id   = string
    tenant_id         = string
  })
  description = "Key Vault for observability"
}
variable "lemon_secret_name" {
  type        = string
  description = "Name of the secret for lemon"
  default = "lemon-secret-key"
}

variable "lemon_app_name" {
  type        = string
  description = "Name of the lemon infra app"
  default     = "lemon-infra"
}

variable "wandb_secret_name" {
  type        = string
  description = "Name of the secret for wand"
  default = "wandb-api-key"
}

variable "lemon_snowflake_secret_name" {
  type        = string
  description = "Name of the secret for lemon snowflake"
  default = "lemon-snowflake-password"
}

variable "lemon_snowflake_user_name" {
  type        = string
  description = "Name of the secret for lemon snowflake user"
  default = "snowflake-account"
}

variable "cluster_name" {
  type        = string
  description = "Globally unique cluster name (e.g. prod-uksouth-7)"
}

variable "lemon_cluster_names" {
  type        = list(string)
  description = "List of all cluster names for lemon cross-cluster egress"
  default     = []
}