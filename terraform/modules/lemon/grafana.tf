variable "grafana_ingress_cluster_name" {
  description = "Cluster name for grafana ingress hostname. Only used for prod-uksouth-7 and stage-southcentralus-hpe-1. If not set, defaults to var.cluster_name."
  type        = string
  default     = null
}

locals {
  grafana_ingress_effective_cluster_name = var.grafana_ingress_cluster_name != null && var.grafana_ingress_cluster_name != "" ? var.grafana_ingress_cluster_name : var.cluster_name
}

resource "helm_release" "grafana" {
  name      = "grafana"
  namespace = kubernetes_namespace.lemon.metadata[0].name

  repository = "https://grafana.github.io/helm-charts"
  chart      = "grafana"
  version    = "8.12.1"

  values = [
    templatefile("${path.module}/config/grafana-values.yaml",
      { embedded-panels=file("${path.module}/dashboards/embedded-panels.json") })
  ]

  set {
    name  = "serviceAccount.annotations.azure\\.workload\\.identity\\/client-id"
    value = azurerm_user_assigned_identity.lemon_identity.client_id
  }

  set {
    name  = "podLabels.azure\\.workload\\.identity\\/use"
    value = "true"
    type  = "string"
  }

  set {
    name  = "grafana\\.ini.azure.workload_identity_client_id"
    value = azurerm_user_assigned_identity.lemon_identity.client_id
  }

  set {
    name  = "datasources.datasources\\.yaml.datasources[0].url"
    value = var.prometheus_url
  }
}

# Grafana Ingress for internal-nginx
resource "kubernetes_ingress_v1" "grafana_internal" {
  count = contains(["prod-uksouth-7", "stage-southcentralus-hpe-1", "prod-southcentralus-hpe-3", "prod-southcentralus-hpe-5"], local.grafana_ingress_effective_cluster_name) ? 1 : 0
  metadata {
    name = "grafana-internal"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    annotations = {
      "kubernetes.io/ingress.global-static-ip-name" = "${local.grafana_ingress_effective_cluster_name}-grafana-kubernetes-ingress"
      "nginx.ingress.kubernetes.io/ssl-redirect" = "true"
      "nginx.ingress.kubernetes.io/force-ssl-redirect" = "true"
      "nginx.ingress.kubernetes.io/auth-cache-key" = "$oauth_cookie_cache_key$http_authorization"
      "nginx.ingress.kubernetes.io/auth-response-headers" = "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
      "nginx.ingress.kubernetes.io/auth-url" = "https://oai-azure-auth-proxy.int.${local.grafana_ingress_effective_cluster_name}.dev.openai.org/oauth2/auth?allowed_groups="
      "nginx.ingress.kubernetes.io/auth-signin" = "https://oauth2-proxy-core-group.aad-auth.int.${local.grafana_ingress_effective_cluster_name}.dev.openai.org/oauth2/start"
      "nginx.ingress.kubernetes.io/proxy-buffer-size" = "128k"
      "nginx.ingress.kubernetes.io/proxy-busy-buffers-size" = "512k"
      "nginx.ingress.kubernetes.io/proxy-buffers-number" = "4"
      "nginx.ingress.kubernetes.io/configuration-snippet" = <<-EOT
        proxy_set_header X-User-Email $upstream_http_x_auth_request_email;
      EOT
    }
  }
  spec {
    ingress_class_name = "internal-nginx"
    rule {
      host = "grafana.int.${local.grafana_ingress_effective_cluster_name}.dev.openai.org"
      http {
        path {
          path = "/"
          path_type = "Prefix"
          backend {
            service {
              name = "grafana"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    tls {
      hosts = ["grafana.int.${local.grafana_ingress_effective_cluster_name}.dev.openai.org"]
    }
  }
  depends_on = [
    helm_release.grafana,
    kubernetes_namespace.lemon
  ]
}
