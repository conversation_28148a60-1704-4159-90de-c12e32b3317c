locals {
  gosb_configmap_name = "gosb-configmap"
  gosb_configmap_sha  = var.cluster_name == "prod-uksouth-7" ? sha256(yamlencode(kubernetes_config_map.gosb-configmap[0].data)) : ""
}

resource "kubernetes_config_map" "gosb-configmap" {
  count = var.cluster_name == "prod-uksouth-7" ? 1 : 0
  
  metadata {
    name      = local.gosb_configmap_name
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }

  data = {
      SNOWFLAKE_WAREHOUSE            = "COMPUTE_WH"
      SNOWFLAKE_USERNAME             = "WEBSERVICEUSER"
      LEMON_SNOWFLAKE_USERNAME       = "WEBSERVICEUSER"
      WANDB_ORGANIZATION             = "https://msaip.wandb.io"
      PERSONAL_NAMESPACE             = "True"
      USE_AZURE_LOGGING              = "True"
      SB_METRICS_BASE_URL            = "http://asyncmetrics.orange-7.internal.genai.ms/"
      LEMON_MSFT                     = "True"
      NOTEBOOKS_AZ_DIR               = "az://iridiumsnowflakeuks/strawberry/upload/v1/notebooks"
      OPENAI_USER                    = "lemon"
      OPENAI_ENCODINGS_BASE          = "az://orngoaiartifacts/data-gym/encodings"
      OPENAI_ENCODER_GYM_BASE        = "az://orngoaiartifacts/data-gym/encodings"
      OPENAI_ENCODINGS_BASE_DATA_GYM = "az://orngoaiartifacts/data-gym/encodings"
      TIKTOKEN_ENCODINGS_BASE        = "az://orngoaiartifacts/data-gym/encodings"
  }
}

resource "kubernetes_deployment" "gosb" {
  count = var.cluster_name == "prod-uksouth-7" ? 1 : 0

  metadata {
    name = "gosb"
    labels = {
      app = "gosb"
    }
    namespace = kubernetes_namespace.lemon.metadata[0].name
  }

  spec {
    replicas = 1
    selector {
      match_labels = {
        app = "gosb"
      }
    }

    template {
      metadata {
        labels = {
          app = "gosb"
        }
        annotations = {
          "checksum/config" = local.gosb_configmap_sha
        }
      }

      spec {
        container {
            name  = "gosb"
            image = "iridiumsdc.azurecr.io/infra/lemon:1284623"
            port {
              name           = "http"
              container_port = 8501
            }
            command = ["/bin/sh", "-c"]
            args = [
              "streamlit run $(oaipkg where viz)/viz/app.py --browser.gatherUsageStats=False"
            ]
            readiness_probe {
              http_get {
                path = "/"
                port = 8501
              }
              initial_delay_seconds = 5
              period_seconds = 10
            }
            liveness_probe {
              http_get {
                path = "/"
                port = 8501
              }
              initial_delay_seconds = 15
              period_seconds = 20
            }
            env_from {
              config_map_ref {
                name = local.gosb_configmap_name
              }
            }
            env_from {
              secret_ref {
                name = kubernetes_secret.lemon-service-principal.metadata[0].name
              }
            }
            env {
              name  = "LEMON_SECRET_KEY"
              value = data.azurerm_key_vault_secret.lemon_secret.value
            }
            env {
              name  = "WANDB_API_KEY"
              value = data.azurerm_key_vault_secret.wandb_secret.value
            }
            env {
              name  = "LEMON_SNOWFLAKE_PASSWORD"
              value = data.azurerm_key_vault_secret.snowflake_password.value
            }
            env {
              name  = "SNOWFLAKE_ACCOUNT"
              value = data.azurerm_key_vault_secret.snowflake_user.value
            }
            env {
              name = "SNOWFLAKE_PASSWORD"
              value = data.azurerm_key_vault_secret.snowflake_password.value
            }
        }
      }
    }
  }
}

resource "kubernetes_service" "gosb_service" {
  count = var.cluster_name == "prod-uksouth-7" ? 1 : 0

  metadata {
    name = "gosb-service"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    annotations = {
      "service.beta.kubernetes.io/azure-load-balancer-internal": "true"
      "config.hash" = var.cluster_name == "prod-uksouth-7" ? sha256(jsonencode(kubernetes_deployment.gosb[0].spec)) : ""
    }
  }
  spec {
    selector = {
      app = "gosb"
    }
    port {
      protocol    = "TCP"
      port        = 80
      target_port = 8501
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_ingress_v1" "gosb_internal_ingress" {
  count = var.cluster_name == "prod-uksouth-7" ? 1 : 0
  metadata {
    name = "gosb-internal"
    namespace = kubernetes_namespace.lemon.metadata[0].name
    annotations = {
      "kubernetes.io/ingress.global-static-ip-name" = "gosb-${var.cluster_name}-kubernetes-ingress"
      "nginx.ingress.kubernetes.io/ssl-redirect" = "true"
      "nginx.ingress.kubernetes.io/force-ssl-redirect" = "true"
      "nginx.ingress.kubernetes.io/auth-cache-key" = "$oauth_cookie_cache_key$http_authorization"
      "nginx.ingress.kubernetes.io/auth-response-headers" = "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
      "nginx.ingress.kubernetes.io/auth-url" = "https://oai-azure-auth-proxy.int.${var.cluster_name}.dev.openai.org/oauth2/auth?allowed_groups="
      "nginx.ingress.kubernetes.io/auth-signin" = "https://oauth2-proxy-core-group.aad-auth.int.${var.cluster_name}.dev.openai.org/oauth2/start"
      "nginx.ingress.kubernetes.io/proxy-buffer-size" = "128k"
      "nginx.ingress.kubernetes.io/proxy-busy-buffers-size" = "512k"
      "nginx.ingress.kubernetes.io/proxy-buffers-number" = "4"
      "nginx.ingress.kubernetes.io/configuration-snippet" = <<-EOT
        proxy_set_header X-User-Email $upstream_http_x_auth_request_email;
        add_header Cache-Control "no-store";
      EOT
    }
  }
  spec {
    ingress_class_name = "internal-nginx"
    rule {
      host = "gosb.int.${var.cluster_name}.dev.openai.org"
      http {
        path {
          path = "/"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service.gosb_service[0].metadata[0].name
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    tls {
      hosts = ["gosb.int.${var.cluster_name}.dev.openai.org"]
    }
  }
  depends_on = [
    kubernetes_service.gosb_service,
    kubernetes_namespace.lemon
  ]
}