variable "name" {
  type        = string
  description = "Azure keyvault name"
}

variable "resource_group_name" {
  type        = string
  description = "Azure resource group name"
}

variable "location" {
  type        = string
  description = "Azure location"
}

variable "capacity" {
  type        = number
  description = "Azure cache capacity"
  default     = 2
}

variable "shard_count" {
  type        = number
  description = "Azure cache shard count"
  default     = 2
}

variable "redis_version" {
  type        = string
  description = "Azure resource group name"
  default     = "6"
}

variable "family" {
  type        = string
  description = "Azure Cache family"
  default     = "C"
}

variable "sku_name" {
  type        = string
  description = "Azure Cache SKU name"
  default     = "Standard"
}

variable "access_keys_authentication_enabled" {
  type        = bool
  description = "Enable access keys authentication"
  default     = false
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resource"
  default     = {}
}

variable "private_dns_zones" {
  description = "A map of private DNS zones with their configurations"
  default     = {}
  type = map(object({
    short_name       = string
    resource_group   = string
    subresource_name = string
  }))
}

variable "virtual_networks" {
  description = "A list of virtual networks with their configurations"
  default     = {}
  type = map(object({
    virtual_network_id   = string
    virtual_network_name = string
    subnet_id            = string
    subnet_name          = string
    resource_group_name  = string
    location             = string
  }))
}
