resource "azurerm_redis_cache" "cache" {
  name                  = var.name
  resource_group_name   = var.resource_group_name
  location              = var.location
  capacity              = var.capacity
  family                = var.family
  sku_name              = var.sku_name
  shard_count           = var.shard_count
  redis_version         = var.redis_version
  non_ssl_port_enabled  = false
  minimum_tls_version   = "1.2"
  access_keys_authentication_enabled = var.access_keys_authentication_enabled
  # Flag not working, TODO: switch to aad auth
  # active_directory_authentication_enabled = true
  public_network_access_enabled = false
}

## TODO: Redis audit if needed
