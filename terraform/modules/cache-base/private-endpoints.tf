data "azurerm_private_dns_zone" "private_dns_zone" {
  for_each            = var.private_dns_zones
  name                = each.key
  resource_group_name = each.value.resource_group
}

locals {
  # TODO: Please suggest a better way to validate this object
  allowed_subresource_names = { for v in ["redisCache"] : v => v }

  # Create a permutation map of private DNS zones per virtual network with their configurations
  private_dns_zone_virtual_network = { for x in flatten([
    for vent_key, vnet in var.virtual_networks : [
      for zone_key, dns in var.private_dns_zones : {
        key                      = "${vent_key}-${zone_key}"
        virtual_network_id       = vnet.virtual_network_id
        virtual_network_name     = vnet.virtual_network_name
        subnet_id                = vnet.subnet_id
        subnet_name              = vnet.subnet_name
        vent_rg_name             = vnet.resource_group_name
        virtual_network_location = vnet.location

        dns_zone_id         = data.azurerm_private_dns_zone.private_dns_zone[zone_key].id
        dns_zone_rg_name    = data.azurerm_private_dns_zone.private_dns_zone[zone_key].resource_group_name
        dns_zone_short_name = dns.short_name
        subresource_name    = local.allowed_subresource_names[dns.subresource_name]
      }
    ]
  ]) : x.key => x }
}

resource "azurerm_private_endpoint" "private_endpoints" {
  for_each = local.private_dns_zone_virtual_network

  name                = "pe-${var.name}-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
  location            = each.value.virtual_network_location
  resource_group_name = each.value.vent_rg_name
  subnet_id           = each.value.subnet_id

  private_service_connection {
    name                           = "conn-${var.name}-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_connection_resource_id = azurerm_redis_cache.cache.id
    subresource_names              = [each.value.subresource_name]
    is_manual_connection           = false
  }

  private_dns_zone_group {
    name                 = "dns-${var.name}-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_dns_zone_ids = [each.value.dns_zone_id]
  }
}
