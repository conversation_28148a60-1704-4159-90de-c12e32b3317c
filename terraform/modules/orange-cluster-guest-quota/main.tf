# Perhonen quota is largely configured via the Singularity control plane. This entails creating the team-resource-manager-config ConfigMap and
# Brix Quota resources with the quota assignments (which are based on Capacity Reservations with Orange-related extra metadata--see
# ModelConstants.Orange in https://msdata.visualstudio.com/Vienna/_git/Singularity?path=%2Fsrc%2Fsubsystems%2Fscheduler%2Fscheduler.global.model%2FModelConstants.cs&_a=contents&version=GBmaster).
# As a special case, we need to maintain Quota resources with zero assigned quota for "guest" teams: these are teams without an explicit quota 
# allocation in the given cluster. This is to get <PERSON><PERSON><PERSON> to report the low-priority usage for users of the guest teams. The Singularity's 
# control plane would update the Quota's allocated amount to zero when the capacity reservation is dropped. However, Singularity doesn't know 
# about all the defined Moonfire teams. This module covers the gap: it creates zero-quota Quota resources for the teams that don't have Quota 
# resources created by Singularity.
#
# Complications and twists: 
# 1. Terraform's heavy statefulness is at odds with the idea of joint management of the Quota resources between the two sides. Yet, neither side 
# has all the information to maintain a Quota resource exclusively. Singularity is the source of the real (non-zero) quota assignment, but it 
# doesn't have all the Moonfire team names to be able to create the guest Quota resources. So, we let this TF module create them for any teams 
# that don't already have Quotas. But from this point on, the resources are managed by Terraform. If non-zero quota is assigned later by 
# Singularity, Terraform would detect drift and revert the changes. Theoretically, this scenario can be enabled by using TF's `lifecycle` 
# `ignore_changes` option, but its implementation is broken in the Kubernetes provider: https://github.com/hashicorp/terraform-provider-kubernetes/issues/1378. 
# The workaround is to use the kubectl provider, which has a properly working `ignore_fields` option.
# 2. Quota resources already created by Singularity should not get into TF's management. To stay clear of them, this module applies the 
# `created-by` label to resources it creates. This is used as a filter in the data source for existing resources.
# 3. But the Singularity-created Quota resources have one valuable piece of metadata not available here: the displayName for the GPU capacity 
# type. Here it is read opportunistically and used in the creation of the guest Quota resources. For the cases this logic can't handle (Quotas 
# already created and new clusters), we'll run Quota patching code in Singularity's handling of CR/quota updates (OrangeQuotaUpdateRequestHandler).

terraform {
  required_providers {
    kubernetes = {
      source = "hashicorp/kubernetes"
    }
    kubectl = {
      source  = "alekc/kubectl"
    }
  }
}

data "kubernetes_resources" "existing_quotas" {
  api_version    = "brix.openai.com/v1alpha1"
  kind           = "Quota"
}

locals {
  gpu_sku = "gpu1" # Default GPU SKU on all Orange clusters (initially at least)

  teams_with_non_terraform_quota = [
    for quota in data.kubernetes_resources.existing_quotas.objects : quota.metadata.name
      if try(quota.metadata.labels["app.kubernetes.io/created-by"] != "terraform", true)
    ]
  quota_display_name = try(
    # Find any Quota resource with a displayName != "gpu1" or similar
    [for quota in data.kubernetes_resources.existing_quotas.objects : quota.spec.allocations[0].displayName
      if !startswith(quota.spec.allocations[0].displayName, "gpu")
    ][0],
    local.gpu_sku)
}

output "display_name" {
  value = local.quota_display_name
}

resource "kubectl_manifest" "guest_quota" {
  for_each = setsubtract(var.all_teams, local.teams_with_non_terraform_quota)
  ignore_fields = ["spec"]
  yaml_body = <<YAML
apiVersion: brix.openai.com/v1alpha1
kind: Quota
metadata:
  labels:
    brix.openai.com/scheduler: perhonen
    app.kubernetes.io/created-by: terraform
  name: ${each.key}
spec:
  allocations:
  - cluster: ${var.cluster_name}
    dedicated: false
    sku: ${local.gpu_sku}
    displayName: ${local.quota_display_name}
    quotaCount: 0
    quotaNode: 0
    priority: 1
YAML
}


