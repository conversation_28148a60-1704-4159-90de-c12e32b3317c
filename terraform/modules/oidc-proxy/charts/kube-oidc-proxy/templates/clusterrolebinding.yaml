kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
  name: {{ include "kube-oidc-proxy.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kube-oidc-proxy.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "kube-oidc-proxy.fullname" . }}
  namespace: {{ .Release.Namespace }}
