{{ $fullname := include "kube-oidc-proxy.fullname" . }}
{{ $defaultTlsSecretName := printf "%s-tls" $fullname }}
{{ $tlsSecretName := .Values.tls.secretName | default $defaultTlsSecretName }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kube-oidc-proxy.fullname" . }}
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "kube-oidc-proxy.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  {{- if .Values.rollingUpdateStrategy }}
  strategy: {{ .Values.rollingUpdateStrategy | nindent 4}}
  {{ end }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "kube-oidc-proxy.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      serviceAccountName: {{ include "kube-oidc-proxy.fullname" . }}
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
        - containerPort: 8443
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
        command: ["kube-oidc-proxy"]
        args:
          - "--secure-port=8443"
          - "--tls-cert-file=/etc/oidc/tls/crt.pem"
          - "--tls-private-key-file=/etc/oidc/tls/key.pem"
          - "--oidc-client-id=$(OIDC_CLIENT_ID)"
          - "--oidc-issuer-url=$(OIDC_ISSUER_URL)"
          - "--oidc-username-claim=$(OIDC_USERNAME_CLAIM)"
          {{- if .Values.oidc.caPEM }}
          - "--oidc-ca-file=/etc/oidc/oidc-ca.pem"
          {{ end }}
          {{- if .Values.oidc.usernamePrefix }}
          - "--oidc-username-prefix=$(OIDC_USERNAME_PREFIX)"
          {{ end }}
          {{- if .Values.oidc.groupsClaim }}
          - "--oidc-groups-claim=$(OIDC_GROUPS_CLAIM)"
          {{ end }}
          {{- if .Values.oidc.groupsPrefix }}
          - "--oidc-groups-prefix=$(OIDC_GROUPS_PREFIX)"
          {{ end }}
          {{- if .Values.oidc.signingAlgs }}
          - "--oidc-signing-algs=$(OIDC_SIGNING_ALGS)"
          {{ end }}
          {{- if .Values.oidc.requiredClaims }}
          - "--oidc-signing-algs=$(OIDC_REQUIRED_CLAIMS)"
          {{ end }}
          {{- if .Values.tokenPassthrough.enabled }}
          - "--token-passthrough"
          {{- if .Values.tokenPassthrough.audiences }}
          - "--token-passthrough-audiences={{ join "," .Values.tokenPassthrough.audiences }}"
          {{ end }}
          {{ end }}
          {{- if .Values.extraImpersonationHeaders.clientIP }}
          - "--extra-user-header-client-ip"
          {{ end }}
          {{- if .Values.extraImpersonationHeaders.headers }}
          - "--extra-user-headers={{ .Values.extraImpersonationHeaders.headers }}"
          {{ end }}
          {{- range $key, $value := .Values.extraArgs -}}
          - "--{{ $key }}={{ $value -}}"
          {{ end }}
        resources:
          {{- toYaml .Values.resources | nindent 12 }}
        env:
        - name: OIDC_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.client-id
        - name: OIDC_ISSUER_URL
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.issuer-url
        - name: OIDC_USERNAME_CLAIM
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.username-claim
        {{- if .Values.oidc.usernamePrefix }}
        - name: OIDC_USERNAME_PREFIX
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.username-prefix
        {{ end }}
        {{- if .Values.oidc.groupsClaim }}
        - name: OIDC_GROUPS_CLAIM
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.groups-claim
        {{ end }}
        {{- if .Values.oidc.groupsPrefix }}
        - name: OIDC_GROUPS_PREFIX
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.groups-prefix
        {{ end }}
        {{- if .Values.oidc.signingAlgs }}
        - name: OIDC_SIGNING_ALGS
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.signing-algs
        {{ end }}
        {{- if .Values.oidc.requiredClaims }}
        - name: OIDC_REQUIRED_CLAIMS
          valueFrom:
            secretKeyRef:
              name: {{ include "kube-oidc-proxy.fullname" . }}-config
              key: oidc.required-claims
        {{ end }}
        volumeMounts:
          {{- if .Values.oidc.caPEM }}
          - name: kube-oidc-proxy-config
            mountPath: /etc/oidc
            readOnly: true
          {{ end }}
          - name: kube-oidc-proxy-tls
            mountPath: /etc/oidc/tls
            readOnly: true
          {{- if .Values.extraVolumeMounts }}{{ toYaml .Values.extraVolumeMounts | trim | nindent 10 }}{{ end }}
      volumes:
        {{ if .Values.oidc.caPEM }}
        - name: kube-oidc-proxy-config
          secret:
            secretName: {{ include "kube-oidc-proxy.fullname" . }}-config
            items:
            - key: oidc.ca-pem
              path: oidc-ca.pem
        {{ end }}
        {{- if .Values.extraVolumes }}{{ toYaml .Values.extraVolumes | trim | nindent 8 }}{{ end }}
        - name: kube-oidc-proxy-tls
          secret:
            secretName: {{ $tlsSecretName }}
            items:
            - key: tls.crt
              path: crt.pem
            - key: tls.key
              path: key.pem
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
