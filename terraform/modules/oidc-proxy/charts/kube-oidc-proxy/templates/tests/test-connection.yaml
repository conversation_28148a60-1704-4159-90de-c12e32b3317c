apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "kube-oidc-proxy.fullname" . }}-test-connection"
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "kube-oidc-proxy.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
