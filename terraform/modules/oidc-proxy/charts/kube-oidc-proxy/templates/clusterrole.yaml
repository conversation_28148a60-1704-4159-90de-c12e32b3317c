kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
  name: {{ include "kube-oidc-proxy.fullname" . }}
rules:
- apiGroups:
  - ""
  resources:
  - "users"
  - "groups"
  - "serviceaccounts"
  verbs:
  - "impersonate"
- apiGroups:
  - "authentication.k8s.io"
  resources:
  - "userextras/scopes"
  - "userextras/remote-client-ip"
  - "tokenreviews"
  # to support end user impersonation
  - "userextras/originaluser.jetstack.io-user"
  - "userextras/originaluser.jetstack.io-groups"
  - "userextras/originaluser.jetstack.io-extra"
  verbs:
  - "create"
  - "impersonate"
