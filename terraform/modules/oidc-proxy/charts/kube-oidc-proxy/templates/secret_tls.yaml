{{- if (not .Values.tls.secretName) }}
{{ $fullname := include "kube-oidc-proxy.fullname" . }}
{{ $ca := genCA (printf "%s-ca" $fullname) 3650 }}
{{ $cn := printf "%s.%s.svc.cluster.local" $fullname .Release.Namespace }}
{{ $server := genSignedCert $cn nil nil 365 $ca }}

apiVersion: v1
kind: Secret
type: kubernetes.io/tls
metadata:
  name: {{ template "kube-oidc-proxy.fullname" . }}-tls
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
data:
  tls.crt: {{ b64enc $server.Cert }}
  tls.key: {{ b64enc $server.Key }}
{{ end }}
