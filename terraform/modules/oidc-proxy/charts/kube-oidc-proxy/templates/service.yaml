apiVersion: v1
kind: Service
metadata:
  name: {{ include "kube-oidc-proxy.fullname" . }}
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
  annotations:
    {{- range $key, $val := .Values.service.annotations }}
    {{ $key }}: {{ $val | quote }}
    {{- end }}
spec:
  type: {{ .Values.service.type }}
{{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: "{{ .Values.service.loadBalancerIP }}"
{{- end }}
{{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml .Values.service.loadBalancerSourceRanges | indent 4 }}
{{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 8443
      protocol: TCP
      name: https
  selector:
    app.kubernetes.io/name: {{ include "kube-oidc-proxy.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
