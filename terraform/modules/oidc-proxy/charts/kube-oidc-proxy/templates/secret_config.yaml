apiVersion: v1
kind: Secret
metadata:
  name: {{ include "kube-oidc-proxy.fullname" . }}-config
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
type: Opaque
data:
  {{- if .Values.oidc.caPEM }}
  oidc.ca-pem: {{ .Values.oidc.caPEM | default "" | b64enc }}
  {{- end }}

  {{- if .Values.oidc.issuerUrl }}
  oidc.issuer-url: {{ .Values.oidc.issuerUrl | b64enc }}
  {{- end }}

  {{- if .Values.oidc.usernameClaim }}
  oidc.username-claim: {{ .Values.oidc.usernameClaim | default "" | b64enc }}
  {{- end }}

  {{- if .Values.oidc.clientId }}
  oidc.client-id: {{ .Values.oidc.clientId | b64enc }}
  {{- end }}

  {{- if .Values.oidc.usernamePrefix }}
  oidc.username-prefix: {{ .Values.oidc.usernamePrefix | default "" | b64enc }}
  {{- end }}

  {{- if .Values.oidc.groupsClaim }}
  oidc.groups-claim: {{ .Values.oidc.groupsClaim | default "" | b64enc }}
  {{- end }}

  {{- if .Values.oidc.groupsPrefix }}
  oidc.groups-prefix: {{ .Values.oidc.groupsPrefix | default "" | b64enc }}
  {{- end }}

  {{- if .Values.oidc.signingAlgs }}
  oidc.signing-algs: {{ join "," .Values.oidc.signingAlgs | default "" | b64enc }}
  {{- end }}

  {{- if .Values.oidc.requiredClaims }}
  oidc.required-claims: {{ include "requiredClaims" . | b64enc }}
  {{- end }}
