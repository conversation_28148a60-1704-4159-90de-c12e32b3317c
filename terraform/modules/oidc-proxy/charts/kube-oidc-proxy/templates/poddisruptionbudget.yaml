{{- if .Values.podDisruptionBudget.enabled -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "kube-oidc-proxy.fullname" . }}
  namespace: {{ $.Release.Namespace }}
  labels:
{{ include "kube-oidc-proxy.labels" . | indent 4 }}
spec:
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "kube-oidc-proxy.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
