# Default values for kube-oidc-proxy.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: nexusstaticacr.azurecr.io/jetstack/kube-oidc-proxy
  tag: v0.3.0
  pullPolicy: IfNotPresent

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

service:
  type: LoadBalancer
  port: 443
  annotations:
    # You can use this field to add annotations to the Service.
    # Define it in a key-value pairs. E.g.
    # service.beta.kubernetes.io/aws-load-balancer-internal: true
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "brix-ing-subnet"

  loadBalancerIP: ""
  loadBalancerSourceRanges: []

tls:
  # `secretName` must be a name of Secret of TLS type. If not provided a
  # self-signed certificate will get generated.
  secretName:

# These values needs to be set in overrides in order to get kube-oidc-proxy
# working.
oidc:
  # A minimal configuration requires setting clientId, issuerUrl and usernameClaim
  # values.
  clientId: ""
  issuerUrl: ""
  usernameClaim: ""

  # PEM encoded value of CA cert that will verify TLS connection to
  # OIDC issuer URL. If not provided, default hosts root CA's will be used.
  caPEM:

  usernamePrefix:
  groupsClaim:
  groupsPrefix:

  signingAlgs:
    - RS256
  requiredClaims: {}

# To enable token passthrough feature
# https://github.com/jetstack/kube-oidc-proxy/blob/master/docs/tasks/token-passthrough.md
tokenPassthrough:
  enabled: false
  audiences: []

# To add extra impersonation headers
# https://github.com/jetstack/kube-oidc-proxy/blob/master/docs/tasks/extra-impersonation-headers.md
extraImpersonationHeaders:
  clientIP: false
  #headers: key1=foo,key2=bar,key1=bar

extraArgs: {}
  #audit-log-path: /audit-log
  #audit-policy-file: /audit/audit.yaml

extraVolumeMounts: {}
  #- name: audit
  #  mountPath: /audit
  #  readOnly: true

extraVolumes: {}
  #- configMap:
      #defaultMode: 420
      #name: kube-oidc-proxy-policy
    #name: audit

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  # ingressClassName: ''

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

# Allows setting the Deployment update strategy
#rollingUpdateStrategy:
#  type: RollingUpdate
#  rollingUpdate:
#    maxSurge: 34%
#    maxUnavailable: 33%

# Enable Pod Disruption Budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi
  #

initContainers: []

nodeSelector: {}

tolerations: []

affinity: {}
