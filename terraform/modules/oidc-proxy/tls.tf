resource "azurerm_key_vault_certificate" "kube-oidc-proxy-tls" {
  depends_on = [
    azurerm_key_vault.tlskeyvault,
    # need this so terraform can access kv
    azurerm_role_assignment.builder,
    azurerm_key_vault_certificate_issuer.ca,
  ]

  name         = local.keyvault_tls_certificate_name
  key_vault_id = azurerm_key_vault.tlskeyvault.id

  certificate_policy {
    issuer_parameters {
      name = local.is_openai_org ? "MSIT" : "Self"
    }

    key_properties {
      exportable = true
      key_size   = 2048
      key_type   = "RSA"
      reuse_key  = true
    }

    lifetime_action {
      action {
        action_type = "AutoRenew"
      }

      trigger {
        days_before_expiry = 30
      }
    }

    secret_properties {
      content_type = "application/x-pem-file"
    }

    x509_certificate_properties {
      # Server Authentication = *******.*******.1
      # Client Authentication = *******.*******.2
      extended_key_usage = ["*******.*******.1"]

      key_usage = [
        "cRLSign",
        "dataEncipherment",
        "digitalSignature",
        "keyAgreement",
        "keyCertSign",
        "keyEncipherment",
      ]

      subject = local.is_openai_org ? "CN=${var.oidc_proxy_dns_names[0]}" : "CN=oidc-${var.cluster_name}"
      subject_alternative_names {
        dns_names = var.oidc_proxy_dns_names
      }
      validity_in_months = 12
    }
  }
}
