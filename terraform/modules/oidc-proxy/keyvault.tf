data "azuread_client_config" "current" {}
data "azurerm_client_config" "current" {}

module "global_settings" {
  source = "../global_settings"
}

resource "azurerm_key_vault" "tlskeyvault" {
  name                       = var.oidc_tlskeyvault_name
  location                   = var.cluster_region
  resource_group_name        = var.cluster_resource_group
  tenant_id                  = data.azurerm_client_config.current.tenant_id
  sku_name                   = "standard"
  enable_rbac_authorization  = true
  soft_delete_retention_days = 7
  lifecycle {
    ignore_changes = [tenant_id] # leave old corp tenant keyvaults unchanged
  }
}

resource "azurerm_role_assignment" "vmss" {
  depends_on = [azurerm_key_vault.tlskeyvault]

  scope                = azurerm_key_vault.tlskeyvault.id
  role_definition_name = "Key Vault Certificate User"
  principal_id         = var.cluster_identity_object_id
}

resource "azurerm_role_assignment" "builder" {
  depends_on = [azurerm_key_vault.tlskeyvault]

  scope                = azurerm_key_vault.tlskeyvault.id
  role_definition_name = "Key Vault Certificates Officer"
  principal_id         = module.global_settings.ame_builder.identity.principal_id
}

resource "azurerm_key_vault_certificate_issuer" "ca" {
  depends_on = [ azurerm_role_assignment.builder ]
  count         = local.is_openai_org ? 1 : 0
  name          = "MSIT"
  key_vault_id  = azurerm_key_vault.tlskeyvault.id
  provider_name = "OneCertV2-PublicCA"
}
