variable "tenant_id" {
  type = string
}

variable "cluster_name" {
  type = string
}

variable "cluster_aks" {
  type        = any
  description = "Data or Resource for Azure AKS cluster"
}

variable "cluster_resource_group" {
  type = string
}

variable "cluster_region" {
  type = string
}

variable "cluster_identity_client_id" {
  type = string
}

variable "cluster_identity_object_id" {
  type        = string
  description = "object ID of the pool identity that will run the oidc proxy that will be granted access to the tls keyvault"
}

variable "oidc_proxy_name" {
  type        = string
  description = "Name of the oidc proxy service, etc."
}

variable "oidc_proxy_namespace" {
  type        = string
  description = "k8s namespace to create for the oidc proxy"
}

variable "oidc_application_id" {
  type        = string
  description = "application id of the oidc application"
}

variable "oidc_use_cluster_ip" {
  type        = bool
  description = "True if oidc DNS record should point to cluster internal IP; otherwise uses service external IP"
}

variable "oidc_tlskeyvault_name" {
  type        = string
  description = "Name to use for OIDC tlskeyvault. A new keyvault is created in the cluster RG"
}

variable "oidc_proxy_dns_names" {
  type        = list(string)
  description = "Fully qualified domain names for the oidc proxy. These are used as certificate SANs"
}

variable "oidc_username_claim" {
  type        = string
  description = "The token claim to use as the username in the oidc proxy. Examples: 'upn', 'oid'"
}

variable "oidc_proxy_node_selector" {
  type        = map(string)
  description = "Node selector for oidc proxy deployment"
  default = {
    "kubernetes.azure.com/mode" = "system"
  }
}
