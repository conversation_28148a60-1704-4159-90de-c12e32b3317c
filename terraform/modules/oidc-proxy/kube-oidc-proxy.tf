resource "kubernetes_namespace" "kube-oidc-proxy" {
  metadata {
    name = var.oidc_proxy_namespace
  }
}

resource "kubectl_manifest" "kube-oidc-proxy-tls" {
  depends_on = [
    kubernetes_namespace.kube-oidc-proxy,
    azurerm_key_vault_certificate.kube-oidc-proxy-tls,
    azurerm_role_assignment.vmss
  ]

  yaml_body = <<YAML
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: ${local.tls_secret_provider_class_name}
  namespace: ${kubernetes_namespace.kube-oidc-proxy.id}
spec:
  provider: azure
  secretObjects:
    - secretName: ${local.oidc_tls_secret_name}
      type: kubernetes.io/tls
      data:
        - objectName: ${local.keyvault_tls_certificate_name}
          key: tls.key
        - objectName: ${local.keyvault_tls_certificate_name}
          key: tls.crt
  parameters:
    useVMManagedIdentity: "true"
    userAssignedIdentityID: ${var.cluster_identity_client_id}
    keyvaultName: ${var.oidc_tlskeyvault_name}
    objects: |
      array:
      - |
        objectName: ${local.keyvault_tls_certificate_name}
        objectType: secret
    tenantId: ${var.tenant_id}
YAML
}

locals {
  chart = "kube-oidc-proxy"
}

resource "helm_release" "kube-oidc-proxy" {
  depends_on = [
    kubernetes_namespace.kube-oidc-proxy,
    kubectl_manifest.kube-oidc-proxy-tls,
  ]

  name      = "kube-oidc-proxy"
  namespace = kubernetes_namespace.kube-oidc-proxy.id

  repository = "${path.module}/charts"
  chart      = "kube-oidc-proxy"

  values = [yamlencode({
    nodeSelector = var.oidc_proxy_node_selector
  })]
  set {
    name  = "fullnameOverride"
    value = var.oidc_proxy_name
  }
  set {
    name  = "tls.secretName"
    value = local.oidc_tls_secret_name
  }
  set {
    name  = "oidc.clientId"
    value = var.oidc_application_id
  }
  set {
    name  = "oidc.issuerUrl"
    value = "https://login.microsoftonline.com/${var.tenant_id}/v2.0"
  }
  set {
    name  = "oidc.usernameClaim"
    value = var.oidc_username_claim
  }
  set {
    name  = "oidc.groupsClaim"
    value = "groups"
  }
  // force creation of tls secret
  set {
    name  = "extraVolumes[0].name"
    value = "tls-certs"
  }
  set {
    name  = "extraVolumes[0].csi.driver"
    value = "secrets-store.csi.k8s.io"
  }
  set {
    name  = "extraVolumes[0].csi.readOnly"
    value = true
  }
  set {
    name  = "extraVolumes[0].csi.volumeAttributes.secretProviderClass"
    value = local.tls_secret_provider_class_name
  }
  set {
    name  = "extraVolumeMounts[0].name"
    value = "tls-certs"
  }
  set {
    name  = "extraVolumeMounts[0].mountPath"
    value = "/mnt/secrets-store"
  }
  set {
    name  = "extraVolumeMounts[0].readOnly"
    value = true
  }

    set {
    name = "hash"
    value = sha1(join("", [
      for f in fileset("${path.module}/charts/${local.chart}", "**") : filesha256("${path.module}/charts/${local.chart}/${f}")
      ])
    )
  }
}

# this is created by the helm chart
data "kubernetes_service" "oidc_proxy" {
  depends_on = [helm_release.kube-oidc-proxy]
  metadata {
    name      = var.oidc_proxy_name
    namespace = var.oidc_proxy_namespace
  }
}

# Used by an output
locals {
  oidc_ip = var.oidc_use_cluster_ip ? data.kubernetes_service.oidc_proxy.spec.0.cluster_ip : data.kubernetes_service.oidc_proxy.status.0.load_balancer.0.ingress.0.ip
}

