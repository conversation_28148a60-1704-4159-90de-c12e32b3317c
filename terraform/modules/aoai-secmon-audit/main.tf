data "azurerm_log_analytics_workspace" "loganalytics" {
  provider = azurerm.auditing-sub
  name = "mumford-sentinel-green"
  resource_group_name = "sentinel"
}

resource "azurerm_monitor_diagnostic_setting" "secmon_audit" {
  name = "AOAISecMon-audit-terraform"
  target_resource_id = var.target_resource_id
  log_analytics_workspace_id = data.azurerm_log_analytics_workspace.loganalytics.id

  enabled_log {
    category_group = "allLogs"
  }

  lifecycle {
    ignore_changes = [
      metric
    ]
  }
}

