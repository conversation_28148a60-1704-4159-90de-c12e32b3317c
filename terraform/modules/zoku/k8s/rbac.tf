resource "kubernetes_cluster_role" "zoku" {
  metadata {
    name = "zoku"
  }

  rule {
    api_groups = ["zoku.openai.com"]
    resources  = ["clusters"]
    verbs      = ["get", "list"]
  }

  rule {
    api_groups = ["brix.openai.com"]
    resources  = ["clusters", "quotas", "skus"]
    verbs      = ["get", "list", "watch", "create", "update", "patch"]
  }

  rule {
    api_groups = [""]
    resources  = ["configmaps"]
    verbs      = ["get", "list", "create", "update", "patch"]
  }
}

resource "kubernetes_cluster_role_binding" "zoku" {
  metadata {
    name = "zoku"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = kubernetes_cluster_role.zoku.metadata[0].name
  }

  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.zoku.metadata[0].name
    namespace = var.namespace
  }
}
