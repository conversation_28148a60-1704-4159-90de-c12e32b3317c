resource "kubernetes_service_account" "zoku" {
  metadata {
    name      = "zoku"
    namespace = var.namespace
  }
}

resource "kubernetes_config_map" "team_resource_manager_config" {
  metadata {
    name      = "team-resource-manager-config"
    namespace = var.team_resource_manager_namespace
  }

  data = {
    "research_allocations" = jsonencode([]),
    "team_allocations"     = jsonencode([]),
  }
}

resource "kubernetes_service" "zoku" {
  metadata {
    name      = "zoku"
    namespace = var.namespace
  }

  spec {
    port {
      name        = "http"
      protocol    = "TCP"
      port        = 8080
      target_port = "8080"
    }

    selector = {
      "app" = "zoku"
    }

    type = "ClusterIP"
  }
}

resource "kubernetes_secret" "zoku-perhonen-auth" {
  metadata {
    namespace = var.namespace
    name      = "zoku-perhonen-auth"
  }
  type = "Opaque"
  data = {
    PERHONEN_AUTH_USERNAME = "perhonen"
    PERHONEN_AUTH_PASSWORD = var.perhonen_password
  }
}

resource "kubernetes_deployment" "zoku" {
  wait_for_rollout = false
  depends_on = [
    kubernetes_secret.zoku-perhonen-auth,
    kubernetes_service_account.zoku,
    kubernetes_cluster_role.zoku,
    kubernetes_cluster_role_binding.zoku,
  ]

  metadata {
    name      = "zoku"
    namespace = var.namespace
    labels = {
      app = "zoku"
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        "app" = "zoku"
      }
    }

    template {
      metadata {
        labels = {
          "app" = "zoku"
        }
      }

      spec {
        service_account_name = kubernetes_service_account.zoku.metadata[0].name
        container {
          name  = "zoku"
          image = "${var.acr}/brix/zoku/unified:${var.image_tag}"

          port {
            name           = "http"
            container_port = 8080
            protocol       = "TCP"
          }
          env {
            name  = "POSTGRES_DB"
            value = var.db_name
          }
          env {
            name  = "POSTGRES_HOSTNAME"
            value = var.db_hostname
          }
          env {
            name  = "POSTGRES_USER"
            value = var.db_username
          }
          env {
            name  = "PERHONEN_URL"
            value = var.perhonen_service_url
          }
          env {
            // This is only used to determine how to construct kubeclient to the cluster
            name = "ENV"
            value = var.env
          }
          env {
            name  = "DRY_RUN"
            value = var.dry_run
          }
          env {
            name  = "RUST_LOG"
            value = var.log_level
          }
          env {
            // Comma separated list of clusters
            name  = "CLUSTER_LIST"
            value = var.cluster_name
          }
          env {
            name  = "ENABLE_NDB_DISCOVERY"
            value = "false"
          }
          env_from {
            secret_ref {
              name     = kubernetes_secret.zoku-perhonen-auth.metadata[0].name
              optional = false
            }
          }
          env_from {
            secret_ref {
              name     = var.db_password_secret
              optional = false
            }
          }

          image_pull_policy = "Always"
        }

        toleration {
          key      = "openai.com/team"
          operator = "Equal"
          value    = "infra"
          effect   = "NoSchedule"
        }
      }
    }
  }
}
