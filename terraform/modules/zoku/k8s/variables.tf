variable "acr" {
  type    = string
  default = "openai.azurecr.io"
}

variable "image_tag" {
  description = "Image tag to use to for the Zoku deployment container"
  type        = string
  default     = "v0.29.0-31-g60acdad7"
}

variable "namespace" {
  description = "The namespace for the project."
  type        = string
  default     = "system"
}

variable "team_resource_manager_namespace" {
  description = "The namespace for the team resource manager"
  type        = string
  default     = "system"
}

variable "env" {
  description = "The environment for the project."
  type        = string
  default     = "dev"
}

variable "perhonen_password" {
  description = "Password for Perhonen basic auth"
  type        = string
  sensitive   = true
}

variable "perhonen_service_url" {
  description = "Internal service URL for Perhonen"
  type        = string
}

variable "cluster_name" {
  description = "The name of the clusters to watch"
  type        = string
}

variable "db_hostname" {
  description = "PostgreSQL database hostname"
  type        = string
}

variable "db_name" {
  description = "PostgreSQL database name"
  type        = string
  default     = "postgres"
}

variable "db_username" {
  description = "PostgreSQL database username"
  type        = string
  default     = "psqladmin"
}

variable "db_password_secret" {
  description = "Name of the Kubernetes secret containing the PostgreSQL password"
  type        = string
  default     = "zoku"
}

variable "dry_run" {
  description = "Whether to run in dry run mode"
  type        = bool
  default     = false
}

variable "log_level" {
  description = "Log level for Zoku"
  type        = string
  default     = "debug"
}
