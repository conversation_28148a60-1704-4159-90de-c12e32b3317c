apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: clusters.zoku.openai.com
spec:
  group: zoku.openai.com
  names:
    plural: clusters
    singular: cluster
    kind: CLUSTER
    shortNames:
      - cl
  scope: Cluster
  versions:
    - name: v1alpha1
      storage: true
      served: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              description: "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources"
              type: string
            kind:
              description: "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds"
              type: string
            metadata:
              type: object
            spec:
              type: object
              properties:
                colos:
                  description: Available colos in this cluster
                  type: array
                  items:
                    type: string
                islands:
                  description: Available islands in this cluster
                  type: array
                  items:
                    type: string
              required:
                - colos
                - islands
      subresources:
        status: {}
