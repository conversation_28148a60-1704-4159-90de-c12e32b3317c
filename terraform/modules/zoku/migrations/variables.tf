variable "namespace" {
  description = "Kubernetes namespace where the job will run"
  type        = string
  default     = "system"
}

variable "config_map_name" {
  description = "Name of the ConfigMap containing SQL initialization scripts"
  type        = string
  default     = "zoku-db-init-scripts"
}

variable "run_id" {
  description = "Unique identifier for this run (to avoid job name conflicts)"
  type        = string
  default     = "latest"
}

variable "postgres_version" {
  description = "PostgreSQL version for the init container"
  type        = string
  default     = "16"
}

variable "db_hostname" {
  description = "PostgreSQL database hostname e.g. zoku-db.zoku.postgres.contoso.com"
  type        = string
}

variable "db_username" {
  description = "PostgreSQL database username"
  type        = string
  default     = "psqladmin"
}

variable "db_name" {
  description = "PostgreSQL database name"
  type        = string
  default     = "postgres"
}

variable "db_password_secret_name" {
  description = "Name of the Kubernetes secret containing the PostgreSQL password"
  type        = string
  default     = "zoku"
}

variable "locations" {
  description = "List of location entries to populate the locations table"
  type = list(object({
    cluster      = string
    emoji        = string
    island       = optional(string)
    colo         = optional(string)
    skus         = list(string)
    feature_flag = optional(list(string), [])
  }))
  default = []
}

variable "skus" {
  description = "List of SKU entries to populate the skus table"
  type = list(object({
    name                    = string
    display_name            = string
    aliases                 = optional(list(string), [])
    instance_type           = string
    allocatable_cpu         = number
    allocatable_memory      = number
    allocatable_accelerator = optional(number)
    capacity_cpu            = number
    capacity_memory         = number
    capacity_accelerator    = optional(number)
  }))
  default = []
}

variable "initial_allocations" {
  description = "Initial resource allocations to populate the compute_resource_allocations table"
  type = list(object({
    account         = string
    cluster         = string
    island          = optional(string)
    colo            = optional(string)
    allocation_type = string
    sku             = string
    node_count      = number
    resource_count  = number
    burst_model     = string
    dedicated       = optional(bool, false)
    deleted         = bool
    workstream      = string
    parent          = optional(number, -1)
    priority        = optional(number, 20)
    editors         = optional(list(string), [])
  }))
  default = []
}
