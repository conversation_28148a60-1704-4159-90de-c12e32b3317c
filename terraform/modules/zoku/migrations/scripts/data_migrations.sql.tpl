-- Zoku Database Data Migrations
-- Using transactions to ensure atomicity and avoid service disruption

BEGIN;

-- LOCATIONS MIGRATION
-- Delete all existing data for clean insert
DELETE FROM locations;

-- Insert location data
%{ if length(locations) > 0 ~}
  %{ for location in locations ~}
    INSERT INTO locations (
        cluster,
        emoji,
        island,
        colo,
        skus,
        feature_flag
    ) VALUES (
        '${location.cluster}',
        '${location.emoji}',
        ${location.island != null ? "'${location.island}'" : "NULL"},
        ${location.colo != null ? "'${location.colo}'" : "NULL"},
        '{${join(",", [for sku in location.skus : format("%s", sku)])}}',
        ${length(location.feature_flag) > 0 ? format("'{%s}'", join(",", [for flag in location.feature_flag : format("%s", flag)])) : "'{}'"}
    );
  %{ endfor ~}
%{ endif ~}

-- SKUS MIGRATION
-- Delete all existing data for clean insert
DELETE FROM skus;

%{ if length(skus) > 0 ~}
  %{ for sku in skus ~}
    INSERT INTO skus (
        name,
        display_name,
        aliases,
        instance_type,
        allocatable_cpu,
        allocatable_memory,
        allocatable_accelerator,
        capacity_cpu,
        capacity_memory,
        capacity_accelerator
    ) VALUES (
        '${sku.name}',
        '${sku.display_name}',
        ${ length(sku.aliases) > 0 ? format("ARRAY['%s']::TEXT[]", join("','", sku.aliases)) : "ARRAY[]::TEXT[]" },
        '${sku.instance_type}',
        ${sku.allocatable_cpu},
        ${sku.allocatable_memory},
        ${sku.allocatable_accelerator != null ? sku.allocatable_accelerator : "NULL"},
        ${sku.capacity_cpu},
        ${sku.capacity_memory},
        ${sku.capacity_accelerator != null ? sku.capacity_accelerator : "NULL"}
    );
  %{ endfor ~}
%{ endif ~}

-- COMPUTE RESOURCE ALLOCATIONS MIGRATION
-- Delete existing allocation-related data for referential integrity
DELETE FROM compute_resource_allocation_editors;
DELETE FROM compute_resource_allocations;

%{ if length(allocations) > 0 ~}
  %{ for allocation in allocations ~}
    WITH new_allocation AS (
        INSERT INTO compute_resource_allocations (
            account,
            cluster,
            island,
            colo,
            allocation_type,
            sku,
            node_count,
            resource_count,
            burst_model,
            dedicated,
            deleted,
            workstream,
            parent,
            priority
        ) VALUES (
            '${allocation.account}',
            '${allocation.cluster}',
            ${allocation.island != null ? "'${allocation.island}'" : "NULL"},
            ${allocation.colo != null ? "'${allocation.colo}'" : "NULL"},
            '${allocation.allocation_type}',
            '${allocation.sku}',
            ${allocation.node_count},
            ${allocation.resource_count},
            '${allocation.burst_model}',
            ${allocation.dedicated},
            ${allocation.deleted},
            '${allocation.workstream}',
            ${allocation.parent},
            ${allocation.priority}
        ) RETURNING id
    )
    %{ if length(allocation.editors) > 0 ~}
      -- Insert editors for this allocation
      %{ for editor in allocation.editors ~}
        INSERT INTO compute_resource_allocation_editors (
            allocation_id,
            editor
        )
        SELECT id, '${editor}' FROM new_allocation;
      %{ endfor ~}
    %{ endif ~}
  %{ endfor ~}
%{ endif ~}

-- Record migration event
INSERT INTO event_history (
    who, entity_id, entity_table, entity_data
) VALUES (
    'terraform_migration',
    0,
    'system',
    jsonb_build_object(
        'action', 'migration',
        'timestamp', NOW()::text
    )
);

COMMIT;