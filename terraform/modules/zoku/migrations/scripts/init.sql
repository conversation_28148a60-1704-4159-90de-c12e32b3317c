-- Zoku Database Initialization Script

-- First apply schema definitions
\i /scripts/schema.sql

-- Then apply data migrations
\i /scripts/data_migrations.sql

-- Verification queries to show data was properly loaded
SELECT 'DATABASE INITIALIZATION COMPLETE' as status;

-- Display sample data from each table
SELECT 'LOCATIONS TABLE SAMPLE:' as table_name;
SELECT * FROM locations LIMIT 5;

SELECT 'SKUS TABLE SAMPLE:' as table_name;
SELECT * FROM skus LIMIT 5;

SELECT 'COMPUTE RESOURCE ALLOCATIONS TABLE SAMPLE:' as table_name;
SELECT * FROM compute_resource_allocations LIMIT 5;

SELECT 'COMPUTE RESOURCE ALLOCATION EDITORS TABLE SAMPLE:' as table_name;
SELECT * FROM compute_resource_allocation_editors LIMIT 5;

SELECT 'EVENT HISTORY TABLE SAMPLE:' as table_name;
SELECT * FROM event_history LIMIT 5;

-- Display record counts for verification
SELECT 
    'TABLE COUNTS:' as verification,
    (SELECT COUNT(*) FROM locations) as locations_count,
    (SELECT COUNT(*) FROM skus) as skus_count,
    (SELECT COUNT(*) FROM compute_resource_allocations) as allocations_count,
    (SELECT COUNT(*) FROM compute_resource_allocation_editors) as editors_count,
    (SELECT COUNT(*) FROM event_history) as events_count; 