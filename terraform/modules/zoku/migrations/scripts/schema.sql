-- Zoku Database Schema Definitions
-- Source: https://dev.azure.com/project-argos/Mimco/_git/brix?path=/zoku/server/src/db_tables
-- Last Commit: 828aa2682de66b566014ceebdc4d8659c482d6c3
-- Commit Date: 2024-12-14

-- Table for storing location related static attributes
CREATE TABLE IF NOT EXISTS locations (
    id SERIAL PRIMARY KEY,
    cluster VARCHAR,
    emoji VARCHAR,
    island VARCHAR,
    colo VARCHAR,
    skus TEXT[] NOT NULL,
    feature_flag TEXT[] NOT NULL DEFAULT '{}'
);

-- Create the unique index for locations
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_location ON locations
(cluster, island, colo);

-- Table for storing SKU information
CREATE TABLE IF NOT EXISTS skus (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    aliases TEXT[],
    instance_type VARCHAR(10) NOT NULL,
    allocatable_cpu INTEGER NOT NULL,
    allocatable_memory BIGINT NOT NULL,
    allocatable_accelerator INTEGER,
    capacity_cpu INTEGER NOT NULL,
    capacity_memory BIGINT NOT NULL,
    capacity_accelerator INTEGER
);

-- Create the unique index for SKUs
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_sku ON skus(name);

-- Table for storing resource allocation intents
CREATE TABLE IF NOT EXISTS compute_resource_allocations (
    id SERIAL PRIMARY KEY,
    last_updated TIMESTAMP NOT NULL DEFAULT NOW(),
    account VARCHAR NOT NULL,
    cluster VARCHAR NOT NULL,
    island VARCHAR,
    colo VARCHAR,
    allocation_type VARCHAR NOT NULL,
    sku VARCHAR NOT NULL,
    node_count INTEGER NOT NULL,
    resource_count INTEGER NOT NULL,
    burst_model VARCHAR NOT NULL,
    dedicated BOOLEAN NOT NULL DEFAULT FALSE,
    deleted BOOLEAN NOT NULL,
    workstream VARCHAR NOT NULL,
    parent INTEGER DEFAULT -1,
    priority INTEGER NOT NULL DEFAULT 20
);

-- Create the unique index for allocations
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_allocation ON compute_resource_allocations
(account, cluster, island, colo, sku);

-- Table for storing resource allocation editors
CREATE TABLE IF NOT EXISTS compute_resource_allocation_editors (
    allocation_id INTEGER NOT NULL REFERENCES compute_resource_allocations(id) ON DELETE CASCADE,
    editor VARCHAR NOT NULL,
    PRIMARY KEY (allocation_id, editor)
);

-- Table for storing update event histories
CREATE TABLE IF NOT EXISTS event_history (
    id SERIAL PRIMARY KEY,
    update_timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    who VARCHAR NOT NULL DEFAULT 'Unknown',
    entity_id INTEGER NOT NULL,
    entity_table VARCHAR NOT NULL,
    entity_data JSONB NOT NULL
);