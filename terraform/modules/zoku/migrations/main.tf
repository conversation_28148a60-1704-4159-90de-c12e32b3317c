resource "kubernetes_config_map" "db_init_scripts" {
  metadata {
    name      = var.config_map_name
    namespace = var.namespace
  }

  data = {
    "init.sql"   = file("${path.module}/scripts/init.sql")
    "schema.sql" = file("${path.module}/scripts/schema.sql")
    "data_migrations.sql" = templatefile("${path.module}/scripts/data_migrations.sql.tpl", {
      locations   = var.locations
      skus        = var.skus
      allocations = var.initial_allocations
    })
  }
}

resource "kubernetes_job" "db_init" {
  wait_for_completion = true
  metadata {
    name      = "zoku-db-init-${var.run_id}"
    namespace = var.namespace
  }

  spec {
    template {
      metadata {
        name = "zoku-db-init"
      }

      spec {
        container {
          name  = "db-init"
          image = "postgres:${var.postgres_version}"
          env {
            name  = "POSTGRES_HOSTNAME"
            value = var.db_hostname
          }

          env {
            name  = "POSTGRES_USER"
            value = var.db_username
          }

          env {
            name  = "POSTGRES_DB"
            value = var.db_name
          }

          env_from {
            secret_ref {
              name = var.db_password_secret_name
            }
          }

          volume_mount {
            name       = "init-scripts"
            mount_path = "/scripts"
          }

          command = ["/bin/bash", "-c"]
          args = [<<-EOF
            echo "Waiting for database to be ready..."
            until PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOSTNAME -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;" >/dev/null 2>&1; do
              echo "Database not ready yet, retrying in 5 seconds..."
              sleep 5
            done
            
            echo "=================================================================="
            echo "Database is ready, applying schema and migrations..."
            echo "=================================================================="
            cd /scripts
            
            # Run initialization and capture output
            echo "Starting database initialization..."
            PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOSTNAME -U $POSTGRES_USER -d $POSTGRES_DB -f init.sql
            
            echo "=================================================================="
            echo "Database initialization complete!"
            echo "=================================================================="
          EOF
          ]
        }

        volume {
          name = "init-scripts"
          config_map {
            name = kubernetes_config_map.db_init_scripts.metadata[0].name
          }
        }

        restart_policy = "OnFailure"

        # Add timeout to prevent job from running indefinitely if there's an issue
        active_deadline_seconds = 300
      }
    }
    backoff_limit = 3
    # Automatically clean up jobs after they complete
    ttl_seconds_after_finished = 300
  }
}
