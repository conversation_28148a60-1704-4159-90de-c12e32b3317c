locals {
  namespace = var.namespace
  name      = "joiner-watch-experiments${var.name_suffix}"
}

resource "kubernetes_deployment" "joiner_watch_experiments" {
  metadata {
    name      = local.name
    namespace = var.namespace
  }

  spec {
    selector {
      match_labels = {
        app = local.name
      }
    }
    # Use 1 replica with 'Recreate' strategy to avoid ever having more than one experiment watcher
    # running at a ``given time, since state is kept in a single blobstore file without any locking.
    replicas = 1
    strategy {
      type = "Recreate"
    }
    template {
      metadata {
        labels = {
          app = local.name
        }
        # Use annotation to trigger redeployment on configmap change
        annotations = { config_hash = var.config_hash }
      }

      spec {
        priority_class_name = var.priority_class_name

        node_selector = {
          "kubernetes.azure.com/mode"             = "user"
          "singularity.azure.com/processing-unit" = "cpu"
        }

        container {
          image = var.image
          name  = local.name

          env_from {
            config_map_ref {
              name = var.configmap_name
            }
          }

          env_from {
            secret_ref {
              name = var.kubernetes_sp_secret_name
            }
          }

          port {
            container_port = 9090
            name           = "metrics"
          }
          liveness_probe {
            http_get {
              path = "/"
              port = 9090
            }
          }
          resources {
            limits = {
              cpu    = "2000m"
              memory = "2048Mi"
            }
            requests = {
              cpu    = "1000m"
              memory = "512Mi"
            }
          }

          # Allows to profile with py-spy and similar tools.
          security_context {
            capabilities {
              add = ["SYS_PTRACE"]
            }
          }
        }
      }
    }
  }
}
