variable "namespace" {
  type = string
}

variable "name_suffix" {
  type = string
}

variable "image" {
  type = string
}

variable "config_hash" {
  type = string
}

variable "configmap_name" {
  type = string
}

variable "override_quota_team" {
  type        = string
  default     = ""
  description = <<-EOT
  Set to use a specific team's quota instead of namespace default. Value should match the
  `openai.com/team` taint on the desired nodes.
EOT
}

variable "priority_class_name" {
  type        = string
  description = "Name of the priority class to use for the pods"
  default     = "team-infra-high"
}

variable "kubernetes_sp_secret_name" {
  type        = string
  description = "Name of the kubernetes secret"
}