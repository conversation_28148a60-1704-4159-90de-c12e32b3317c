variable "namespace" {
  type = string
}

variable "name_suffix" {
  type = string
}

variable "image" {
  type = string
}

variable "config_hash" {
  type = string
}

variable "configmap_name" {
  type = string
}

variable "replicas" {
  type = number
}

variable "resource_request_cpu" {
  type    = string
  default = "6000m"
}

variable "resource_request_memory" {
  type    = string
  default = "24Gi"
}

variable "storage_resource_request" {
  type        = string
  description = "Size of PVC storage request, including unit, e.g. 8Ti or 1Gi"
}

variable "override_quota_team" {
  type        = string
  default     = ""
  description = <<-EOT
  Set to use a specific team's quota instead of namespace default. Value should match the
  `openai.com/team` taint on the desired nodes.
EOT
}

variable "priority_class_name" {
  type        = string
  description = "Name of the priority class to use for the pods"
  default     = "team-infra-high"
}
