locals {
  namespace         = var.namespace
  name              = "joiner-store-v3${var.name_suffix}"
  store_volume_name = "joiner-store-v3-volume${var.name_suffix}"
}

resource "kubernetes_stateful_set" "joiner_store" {
  metadata {
    name      = local.name
    namespace = var.namespace
  }

  spec {
    service_name          = "${local.name}-headless"
    replicas              = var.replicas
    pod_management_policy = "Parallel"

    selector {
      match_labels = {
        app = local.name
      }
    }

    volume_claim_template {
      metadata {
        name = local.store_volume_name
      }

      spec {
        access_modes       = ["ReadWriteOnce"]
        storage_class_name = "managed-premium-nocache"
        resources {
          requests = {
            storage = var.storage_resource_request
          }
        }
      }
    }

    template {
      metadata {
        labels = {
          app = local.name
        }
        # Use annotation to trigger redeployment on secret or configmap change
        annotations = { config_hash = var.config_hash }
      }

      spec {
        priority_class_name = var.priority_class_name
        # Allow rocksdb to gracefully shutdown.
        termination_grace_period_seconds = 10

        node_selector = {
          "kubernetes.azure.com/mode" = "user"
          "singularity.azure.com/processing-unit" = "cpu"
        }

        container {
          image = var.image
          name  = local.name

          volume_mount {
            name       = local.store_volume_name
            mount_path = "/joiner_store"
          }

          env_from {
            config_map_ref {
              name = var.configmap_name
            }
          }

          port {
            container_port = 8000
            name           = "http"
          }
          port {
            container_port = 9090
            name           = "metrics"
          }
          liveness_probe {
            http_get {
              path = "/healthz"
              port = 9090
            }

            initial_delay_seconds = 600    # 10-minute grace period on startup
            period_seconds        = 60     # 1 minute
            timeout_seconds       = 9 * 60 # 9 minutes
            failure_threshold     = 5      # 1 hour max
          }
          resources {
            requests = {
              cpu    = var.resource_request_cpu
              memory = var.resource_request_memory
            }

            # No limits due to kernel page cache behavior, see
            # https://web.archive.org/web/20221118100149/https://github.com/kubernetes/kubernetes/issues/43916
          }

          # Allows to profile with py-spy and similar tools.
          security_context {
            capabilities {
              add = ["SYS_PTRACE"]
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "joiner_store_v3_headless" {
  metadata {
    name      = "${local.name}-headless"
    namespace = var.namespace
  }

  spec {
    cluster_ip = "None"

    selector = {
      app = local.name
    }

    port {
      port        = 8000
      target_port = 8000
      protocol    = "TCP"
      name        = "http"
    }

    port {
      port        = 9090
      target_port = 9090
      protocol    = "TCP"
      name        = "metrics"
    }
  }
}
