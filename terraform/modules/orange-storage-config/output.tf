module "azure-naming" {
  source = "../../modules/azure-naming"
}

locals {
  global_rg_name = "orange-resources"
  regional_rg_names = {
    for region in keys(merge(local.regions, local.regions_to_provision)) : region => "orange-${region}-resources"
  }

  regional_storage_accounts = merge([
    for region in keys(local.regions) : {
      for tent in keys(local.tents) : "orng${module.azure-naming.regions[region].short_name}${tent}" => {
        description         = "regional storage account for tent ${tent} in region ${region}"
        region              = region
        tent                = tent
        resource_group_name = local.regional_rg_names[region]
      }
    }
  ]...)
  global_storage_accounts = {
    for tent in keys(local.tents) : "orng${tent}" => {
      description         = "global storage account for tent ${tent}"
      tent                = tent
      resource_group_name = local.global_rg_name
    }
  }
  extra_global_accounts_augmented = { for key, value in local.extra_global_accounts : key => merge(
    value,
    { resource_group_name = local.global_rg_name }
  ) }
  all_storage_accounts = merge(
    local.regional_storage_accounts,
    local.global_storage_accounts,
    local.extra_global_accounts_augmented
  )
}

output "global_rg_name" {
  description = "name of the global resource group for model storage"
  value       = local.global_rg_name
}

output "regional_rg_names" {
  description = "map of regional resource group names for model storage"
  value       = local.regional_rg_names
}

output "tents" {
  description = "set of tents with generated storage accounts for data/models"
  value       = local.tents
}

output "regions" {
  description = "set of regions with generated storage accounts for data/models"
  value       = local.regions
}

output "regions_to_provision" {
  description = "Map of regions currently being provisioned."
  value       = local.regions_to_provision
}

output "extra_global_accounts" {
  description = "set of extra global accounts with oai data"
  value       = local.extra_global_accounts
}

output "all_storage_accounts" {
  description = "map of all storage accounts, both generated and ad-hoc, with tenting information"
  value       = local.all_storage_accounts
}
