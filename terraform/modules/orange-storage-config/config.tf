# tented storage accounts for data/models
# - generates orng<TENT> global accounts
# - generates orng<REGION><TENT> regional accounts
# orange-storage-bootstrapping/ will create these accounts
# orange-user-onboarding/ will assign rbac for users

locals {
  # set of tents to create global/regional storage for
  tents = {
    "cresco" = {}
  }
  # set of full azure region names to create regional storage for
  regions = {
    "uksouth"        = {}
    "southcentralus" = {}
    "eastus2"        = {}
    "westus2"        = {}
    "australiaeast"  = {}
    "centralus"      = {}
    "canadacentral"  = {}
  }

  regions_to_provision = {
  }

  # map of ad-hoc global storage accounts to create
  extra_global_accounts = {
    "orngoaiartifacts" = {
      description    = "contains oai wheels"
      tent           = "cresco"
      builder_access = true
      containers = {
        "storage-map" = {
          description = "regional oai storage account mapping"
          permissions = [
            {
              name     = "LemonInfraStorageMap"
              objectid = "c5b25592-4c39-4d18-ac13-1c99e8b87471"
              role     = "Storage Blob Data Reader"
              type     = "ServicePrincipal"
            }
          ]
        }
        "data-gym" = {
          description = "regional oai data gym"
          permissions = [
            {
              name     = "LemonInfraDataGym"
              objectid = "c5b25592-4c39-4d18-ac13-1c99e8b87471"
              role     = "Storage Blob Data Reader"
              type     = "ServicePrincipal"
            }
          ]
        }
      }
    }
    "orngstore" = {
      description = "contains untented data/models"
      tent        = ""
    }
    "orngtransfer" = {
      description                      = "Container contains models/data transferred from AML Green"
      tent                             = "cresco"
      builder_access                   = true
      versioning_enabled               = true
      change_feed_enabled              = true
      cross_tenant_replication_enabled = true
      containers = {
        "grninbound" = {
          description = "data transferred from AML Green to Orange"
        }
        "grnoutbound" = {
          description = "data to be transfered from Orange to AML Green"
        }
        "ameinbound" = {
          description = "data to be transferred from AME to Orange"
        }
        "devaultoutbound" = {
          description = "Outbound container for deploying models to devault registry"
          permissions = [
            {
              name     = "StorageBrokerDevault"
              objectid = "54d2df28-dcb5-4216-acba-1fd664fba2e2"
              role     = "Storage Blob Data Reader"
              type     = "ServicePrincipal" # User, Group, ServicePrincipal
            }
          ]
        }
      }
    }
    "m365transfer" = {
      description         = "Office 365 data transfer storage account"
      tent                = ""
      builder_access      = true
      versioning_enabled  = true
      change_feed_enabled = true
      containers = {
        "grninbound" = {
          description = "data transferred from AML Green to Orange"
        }
        "grnoutbound" = {
          description = "data to be transfered from Orange to AML Green"
        }
      }
    },
    "orngmodels" = {
      description = "Yet another orange models storage account"
      tent        = "cresco"
    },
    "orngdata" = {
      description = "Yet another orange data storage account"
      tent        = ""
    },
    "orngcaas" = {
      description    = "Container contains training data for CaaS"
      tent           = "cresco"
      builder_access = true
      location       = "eastus2"
      containers = {
        "data" = {
          description = "Contains Caas training data"
          permissions = [
            {
              name     = "caas-data-reader"
              objectid = "70e98d21-8300-46f8-b30d-9bfa045fe9a7"
              role     = "Storage Blob Data Reader"
              type     = "ServicePrincipal" # User, Group, ServicePrincipal
            }
          ]
        }
      }
    }
    "orngcaasscus" = {
      description    = "Container contains training data for CaaS"
      tent           = "cresco"
      builder_access = true
      location       = "southcentralus"
      containers = {
        "data" = {
          description = "Contains Caas training data"
          permissions = [
            {
              name     = "caas-data-reader"
              objectid = "70e98d21-8300-46f8-b30d-9bfa045fe9a7"
              role     = "Storage Blob Data Reader"
              type     = "ServicePrincipal" # User, Group, ServicePrincipal
            }
          ]
        }
      }
    }
  }
}
