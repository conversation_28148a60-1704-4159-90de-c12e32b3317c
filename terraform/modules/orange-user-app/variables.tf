variable "user_alias" {
  description = "Alias for the user being onboarded, if not a robot user."
  type        = string
  default     = null
}

variable "service_tree_id" {
  description = "ID of the service tree to associate the user app with."
  type        = string
  default     = "f8b58987-3772-4fbd-be43-5944ed5ba320" # AI Supercomputer - Orange https://microsoftservicetree.com/services/f8b58987-3772-4fbd-be43-5944ed5ba320/
}

variable "app_name_suffix" {
  description = "Suffix to append to the app name."
  type        = string
  default     = "orange-app"
}

variable "group_name_suffix" {
  description = "Suffix to append to the group name."
  type        = string
  default     = "orange-user"
}

variable "tenant_domain" {
  description = "Domain of the tenant."
  type        = string
  default     = "green.microsoft.com"
}

variable "key_vault_id" {
  description = "ID of a keyvault in which to store the user app ID and secret"
  type        = string
}

variable "msi_object_id" {
  description = "Object ID of a managed identity (MSI) to add to the group, if onboarding a robot user. Optional."
  type        = string
  default     = null
}