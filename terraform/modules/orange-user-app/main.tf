data "azuread_client_config" "current" {}

# App Registration
resource "azuread_application" "user_app" {
  display_name                 = "${var.user_alias}-${var.app_name_suffix}"
  owners                       = [data.azuread_client_config.current.object_id]
  service_management_reference = var.service_tree_id
}

# Service Principal
resource "azuread_service_principal" "user_sp" {
  depends_on = [azuread_application.user_app]
  client_id  = azuread_application.user_app.client_id
  owners     = [data.azuread_client_config.current.object_id]
}

resource "azuread_application_password" "user_sp_password" {
  depends_on     = [azuread_application.user_app, azuread_service_principal.user_sp]
  application_id = azuread_application.user_app.id
}

# Each user has 2 identities: user and service principal
# This group is created to group these 2 identities under a single resources
# Any future access that is granted to this user will be granted to this group
resource "azuread_group" "user_group" {
  display_name     = "${var.user_alias}-${var.group_name_suffix}"
  owners           = [data.azuread_client_config.current.object_id]
  security_enabled = true
}

# Only create the AAD user data source if msi_object_id is not set
locals {
  use_aad_user = var.msi_object_id == null
}

data "azuread_user" "user" {
  count = local.use_aad_user ? 1 : 0
  user_principal_name = "${var.user_alias}@${var.tenant_domain}"
}

# Add user account to user group (only if not MSI)
resource "azuread_group_member" "user_group_member" {
  count           = local.use_aad_user ? 1 : 0
  group_object_id = azuread_group.user_group.object_id
  member_object_id = data.azuread_user.user[0].object_id
}

# Add service principal to user group
resource "azuread_group_member" "user_sp_group_member" {
  group_object_id  = azuread_group.user_group.object_id
  member_object_id = azuread_service_principal.user_sp.object_id
}

# Add MSI to user group if provided
resource "azuread_group_member" "msi_group_member" {
  count           = var.msi_object_id != null ? 1 : 0
  group_object_id = azuread_group.user_group.object_id
  member_object_id = var.msi_object_id
}

# We store the app ID and secret into a keyvault; separate automation
# copies them into cluster secrets

resource "azurerm_key_vault_secret" "user_kv_clientid" {
  name         = "${var.user_alias}-clientid"
  value        = azuread_application.user_app.client_id
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "user_kv_secret" {
  name         = "${var.user_alias}-secret"
  value        = azuread_application_password.user_sp_password.value
  key_vault_id = var.key_vault_id
}
