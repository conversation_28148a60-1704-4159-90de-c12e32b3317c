The builder pool for resources that we need to access their data plabe will require special access to be granted to the builder.

This module will create a private endpoint and DNS entry in the builder's vnet. It will also grant the builder identity a role assignment to the resource.

For example we create a Key Vault that we want to create a secret in, since all our keyvaults have public access disabled, we need to create a private endpoint into the builder virtual network. Then for the builder to be able to add a secret to the keyvault it will need "Key Vault Secret Officer" role assignment.
