variable "name" {
  description = "Private endpoint identifier"
  type        = string
}

variable "dns_zone" {
  description = "DNS zone object"
  type = object({
    name             = string
    short_name       = string
    subresource_name = string
  })
}

variable "resource_id" {
  description = "Resource ID of the service to connect to"
  type        = string
}

variable "role_assignments" {
  description = "Role assignments to be applied to resource"
  type        = set(string)
}
