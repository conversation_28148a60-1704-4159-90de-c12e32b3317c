locals {
  subnet_id = "/subscriptions/${module.global.builder.subscription}/resourceGroups/${module.global.builder.resource_group}/providers/Microsoft.Network/virtualNetworks/${module.global.builder.vnet_name}/subnets/${module.global.builder.subnet_name}"
}

data "azurerm_private_dns_zone" "private_dns_zone" {
  name                = var.dns_zone.name
  resource_group_name = module.global.builder.resource_group
}

resource "azurerm_private_endpoint" "builder_private_endpoint" {
  name                = "pe-${var.name}"
  location            = module.global.builder.location
  resource_group_name = module.global.builder.resource_group
  subnet_id           = local.subnet_id

  private_service_connection {
    name                           = "conn-${var.name}"
    private_connection_resource_id = var.resource_id
    subresource_names              = [var.dns_zone.subresource_name]
    is_manual_connection           = false
  }

  private_dns_zone_group {
    name                 = "dns-${var.name}"
    private_dns_zone_ids = [data.azurerm_private_dns_zone.private_dns_zone.id]
  }
}
