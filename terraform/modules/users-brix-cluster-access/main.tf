
resource "kubernetes_cluster_role_binding" "brix_cluster_access" {
  count = length(var.users) > 0 ? 1 : 0

  metadata {
    name = "brix-cluster-access"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-cluster-access"
  }
  dynamic "subject" {
    for_each = var.users
    iterator = user_iterator
    content {
      api_group = "rbac.authorization.k8s.io"
      kind      = "User"
      name      = user_iterator.value.auth_identifier
      namespace = user_iterator.key
    }
  }
}

resource "kubernetes_cluster_role_binding" "brix_cluster_access-oid" {
  count = length(var.users) > 0 ? 1 : 0

  metadata {
    name = "brix-cluster-access-oid"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-cluster-access"
  }
  dynamic "subject" {
    for_each = var.users
    iterator = user_iterator
    content {
      api_group = "rbac.authorization.k8s.io"
      kind      = "User"
      name      = user_iterator.value.object_id
      namespace = user_iterator.key
    }
  }
}

moved {
  from = kubernetes_cluster_role_binding.brix_cluster_access
  to   = kubernetes_cluster_role_binding.brix_cluster_access[0]
}

# We used to have one role binding with both service accounts and users.
# It seemed like we were hitting some sort of bug in the provider for plan diffs
# where a User -> ServiceAccount replacement would have the wrong api_group; this
# is an attempt to work around that.
resource "kubernetes_cluster_role_binding" "brix_cluster_access_svcacct" {
  count = length(var.users) > 0 ? 1 : 0

  metadata {
    name = "brix-cluster-access-svcacct"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "brix-cluster-access"
  }
  dynamic "subject" {
    for_each = var.users
    iterator = user_iterator
    content {
      api_group = ""
      kind      = "ServiceAccount"
      name      = "default"
      namespace = user_iterator.key
    }
  }
}
