variable "vnet_resource_group_name" {
  type = string
}

variable "vnet_location" {
  type = string
}

variable "subnet_id" {
  type        = string
  description = "The ID of the subnet within the VNet"
}

variable "cluster_name" {
  type        = string
  description = "Cluster name used as the string for DNS registration, {cluster_name}-caas.internal.genai.ms"
}

variable "private_dns_zone" {
  type        = object({
    id                  = string
    name                = string
    resource_group_name = string
  })
  description = "private dns zone to register service FQDN"
}
