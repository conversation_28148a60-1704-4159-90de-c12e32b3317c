locals {
  caas_alias = "pls-a33000e912a46420088736973e431354.e58b8311-2433-404f-918c-aecc4384becd.eastus2.azure.privatelinkservice"
}

resource "azurerm_private_endpoint" "caas_private_endpoint" {
  name                = "pe-caas-${var.vnet_location}"
  location            = var.vnet_location
  resource_group_name = var.vnet_resource_group_name
  subnet_id           = var.subnet_id

  private_service_connection {
    name                              = "conn-caas-${var.vnet_location}"
    private_connection_resource_alias = local.caas_alias
    is_manual_connection              = true
    request_message                   = "Cluster ${var.cluster_name} private-endpoint connection"
  }

  private_dns_zone_group {
    name                 = "dns-caas-${var.vnet_location}"
    private_dns_zone_ids = [var.private_dns_zone.id]
  }
}

# add a record for the caas private endpoint
resource "azurerm_private_dns_a_record" "caas_service_record" {
  name                = "caas.${var.cluster_name}"
  zone_name           = var.private_dns_zone.name
  resource_group_name = var.private_dns_zone.resource_group_name
  ttl                 = 300
  records             = [azurerm_private_endpoint.caas_private_endpoint.private_service_connection[0].private_ip_address]
}
