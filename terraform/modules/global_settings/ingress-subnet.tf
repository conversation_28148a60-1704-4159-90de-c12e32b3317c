# This is the place for all cluster service and ingress subnet CIDR, make sure the CIDR is unique across all clusters


locals {

  // this subnet is for clusters that vnet range is 10.100.0.0/16
  // those clusters should take a /25 in the range below
  // 64 clusters can be created in this range
  all_ingress_subnet = "10.100.96.0/19"
}

locals {
  ingress_subnet_cidr_map = {
    "prod-uksouth-7" = cidrsubnet(local.all_ingress_subnet, 6, 0)
    "prod-uksouth-8" = cidrsubnet(local.all_ingress_subnet, 6, 1)

    "stage-southcentralus-hpe-1" = cidrsubnet(local.all_ingress_subnet, 6, 2)

    "prod-southcentralus-hpe-1" = cidrsubnet(local.all_ingress_subnet, 6, 3)
    "prod-southcentralus-hpe-2" = cidrsubnet(local.all_ingress_subnet, 6, 4)
    "prod-southcentralus-hpe-3" = cidrsubnet(local.all_ingress_subnet, 6, 5)
    "prod-southcentralus-hpe-4" = cidrsubnet(local.all_ingress_subnet, 6, 6)
    "prod-southcentralus-hpe-5" = cidrsubnet(local.all_ingress_subnet, 6, 7)

    // this cluster has global unique vnet range, 172.16.144.0/21 and 172.16.152.0/21 so take last /25 in the range
    "prod-uksouth-9" = cidrsubnet("172.16.152.0/21", 4, 15)

    // this cluster has global unique vnet range, 172.18.240.0/21 and 172.18.248.0/21 so take last /25 in the range
    "prod-uksouth-15" = cidrsubnet("172.18.248.0/21", 4, 15)

    // this cluster has global unique vnet range, 172.19.48.0/21 and 172.19.56.0/21 so take last /25 in the range
    "prod-westus2-19" = cidrsubnet("172.19.56.0/21", 4, 15)

    // this cluster has global unique vnet range, 172.19.232.0/21 and 172.19.224.0/21 so take last /25 in the range
    "prod-eastus2-30" = cidrsubnet("172.19.232.0/21", 4, 15)

    // this cluster has global unique vnet range, 172.18.40.0/21 so take last /25 in the range
    "stage-southcentralus-2" = cidrsubnet("172.18.40.0/21", 4, 15)
  }

  ingress_subnet_cidr_list = values(local.ingress_subnet_cidr_map)
  unique_cidr_list         = distinct(local.ingress_subnet_cidr_list)
}

check "validate_unique_cidrs" {
  assert {
    condition     = length(local.ingress_subnet_cidr_list) == length(local.unique_cidr_list)
    error_message = "Duplicate CIDRs found in ingress_subnet_cidr_map!"
  }
}

output "ingress_subnet_cidr" {
  value = local.ingress_subnet_cidr_map
}
