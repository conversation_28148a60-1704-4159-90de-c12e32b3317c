locals {
  security_groups = {
    base   = "orange-base-access",
    cresco = "orange-cresco-access",
  }

  # Storage accounts with no tent have ""
  # this is a workaround to add a group name to the "" key
  # makes things easier when using for_each
  security_groups_compat = merge(
    { "" = local.security_groups.base },
    local.security_groups
  )

  # Build pool settings
  # These are hardcoded from the orange-builder root module
  # TODO: We should switch it around where the root module reads this info
  # and uses it as the source of truth
  builder_pool = "orange-builder"
  builder = {
    identity = {
      principal_id = "82cb6bbd-45fc-4139-9980-bf074453ab80"
      resource_id  = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-builder/providers/Microsoft.ManagedIdentity/userAssignedIdentities/msi_orange-builder"
    },
    resource_group = local.builder_pool,
    location       = "eastus2",
    vnet_name      = "${local.builder_pool}-vnet",
    subnet_name    = "${local.builder_pool}-resources-subnet",
    subscription   = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a",
  }
  ame_builder = {
    identity = {
      principal_id = "337eeb41-0bf2-4205-9eec-7a05b177ae37"
    },
    ip = "************",
  }

  # Infra cross-tenant sync keyvault
  infra_sync = {
    keyvault = {
      id                = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-global/providers/Microsoft.KeyVault/vaults/orange-infra-sp-sync-kv"
      subscription_name = "AIPLATFORM-ORANGE-INFRA"
      subscription_id   = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
      tenant_id         = "8b9ebe14-d942-49e7-ace9-14496d0caff0"
    }
    app_sp_client_id = "79f57bc6-dd99-4067-b1f1-f6abb241be4b" # orange-user-sp-sync
  }

  # Cluster oidc/oauth apps
  cluster_apps = {
    oidc_application_id = "9d262de2-f0d9-4d54-8f96-9c79e49280c4"
    oauth_application_id = "27917e75-d300-4398-8420-12311867214a"
  }

  # DNS Zones
  # these are all the DNS Zones uses accross the system
  private_dns_zones = {
    aoai = {
      subresource_name = "account"
      name             = "privatelink.openai.azure.com",
      short_name       = "privatelink-aoai",
    }
    blob = {
      subresource_name = "blob"
      name             = "privatelink.blob.core.windows.net",
      short_name       = "privatelink-blob",
    }
    postgresql = {
      subresource_name = "postgresqlServer"
      name             = "privatelink.postgres.database.azure.com",
      short_name       = "privatelink-postgresql",
    }
    queue = {
      subresource_name = "queue"
      name             = "privatelink.queue.core.windows.net",
      short_name       = "privatelink-queue",
    }
    vault = {
      subresource_name = "vault"
      name             = "privatelink.vaultcore.azure.net",
      short_name       = "privatelink-vault",
    }
    sites = {
      subresource_name = "sites"
      name             = "privatelink.azurewebsites.net",
      short_name       = "privatelink-sites",
    }
    redisCache = {
      subresource_name = "redisCache"
      name             = "privatelink.redis.cache.windows.net",
      short_name       = "privatelink-redisCache",
    }
    redisEnterprise = {
      subresource_name = "redisEnterprise"
      name             = "privatelink.redisenterprise.cache.azure.net",
      short_name       = "privatelink-redisenterprise",
    }
    wandb = {
      subresource_name  = "wandb-msaip-vpc-feip-private"
      name              = "wandb.io"
      short_name        = "privatelink-wandb"
      dns_name_a_record = "msaip"
      private_link_resource_id = "/subscriptions/c213eb8e-d0e7-4bbb-985a-2f8deac5c1c5/resourceGroups/wandb-msaip/providers/Microsoft.Network/applicationGateways/wandb-msaip-ag"
    }
    snowflake = {
      name       = "privatelink.snowflakecomputing.com"
      short_name = "privatelink-snowflake"
      snowflake_a_records = {
        regionless_snowsight_privatelink_url = "app-aomgsqf-wya76377"
        snowsight_privatelink_url            = "app.uk-south"
        regionless_privatelink_ocsp_url      = "ocsp.aomgsqf-wya76377"
        privatelink_account_url              = "mw60925.uk-south"
        spcs_registry_privatelink_url        = "aomgsqf-wya76377.registry"
        regionless_privatelink_account_url   = "aomgsqf-wya76377"
        spcs_auth_privatelink_url            = "sfc-endpoint-login.cwlzqe"
        privatelink_ocsp_url                 = "ocsp.mw60925.uk-south"
      }
    }
  }
}

output "builder" {
  value = local.builder
}

output "ame_builder" {
  value = local.ame_builder
}

output "infra_sync" {
  value = local.infra_sync
}

output "cluster_apps" {
  value = local.cluster_apps
}

output "private_dns_zones" {
  value = local.private_dns_zones
}

output "security_groups" {
  value = local.security_groups
}

output "security_groups_compat" {
  value = local.security_groups_compat
}
