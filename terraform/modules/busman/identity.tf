resource "azurerm_user_assigned_identity" "busman_identity" {
  name                = "busman-identity"
  resource_group_name = var.cluster_rg.name
  location            = var.cluster_rg.location
}

resource "azurerm_role_assignment" "busman_monitoring_reader" {
  scope                = var.cluster_rg.id
  role_definition_name = "Monitoring Reader"
  principal_id         = azurerm_user_assigned_identity.busman_identity.principal_id
}

resource "azurerm_role_assignment" "busman_monitoring_data_reader" {
  scope                = "/subscriptions/fbbd0f8a-b594-4fee-b381-3713acf07e7e/resourceGroups/orange-${var.prometheus_env}-azure-monitor-workspace_rg/providers/Microsoft.Monitor/accounts/orange-${var.prometheus_env}-amw"
  role_definition_name = "Monitoring Data Reader"
  principal_id         = azurerm_user_assigned_identity.busman_identity.principal_id
}

resource "azurerm_federated_identity_credential" "busman_federated_identity_credential" {
  name                = "busman-federated-identity-credential"
  resource_group_name = var.cluster_rg.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.busman_identity.id
  subject             = "system:serviceaccount:${local.namespace}:busman"
}

resource "kubernetes_service_account" "busman_service_account" {
  metadata {
    name      = "busman"
    namespace = local.namespace
    annotations = {
      "azure.workload.identity/client-id" = azurerm_user_assigned_identity.busman_identity.client_id
    }
  }
}
