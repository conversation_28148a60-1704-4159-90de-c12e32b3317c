# Create a load balancer service to expose the busman app to users.
resource "kubernetes_service" "busman_lb" {
  metadata {
    name      = "busman-lb"
    namespace = local.namespace
  }

  spec {
    type = "LoadBalancer"

    selector = {
      app = "busman-app"
    }

    port {
      name        = "app"
      port        = 8501
      protocol    = "TCP"
      target_port = 8501
    }
  }

  depends_on = [kubernetes_deployment.busman_app]
}
