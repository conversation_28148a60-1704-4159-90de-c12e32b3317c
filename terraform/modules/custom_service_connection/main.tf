locals {
  endpoint_name = var.override_endpoint_name != null ? var.override_endpoint_name : var.dns_name
}


# Request message in unsettable using null, we need the message to mark the request
# as coming from the project orange team. This makes it easier to identify and approve.
# There for we will create 2 different sections based on if the request is Auto or Manual
resource "azurerm_private_endpoint" "service_private_endpoint" {
  name                = "pe-${local.endpoint_name}"
  location            = var.virtual_network.location
  resource_group_name = var.virtual_network.resource_group_name
  subnet_id           = var.virtual_network.subnet_id

  # Manual PE connection
  dynamic "private_service_connection" {
    for_each = var.is_manual_connection ? ["manual"] : []
    content {
      name                              = "conn-${local.endpoint_name}"
      private_connection_resource_alias = var.service_alias
      private_connection_resource_id    = var.service_resource_id
      is_manual_connection              = true
      subresource_names                 = var.subresource_names
      request_message                   = "Project Orange private-endpoint connection request"
    }
  }

  # Automatic PE connection
  dynamic "private_service_connection" {
    for_each = var.is_manual_connection ? [] : ["automatic"]
    content {
      name                              = "conn-${local.endpoint_name}"
      private_connection_resource_alias = var.service_alias
      private_connection_resource_id    = var.service_resource_id
      is_manual_connection              = false
      subresource_names                 = var.subresource_names
    }
  }

  # This adds a linked dns record, if supported by the endpoint type
  private_dns_zone_group {
    name                 = "dns-${local.endpoint_name}"
    private_dns_zone_ids = [var.private_dns_zone.id]
  }
}

# add a record for the service private endpoint
resource "azurerm_private_dns_a_record" "service_record" {
  count      = var.custom_a_record ? 1 : 0
  depends_on = [azurerm_private_endpoint.service_private_endpoint]

  name                = var.dns_name
  zone_name           = var.private_dns_zone.name
  resource_group_name = var.private_dns_zone.resource_group_name
  ttl                 = 300
  records             = [azurerm_private_endpoint.service_private_endpoint.private_service_connection[0].private_ip_address]
}


# add records for the service private endpoint
resource "azurerm_private_dns_a_record" "service_records" {
  for_each   = var.custom_a_records ? toset(var.records) : toset([])
  depends_on = [azurerm_private_endpoint.service_private_endpoint]

  name                = each.key
  zone_name           = var.private_dns_zone.name
  resource_group_name = var.private_dns_zone.resource_group_name
  ttl                 = 300
  records             = [azurerm_private_endpoint.service_private_endpoint.private_service_connection[0].private_ip_address]
}

locals {
  custom_service_fqdn = try(
    azurerm_private_endpoint.service_private_endpoint.private_dns_zone_configs[0].record_sets[0].fqdn,
    try(
      azurerm_private_dns_a_record.service_record[0].fqdn,
      "${var.dns_name}.${var.private_dns_zone.name}"
  ))
}

# add cname record for the service private endpoint
resource "azurerm_private_dns_cname_record" "service_record" {
  for_each   = var.cnames
  depends_on = [azurerm_private_endpoint.service_private_endpoint]

  name                = each.key
  zone_name           = var.private_dns_zone.name
  resource_group_name = var.private_dns_zone.resource_group_name
  ttl                 = 300
  record              = local.custom_service_fqdn
}
