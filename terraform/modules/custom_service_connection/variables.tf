variable "dns_name" {
  type        = string
  description = "The name to use for the DNS zone"
}

variable "override_endpoint_name" {
  type        = string
  description = "Override endpoint resource name when it must differ from dns name"
  default     = null
}

variable "virtual_network" {
  type = object({
    resource_group_name   = string
    subnet_id             = string
    location              = string
  })
}

variable "custom_a_record" {
  type        = bool
  description = "Whether to create a custom A record for the service"
  default     = false
}

variable "custom_a_records" {
  type        = bool
  description = "Whether to create multiple A records for the service"
  default     = false

  validation {
    condition = !(var.custom_a_record && var.custom_a_records)
    error_message = "Both 'custom_a_record' and 'custom_a_records' cannot be true at the same time."
  }
}

variable "records" {
  type        = any
  description = "Custom A records to make for the service"
  default     = []
}

variable "is_manual_connection" {
  type        = bool
  description = "Whether the connection is manual or automatic"
  default     = true
}

variable "private_dns_zone" {
  type = object({
    id                  = string
    name                = string
    resource_group_name = string
  })
  description = "private dns zone to register service FQDN"
}

variable "service_alias" {
  type        = string
  description = "The alias of the service"
  default     = null
}

variable "service_resource_id" {
  type        = string
  description = "The resource ID of the service"
  default     = null

  validation {
    condition     = anytrue([
      alltrue([var.service_resource_id == null, var.service_alias != null, var.is_manual_connection]),
      alltrue([var.service_resource_id != null, var.service_alias == null]),
    ])
    error_message = "service_alias or service_resource_id must be provided"
  }
}

variable "subresource_names" {
  type        = set(string)
  description = "The name of the subresource"
  default     = []
}

variable "cnames" {
  type        = set(string)
  description = "List of cnames to add to the DNS zone"
  default     = []
}