#!/bin/bash
 
# Input arguments
BASE64_PFX="${1}"

# Decode Base64 and process the PFX in memory
STRIPPED_PFX="$(openssl pkcs12 -in <(echo "${BASE64_PFX}" | base64 -d) -nocerts -nodes -passin pass:| \
 openssl pkcs12 -export -inkey /dev/stdin -in <(openssl pkcs12 -in <(echo "${BASE64_PFX}" | base64 -d) -clcerts -nokeys -passin pass:) -passout pass: | base64 -w 0)"

echo "{\"pfx\": \"${STRIPPED_PFX}\"}"
 
