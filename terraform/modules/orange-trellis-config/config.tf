locals {
  # Configuration for trellis global resources
  environment = "prod" # Environment(dev | test | prod) for the global resources. Used as a suffix for resource names.
  
  global_resource_config = {
    tenant_id       = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # The Green tenant ID
    subscription_id = "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e" # AIPLATFORM-ORANGE-OAI-ASSETS
    region          = "uksouth" # Region for global resources
    environment     = local.environment
    resource_group  = "trellis-${local.environment}"
    keyvault        = {
      name         = "orng-trellis-${local.environment}"
      secret_names = {
        postgresql_admin_password = "postgresql-admin-password"
        redis_password            = "redis-password"
        trellis_sp_id             = "trellis-sp-id"
        trellis_sp_password       = "trellis-sp-password"
      }
    }
    postgressql     = {
      server_name   = "orng-trellis-sql-${local.environment}"
      database_name = "trellis"
      admin_login   = "trellis"
    }
    redis           = {
      server_name = "orng-trellis-redis-${local.environment}"
    }
    owners         = [
      "d99bd959-5324-4af0-9e6f-f2aea93fae48", # orange-onboarding
      "82cb6bbd-45fc-4139-9980-bf074453ab80", # msi_orange-builder
      "35c4ebca-80b6-47ab-a905-3f88c4c7175d"  # <EMAIL>
    ]
    admin_group    = {
      name = "orange-datatool-admins"
      # Member ids can be abtained by:
      #   az login --tenant green.microsoft.com
      #   az ad signed-in-user show --query id --output tsv
      members = [
        "d99bd959-5324-4af0-9e6f-f2aea93fae48", # orange-onboarding
        "82cb6bbd-45fc-4139-9980-bf074453ab80", # msi_orange-builder
        "35c4ebca-80b6-47ab-a905-3f88c4c7175d", # <EMAIL>
        "cfa84ba4-5583-4886-a093-8fff6719a2df", # <EMAIL>
        "ba28c15d-e897-4d3e-96b2-ca220bfde5a6"  # <EMAIL>
      ]
    }
    aks_namespace = "trellis-${local.environment}" # Namespace for the AKS cluster where Trellis will be deployed
  }
}