variable "subnet_name" {
  type        = string
  description = "Subnet name to use for private endpoints"
}

variable "redis_region_name" {
  type        = string
  description = "Region name to use for redis enterprise"
}

variable "override_redis_endpoint_name" {
  type        = string
  description = "Override endpoint name for redis endpoints, required to avoid `Call to Microsoft.Cache/redisEnterprise failed. Error message: Private link with name 'pe-orngharmony.uksouth' already exists.`"
  default     = null
}

variable "trellis_environment_name" {
  type        = string
  description = "Deployment environment (e.g., dev, test, prod) for Trellis"
}

variable "cluster_name" {
  type        = string
  description = "Name of the cluster"
}
