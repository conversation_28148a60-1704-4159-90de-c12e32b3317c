module "global" {
  source = "../global_settings"
}

locals {
  # Global settings for storage account private endpoints
  storage_account_endpoints = {
    orngtransfer = {
      private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngtransfer"
    }
    m365transfer = {
      private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/m365transfer"
    }
    orngcaas = {
      private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngcaas"
    }
    orngcaasscus = {
      private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngcaasscus"
    }
  }

  aoai_private_endpoints = {
    "github-research-aoai-eastus2" = {
      custom_a_record     = false
      subnet_name         = var.subnet_name
      dns_zone_name       = module.global.private_dns_zones["aoai"].name
      service_resource_id = "/subscriptions/dafd1668-60a8-412f-a27f-7b80943885bb/resourceGroups/github-research/providers/Microsoft.CognitiveServices/accounts/github-research-aoai-eastus2"
      subresource_names   = [module.global.private_dns_zones["aoai"].subresource_name]
      custom_a_record     = true
    }
  }

  # caas.azure.com
  caas_azure_endpoints = {
    dns                  = "privatelink.caas.azure.com"
    is_manual_connection = false
    custom_a_record      = true
    stamps = {
      "admin" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-global-pkg-cache_aks-caas-pkg-cache-ame_eastus2/providers/Microsoft.Network/privateLinkServices/pls-aks-caas-pkg-cache-ame"
      }
      "eastus2-07" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-eastus2-07_aks-caas-eastus2-07-ame_eastus2/providers/Microsoft.Network/privateLinkServices/pls-a5d12135554b842eda1135c9e87b21b1"
        cnames              = ["*.app.eastus2-07"]
      }
      "eastus2-08" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-eastus2-08_aks-caas-eastus2-08-ame_eastus2/providers/Microsoft.Network/privateLinkServices/pls-aks-caas-eastus2-08-ame"
        cnames              = ["*.app.eastus2-08"]
      }
      "eastus2-09" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-eastus2-09_aks-caas-eastus2-09-ame_eastus2/providers/Microsoft.Network/privateLinkServices/pls-aks-caas-eastus2-09-ame"
        cnames              = ["*.app.eastus2-09"]
      }
      "southcentralus-02" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-southcentralus-02_aks-caas-southcentralus-02-ame_southcentralus/providers/Microsoft.Network/privateLinkServices/pls-a32d595f1be974f14a07f2960116732f"
        cnames              = ["*.app.southcentralus-02"]
      }
      "southcentralus-03" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-southcentralus-03_aks-caas-southcentralus-03-ame_southcentralus/providers/Microsoft.Network/privateLinkServices/pls-aks-caas-southcentralus-03-ame"
        cnames              = ["*.app.southcentralus-03"]
      }
      "southcentralus-04" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-southcentralus-04_aks-caas-southcentralus-04-ame_southcentralus/providers/Microsoft.Network/privateLinkServices/pls-aks-caas-southcentralus-04-ame"
        cnames              = ["*.app.southcentralus-04"]
      }
      "westus2-02" = {
        service_resource_id = "/subscriptions/1e563872-7a27-4fb9-92cb-f5b815503201/resourceGroups/mc_caas-westus2-02_aks-caas-westus2-02-ame_westus2/providers/Microsoft.Network/privateLinkServices/pls-a43d85887bed34314b0e2a6ad3002e30"
        cnames              = ["*.app.westus2-02"]
      }
    }
  }

  caas_azure_service_private_endpoints = {
    for key, stamp in local.caas_azure_endpoints.stamps : key => {
      service_resource_id  = stamp.service_resource_id
      custom_a_record      = local.caas_azure_endpoints.custom_a_record
      subnet_name          = var.subnet_name
      dns_zone_name        = local.caas_azure_endpoints.dns
      srv_record           = try(stamp.srv_record, null)
      is_manual_connection = try(local.caas_azure_endpoints.is_manual_connection, null)
      cnames               = try(stamp.cnames, [])
    }
  }

  ces_service_private_endpoints = {
    "ces.eastus" = {
      custom_a_record     = true
      subnet_name         = var.subnet_name
      dns_zone_name       = module.global.private_dns_zones["sites"].name
      service_resource_id = "/subscriptions/0eec6e77-919e-4e5a-a2ef-25eea725b31f/resourceGroups/ces-app-service/providers/Microsoft.Web/sites/ces"
      subresource_names   = [module.global.private_dns_zones["sites"].subresource_name]
    }
  }

  key_vault_private_endpoints = {
    "orngcresco-vault" = {
      subnet_name         = var.subnet_name
      dns_zone_name       = module.global.private_dns_zones["vault"].name
      service_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.KeyVault/vaults/orngcresco-vault"
      subresource_names   = [module.global.private_dns_zones["vault"].subresource_name]
    }
    "orngharmony-vault" = {
      dns_zone_name       = module.global.private_dns_zones["vault"].name
      subnet_name         = var.subnet_name
      service_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.KeyVault/vaults/orngharmony-vault"
      subresource_names   = [module.global.private_dns_zones["vault"].subresource_name]
    }
    "orngkafka-vault" = {
      dns_zone_name       = module.global.private_dns_zones["vault"].name
      subnet_name         = var.subnet_name
      service_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.KeyVault/vaults/orngkafka-vault"
      subresource_names   = [module.global.private_dns_zones["vault"].subresource_name]
    }
  }

  redis_enterprise_private_endpoints = {
    # Redis Enterprise private link dns names look like
    # <cluster>.<region>.privatelink.redisenterprise.cache.azure.net
    "orngharmony.${var.redis_region_name}" = {
      subnet_name            = var.subnet_name
      dns_zone_name          = module.global.private_dns_zones["redisEnterprise"].name
      service_resource_id    = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Cache/redisEnterprise/orngharmony"
      subresource_names      = [module.global.private_dns_zones["redisEnterprise"].subresource_name]
      override_endpoint_name = var.override_redis_endpoint_name
    }
  }

  # Trellis private endpoints
  trellis_postgresql_private_endpoints = {
    "orng-trellis-sql-${var.trellis_environment_name}-${var.cluster_name}" = {
      subnet_name         = var.subnet_name
      dns_zone_name       = module.global.private_dns_zones["postgresql"].name
      service_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/trellis-${var.trellis_environment_name}/providers/Microsoft.DBforPostgreSQL/flexibleServers/orng-trellis-sql-${var.trellis_environment_name}"
      subresource_names   = [module.global.private_dns_zones["postgresql"].subresource_name]
    }
  }

  trellis_keyvault_private_endpoints = {
    "orng-trellis-vault-${var.trellis_environment_name}-${var.cluster_name}" = {
      subnet_name         = var.subnet_name
      dns_zone_name       = module.global.private_dns_zones["vault"].name
      service_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/trellis-${var.trellis_environment_name}/providers/Microsoft.KeyVault/vaults/orng-trellis-${var.trellis_environment_name}"
      subresource_names   = [module.global.private_dns_zones["vault"].subresource_name]
    }
  }


  wandb_private_endpoints = {
    "msaip" = {
      subnet_name          = var.subnet_name
      dns_zone_name        = module.global.private_dns_zones["wandb"].name
      service_resource_id  = module.global.private_dns_zones["wandb"].private_link_resource_id
      subresource_names    = [module.global.private_dns_zones["wandb"].subresource_name]
      is_manual_connection = true
      custom_a_record      = true
    }
  }
  
  snowflake_private_endpoints = {
    "snowflake-${var.cluster_name}" = {
      subnet_name          = var.subnet_name
      dns_zone_name        = module.global.private_dns_zones["snowflake"].name
      service_alias        = "sf-pvlinksvc-azuksouth.e2f9fa41-cf7e-4e13-8a45-e6f23913990f.uksouth.azure.privatelinkservice"
      is_manual_connection = true
      custom_a_records     = true
      records              = values(module.global.private_dns_zones["snowflake"].snowflake_a_records)
    }
  }
}

output "all_private_endpoints" {
  value = merge(
    local.aoai_private_endpoints,
    local.caas_azure_service_private_endpoints,
    local.ces_service_private_endpoints,
    local.key_vault_private_endpoints,
    local.redis_enterprise_private_endpoints,
    local.trellis_keyvault_private_endpoints,
    local.trellis_postgresql_private_endpoints,
    local.snowflake_private_endpoints,
    local.wandb_private_endpoints
  )
  description = "All custom service private endpoints"
}

output "storage_account_endpoints" {
  value       = local.storage_account_endpoints
  description = "Storage account service private endpoints"
}
