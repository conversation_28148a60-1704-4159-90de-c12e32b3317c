# Create the kandan deployment in Kubernetes.
resource "kubernetes_deployment" "kandan_app" {
  metadata {
    name      = "kandan-app"
    namespace = local.namespace
    labels = {
      app = "kandan-app"
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        app = "kandan-app"
      }
    }

    template {
      metadata {
        labels = {
          app                                 = "kandan-app"
          "azure.workload.identity/use"       = "true"
          "azure.workload.identity/client-id" = azurerm_user_assigned_identity.kandan_identity.client_id
        }
      }

      spec {
        automount_service_account_token = true
        service_account_name            = kubernetes_service_account.kandan_service_account.metadata[0].name

        container {
          name  = "kandan-app"
          image = "${local.image.app.repository}:${local.image.app.tag}"

          env {
            name  = "PROMETHEUS_URL"
            value = var.prometheus_url
          }

          env {
            name = "HARMONY_REDIS_CONNECTION"
            value_from {
              secret_key_ref {
                name = "kandan-harmony-redis"
                key  = "redis-connection-string"
              }
            }
          }

          port {
            container_port = 8501
          }

          readiness_probe {
            failure_threshold = 5
            http_get {
              path   = "/"
              port   = 8501
              scheme = "HTTP"
            }
            initial_delay_seconds = 5
            period_seconds        = 10
            success_threshold     = 1
            timeout_seconds       = 5
          }

          resources {
            requests = {
              cpu    = "4"
              memory = "2Gi"
            }
            limits = {
              cpu    = "8"
              memory = "8Gi"
            }
          }
        }

        affinity {
          node_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 1
              preference {
                match_expressions {
                  key      = "singularity.azure.com/processing-unit"
                  operator = "In"
                  values   = ["cpu", "system"]
                }
              }
            }
          }
        }
      }
    }
  }
}
