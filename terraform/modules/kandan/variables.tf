variable "namespace" {
  description = "The Kubernetes namespace for kandan in the AKS cluster"
  type        = string
}

variable "cluster_name" {
  description = "The name of the Azure Kubernetes Service (AKS) cluster where kandan will be deployed"
  type        = string
}

variable "cluster_rg" {
  type        = any
  description = "Data or Resource for Azure resource group for the AKS cluster"
}

variable "oidc_issuer_url" {
  description = "OIDC issuer url for federated identity credential"
  type        = string
}

variable "prometheus_env" {
  type        = string
  description = "The environment for prometheus"
}

variable "prometheus_url" {
  type        = string
  description = "The url used when reading data from prometheus"
}