# Create a load balancer service to expose the kandan app to users.
resource "kubernetes_service" "kandan_lb" {
  metadata {
    name      = "kandan-lb"
    namespace = local.namespace
  }

  spec {
    type = "LoadBalancer"

    selector = {
      app = "kandan-app"
    }

    port {
      name        = "app"
      port        = 8501
      protocol    = "TCP"
      target_port = 8501
    }
  }

  depends_on = [kubernetes_deployment.kandan_app]
}
