variable "location" {
  type        = string
  description = "Azure location for the monitoring resources"
}

variable "grafana_admins_group" {
  type        = string
  description = "Azure AD group that should have admin access to Grafana"
  default     = ""
}

variable "grafana_admins_group_id" {
  type        = string
  description = "Azure AD group object id that should have admin access to Grafana"
  default     = ""
  
}

variable "grafana_editors_groups" {
  type        = list(string)
  description = "Azure AD groups that should have editor access to Grafana"
  default     = []
}

variable "grafana_editors_group_ids" {
  type        = list(string)
  description = "Azure AD group object ids that should have editor access to <PERSON>ana"
  default     = []
}

variable "grafana_workspace_name" {
  type        = string
  description = "Grafa workspace name"
  default     = ""
}
