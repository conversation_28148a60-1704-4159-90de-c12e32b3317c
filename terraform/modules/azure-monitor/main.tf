resource "azurerm_resource_group" "rg" {
  location = var.location
  name     = "azuremonitor-${var.location}"
}

resource "azurerm_monitor_workspace" "azuremonitorworkspace" {
  name                = "azuremonitorworkspace-${var.location}"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
}

resource "azurerm_dashboard_grafana" "grafana" {
  name                = var.grafana_workspace_name != "" ? var.grafana_workspace_name : "grafana-${var.location}"
  resource_group_name = azurerm_resource_group.rg.name
  location            = var.location

  identity {
    type = "SystemAssigned"
  }

  azure_monitor_workspace_integrations {
    resource_id = azurerm_monitor_workspace.azuremonitorworkspace.id
  }
}

resource "azurerm_role_assignment" "datareaderrole" {
  scope              = azurerm_monitor_workspace.azuremonitorworkspace.id
  role_definition_id = "/subscriptions/${split("/", azurerm_monitor_workspace.azuremonitorworkspace.id)[2]}/providers/Microsoft.Authorization/roleDefinitions/b0d8363b-8ddd-447d-831f-62ca05bff136"
  principal_id       = azurerm_dashboard_grafana.grafana.identity.0.principal_id
}

data "azurerm_subscription" "current" {}

# Give Managed Grafana instances access to read monitoring data in current subscription.
resource "azurerm_role_assignment" "monitoring_reader" {
  scope                = data.azurerm_subscription.current.id
  role_definition_name = "Monitoring Reader"
  principal_id         = azurerm_dashboard_grafana.grafana.identity[0].principal_id
}

# https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/prometheus-grafana
resource "azurerm_role_assignment" "monitoring_data_reader" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "Monitoring Data Reader"
  principal_id         = azurerm_dashboard_grafana.grafana.identity[0].principal_id
}

data "azuread_groups" "admin_groups" {
  count = var.grafana_admins_group_id == "" ? 1 : 0
  display_names = [var.grafana_admins_group]
}

# Admin access
resource "azurerm_role_assignment" "grafana_admin" {
  scope                = azurerm_dashboard_grafana.grafana.id
  role_definition_name = "Grafana Admin"
  principal_id         = var.grafana_admins_group_id == "" ? data.azuread_groups.admin_groups[0].object_ids[0] : var.grafana_admins_group_id
}

# Editor access
data "azuread_groups" "editor_groups" {
  count = length(var.grafana_editors_group_ids) == 0 ? 1 : 0
  display_names = var.grafana_editors_groups
}

resource "azurerm_role_assignment" "grafana_editor" {
  for_each             = toset(var.grafana_editors_group_ids == [] ? data.azuread_groups.editor_groups[0].object_ids : var.grafana_editors_group_ids) 
  scope                = azurerm_dashboard_grafana.grafana.id
  role_definition_name = "Grafana Editor"
  principal_id         = each.value
}


resource "azurerm_log_analytics_workspace" "loganalytics" {
  name                = "loganalytics-${var.location}"
  location            = var.location
  resource_group_name = azurerm_resource_group.rg.name
}

# log analytics workspace readers
resource "azurerm_role_assignment" "log_analytics_readers" {
  for_each             = toset(var.grafana_editors_group_ids == [] ? data.azuread_groups.editor_groups[0].object_ids : var.grafana_editors_group_ids) 
  scope                = azurerm_log_analytics_workspace.loganalytics.id
  role_definition_name = "Log Analytics Reader"
  principal_id         = each.value
}
