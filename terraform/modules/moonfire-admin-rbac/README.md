# Moonfire Admin RBAC Module

This module creates Kubernetes RBAC resources to enable Moonfire team administrators to manage their team's Brix jobs within Orange clusters.

## Purpose

The module creates:
1. A cluster role (`moonfire-team-admin`) with elevated permissions for managing Brix jobs
2. Cluster role bindings that connect Azure AD admin groups to the cluster role

## Permissions Granted

Team admins receive the following permissions:
- **Brix job management**: pause, cancel, delete, update, and patch operations on `brix.openai.com/*` resources
- **Read-only access** to:
  - Pods, ConfigMaps, services, namespaces
  - Pod logs
  - Nodes (list/get only)
  - RBAC resources (view only)
  - Priority classes, ingresses, runtime classes
  - StatefulSets, endpoints, leases, PVCs
  - Pod groups, certificates

## Usage

This module is automatically instantiated for each Orange cluster in the `orange-user-cluster-onboarding` configuration. It receives the appropriate admin groups for each cluster based on hardcoded team-to-cluster mappings defined in `orange-team-onboarding`.

### Variables

- `moonfire_team_admin_groups`: Map of Azure AD admin group names to their object IDs for the target cluster

### Example

```hcl
module "moonfire-admin-rbac" {
  source = "../modules/moonfire-admin-rbac"
  
  moonfire_team_admin_groups = {
    "TEAM-MOONFIRE-M365-ADMIN"     = "12345678-1234-1234-1234-123456789012"
    "TEAM-MOONFIRE-GENAICORE-ADMIN" = "87654321-4321-4321-4321-210987654321"
  }
}
```

## Security Notes

- Admin permissions are scoped to Brix job management only
- Admins cannot modify RBAC resources themselves
- Admins have read-only access to cluster resources for troubleshooting
- Each admin group is bound only to clusters where they have been explicitly granted access
