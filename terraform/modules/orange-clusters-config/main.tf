module "global" {
  source = "../global_settings"
}

module "clusters" {
  source = "./clusters"

  global_settings = module.global
}

locals {
  cluster_names = [
    for path in fileset(path.module, "clusters/*.tf") :
    substr(basename(path), 0, length(basename(path)) - 3) # Remove the .tf extension
    if basename(path) != "variables.tf"
  ]
  clusters = { for name in local.cluster_names : name => module.clusters[name] }
}

output "clusters" {
  value = local.clusters
  description = "Map from all cluster names to their cluster configs"
}
