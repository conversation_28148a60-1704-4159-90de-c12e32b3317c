module "stage-southcentralus-2_custom_service_settings" {
  source = "../../custom_service_settings"

  subnet_name                  = "AKS"
  redis_region_name            = "uksouth"
  override_redis_endpoint_name = "stage-southcentralus-2.orngharmony.southcentralus"
  trellis_environment_name     = "prod"
  cluster_name                 = "stage-southcentralus-2"
}

locals {
  stage-southcentralus-2 = {
    cluster_subscription          = var.global_settings.subscriptions.aisc-stage-ame-dp-ipp-orange-01
    cluster_fqdn                  = "azmlstagescusazhub2.api.federation.singularity-stage.azure.net"
    cluster_aks_name              = "aks"
    cluster_identity_name         = "aks"
    cluster_name                  = "stage-southcentralus-2"
    location                      = "southcentralus"
    oidc_application_id           = var.oidc_application_id
    oidc_tenant_id                = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green
    oauth_application_id          = var.oauth_application_id
    oauth_tenant_id               = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green
    workload_auth_type            = "secrets"
    tailsnail_rate_mbit           = 2
    resource_group_name           = "azml-stage-southcentralus-azhub-2_rg"
    subnet_name                   = "AKS"
    firewall_name                 = "firewall"
    firewall_policy_name          = "firewallpolicy"
    unmanaged_pools_identity_name = "aksbootstrap"
    acr_pull_identity_name        = "aksacr"
    vnet_name                     = "aks-vnet"
    enable_kubecache              = true
    enable_blobpipe               = true

    remap_oai_artifact_storage_account = "orngoaiartifacts"
    # This could bea regional account instead, but so far this remapping has only been relevant for a few small config files.
    remap_oai_data_storage_account = "orngoaiartifacts"
    applied_storage_map            = "az://orngoaiartifacts/storage-map/storage-map.json"
    openai_encodings_base_data_gym = "az://orngoaiartifacts/data-gym/encodings"

    prometheus_env          = "stage"
    prometheus_url          = "https://orange-stage-amw-effehufxabf5g6e5.eastus.prometheus.monitor.azure.com"
    prometheus_cluster_name = "azml_stage_southcentralus_2"
    lemon_grafana_url       = "http://lemon-grafana.orange-stage-southcentralus-2.internal.genai.ms"

    lemon_adx_kusto_cluster_us = "aiscprodkusto.westus2"
    lemon_adx_kusto_cluster_eu = "aiscprodeurope.westeurope"
    lemon_adx_kusto_database   = "nexus-logs"

    acr_pull_repositories = {
      "iridiumsdc" = {
        name                = "iridiumsdc"
        resource_group_name = "common"
      }
    }

    services_private_endpoints = module.stage-southcentralus-2_custom_service_settings.all_private_endpoints

    external_model_storage_private_endpoints = merge(module.stage-southcentralus-2_custom_service_settings.storage_account_endpoints, {
      # TODO: We're trying to validate that we can avoid linking in orngcresco (which is in UKSouth) to avoid
      # accidental cross region traffic.
      # orngcresco = {
      #  private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngcresco"
      # }
      orngoaiartifacts = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngoaiartifacts"
      }
      orngstore = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngstore"
      }
      orngscuscresco = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-southcentralus-resources/providers/Microsoft.Storage/storageAccounts/orngscuscresco"
      }
      orngsnowflakescus = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-southcentralus/providers/Microsoft.Storage/storageAccounts/orngsnowflakescus"
      }
      # TODO: We should only need orngsnowflakescus, but we haven't figured out how to repoint observability stuff from orngsnowflakeuks
      # Linking in orngsnowflakeuks for now to get past snowflake related errors in initial testing.
      orngsnowflakeuks = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngsnowflakeuks"
      }
      ornginfrascus = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-southcentralus/providers/Microsoft.Storage/storageAccounts/ornginfrascus"
      }
    })

    storage_queue_private_endpoints = {}

    # Bare metal GPU nodes; topology file not required
    nccl_host_topology_path = ""

    # This cluster has Sapphire Rapids CPUs with Intel's AMX extension. We are missing 'amx' wheels like the following:
    #   cpu_optimizer_kernels-0.1.0+tree.tdfdf455250.cpu.amx.torch.241.os.noble-cp311-cp311-linux_x86_64.whl
    # (note the "amx"). Look for 'avx2' wheels instead, since we actually have those.
    # override_cpu_isalevel = "avx2"

    # Align with prod
    brix_image_tag     = "v0.32.0-2-ga8ae5dcb-dirty-cloudtest"
    enable_perhonen    = true
    perhonen_image_tag = "perhonen-v0.0.1-a8ae5dcb-dirty-cloudtest-amd64"

    # Enable Redis exporter for orngharmony monitoring
    enable_redis_exporter = true
  }
}

output "stage-southcentralus-2" {
  value       = local.stage-southcentralus-2
  description = "Cluster configuration object"
}
