module "prod-westus2-19_custom_service_settings" {
  source = "../../custom_service_settings"

  subnet_name                  = "AKS"
  redis_region_name            = "uksouth"
  override_redis_endpoint_name = "prod-westus2-19.orngharmony.westus2"
  trellis_environment_name     = "prod"
  cluster_name                 = "prod-westus2-19"
}

locals {
  prod-westus2-19 = {
    cluster_subscription          = var.global_settings.subscriptions.aisc-prod-ame-dp-ipp-orange-westus2-01
    cluster_fqdn                  = "azmlprodwus2azhub19.api.federation.singularity.azure.net"
    cluster_identity_name         = "aks"
    cluster_aks_name              = "aks"
    cluster_name                  = "prod-westus2-19"
    location                      = "westus2"
    oidc_application_id           = var.oidc_application_id
    oidc_tenant_id                = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green
    oauth_application_id          = var.oauth_application_id
    oauth_tenant_id               = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green
    workload_auth_type            = "secrets"
    tailsnail_rate_mbit           = 2
    resource_group_name           = "azml-prod-westus2-azhub-19_rg"
    subnet_name                   = "AKS"
    firewall_name                 = "firewall"
    firewall_policy_name          = "firewallpolicy"
    unmanaged_pools_identity_name = "aksbootstrap"
    acr_pull_identity_name        = "aksacr"
    vnet_name                     = "aks-vnet"
    enable_kubecache              = true
    enable_blobpipe               = true
    enable_joiner                 = false
    namespaces                    = {}

    remap_oai_artifact_storage_account = "orngoaiartifacts"
    # This could bea regional account instead, but so far this remapping has only been relevant for a few small config files.
    remap_oai_data_storage_account = "orngoaiartifacts"
    applied_storage_map            = "az://orngoaiartifacts/storage-map/storage-map.json"
    openai_encodings_base_data_gym = "az://orngoaiartifacts/data-gym/encodings"

    prometheus_env          = "prod"
    prometheus_url          = "https://orange-prod-amw-gndfg2brcjcehbef.eastus.prometheus.monitor.azure.com"
    prometheus_cluster_name = "azml_prod_westus2_azhub_19"
    lemon_grafana_url       = "http://lemon-grafana.orange-prod-westus2-19.internal.genai.ms"

    lemon_adx_kusto_cluster_us = "aiscprodkusto.westus2"
    lemon_adx_kusto_cluster_eu = "aiscprodeurope.westeurope"
    lemon_adx_kusto_database   = "nexus-logs"

    acr_pull_repositories = {
      "iridiumsdc" = {
        name                = "iridiumsdc"
        resource_group_name = "common"
      }
    }

    services_private_endpoints = module.prod-westus2-19_custom_service_settings.all_private_endpoints

    external_model_storage_private_endpoints = {
      # TODO: We're trying to validate that we can avoid linking in orngcresco (which is in UKSouth) to avoid
      # accidental cross region traffic.
      # orngcresco = {
      #  private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngcresco"
      # }
      orngoaiartifacts = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngoaiartifacts"
      }
      orngstore = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngstore"
      }
      orngwus2cresco = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-westus2-resources/providers/Microsoft.Storage/storageAccounts/orngwus2cresco"
      }
      orngsnowflakewus2 = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-westus2/providers/Microsoft.Storage/storageAccounts/orngsnowflakewus2"
      }
      # TODO: We should only need orngsnowflakewus2, but we haven't figured out how to repoint observability stuff from orngsnowflakeuks
      # Linking in orngsnowflakeuks for now to get past snowflake related errors in initial testing.
      orngsnowflakeuks = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngsnowflakeuks"
      }
      orngobsvmai = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngobsvmai"
      }
      orngtransfer = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngtransfer"
      }
      ornginfrawus2 = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-westus2/providers/Microsoft.Storage/storageAccounts/ornginfrawus2"
      }
      m365transfer = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/m365transfer"
      }
    }

    storage_queue_private_endpoints = {}

    # Azure NDv4 VMs require NCCL topology file
    nccl_host_topology_path = "/opt/microsoft/ndv4/topo.xml"

    # Brix and Perhonen (commits until March 31)
    # https://dev.azure.com/project-argos/Mimco/_git/brix/commit/a8ae5dcb584624be32a4d55dd95a7cc2294ada1c
    brix_image_tag     = "v0.16.4-a8ae5dcb-dirty-cloudtest-amd64"
    perhonen_image_tag = "perhonen-v0.0.1-a8ae5dcb-dirty-cloudtest-amd64"
    enable_perhonen    = true

    # This cluster has Sapphire Rapids CPUs with Intel's AMX extension. We are missing 'amx' wheels like the following:
    #   cpu_optimizer_kernels-0.1.0+tree.tdfdf455250.cpu.amx.torch.241.os.noble-cp311-cp311-linux_x86_64.whl
    # (note the "amx"). Look for 'avx2' wheels instead, since we actually have those.
    override_cpu_isalevel = "avx2"
  }
}

output "prod-westus2-19" {
  value       = local.prod-westus2-19
  description = "Cluster configuration object"
}
