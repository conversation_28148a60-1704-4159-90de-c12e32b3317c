module "prod-uksouth-8_custom_service_settings" {
  source = "../../custom_service_settings"

  subnet_name                  = "AKS"
  redis_region_name            = "uksouth"
  override_redis_endpoint_name = "prod-uksouth-8.orngharmony.uksouth"
  trellis_environment_name     = "prod"
  cluster_name                 = "prod-uksouth-8"
}

locals {
  prod-uksouth-8 = {
    cluster_subscription          = var.global_settings.subscriptions.aisc-prod-ame-dp-ipp-orange-uksouth-01
    cluster_fqdn                  = "azmlproduksazhub8.api.federation.singularity.azure.net"
    cluster_aks_name              = "aks"
    cluster_identity_name         = "aks"
    cluster_name                  = "prod-uksouth-8"
    location                      = "uksouth"
    oidc_application_id           = var.oidc_application_id
    oidc_tenant_id                = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green
    oauth_application_id          = var.oauth_application_id
    oauth_tenant_id               = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green
    workload_auth_type            = "secrets"
    tailsnail_rate_mbit           = 2
    resource_group_name           = "azml-prod-uksouth-azhub-8_rg"
    subnet_name                   = "AKS"
    firewall_name                 = "firewall"
    firewall_policy_name          = "firewallpolicy"
    unmanaged_pools_identity_name = "aksbootstrap"
    acr_pull_identity_name        = "aksacr"
    vnet_name                     = "aks-vnet"
    enable_kubecache              = true
    enable_blobpipe               = true

    remap_oai_artifact_storage_account = "orngoaiartifacts"
    # New clusters are using orngoaiartifacts here instead; keeping orngcresco to avoid breakage risk
    remap_oai_data_storage_account = "orngcresco"
    applied_storage_map            = "az://orngoaiartifacts/storage-map/storage-map.json"
    openai_encodings_base_data_gym = "az://orngoaiartifacts/data-gym/encodings"

    prometheus_env          = "prod"
    prometheus_url          = "https://orange-prod-amw-gndfg2brcjcehbef.eastus.prometheus.monitor.azure.com"
    prometheus_cluster_name = "azml_prod_uksouth_azhub_8"
    lemon_grafana_url       = "http://lemon-grafana.orange-7.internal.genai.ms"

    lemon_adx_kusto_cluster_us = "aiscprodkusto.westus2"
    lemon_adx_kusto_cluster_eu = "aiscprodeurope.westeurope"
    lemon_adx_kusto_database   = "nexus-logs"

    acr_pull_repositories = {
      "iridiumsdc" = {
        name                = "iridiumsdc"
        resource_group_name = "common"
      }
    }

    services_private_endpoints = module.prod-uksouth-8_custom_service_settings.all_private_endpoints

    external_model_storage_private_endpoints = merge(module.prod-uksouth-8_custom_service_settings.storage_account_endpoints, {
      orngcresco = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngcresco"
      }
      orngoaiartifacts = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngoaiartifacts"
      }
      orngstore = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngstore"
      }
      orngukscresco = {
        private_connection_resource_id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-uksouth-resources/providers/Microsoft.Storage/storageAccounts/orngukscresco"
      }
      orngsnowflakeuks = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngsnowflakeuks"
      }
      orngobsvmai = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngobsvmai"
      }
      ornginfrauks = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-uksouth/providers/Microsoft.Storage/storageAccounts/ornginfrauks"
      }
      # Adding PE for storage account used by Joiner apps
      orngeventsdevuks = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngeventsdevuks"
      }
    })

    storage_queue_private_endpoints = {
      # Adding PE for queue in storage account used by Joiner apps
      orngeventsdevuks-queue = {
        private_connection_resource_id = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-observability-uksouth/providers/Microsoft.Storage/storageAccounts/orngeventsdevuks"
      }
    }

    nccl_host_topology_path = "/opt/microsoft/ndv4/topo.xml"

    # Brix and Perhonen (commits until March 31)
    # https://dev.azure.com/project-argos/Mimco/_git/brix/commit/a8ae5dcb584624be32a4d55dd95a7cc2294ada1c
    brix_image_tag     = "v0.16.4-a8ae5dcb-dirty-cloudtest-amd64"
    perhonen_image_tag = "perhonen-v0.0.1-a8ae5dcb-dirty-cloudtest-amd64"
    enable_perhonen    = true
  }
}

output "prod-uksouth-8" {
  value       = local.prod-uksouth-8
  description = "Cluster configuration object"
}
