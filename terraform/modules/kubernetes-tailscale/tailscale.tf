locals {
  proxy_class_name   = "subnet-routers"
  operator_name      = "orange-tailscale-operator-${var.cluster_name}"
  subnet_router_name = "orange-subnets-${var.cluster_name}"
}

data "azurerm_key_vault_secret" "tailscale-client-id" {
  name         = var.keyvault_client_id_name
  key_vault_id = var.tailscale_keyvault_id
}

data "azurerm_key_vault_secret" "tailscale-client-secret" {
  name         = var.keyvault_client_secret_name
  key_vault_id = var.tailscale_keyvault_id
}

resource "helm_release" "tailscale-operator" {
  name       = "tailscale-operator"
  repository = "https://pkgs.tailscale.com/helmcharts"
  chart      = "tailscale-operator"
  namespace  = var.namespace_name
  version    = "1.80.3"

  set {
    name  = "operatorConfig.image.repository"
    value = "nexusstaticacr.azurecr.io/tailscale/k8s-operator"
  }
  set {
    name  = "proxyConfig.image.repository"
    value = "nexusstaticacr.azurecr.io/tailscale/tailscale"
  }
  set {
    name  = "oauth.clientId"
    value = data.azurerm_key_vault_secret.tailscale-client-id.value
  }
  set_sensitive {
    name  = "oauth.clientSecret"
    value = data.azurerm_key_vault_secret.tailscale-client-secret.value
  }
  set {
    name  = "operatorConfig.defaultTags[0]"
    value = "tag:${var.tag_tailnet_operator}"
  }
  set {
    name  = "operatorConfig.hostname"
    value = local.operator_name
  }
  set {
    name  = "proxyConfig.defaultTags"
    value = "tag:${var.tag_tailnet_subnet}"
  }
  values = [yamlencode({
    operatorConfig = {
      nodeSelector = {
        "singularity.azure.com/processing-unit" = "system" # TODO generalize selector of oidc-proxy for all infra pods
      }
    }
  })]
}

resource "kubectl_manifest" "tailscale-proxy-class" { # TODO generalize selector of oidc-proxy for all infra pods
  depends_on = [helm_release.tailscale-operator]

  yaml_body = <<YAML
apiVersion: tailscale.com/v1alpha1
kind: ProxyClass
metadata:
  name: "${local.proxy_class_name}"
spec:
  statefulSet:
    pod:
      nodeSelector:
        singularity.azure.com/processing-unit: "system"
      labels:
        tailscale.orange.internal/subnet-router: "true"
      %{if length(var.subnet_router_annotations) > 0}
      annotations:
      %{for k, v in var.subnet_router_annotations}
        ${k}: "${v}"
      %{endfor}
      %{endif}
YAML
}

resource "kubectl_manifest" "tailscale-route-connectors" {
  depends_on = [
    helm_release.tailscale-operator,
    kubectl_manifest.tailscale-proxy-class,
  ]

  yaml_body = <<YAML
apiVersion: tailscale.com/v1alpha1
kind: Connector
metadata:
  name: "${local.subnet_router_name}"
spec:
  hostname: "${local.subnet_router_name}"
  proxyClass: "${local.proxy_class_name}"
  subnetRouter:
    advertiseRoutes:
    ${indent(4, yamlencode(values(var.advertise_routes)))}
YAML
}
