variable "cluster_name" {
  description = "Cluster friendly name"
  type        = string
}

variable "tailscale_keyvault_id" {
  description = "Resource ID of keyvault containing Tailscale credentials"
  type        = string
}

variable "keyvault_client_id_name" {
  description = "Name of the keyvault secret containing Tailscale client id"
  type        = string
}

variable "keyvault_client_secret_name" {
  description = "Name of the keyvault secret containing Tailscale client secret"
  type        = string
}

variable "tag_tailnet_operator" {
  description = "Tag for the tailnet operator"
  type        = string
}

variable "tag_tailnet_subnet" {
  description = "Tag for proxies and routers created by tailnet operator. `tag_tailnet_operator` must be an owner"
  type        = string
}

variable "advertise_routes" {
  description = "Map of subnet routes in cluster vnet to advertise to Tailscale"
  type        = map(string)
}

variable "subnet_router_annotations" {
  description = "Annotations to add to subnet router pods"
  type        = map(string)
}

variable "namespace_name" {
  description = "Namespace to run tailscale operator in"
  type        = string
}
