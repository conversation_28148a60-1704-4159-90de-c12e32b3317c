#  These are third-party SPs used by Snowflake
variable "snowflake_storage_sp" {
  type        = string
  description = "The object ID of the storage integration service principal to assign roles to"
  default     = "998f8f49-f898-4875-99d3-3fb71b7d8a3f"
}

variable "snowflake_notification_sp" {
  type        = string
  description = "The object ID of the notifcation integration service principal to assign roles to"
  default     = "0ca1bd69-81be-4e5c-8e18-49d470915a4b"
}

variable "team_name" {
  description = "Team name wihtout any prefix/suffix (lower-case string)"
  type        = string
}
