module "global" {
  source = "../global_settings"
}

data "azuread_group" "groups" {
  provider = azuread.onboarding-sp
  for_each = module.global.security_groups_compat

  display_name = each.value
}

module "team_storage" {
  source    = "../storage-base"
  providers = {
    azurerm.infra = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  name                              = "orngobsv${var.team_name}"
  resource_group_name               = "orange-observability-uksouth"
  location                          = "uksouth"
  # storage account needs public access so snowflake can scrape them
  public_network_access_enabled     = true
  cross_tenant_replication_enabled  = false

  # Permissions and access control
  builder_access         = true
  builder_access_service = "queue"
  builder_role_assignments = [
    "Storage Blob Data Contributor",
    "Storage Queue Data Contributor",
    "EventGrid EventSubscription Contributor"
  ]
  group_role_assignments = {
    "Storage Blob Data Contributor" = [
      # TODO: maybe should instead have equivalent per-team accounts with user+SP
      data.azuread_group.groups["cresco"].object_id,
    ]
  }

  # Containers to create in the storage account
  containers = {
    strawberry = {description = "container for strawberry"}
  }

  tags = {
    "Purpose"         = "Observability"
    "oai-sensitivity" = "critical"
  }
}

# Queue to listen to events of Blob Storage
resource "azurerm_storage_queue" "team_queue" {
  depends_on = [module.team_storage]

  name                 = "strawberry-queue"
  storage_account_name = module.team_storage.storage_account.name
}

# Subscribes to events from the storage account and sends them to the queue
resource "azurerm_eventgrid_event_subscription" "team_event_subscription" {
  depends_on = [module.team_storage, azurerm_storage_queue.team_queue]

  name  = "orngsnow-${var.team_name}-event-sub"
  scope = module.team_storage.storage_account.id

  storage_queue_endpoint {
    storage_account_id = module.team_storage.storage_account.id
    queue_name         = azurerm_storage_queue.team_queue.name
  }

  # Only listens for blob created events
  included_event_types = [
    "Microsoft.Storage.BlobCreated"
  ]
}

# Assign "Storage Blob Data Reader" role to the storage integration service principal for the storage account
resource "azurerm_role_assignment" "blob_data_reader" {
  depends_on = [module.team_storage]

  principal_id          = var.snowflake_storage_sp
  role_definition_name  = "Storage Blob Data Reader"
  scope                 = module.team_storage.storage_account.id
}

# Assign "Storage Queue Data Contributor" role to the notification integration service principal for the queue
resource "azurerm_role_assignment" "queue_data_contributor" {
  depends_on = [module.team_storage, azurerm_storage_queue.team_queue]

  principal_id          = var.snowflake_notification_sp
  role_definition_name  = "Storage Queue Data Contributor"
  scope                 = module.team_storage.storage_account.id
}
