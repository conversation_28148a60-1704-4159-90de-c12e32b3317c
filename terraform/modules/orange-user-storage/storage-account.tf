locals {  # Storage Account Naming Logic:
  # 1. Azure Requirements:
  #    - Length: 3-24 characters
  #    - Only lowercase letters and numbers allowed
  #
  # 2. Handling Service/Robot Accounts:
  #    - Removes special characters (like hyphens, underscores, etc.)
  #    - Converts to lowercase
  #    - Truncates to fit length limit with prefix
  #
  # Note: Name conflicts may occur if multiple accounts share similar prefixes
  # after cleaning and truncation. Pipeline will fail in such cases.
  
  # Remove common special characters that are invalid in storage account names
  step1_cleaned = replace(var.user_alias, "-", "")
  step2_cleaned = replace(local.step1_cleaned, "_", "")
  step3_cleaned = replace(local.step2_cleaned, ".", "")
  cleaned_alias = lower(local.step3_cleaned)
  
  max_alias_len = 24 - length(var.storage_account_prefix)
  truncated_alias = substr(local.cleaned_alias, 0, local.max_alias_len)
  final_storage_account_name = "${var.storage_account_prefix}${local.truncated_alias}"
}

resource "azurerm_storage_account" "user_storage" {
  name                     = local.final_storage_account_name
  resource_group_name      = var.storage_resource_group
  location                 = var.storage_region
  account_tier             = "Standard"
  account_replication_type = "LRS"

  cross_tenant_replication_enabled = false
  allow_nested_items_to_be_public  = false
  shared_access_key_enabled        = false
  public_network_access_enabled    = false
}

module "user_storage_secmon_audit" {
  source = "../aoai-secmon-audit"
  target_resource_id = "${azurerm_storage_account.user_storage.id}/blobServices/default"
  providers = {
    azurerm = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
  }
}

resource "azurerm_role_assignment" "group_blob_contributor" {
  principal_id                     = var.group_object_id
  skip_service_principal_aad_check = true
  role_definition_name             = "Storage Blob Data Contributor"
  scope                            = azurerm_storage_account.user_storage.id
  principal_type                   = "Group"
}

# Assign the "Reader" role to the group for the storage account
# So the user can check the storage account existence using az cli / portal
resource "azurerm_role_assignment" "user_storage_account_reader" {
  principal_id                     = var.group_object_id
  skip_service_principal_aad_check = true
  role_definition_name             = "Reader"
  scope                            = azurerm_storage_account.user_storage.id
  principal_type                   = "Group"
}