resource "azurerm_resource_group" "wandb_resources" {
  name     = var.resource_group_name
  location = var.location
}

resource "azurerm_storage_account" "default" {
  name                     = var.name
  resource_group_name      = azurerm_resource_group.wandb_resources.name
  location                 = azurerm_resource_group.wandb_resources.location
  account_tier             = "Standard"
  account_replication_type = "ZRS"
  min_tls_version          = "TLS1_2"

  blob_properties {
    cors_rule {
      allowed_headers    = ["*"]
      allowed_methods    = ["GET", "HEAD", "PUT"]
      allowed_origins    = ["*"]
      exposed_headers    = ["*"]
      max_age_in_seconds = 3600
    }
  }

  queue_properties {
    logging {
      delete                = true
      read                  = true
      write                 = true
      version               = "1.0"
      retention_policy_days = 10
    }
  }

  tags = var.tags
}

resource "azurerm_storage_container" "default" {
  name                  = var.blob_container_name
  storage_account_name  = azurerm_storage_account.default.name
  container_access_type = "private"
}

data "azapi_resource" "storage_blob" {
  type        = "Microsoft.Storage/storageAccounts/blobServices@2021-04-01"
  resource_id = "${azurerm_storage_account.default.id}/blobServices/default"
}

module "model_storage_audit" {
  source             = "../../modules/audit"
  target_resource_id = data.azapi_resource.storage_blob.id
  location           = var.location
}
