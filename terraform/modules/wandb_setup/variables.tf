variable "name" {
  type        = string
  description = "The name of the storage account to be created."
}

variable "resource_group_name" {
  type        = string
  description = "The name of the resource group in which to create the database."
}

variable "location" {
  type        = string
  description = "Specifies the supported Azure location where the resource exists."
}

variable "tags" {
  default     = {}
  type        = map(string)
  description = "Map of tags for resource"
}

variable "blob_container_name" {
  description = "Name of azure storage account container for storing blobs"
  type        = string
  default     = "wandb"
}
