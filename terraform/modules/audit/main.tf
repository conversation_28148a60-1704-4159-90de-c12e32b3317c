data "azurerm_log_analytics_workspace" "loganalytics" {
  name                = "loganalytics-${var.location}"
  resource_group_name = "azuremonitor-${var.location}"
}

resource "azurerm_monitor_diagnostic_setting" "resource_audit" {
  name                       = "resource_audit"
  target_resource_id         = var.target_resource_id
  log_analytics_workspace_id = data.azurerm_log_analytics_workspace.loganalytics.id

  enabled_log {
    category_group = "audit"
  }

  lifecycle {
    ignore_changes = [
      metric
    ]
  }
}
