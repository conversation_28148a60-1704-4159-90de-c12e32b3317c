resource "kubernetes_namespace" "devbox_redirect" {
  metadata {
    name = "devbox"
  }
}

resource "kubernetes_config_map" "devbox_redirect_nginx_conf" {
  metadata {
    name      = "devbox-redirect-nginx-conf"
    namespace = kubernetes_namespace.devbox_redirect.metadata[0].name
  }
  data = {
    "nginx.conf" = <<-EOT
        pid /tmp/nginx.pid;
        worker_processes  1;
        events { worker_connections  1024; }
        http {
          server {
            listen 8080;
            location / {
              access_by_lua_block {
                local email = ngx.req.get_headers()["X-Auth-Request-Email"]
                local host = ngx.var.host  -- the current Host header (e.g. devbox.int.stage-...)
            
                if email and host then
                  local user = email:match("^([^@]+)")
                  if user then
                    local new_host = host:gsub("^devbox%.", "devbox-" .. user .. ".")
                    local target = "https://" .. new_host .. ngx.var.request_uri
                    return ngx.redirect(target, 302)
                  end
                end

                return ngx.exit(ngx.HTTP_FORBIDDEN)
              }
            }
          }
        }    
    EOT
  }
}

resource "kubernetes_deployment" "devbox_redirect" {
  metadata {
    name      = "devbox-redirect"
    namespace = kubernetes_namespace.devbox_redirect.metadata[0].name
    annotations = {
      "nginx-conf-hash" = sha1(kubernetes_config_map.devbox_redirect_nginx_conf.data["nginx.conf"])
    }
    labels = {
      app = "devbox-redirect"
    }
  }
  spec {
    replicas = 1
    selector {
      match_labels = {
        app = "devbox-redirect"
      }
    }
    template {
      metadata {
        labels = {
          app = "devbox-redirect"
        }
      }
      spec {
        container {
          name    = "devbox-redirect"
          image   = "mcr.microsoft.com/oss/kubernetes/ingress/nginx-ingress-controller:v1.11.0"
          command = ["nginx", "-g", "daemon off;"]
          volume_mount {
            name       = "nginx-conf"
            mount_path = "/etc/nginx/nginx.conf"
            sub_path   = "nginx.conf"
          }
        }
        volume {
          name = "nginx-conf"
          config_map {
            name = kubernetes_config_map.devbox_redirect_nginx_conf.metadata[0].name
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "devbox_redirect" {
  metadata {
    name      = "devbox-redirect"
    namespace = kubernetes_namespace.devbox_redirect.metadata[0].name
  }
  spec {
    selector = {
      app = "devbox-redirect"
    }
    port {
      protocol    = "TCP"
      port        = 8080
      target_port = 8080
    }
  }
}

resource "kubernetes_ingress_v1" "devbox_redirect" {
  metadata {
    name      = "devbox-redirect"
    namespace = kubernetes_namespace.devbox_redirect.metadata[0].name
    annotations = {
      "nginx.ingress.kubernetes.io/auth-cache-key"        = "$oauth_cookie_cache_key$http_authorization"
      "nginx.ingress.kubernetes.io/auth-response-headers" = "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
      "nginx.ingress.kubernetes.io/auth-url"              = "https://oai-azure-auth-proxy.int.${var.config.cluster_name}.dev.openai.org/oauth2/auth"
      "nginx.ingress.kubernetes.io/auth-signin"           = "https://oai-azure-auth-proxy.int.${var.config.cluster_name}.dev.openai.org/oauth2/start"
    }
  }
  spec {
    ingress_class_name = "internal-nginx"
    rule {
      host = "devbox.int.${var.config.cluster_name}.dev.openai.org"
      http {
        path {
          path      = "/"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service.devbox_redirect.metadata[0].name
              port {
                number = 8080
              }
            }
          }
        }
      }
    }
    tls {
      hosts = ["devbox.int.${var.config.cluster_name}.dev.openai.org"]
    }
  }
}
