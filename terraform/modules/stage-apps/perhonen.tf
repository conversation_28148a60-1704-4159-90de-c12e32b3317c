module "perhonen" {
  count = var.config.enable_perhonen ? 1 : 0
  
  source = "../perhonen"

  perhonen_version = var.config.perhonen_image_tag
  env              = var.config.perhonen_env
  cpu              = var.config.perhonen_cpu
  memory           = var.config.perhonen_memory
  replicas         = var.config.perhonen_replicas
  cluster          = var.config.cluster_name
  acr              = var.config.perhonen_acr
  namespace        = kubernetes_namespace.system.metadata[0].name
  enable_eviction  = var.config.perhonen_enable_eviction
  use_unallocated  = var.config.perhonen_use_unallocated
  dry_run          = var.config.perhonen_dry_run
}
