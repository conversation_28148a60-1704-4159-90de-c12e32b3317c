# Purpose: Create a cluster role binding for the builder identity to have cluster-admin access to the cluster.
# Note that the builder identity is in Green tenant, so we can't do this assignment as Azure RBAC (AME cluster).
# This access comes through the 'oidc-oid' proxy which puts an AAD OID as the k8s username.
resource "kubernetes_cluster_role_binding" "builder_cluster_admin_binding" {
  metadata {
    name = "orange-builder-cluster-admin-binding"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "cluster-admin"
  }

  subject {
    kind      = "User"
    name      = module.global_settings.builder.identity.principal_id
    api_group = "rbac.authorization.k8s.io"
  }
}
