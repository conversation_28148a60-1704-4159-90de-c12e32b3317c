# This is the oidc proxy at e.g. oidc.orange-7.internal.genai.ms
# It uses 'upn' (e.g. <user>@green.microsoft.com) as the username claim, and so it
# only works for human users. Role assignments for users are in terms of UPN,
# so human users should generally use this one.

module "oidc-openaidev" {
  depends_on = [azurerm_subnet.internal-nginx-subnet]
  source     = "../oidc-proxy"

  cluster_name               = var.config.cluster_name
  cluster_aks                = var.config.cluster_aks
  cluster_resource_group     = var.config.cluster_rg.name
  cluster_region             = var.config.location
  tenant_id                  = var.config.oidc_tenant_id
  cluster_identity_object_id = var.config.cluster_identity.principal_id
  cluster_identity_client_id = var.config.cluster_identity.client_id

  oidc_use_cluster_ip = false
  # Note the keyvault name is unique per instance
  oidc_tlskeyvault_name    = local.oidc_openaidev_tlskeyvault_name
  oidc_proxy_node_selector = var.config.oidc_proxy_node_selector
  oidc_application_id      = var.config.oidc_application_id

  oidc_proxy_namespace = "kube-oidc-proxy-openaidev"
  oidc_proxy_name      = "kube-oidc-proxy-openaidev"
  # (existing certs are in kubeconfigs)
  oidc_proxy_dns_names = ["${var.config.cluster_name}.k8s.dev.openai.org"]
  oidc_username_claim  = "upn"
}

resource "azurerm_private_dns_a_record" "k8s-openaidev-a-record-per-sub" {
  depends_on          = [azurerm_private_dns_zone.openai_org_dns_zone]
  provider            = azurerm
  name                = "${var.config.cluster_name}.k8s.dev"
  zone_name           = "openai.org"
  resource_group_name = local.external_dns_sub_config.resource_group_name
  ttl                 = 300
  records             = [module.oidc-openaidev.kube-oidc-proxy-ip-address]
}

# This is the oidc proxy at e.g. oidc-oid.orange-7.orange.internal
# It uses 'oid' (AAD object ID) as the username claim, and so it
# works for robot accounts (MSI, SP, etc.) in addition to human users.
module "oidc-oid" {
  depends_on = [azurerm_subnet.internal-nginx-subnet]
  source     = "../oidc-proxy"

  cluster_name               = var.config.cluster_name
  cluster_aks                = var.config.cluster_aks
  cluster_resource_group     = var.config.cluster_rg.name
  cluster_region             = var.config.location
  tenant_id                  = var.config.oidc_tenant_id
  cluster_identity_object_id = var.config.cluster_identity.principal_id
  cluster_identity_client_id = var.config.cluster_identity.client_id

  oidc_use_cluster_ip = var.config.oidc_use_cluster_ip
  # Note the keyvault name is unique per instance
  oidc_tlskeyvault_name    = local.oidc_oid_tlskeyvault_name
  oidc_proxy_node_selector = var.config.oidc_proxy_node_selector
  oidc_application_id      = var.config.oidc_application_id

  oidc_proxy_namespace = "kube-oidc-proxy-oid"
  oidc_proxy_name      = "kube-oidc-proxy-oid"
  oidc_proxy_dns_names = ["oidc-oid.${var.config.cluster_name}.orange.internal"]
  oidc_username_claim  = "oid"
}
