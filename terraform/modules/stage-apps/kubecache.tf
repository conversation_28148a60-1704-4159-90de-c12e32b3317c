data "azurerm_client_config" "green" {
  provider = azurerm.infra-secret-reader
}


module "azure-naming" {
  source = "../azure-naming"
}

module "kubecache" {
  count      = var.config.enable_kubecache ? 1 : 0
  source     = "../kubecache"
  namespace  = kubernetes_namespace.scaling.metadata[0].name

  providers = {
    azurerm = azurerm.infra-secret-reader
    kubectl = kubectl
    helm    = helm
  }

  tenant_id    = data.azurerm_client_config.green.tenant_id # Green Tenant
  cluster_name = var.config.cluster_name

  # Explicitly provide the secret names to avoid confusion
  client_id_name     = "scaling-infra-clientid"
  client_secret_name = "scaling-infra-secret"

  storage_account_name = "ornginfra${module.azure-naming.regions[var.config.location].short_name}"

  infra_sync_keyvault = {
    id                = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-global/providers/Microsoft.KeyVault/vaults/orange-infra-sp-sync-kv"
    subscription_name = "AIPLATFORM-ORANGE-INFRA"
    subscription_id   = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
    tenant_id         = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green Tenant
  }

  infra_app_sync_sp_client_id = "79f57bc6-dd99-4067-b1f1-f6abb241be4b" # orange-user-sp-sync
}

