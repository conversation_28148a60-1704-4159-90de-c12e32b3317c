# Terraform version or providers required for the module

terraform {
  required_providers {
    azurerm = {
      version               = "4.19.0"
      configuration_aliases = [azurerm.common, azurerm.ame-infra, azurerm.infra-secret-reader]
    }
    kubernetes = {
      version = "2.31.0"
    }
    helm = {
      version = ">= 2.12.0, < 3.0.0"
    }
    kubectl = {
      source  = "alekc/kubectl"
      version = ">= 2.0.4"
    }
    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
  }
}
