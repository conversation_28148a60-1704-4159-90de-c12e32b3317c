module "global_settings" {
  source = "../global_settings"
}

data "azurerm_client_config" "ame" {}

resource "azurerm_key_vault" "cluster_kv" {
  name                      = local.cluster_keyvault_name
  location                  = var.config.cluster_rg.location
  resource_group_name       = var.config.cluster_rg.name
  tenant_id                 = data.azurerm_client_config.ame.tenant_id
  sku_name                  = "standard"
  enable_rbac_authorization = true
}

resource "azurerm_role_assignment" "cluster_identity" {
  depends_on = [azurerm_key_vault.cluster_kv]

  scope                = azurerm_key_vault.cluster_kv.id
  role_definition_name = "Key Vault Certificate User"
  principal_id         = var.config.cluster_identity.principal_id
}

resource "azurerm_role_assignment" "aks_sec_identity" {
  depends_on = [azurerm_key_vault.cluster_kv]

  scope                = azurerm_key_vault.cluster_kv.id
  role_definition_name = "Key Vault Certificate User"
  principal_id         = var.config.cluster_aks.key_vault_secrets_provider.0.secret_identity.0.object_id
}

data "azapi_resource" "aks" {
  type                   = "Microsoft.ContainerService/managedClusters@2025-02-01"
  name                   = var.config.cluster_aks.name
  parent_id              = var.config.cluster_rg.id
  response_export_values = ["*"]
}

resource "azurerm_role_assignment" "aks_web_identity" {
  depends_on = [azurerm_key_vault.cluster_kv]

  scope                = azurerm_key_vault.cluster_kv.id
  role_definition_name = "Key Vault Certificate User"
  principal_id         = data.azapi_resource.aks.output.properties.ingressProfile.webAppRouting.identity.objectId
}

resource "azurerm_role_assignment" "cluster_kv_builder" {
  depends_on = [azurerm_key_vault.cluster_kv]

  scope                = azurerm_key_vault.cluster_kv.id
  role_definition_name = "Key Vault Certificates Officer"
  principal_id         = module.global_settings.ame_builder.identity.principal_id
}

resource "azurerm_key_vault_certificate_issuer" "ca" {
  depends_on = [
    azurerm_role_assignment.cluster_kv_builder,
  ]
  name          = "MSIT"
  key_vault_id  = azurerm_key_vault.cluster_kv.id
  provider_name = "OneCertV2-PublicCA"
}
