resource "kubernetes_storage_class_v1" "managed-premium-nocache" {
  metadata {
    name = "managed-premium-nocache"
  }

  storage_provisioner = "kubernetes.io/azure-disk"

  parameters = {
    storageaccounttype = "Premium_LRS"
    kind               = "Managed"
    cachingmode        = "None"
  }

  allow_volume_expansion = true
  reclaim_policy         = "Retain"
  volume_binding_mode    = "WaitForFirstConsumer"
}

resource "kubernetes_storage_class_v1" "managed-premium" {
  metadata {
    name = "managed-premium"
  }

  storage_provisioner = "disk.csi.azure.com"

  parameters = {
    skuName = "Premium_LRS"
  }

  reclaim_policy      = "Delete"
  volume_binding_mode = "WaitForFirstConsumer"
}
