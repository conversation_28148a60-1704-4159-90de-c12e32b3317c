resource "random_password" "redis_password" {
  length = 8
}

locals {
  affinity = {
    "nodeAffinity" = {
      "requiredDuringSchedulingIgnoredDuringExecution" = {
        "nodeSelectorTerms" = [
          {
            "matchExpressions" = [
              {
                "key"      = "singularity.azure.com/processing-unit"
                "operator" = "In"
                "values"   = ["system"]
              }
            ]
          }
        ]
      }
    }
  }
  yamlparam = yamlencode({
    "redis" = {
      "enabled" = true
      "master" = {
        "affinity" = local.affinity
        "persistence" = {
          "enabled" = false
        }
      }
      "replica" : {
        "replicaCount" = 0
      }
      "global" = {
        "redis" = {
          "password" = random_password.redis_password.result
        }
      }
    }
    "sessionStorage" = {
      "type" = "redis"
    }
    "config" = {
      "clientID"   = var.config.oauth_application_id
      "configFile" = <<-TOML
          email_domains = [ "*" ]
          provider = "entra-id"
          oidc_issuer_url = "https://login.microsoftonline.com/${var.config.oauth_tenant_id}/v2.0"
          client_id  = "${var.config.oauth_application_id}"
          scope = "openid profile"
          entra_id_federated_token_auth = true
          # session_cookie_minimal = true
          set_xauthrequest = true
          set_authorization_header = true
          pass_user_headers = true
          skip_jwt_bearer_tokens = true
          cookie_expire = 0          
          cookie_domains = [ ".${var.config.cluster_name}.dev.openai.org" ]
          whitelist_domains = [ "*.${var.config.cluster_name}.dev.openai.org" ]
        TOML
    }
    "affinity" = local.affinity
    "serviceAccount" = {
      "annotations" = {
        "azure.workload.identity/client-id" = var.config.oauth_application_id
      }
    }
    "podLabels" = {
      "azure.workload.identity/use" = "true"
    }
    "ingress" = {
      "annotations" = {
        "kubernetes.azure.com/tls-cert-keyvault-uri"              = azurerm_key_vault_certificate.oauth-tls.versionless_id
        "nginx.ingress.kubernetes.io/proxy-buffering"             = "on"
        "nginx.ingress.kubernetes.io/proxy-buffer-size"           = "128k"
        "nginx.ingress.kubernetes.io/proxy-buffers-number"        = "4"
        "nginx.ingress.kubernetes.io/large-client-header-buffers" = "4 64k"
      }
      "enabled"  = true
      "pathType" = "Prefix"
      "path"     = "/"
      "hosts" = [
        "oai-azure-auth-proxy.int.${var.config.cluster_name}.dev.openai.org",
        "oauth2-proxy-core-group.aad-auth.int.${var.config.cluster_name}.dev.openai.org"
      ]
      "tls" = [
        {
          "hosts" = [
            "oai-azure-auth-proxy.int.${var.config.cluster_name}.dev.openai.org",
            "oauth2-proxy-core-group.aad-auth.int.${var.config.cluster_name}.dev.openai.org"
          ]
          "secretName" = "keyvault-oauth2-proxy"
        }
      ]
      "supportsIngressClassName" = true
      "className"                = "internal-nginx"
    }
  })
}

resource "kubernetes_namespace" "oauth2" {
  metadata {
    name = "oauth2-proxy"
  }
}

resource "helm_release" "oauth2" {
  name       = "oauth2-proxy"
  namespace  = kubernetes_namespace.oauth2.id
  repository = "https://oauth2-proxy.github.io/manifests"
  chart      = "oauth2-proxy"
  version    = "7.12.9"

  values = [
    local.yamlparam,
    yamlencode({ hash = sha256(local.yamlparam) })
  ]
}

resource "azurerm_key_vault_certificate" "oauth-tls" {
  depends_on = [
    azurerm_key_vault.cluster_kv,
    azurerm_key_vault_certificate_issuer.ca,
    # need this so terraform can access kv
    azurerm_role_assignment.cluster_kv_builder
  ]

  name         = "oauth2-proxy"
  key_vault_id = azurerm_key_vault.cluster_kv.id

  certificate_policy {
    issuer_parameters {
      name = "MSIT"
    }

    key_properties {
      exportable = true
      key_size   = 2048
      key_type   = "RSA"
      reuse_key  = true
    }

    lifetime_action {
      action {
        action_type = "AutoRenew"
      }

      trigger {
        days_before_expiry = 30
      }
    }

    secret_properties {
      content_type = "application/x-pem-file"
    }

    x509_certificate_properties {
      # Server Authentication = *******.*******.1
      # Client Authentication = *******.*******.2
      extended_key_usage = ["*******.*******.1"]

      key_usage = [
        "cRLSign",
        "dataEncipherment",
        "digitalSignature",
        "keyAgreement",
        "keyCertSign",
        "keyEncipherment",
      ]

      subject = "CN=${var.config.cluster_name}.dev.openai.org"
      subject_alternative_names {
        dns_names = [
          "oai-azure-auth-proxy.int.${var.config.cluster_name}.dev.openai.org",
          "oauth2-proxy-core-group.aad-auth.int.${var.config.cluster_name}.dev.openai.org"
        ]
      }
      validity_in_months = 12
    }
  }
}
