resource "kubernetes_namespace" "namespaces" {
  for_each = var.config.namespaces
  metadata {
    name = each.key
  }
}

data "azuread_group" "namespaces-group" {
  for_each     = var.config.namespaces
  display_name = each.value.aad_group
}

resource "azurerm_role_assignment" "namespaces-access" {
  for_each             = var.config.namespaces
  scope                = "${var.config.cluster_aks.id}/namespaces/${each.key}"
  role_definition_name = "Azure Kubernetes Service RBAC Writer"
  principal_id         = data.azuread_group.namespaces-group[each.key].object_id
}

# give the groups list cluster credetials access as well
resource "azurerm_role_assignment" "cluster-access" {
  for_each             = var.config.namespaces
  scope                = var.config.cluster_aks.id
  role_definition_name = "Azure Kubernetes Service Cluster User Role"
  principal_id         = data.azuread_group.namespaces-group[each.key].object_id
}

resource "kubernetes_namespace" "monitoring" {
  metadata {
    name = "monitoring"
  }
}

resource "kubernetes_namespace" "scaling" {
  metadata {
    name = "scaling"
  }
}

resource "kubernetes_namespace" "tailscale" {
  metadata {
    name = "tailscale"
  }
}

# for brix, perhonen, etc
resource "kubernetes_namespace" "system" {
  metadata {
    name = "system"
  }
}
