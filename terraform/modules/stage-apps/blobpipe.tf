module "blobpipe" {
  count = var.config.enable_blobpipe ? 1 : 0

  // We setup infra azure service principal/storage account cm in kubecache module
  depends_on = [module.kubecache]
  source     = "../blobpipe"

  // Reuse the same "scaling" namespace for infra-apps
  namespace = kubernetes_namespace.scaling.metadata[0].name

  cluster_name         = var.config.cluster_name
  storage_account_name = "ornginfra${module.azure-naming.regions[var.config.location].short_name}"
}
