locals {
  cluster_name = var.config.cluster_name
  enable_coredns_custom = true
}

resource "kubectl_manifest" "aks-coredns-custom" {
  count = local.enable_coredns_custom ? 1 : 0
  yaml_body = <<YAML
apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns-custom
  namespace: kube-system
data:
  svc_openai_org.server: |
    svc.${local.cluster_name}.dev.openai.org:53 {
      rewrite name suffix ${local.cluster_name}.dev.openai.org cluster.local answer auto

      template IN AAAA {
        rcode NXDOMAIN
      }      

      forward . 127.0.0.1
    }
YAML
}

resource "kubernetes_service" "coredns_internal" {
  metadata {
    name      = "coredns-internal-pls"
    namespace = "kube-system"
    annotations = {
      "service.beta.kubernetes.io/azure-load-balancer-internal"        = "true"
      "service.beta.kubernetes.io/azure-load-balancer-internal-subnet" = "brix-ing-subnet"
      "service.beta.kubernetes.io/azure-pls-create"                    = "true"
      "service.beta.kubernetes.io/azure-pls-resource-group"            = var.config.cluster_rg.name
      "service.beta.kubernetes.io/azure-pls-name"                      = "coredns"
    }
  }

  spec {
    selector = {
      "k8s-app" = "kube-dns"
    }

    type = "LoadBalancer"

    port {
      name        = "udp-tcp"
      port        = 53
      target_port = 53
      protocol    = "TCP"
    }

    port {
      name        = "udp"
      port        = 53
      target_port = 53
      protocol    = "UDP"
    }
  }
}


