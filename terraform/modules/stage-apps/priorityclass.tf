resource "kubernetes_priority_class" "additional" {
  for_each = { for class in var.config.priority_classes : class.name => class }

  metadata {
    name   = each.key
    labels = merge(lookup(each.value, "labels", {}), { "app.kubernetes.io/managed-by" = "terraform" })
  }
  value          = each.value.priority
  global_default = lookup(each.value, "default", false)
  description    = lookup(each.value, "description", null)
}
