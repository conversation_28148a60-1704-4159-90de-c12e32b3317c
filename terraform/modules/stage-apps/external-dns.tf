data "azurerm_subscription" "openai-dns-sub" {
  provider = azurerm.common
}

data "azurerm_subscription" "cluster-sub" {
  provider = azurerm
}

locals {
  external_dns_sub_config = {
    tenant_id           = data.azurerm_subscription.cluster-sub.tenant_id
    subscription_id     = data.azurerm_subscription.cluster-sub.subscription_id
    resource_group_name = var.config.cluster_rg.name
  }
}

module "external-dns" {
  depends_on = [ 
    azurerm_private_dns_zone.openai_org_dns_zone
   ]
  source = "../external-dns"

  providers = {
    azurerm    = azurerm
    kubernetes = kubernetes
    kubectl    = kubectl
  }

  cluster_name            = var.config.cluster_name
  cluster_aks             = var.config.cluster_aks
  cluster_resource_group  = var.config.cluster_rg.name
  cluster_region          = var.config.location
  dnszone_subscription_id = local.external_dns_sub_config.subscription_id
  dnszone_resource_group  = local.external_dns_sub_config.resource_group_name
  dnszone_tenant_id       = local.external_dns_sub_config.tenant_id
}

resource "azurerm_private_dns_zone" "openai_org_dns_zone" {
  name                = "openai.org"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_openai_link" {
  name                  = "link-orange-vnet"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.openai_org_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
  registration_enabled  = false
}
