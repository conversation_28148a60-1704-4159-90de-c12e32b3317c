# Purpose: Enable the PIMSG-ORANGE-OWNER group to have cluster-admin access to the cluster
# after activating the PIMSG-ORANGE-OWNER role in the Azure AD tenant.
resource "kubernetes_cluster_role_binding" "pimsg_cluster_admin_binding" {
  metadata {
    name = "orange-pimsg-orange-owner-cluster-admin-binding"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "cluster-admin"
  }

  subject {
    kind = "Group"
    # PIMSG-ORANGE-OWNER
    name      = "6daa1947-74b3-43a3-bd00-ba06d81c2a46"
    api_group = "rbac.authorization.k8s.io"
  }
}