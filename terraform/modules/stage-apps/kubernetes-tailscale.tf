data "azurerm_key_vault" "tailscale-keyvault" {
  provider = azurerm.infra-secret-reader

  name                = "orangetailscalekeys"
  resource_group_name = "orange-tailscale"
}

data "kubernetes_config_map" "cilium-config" {
  metadata {
    name      = "cilium-config"
    namespace = "kube-system"
  }
}

# Expose observability services
module "observability-config" {
  source = "../../modules/orange-observability-config"
}

locals {
  observability_routes = {
    for dns, info in module.observability-config.services :
    dns => "${info.cluster_ip}/32"
  if info.cluster_name == var.config.cluster_name }
}

module "kubernetes-tailscale" {
  depends_on = [module.iridmission] # Explicit dependency on admission controller for tailsnail
  source     = "../kubernetes-tailscale"
  providers = {
    azurerm = azurerm.infra-secret-reader
    kubectl = kubectl
  }

  cluster_name                = var.config.cluster_name
  tailscale_keyvault_id       = data.azurerm_key_vault.tailscale-keyvault.id
  keyvault_client_id_name     = "tailscale-client-id"
  keyvault_client_secret_name = "tailscale-client-secret"
  tag_tailnet_operator        = "orange"
  tag_tailnet_subnet          = "orange-cluster"
  namespace_name              = kubernetes_namespace.tailscale.metadata[0].name

  advertise_routes = merge(
    local.observability_routes,
    { for i, c in split(" ", data.kubernetes_config_map.cilium-config.data["cluster-pool-ipv4-cidr"]) : "workload-cidr-${i + 1}" => c },
    {
      oidc-oid-proxy-cidr = "${module.oidc-oid.kube-oidc-proxy-ip-address}/32"
    },
    { ingress-cidr = local.ingress_subnet_cidr }
  )

  subnet_router_annotations = length(module.tailsnail) == 1 ? {
    "tailsnail.orange.internal/hash" = module.tailsnail[0].tailsnail-hash
  } : {}
}
