locals {
  class_name = "internal-nginx"  
}

resource "kubectl_manifest" "akswebrouting-nginx-class" {
  depends_on = [
    azurerm_key_vault_certificate.ingress-default-tls,
  ]
  yaml_body = <<YAML
apiVersion: approuting.kubernetes.azure.com/v1alpha1
kind: NginxIngressController
metadata:
  name: ${local.class_name}
spec:
  ingressClassName: ${local.class_name}
  controllerNamePrefix: ${local.class_name}
  loadBalancerAnnotations: 
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: ${azurerm_subnet.internal-nginx-subnet.name}
  defaultSSLCertificate:
    keyVaultURI: ${azurerm_key_vault_certificate.ingress-default-tls.versionless_id}
YAML
}

resource "azurerm_subnet" "internal-nginx-subnet" {
  name                                          = "brix-ing-subnet"
  resource_group_name                           = var.config.cluster_rg.name
  virtual_network_name                          = var.config.cluster_vnet.name
  private_link_service_network_policies_enabled = false
  address_prefixes                              = [local.ingress_subnet_cidr]
}

data "kubernetes_config_map" "cm" {
  depends_on = [kubectl_manifest.akswebrouting-nginx-class]
  metadata {
    name      = "${local.class_name}-0"
    namespace = "app-routing-system"
  }
}

resource "kubectl_manifest" "nginx_cm_patch" {
  depends_on = [
    data.kubernetes_config_map.cm,
    kubectl_manifest.akswebrouting-nginx-class
  ]
  yaml_body = yamlencode({
    "apiVersion" = "v1"
    "kind"       = "ConfigMap"
    "metadata" = {
      "name"      = data.kubernetes_config_map.cm.metadata[0].name
      "namespace" = data.kubernetes_config_map.cm.metadata[0].namespace
    }
    "data" = merge(
      data.kubernetes_config_map.cm.data,
      {
        # "large-client-header-buffers" = "4 64k",
        # "proxy-buffer-size"           = "128k",
        # "proxy-buffering"             = "on",
        # "proxy-buffers-number"        = "4",
        "server-snippet"              = <<-EOT
          set $oauth_cookie_cache_key $cookie__oauth2_proxy_0$cookie__oauth2_proxy;        
        EOT
      }
    )
  })
}

resource "azurerm_key_vault_certificate" "ingress-default-tls" {
  depends_on = [
    azurerm_key_vault.cluster_kv,
    azurerm_key_vault_certificate_issuer.ca,
    # need this so terraform can access kv
    azurerm_role_assignment.cluster_kv_builder
  ]

  name         = "ingress-default"
  key_vault_id = azurerm_key_vault.cluster_kv.id

  certificate_policy {
    issuer_parameters {
      name = "MSIT"
    }

    key_properties {
      exportable = true
      key_size   = 2048
      key_type   = "RSA"
      reuse_key  = true
    }

    lifetime_action {
      action {
        action_type = "AutoRenew"
      }

      trigger {
        days_before_expiry = 30
      }
    }

    secret_properties {
      content_type = "application/x-pem-file"
    }

    x509_certificate_properties {
      # Server Authentication = *******.*******.1
      # Client Authentication = *******.*******.2
      extended_key_usage = ["*******.*******.1"]

      key_usage = [
        "cRLSign",
        "dataEncipherment",
        "digitalSignature",
        "keyAgreement",
        "keyCertSign",
        "keyEncipherment",
      ]

      subject = "CN=*.int.${var.config.cluster_name}.dev.openai.org"
      validity_in_months = 12
    }
  }
}
