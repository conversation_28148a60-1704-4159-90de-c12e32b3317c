locals {
  # CIDR for the ingress subnet. Globally unique subnet within cluster vnet, or '' for ingress disabled
  ingress_subnet_cidr = module.global_settings.ingress_subnet_cidr[var.config.cluster_name]

  # Keyvault names must be globally unique, so we include the cluster name
  oidc_tlskeyvault_name           = "orng-${substr(sha1(var.config.cluster_name), 0, 7)}-tlskeyvault"
  oidc_openaidev_tlskeyvault_name = "orng-${substr(sha1(var.config.cluster_name), 0, 7)}-oai-tls"
  oidc_oid_tlskeyvault_name       = "orng-${substr(sha1(var.config.cluster_name), 0, 7)}-oid-tls"
  cluster_keyvault_name           = "orng-${substr(sha1(var.config.cluster_name), 0, 7)}-clusterkv"
}
