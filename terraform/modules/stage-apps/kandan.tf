locals {
  kandan_cluster_name = var.config.cluster_name
  kandan_namespace    = "kandan"

  # Deploy kandan to these specific clusters
  kandan_clusters = [
    "prod-uksouth-7",
    "prod-southcentralus-hpe-3",
    "stage-southcentralus-hpe-1"
  ]

  should_deploy_kandan = contains(local.kandan_clusters, local.kandan_cluster_name)
}

resource "kubernetes_namespace" "kandan_namespace" {
  count = local.should_deploy_kandan ? 1 : 0
  metadata {
    name = local.kandan_namespace
  }
}

module "kandan" {
  source = "../kandan"
  count = local.should_deploy_kandan ? 1 : 0

  namespace       = local.kandan_namespace
  cluster_name    = local.kandan_cluster_name
  cluster_rg      = var.config.cluster_rg
  oidc_issuer_url = var.config.cluster_aks.oidc_issuer_url
  prometheus_env  = var.config.prometheus_env
  prometheus_url  = var.config.prometheus_url
}
