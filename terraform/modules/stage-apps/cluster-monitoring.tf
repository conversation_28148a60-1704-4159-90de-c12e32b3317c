module "cluster-monitoring" {
  source = "../cluster-monitoring"

  providers = {
    azurerm                     = azurerm
    azurerm.infra-secret-reader = azurerm.infra-secret-reader
    kubernetes                  = kubernetes
  }

  monitoring_namespace = kubernetes_namespace.monitoring
  
  # Harmony vault information for Redis exporter
  harmony_vault = {
    id                = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.KeyVault/vaults/orngharmony-vault"
    subscription_name = "AIPLATFORM-ORANGE-OAI-ASSESTS"
    subscription_id   = "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e"
    tenant_id         = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green Tenant
  }
  
  enable_redis_exporter = false # Temporarily disabled due to harmony vault access issues
}
