module "juicer" {
  source = "../juicer"

  location                = var.config.location
  cluster_name            = var.config.cluster_name
  cluster_rg              = var.config.cluster_rg
  oidc_issuer_url         = var.config.cluster_aks.oidc_issuer_url
  prometheus_env          = var.config.prometheus_env
  prometheus_url          = var.config.prometheus_url
  prometheus_cluster_name = var.config.prometheus_cluster_name

  providers = {
    azurerm           = azurerm
    azurerm.ame-infra = azurerm.ame-infra
    kubernetes        = kubernetes
  }
}
