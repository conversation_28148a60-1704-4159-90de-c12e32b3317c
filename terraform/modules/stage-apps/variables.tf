variable "config" {
  type        = object({
    # Azure
    cluster_name = string # Globally unique cluster name
    location     = string # Azure location for the cluster

    # Resources
    cluster_rg               = any # Data or Resource for Azure resource group for the AKS cluster
    cluster_aks              = any # Data or Resource for Azure AKS cluster
    cluster_vnet             = any # Data or Resource for Cluster virtual network
    cluster_k8s_subnet       = any # Data or Resource for Kubernetes subnet on the cluster virtual network
    unmanaged_pools_identity = any # Data or Resource for Azure managed identity for unmanaged Kubelet agent pools
    cluster_identity         = any # Data or Resource for Azure managed identity for AKS cluster

    # Kubernetes
    namespaces = optional(map(object({
      aad_group = string
    })), {})
    priority_classes = optional(list(object({
      name        = string
      priority    = number
      default     = optional(bool)
      description = optional(string)
      labels      = optional(map(string))
    })), [
      {
        name     = "idle"
        priority = -10
      },
      {
        name        = "low-priority"
        priority    = -3
        description = "Low-priority class for preemptible experiments."
      },
      {
        name     = "team-medium"
        priority = -2
      },
      {
        name     = "team-high"
        priority = 0
      },
      {
        name     = "team-mild"
        priority = -4
        default  = true
      },
      {
        name     = "team-infra-high"
        priority = 10
        labels   = { "openai.com/team" = "hwhealth" }
      },
      {
        name     = "team-critical"
        priority = 10000000
      },
      {
        name     = "reserved"
        priority = 20000000
      },
      {
        name     = "team-infra-critical"
        priority = 100000000
      },
      {
        name     = "research-cluster-critical"
        priority = 550000000
        labels   = { "openai.com/team" = "platform" }
      },
      {
        name     = "research-node-critical"
        priority = 550000500
        labels   = { "openai.com/team" = "platform" }
      },
      {
        name     = "security-high-priority"
        priority = **********
        labels   = { "openai.com/team" = "security" }
      }
    ]) # List of priority classes to create
    oidc_application_id                = string # ID for the oidc application created in the ad-ops step
    oidc_use_cluster_ip                = optional(bool, true) # True if oidc DNS record should point to cluster internal IP; otherwise uses service external IP
    oauth_application_id               = string # ID for the oauth application created in the ad-ops step
    oauth_tenant_id                    = string # Tenant ID for the oauth application created in the ad-ops step
    enable_blobcache                   = optional(bool, true) # Enable Blobcache Service
    enable_kubecache                   = optional(bool, false) # Enable Kubecache Service
    enable_blobpipe                    = optional(bool, false) # Enable Blobpipe Service
    enable_bastion                     = optional(bool, true) # Enable Bastion Proxy Service
    nccl_host_topology_path            = optional(string, "") # Path to the NCCL host topology file on the host
    oidc_proxy_node_selector           = optional(map(string), {
      "singularity.azure.com/processing-unit" = "system"
    }) # Node selector for oidc proxy deployment, defaults to system nodes
    openai_encodings_base_data_gym     = string # Value for OPENAI_ENCODINGS_BASE_DATA_GYM
    remap_oai_artifact_storage_account = string # Account to redirect az://oaiartifacts to
    remap_oai_data_storage_account     = string # Account to redirect az://oaimichael and az://oaiphx1 to
    applied_storage_map                = string # Value for APPLIED_STORAGE_MAP
    oidc_tenant_id                     = string # Tenant of user apps & keyvault used by oidc-proxy
    workload_auth_type                 = string # Whether to use certificates or secrets for workload to user sp authentication
    tailsnail_rate_mbit                = number # Tailsnail egress rate limit in Mbit/s, -1 to disable
    override_cpu_isalevel              = optional(string, "") # Sets OAIPKG_CPU_ISALEVEL to the specified value (e.g. avx2, avx512)
    enable_joiner                      = optional(bool, false) # Whether to enable joiner or not in the cluster. This service should be sufficient to run on a single cluster in a region
    # Build Pipeline: https://dev.azure.com/project-argos/Mimco/_build?definitionId=1656
    brix_image_tag                     = optional(string, "v0.16.4-2e674dd4-dirty-cloudtest-amd64") # Image tag for the Brix operator
    # Build Pipeline: https://dev.azure.com/project-argos/Mimco/_build?definitionId=2205
    perhonen_image_tag                 = optional(string, "v0.0.1-a5f2541d-dirty-cloudtest-amd64")
    perhonen_env                       = optional(string, "dev")
    perhonen_cpu                       = optional(string, "1") # eg "16"
    perhonen_memory                    = optional(string, "1Gi") # eg "64Gi"
    perhonen_replicas                  = optional(number, 1)
    perhonen_acr                       = optional(string, "iridiumsdc.azurecr.io")
    perhonen_enable_eviction           = optional(bool, false)
    perhonen_use_unallocated           = optional(bool, true)
    perhonen_dry_run                   = optional(string, "false")
    enable_perhonen                    = optional(bool, false)
    prometheus_env                     = optional(string, "uksouth")
    prometheus_url                     = string
    prometheus_cluster_name            = string # Name of the cluster for Prometheus
    lemon_grafana_url                  = string # The url used by lemon to embed Grafana charts
    enable_redis_exporter              = optional(bool, false) # Enable Redis exporter for monitoring shared Redis Enterprise cluster
    lemon_adx_kusto_cluster_eu         = string # The ADX Kusto cluster for experiments running in EU clusters
    lemon_adx_kusto_cluster_us         = string # The ADX Kusto cluster for experiments running in US clusters
    lemon_adx_kusto_database           = string # The ADX Kusto database name for both US and EU Kusto clusters
    enable_file_logs_collector         = optional(bool, true) # Enable fluent-bit sidecar for file logs
  })
  validation {
    condition     = contains(["certificates", "secrets"], var.config.workload_auth_type)
    error_message = "workload_auth_type must be either 'certificates' or 'secrets'"
  }
  description = "Configuration for the applications stage"
}
