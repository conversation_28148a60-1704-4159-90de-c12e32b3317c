locals {
  joiner_app_count          = var.config.enable_joiner ? 1 : 0
  kubernetes_sp_secret_name = "azure-service-principal"
}

module "lemon" {
  source = "../lemon"

  location                   = var.config.location
  oidc_issuer_url            = var.config.cluster_aks.oidc_issuer_url
  cluster_rg                 = var.config.cluster_rg
  prometheus_env             = var.config.prometheus_env
  prometheus_url             = var.config.prometheus_url
  lemon_grafana_url          = var.config.lemon_grafana_url
  lemon_adx_kusto_cluster_eu = var.config.lemon_adx_kusto_cluster_eu
  lemon_adx_kusto_cluster_us = var.config.lemon_adx_kusto_cluster_us
  lemon_adx_kusto_database   = var.config.lemon_adx_kusto_database
  cluster_name               = var.config.cluster_name

  providers = {
    azurerm                     = azurerm
    azurerm.infra-secret-reader = azurerm.infra-secret-reader
    kubernetes                  = kubernetes
  }

  orng_observability_keyvault = {
    id                = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-global/providers/Microsoft.KeyVault/vaults/orange-infra-sp-sync-kv"
    subscription_name = "AIPLATFORM-ORANGE-INFRA"
    subscription_id   = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
    tenant_id         = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green Tenant
  }
}


# Joiner app
module "joiner-common" {
  count = local.joiner_app_count

  providers = {
    azurerm.infra-secret-reader = azurerm.infra-secret-reader
  }

  source = "../joiner-common"

  namespace    = module.lemon.namespace
  sp_tenant_id = data.azurerm_client_config.green.tenant_id
  infra_sync_keyvault = {
    id                        = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-infra-global/providers/Microsoft.KeyVault/vaults/orange-infra-sp-sync-kv"
    subscription_name         = "AIPLATFORM-ORANGE-INFRA"
    subscription_id           = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
    tenant_id                 = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green Tenant
  }
  kubernetes_sp_secret_name = local.kubernetes_sp_secret_name
}

module "joiner-store-v3" {
  count = local.joiner_app_count

  source = "../joiner-store-v3"

  image          = "iridiumsdc.azurecr.io/observability/joiner_store_rust_v2@sha256:8d5ac95fcc7f59955aa2cbb8ca877fa55d1941e327389799bb0a375c05ef14fd" # Build link: https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1163817&view=logs&j=12f1170f-54f2-53f3-20dd-22fc7dff55f9&t=59a85588-b0ba-5043-24c4-d9e29d89c6f6
  name_suffix    = ""
  config_hash    = module.joiner-common[0].kubernetes_store_configmap_hash
  configmap_name = module.joiner-common[0].kubernetes_configmap_name
  replicas       = 1
  namespace      = module.lemon.namespace

  # Using same values as used by OAI joiner store
  resource_request_cpu     = "8000m"
  resource_request_memory  = "64Gi"
  storage_resource_request = "16Ti"
}

module "joiner-watch-experiments" {
  count = local.joiner_app_count

  source = "../joiner-watch-experiments"

  image                     = "iridiumsdc.azurecr.io/observability/joiner_watch_experiments@sha256:2012c18c04ebeddca3cf43701b1ce77618e5be0b044429c9aeb3dc77f22bc424" # Build link: https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1163817&view=logs&j=12f1170f-54f2-53f3-20dd-22fc7dff55f9&t=e7d422da-e621-5d9c-7d76-ef2f3144435b
  name_suffix               = ""
  config_hash               = module.joiner-common[0].kubernetes_store_configmap_hash
  configmap_name            = module.joiner-common[0].kubernetes_configmap_name
  namespace                 = module.lemon.namespace
  kubernetes_sp_secret_name = local.kubernetes_sp_secret_name
}

module "joiner-worker" {
  count = local.joiner_app_count

  source = "../joiner-worker"

  image                     = "iridiumsdc.azurecr.io/observability/joiner_worker@sha256:5cdef974e71e6b0763de8be81a92e74e58d23a5b95e00e13c7ac3f707b7cc878" # Build link: https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1163817&view=logs&j=12f1170f-54f2-53f3-20dd-22fc7dff55f9&t=c3091b1e-314e-512e-11db-151fdba14cd6
  name_suffix               = ""
  config_hash               = module.joiner-common[0].kubernetes_store_configmap_hash
  configmap_name            = module.joiner-common[0].kubernetes_configmap_name
  namespace                 = module.lemon.namespace
  replicas                  = 1
  store_statefulset_name    = replace(module.joiner-store-v3[0].statefulset_name, "-v3", "")
  store_service_name        = replace(module.joiner-store-v3[0].service_name, "-v3", "")
  kubernetes_sp_secret_name = local.kubernetes_sp_secret_name
}
