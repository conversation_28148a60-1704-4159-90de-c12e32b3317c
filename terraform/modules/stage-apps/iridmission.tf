module "iridmission" {
  depends_on = [
    module.brix, # for brix CRDs
  ]
  providers = {
    kubernetes = kubernetes
    kubectl    = kubectl
  }

  source = "../iridmission"

  cluster_name                       = var.config.cluster_name
  cluster_region                     = var.config.location
  nccl_host_topology_path            = var.config.nccl_host_topology_path
  workload_auth_type                 = var.config.workload_auth_type
  openai_encodings_base_data_gym     = var.config.openai_encodings_base_data_gym
  remap_oai_artifact_storage_account = var.config.remap_oai_artifact_storage_account
  remap_oai_data_storage_account     = var.config.remap_oai_data_storage_account
  applied_storage_map                = var.config.applied_storage_map
  tailsnail_enabled                  = (var.config.tailsnail_rate_mbit > -1)
  override_cpu_isalevel              = var.config.override_cpu_isalevel
  enable_file_logs_collector         = var.config.enable_file_logs_collector
}
