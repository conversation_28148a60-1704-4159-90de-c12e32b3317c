locals {
  busman_cluster_name = var.config.cluster_name
  busman_namespace    = "busman"

  # Deploy busman to these specific clusters
  busman_clusters = [
    "prod-uksouth-7",
    "prod-southcentralus-hpe-3",
    "stage-southcentralus-hpe-1"
  ]

  should_deploy_busman = contains(local.busman_clusters, local.busman_cluster_name)
}

resource "kubernetes_namespace" "busman_namespace" {
  count = local.should_deploy_busman ? 1 : 0
  metadata {
    name = local.busman_namespace
  }
}

module "busman" {
  source = "../busman"
  count = local.should_deploy_busman ? 1 : 0

  namespace       = local.busman_namespace
  cluster_name    = local.busman_cluster_name
  cluster_rg      = var.config.cluster_rg
  oidc_issuer_url = var.config.cluster_aks.oidc_issuer_url
  prometheus_env  = var.config.prometheus_env
  prometheus_url  = var.config.prometheus_url
}
