locals {
  openai_proxy_cluster_name = var.config.cluster_name
  openai_proxy_namespace    = "openai-proxy"

  # Deploy openai_proxy to these specific clusters
  openai_proxy_clusters = [
    "prod-uksouth-7",
    "prod-southcentralus-hpe-3",
    "stage-southcentralus-hpe-1"
  ]

  should_deploy_openai_proxy = contains(local.openai_proxy_clusters, local.openai_proxy_cluster_name)
}

resource "kubernetes_namespace" "openai_proxy_namespace" {
  count = local.should_deploy_openai_proxy ? 1 : 0
  metadata {
    name = local.openai_proxy_namespace
  }
}

module "openai_proxy" {
  source = "../openai_proxy"
  count = local.should_deploy_openai_proxy ? 1 : 0

  namespace       = local.openai_proxy_namespace
  cluster_name    = local.openai_proxy_cluster_name
  cluster_rg      = var.config.cluster_rg
  oidc_issuer_url = var.config.cluster_aks.oidc_issuer_url
  prometheus_env  = var.config.prometheus_env
  prometheus_url  = var.config.prometheus_url
}
