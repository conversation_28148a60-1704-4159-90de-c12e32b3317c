# 🧠 OpenAI Proxy MSFT

A FastAPI-based OpenAI-compatible proxy service for supporting local model API calls. This service provides a standard OpenAI Chat Completions API interface and internally uses BusTokenCompleter to communicate with local model engines running in Pods.

## 📋 Project Description

OpenAI Proxy MSFT is a high-performance proxy service designed to provide seamless local model access capabilities for various OpenAI-compatible clients. The core features of this service include:

### 🎯 Key Features

- **OpenAI Compatible API**: Fully compatible with OpenAI Chat Completions API v1 specification
- **Local Model Support**: Connects to locally running model engines through BusTokenCompleter
- **Tool Calling Support**: Supports OpenAI format function calling and tool calls
- **Streaming Response**: Supports both streaming and non-streaming response modes
- **Multi-Model Configuration**: Supports dynamic model configuration in `topic$renderer` format
- **High-Performance Architecture**: Based on FastAPI and asynchronous processing, supports high-concurrency requests

### 🏗️ System Architecture

```
Client (OpenAI SDK) → OpenAI Proxy MSFT → BusTokenCompleter → Local Model Engines (Pods)
```

1. **Client Layer**: Any client that supports OpenAI API (Python SDK, curl, etc.)
2. **Proxy Layer**: OpenAI Proxy MSFT service, responsible for protocol conversion and request routing
3. **Message Processing Layer**: TokenMessageCompleter handles message format conversion and rendering
4. **Bus Layer**: BusTokenCompleter handles communication with model engines
5. **Model Layer**: Actual model engines running in Pods

### 🔧 Core Components

- **OpenAIProxy**: Main proxy service class that handles request routing and component management
- **BusTokenCompleter**: Handles communication with local model engines, supports multiple QoS strategies
- **TokenMessageCompleter**: Message format conversion and rendering management
- **Renderer**: Converts conversation format to token sequences understandable by models

## ⚙️ Configuration

### Model Configuration Format

The service uses a special model identifier format: `topic$renderer`

**Format Description**:

- `topic`: Path to the model snapshot, no `az://` prefix needed
- `renderer`: Renderer name for message format conversion
- Separator: `$` separates topic and renderer

**Example**:

```
bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga$harmony_v4.0.16
```

### BusTokenCompleter Configuration

- **QoS Type**: Uses `QoSType.ROUND_ROBIN_BY_POD` for load balancing by default
- **Temperature Parameter**: Default temperature = 1.0
- **Retry Mechanism**: Supports automatic retry for failed requests
- **Caching Mechanism**: Component-level caching to avoid repeated initialization

### Service Configuration

- **Host Address**: Default `0.0.0.0` (listens on all interfaces)
- **Port**: Default `8500`
- **Log Level**: Default `info`
- **Auto Reload**: Enabled with `--reload` in development mode

## 🚀 Usage

### Client Configuration Examples

#### Python OpenAI SDK

```python
from openai import OpenAI

# Configure client to use local proxy
client = OpenAI(
    base_url="http://localhost:8500/v1",
    api_key="dummy"  # Local service doesn't require real API key
)

# Send request
response = client.chat.completions.create(
    model="your_topic$your_renderer",
    messages=[
        {"role": "user", "content": "Hello, world!"}
    ]
)

print(response.choices[0].message.content)
```

#### Streaming Response

```python
stream = client.chat.completions.create(
    model="your_topic$your_renderer",
    messages=[
        {"role": "user", "content": "Tell me a story"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

## 🔍 API Specification

### Request Format

Follows OpenAI Chat Completions API v1 specification:

```json
{
  "model": "topic$renderer",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello!"}
  ],
  "tools": [...],           // Optional: tool definitions
  "tool_choice": "auto",    // Optional: tool selection strategy
  "stream": false,          // Optional: whether to stream response
  "temperature": 1.0,       // Optional: sampling temperature
  "max_tokens": null,       // Optional: maximum number of tokens
  "top_p": 1.0,            // Optional: nucleus sampling parameter
  "n": 1,                  // Optional: number of completion choices
  "stop": null             // Optional: stop sequences
}
```

### Response Format

#### Non-Streaming Response

```json
{
  "id": "chatcmpl-...",
  "object": "chat.completion",
  "created": **********,
  "model": "topic$renderer",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you?",
        "tool_calls": null
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  }
}
```

#### Streaming Response

```
data: {"id":"chatcmpl-...","object":"chat.completion.chunk","created":**********,"model":"topic$renderer","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-...","object":"chat.completion.chunk","created":**********,"model":"topic$renderer","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-...","object":"chat.completion.chunk","created":**********,"model":"topic$renderer","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 🤝 Contributing

Issues and Pull Requests are welcome to improve this project.

## 📄 License

This project follows internal licensing agreements.
