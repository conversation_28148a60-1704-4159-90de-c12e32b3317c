# Create a load balancer service to expose the openai_proxy app to users.
resource "kubernetes_service" "openai_proxy_lb" {
  metadata {
    name      = "openai-proxy-lb"
    namespace = local.namespace
  }

  spec {
    type = "LoadBalancer"

    selector = {
      app = "openai-proxy-app"
    }

    port {
      name        = "app"
      port        = 8500
      protocol    = "TCP"
      target_port = 8500
    }
  }

  depends_on = [kubernetes_deployment.openai_proxy_app]
}
