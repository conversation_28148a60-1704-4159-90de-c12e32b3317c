terraform {
  required_providers {
    azurerm = ">= 4.19.0"
  }
}

# Remote state in green tenant
data "terraform_remote_state" "remote-state" {
  backend = "azurerm"
  config = {
    resource_group_name  = "orange-terraform"
    storage_account_name = "orangetfstate"
    container_name       = "tfstate"
    subscription_id      = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"

    use_azuread_auth = true
    key              = var.state-key
  }
}

output "remote-state-outputs" {
  value       = data.terraform_remote_state.remote-state.outputs
  description = "Output from the remote state"
}
