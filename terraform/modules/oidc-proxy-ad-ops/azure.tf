
resource "random_uuid" "oauth_permission_id" {}

data "azuread_client_config" "current" {}

module "global_settings" {
  source = "../global_settings"
}

resource "azuread_application" "apiserver" {
  display_name = "Orange Login"
  owners = [
    data.azuread_client_config.current.object_id,
    # TODO
    # properly setup owners. can't be groups.
  ]
  identifier_uris         = concat(
    ["api://${var.cluster_name}-oidc-proxy"],
    [for name in module.global_settings.cluster_name_list : "api://brix-bastion-aad-oauth-dev-${name}"],
  )
  sign_in_audience        = "AzureADMyOrg"
  prevent_duplicate_names = true

  api {
    requested_access_token_version = 2

    oauth2_permission_scope {
      admin_consent_description  = "Allow the application to access OIDC on behalf of  the signed-in user"
      admin_consent_display_name = "Access OIDC"
      enabled                    = true
      id                         = random_uuid.oauth_permission_id.id
      type                       = "User"
      user_consent_description   = "Allow the application to access OIDC on your behalf"
      user_consent_display_name  = "Access OIDC"
      value                      = "user_impersonation"
    }
  }

  # this will require --oidc-extra-scope=profile
  optional_claims {
    id_token {
      name = "upn"
    }

    id_token {
      name = "groups"
    }
  }

  fallback_public_client_enabled = true

  # this is needed to be able to get upn
  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000"
    resource_access {
      id   = "64a6cdd6-aab1-4aaf-94b8-3cc8405e90d0"
      type = "Scope"
    }
    resource_access {
      id   = "14dad69e-099b-42c9-810b-d002981feec1"
      type = "Scope"
    }
  }

  service_management_reference = var.service_tree_id

  group_membership_claims = ["SecurityGroup"]

  # used for local oidc-login redirection
  public_client {
    redirect_uris = ["http://localhost:8000"]
  }
}

resource "azuread_application_pre_authorized" "azure_cli" {
  application_object_id = azuread_application.apiserver.id
  authorized_app_id     = "04b07795-8ddb-461a-bbee-02f9e1bf7b46"
  permission_ids        = [random_uuid.oauth_permission_id.id]
}

output "oidc_application_id" {
  description = "oidc application id"
  value       = azuread_application.apiserver.application_id

  depends_on = [azuread_application.apiserver]
}
