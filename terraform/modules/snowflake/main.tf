locals {
  user_to_team = {
    for user, data in var.users :
    user => [
      for team in data.teams : element(split("-", team), length(split("-", team)) - 1)
    ]
  }
  team_user_pairs = flatten([
    for user, data in var.users : [
      for team in data.teams : {
        team = element(split("-", team), length(split("-", team)) - 1)
        user = user
      }
    ]
  ])

  team_to_users = {
    for pair in local.team_user_pairs : pair.team => pair.user... 
  }
}


resource "snowflake_user" "orange_users" {
  for_each             = toset(keys(var.users))

  name                 = upper(each.key)
  login_name           = "${each.key}@green.microsoft.com"
  email                = "${each.key}@microsoft.com"
  default_role         = "PUBLIC"
  disable_mfa          = "false"
  must_change_password = "false" 

  lifecycle {
    # These can be changed manually by the user or continuously change
    ignore_changes = [
      default_role,
      disable_mfa,
      first_name,
      middle_name,
      last_name,
      default_role,
      default_warehouse,
      default_namespace,
      network_policy,
      mins_to_bypass_mfa,
      days_to_expiry,
      mins_to_unlock,
      disabled,
      default_secondary_roles_option
    ]
  }
}


resource "snowflake_warehouse" "team_warehouse" {
  for_each = toset(keys(local.team_to_users))

  name     = upper("${each.key}_warehouse") 

  # Warehouse can be scaled up
  lifecycle {
    ignore_changes = all
  }
}

resource "snowflake_database" "team_database" {
  for_each = toset(keys(local.team_to_users))

  name     = upper("${each.key}_database") 
}

resource "snowflake_account_role" "team_role" {
  for_each = toset(keys(local.team_to_users))

  name     = upper("${each.key}_role")
}


# Add team role to users, databases, and warehouse for RBAC
resource "snowflake_grant_account_role" "grant_team_roles_to_users" {
  for_each    = {
    for pair in local.team_user_pairs :
    "${pair.user}_${pair.team}" => pair
  }
  depends_on = [snowflake_account_role.team_role, snowflake_user.orange_users]
  role_name  = snowflake_account_role.team_role[each.value.team].name
  user_name  = upper(each.value.user)
}

resource "snowflake_grant_privileges_to_account_role" "database_grant" {
  for_each          = snowflake_account_role.team_role
  depends_on        = [snowflake_account_role.team_role, snowflake_database.team_database]
  privileges        = ["USAGE"]
  account_role_name = each.value.name
  on_account_object {
    object_type  = "DATABASE"
    object_name  = snowflake_database.team_database[replace(each.key, "ROLE", "DATABASE")].name
  }
}

resource "snowflake_grant_privileges_to_account_role" "warehouse_grant" {
  for_each          = snowflake_account_role.team_role
  depends_on        = [snowflake_account_role.team_role, snowflake_warehouse.team_warehouse]
  privileges        = ["USAGE"]
  account_role_name = each.value.name
  on_account_object {
    object_type = "WAREHOUSE"
    object_name = snowflake_warehouse.team_warehouse[replace(each.key, "ROLE", "WAREHOUSE")].name
  }
}