module "global" {
  source = "../global_settings"
}

resource "azurerm_storage_account" "storage" {
  name                     = var.name
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  is_hns_enabled                   = false
  cross_tenant_replication_enabled = var.cross_tenant_replication_enabled
  allow_nested_items_to_be_public  = false
  shared_access_key_enabled        = false

  public_network_access_enabled = var.public_network_access_enabled

  blob_properties {
    versioning_enabled  = var.versioning_enabled
    change_feed_enabled = var.change_feed_enabled
  }

  tags = var.tags

  lifecycle {
    prevent_destroy = true
  }
}

resource "azurerm_storage_container" "container" {
  for_each = var.containers

  name                  = each.key
  storage_account_id    = azurerm_storage_account.storage.id
  container_access_type = "private"
}

locals {
  valid_principal_types = {
    "user"             = "User"
    "group"            = "Group"
    "serviceprincipal" = "ServicePrincipal"
  }

  container_permissions = merge([
    for container_name, container in var.containers : {
      for permission in container.permissions : permission.name => {
        scope                = azurerm_storage_container.container[container_name].id
        role_definition_name = permission.role
        principal_id         = permission.objectid
        principal_type       = lookup(local.valid_principal_types, lower(permission.type), null)
      }
    }
  ]...)
}

resource "azurerm_role_assignment" "container_permissions" {
  for_each = local.container_permissions
  depends_on = [azurerm_storage_container.container]

  scope                = each.value.scope
  role_definition_name = each.value.role_definition_name
  principal_id         = each.value.principal_id
  principal_type       = each.value.principal_type
}

module "builder-access" {
  providers = {
    azurerm = azurerm.infra
  }
  count  = var.builder_access ? 1 : 0
  source = "../orange-builder-access"

  name             = azurerm_storage_account.storage.name
  dns_zone         = module.global.private_dns_zones[var.builder_access_service]
  resource_id      = azurerm_storage_account.storage.id
  role_assignments = var.builder_role_assignments
}

locals {
  role_assignments = merge([
    for role, object_ids in var.role_assignments : {
      for object_id in object_ids : "${role}-${object_id}" => {
        object_id            = object_id
        role_definition_name = role
      }
    }
  ]...)

  group_role_assignments = merge([
    for role, object_ids in var.group_role_assignments : {
      for object_id in object_ids : "${role}-${object_id}" => {
        object_id            = object_id
        role_definition_name = role
      }
    }
  ]...)
}

resource "azurerm_role_assignment" "permissions" {
  for_each = local.role_assignments

  scope                = azurerm_storage_account.storage.id
  role_definition_name = each.value.role_definition_name
  principal_id         = each.value.object_id
}

resource "azurerm_role_assignment" "group_permissions" {
  for_each = local.group_role_assignments

  scope                = azurerm_storage_account.storage.id
  role_definition_name = each.value.role_definition_name
  principal_id         = each.value.object_id
  principal_type       = "Group"
}

data "azapi_resource" "storage_blob" {
  type        = "Microsoft.Storage/storageAccounts/blobServices@2021-04-01"
  resource_id = "${azurerm_storage_account.storage.id}/blobServices/default"
}

module "storage_secmon_audit" {
  source = "../aoai-secmon-audit"
  providers = {
    azurerm              = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  target_resource_id = data.azapi_resource.storage_blob.id
}
