variable "name" {
  type        = string
  description = "Azure storage account name"
}

variable "resource_group_name" {
  type        = string
  description = "Azure resource group name"
}

variable "location" {
  type        = string
  description = "Azure location for models storage resources"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resource"
  default     = {}
}

variable "role_assignments" {
  type        = map(set(string))
  description = "role assignment definition name as the key and a set of user/service principal object ids"
  default     = {}
}

variable "group_role_assignments" {
  type        = map(set(string))
  description = "role assignment definition name as the key and a set of group object ids"
  default     = {}
}

variable "public_network_access_enabled" {
  type        = bool
  description = "Whether public network access is enabled for the storage account"
  default     = false
}

variable "versioning_enabled" {
  type        = bool
  description = "Whether versioning is enabled for the storage account"
  default     = false
}

variable "containers" {
  type        = map(object({
    description = string
    permissions = optional(list(object({
      name     = string
      objectid = string
      role     = string
      type     = string # User, Group, ServicePrincipal
    })), [])
  }))
  description = "Map of containers to create in the storage account"
  default     = {}
  validation {
    condition     = var.builder_access == true || length(keys(var.containers)) == 0
    error_message = "Builder access must be enabled to create containers"
  }
}

variable "change_feed_enabled" {
  type        = bool
  description = "Whether change feed is enabled for the storage account"
  default     = false
}

variable "builder_access" {
  type        = bool
  description = "Whether to allow builder access to the storage account"
  default     = false
}

variable "builder_access_service" {
  type        = string
  description = "Service to grant builder access to"
  default     = "blob"
}

variable "builder_role_assignments" {
  type        = set(string)
  description = "Role assignments to grant to the builder identity"
  default     = ["Storage Blob Data Contributor"]
}

variable "cross_tenant_replication_enabled" {
  type        = bool
  description = "Whether cross tenant replication is enabled for the storage account"
  default     = false
}
