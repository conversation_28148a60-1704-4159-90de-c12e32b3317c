variable "perhonen_version" {
  type = string
}

variable "env" {
  type = string
}

variable "password_length" {
  type        = number
  description = "Length of the generated password for perhonen auth"
  default     = 24
}

variable "cpu" {
  type    = string
  default = "16"
}

variable "memory" {
  type    = string
  default = "64Gi"
}

variable "replicas" {
  type    = number
  default = 1
}

variable "cluster" {
  type = string
}

variable "acr" {
  type    = string
  default = "openai.azurecr.io"
}

variable "namespace" {
  type    = string
  default = "system"
}

variable "enable_eviction" {
  type    = bool
  default = false
}

variable "use_unallocated" {
  type    = bool
  default = true
}

variable "log_level" {
  type = string
  // TODO: switch to info after testing
  default = "debug"
}

variable "dry_run" {
  type    = string
  default = "false"
}
