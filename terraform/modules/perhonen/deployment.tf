resource "kubernetes_service_account_v1" "perhonen-service-account" {
  metadata {
    name      = "perhonen"
    namespace = var.namespace
    labels    = { app = "perhonen" }
  }
}

resource "kubernetes_cluster_role_v1" "perhonen-cluster-role" {
  metadata {
    name = "perhonen"
  }
  # Basic resources required to run the operator.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = [""]
    resources  = ["pods", "secrets", "configmaps", "services", "namespaces"]
  }

  rule {
    verbs      = ["delete"]
    api_groups = [""]
    resources  = ["pods"]
  }

  # Required to observe Quota and SKU availability.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = [""]
    resources  = ["nodes"]
  }
  # Required to queue tasks based on pod priority and validate pod templates.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["scheduling.k8s.io"]
    resources  = ["priorityclasses"]
  }
  # Required to observe the sku CRD from brix.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["brix.openai.com"]
    resources  = ["skus"]
  }

  rule {
    verbs      = ["*"]
    api_groups = ["brix.openai.com"]
    resources  = ["quotas"]
  }

  rule {
    api_groups = ["brix.openai.com"]
    resources  = ["quotas/status"]
    verbs      = ["*"]
  }

  # Required to observe the quota CRD from zoku.
  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["brix.openai.com"]
    resources  = ["clusters"]
  }

  rule {
    verbs      = ["get", "list", "watch"]
    api_groups = ["zoku.openai.com"]
    resources  = ["clusters"]
  }

  rule {
    verbs      = ["update", "patch"]
    api_groups = ["brix.openai.com"]
    resources  = ["workloads/status"]
  }

  rule {
    verbs      = ["watch", "get", "list"]
    api_groups = ["brix.openai.com"]
    resources  = ["workloads"]
  }

  rule {
    verbs      = ["create"]
    api_groups = [""]
    resources  = ["pods/binding"]
  }

  rule {
    verbs      = ["create", "patch", "update"]
    api_groups = ["events.k8s.io"]
    resources  = ["events"]
  }
}

resource "kubernetes_cluster_role_binding_v1" "perhonen-cluster-role-binding" {
  depends_on = [
    kubernetes_service_account_v1.perhonen-service-account,
    kubernetes_cluster_role_v1.perhonen-cluster-role,
  ]
  metadata {
    name = "perhonen"
  }
  subject {
    kind      = "ServiceAccount"
    name      = "perhonen"
    namespace = var.namespace
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "perhonen"
  }
}

resource "kubernetes_service" "perhonen" {
  metadata {
    name      = "perhonen"
    namespace = var.namespace
  }

  spec {
    selector = {
      app = "perhonen"
    }

    port {
      name        = "http"
      port        = 8000
      target_port = 8000
    }
  }
}

resource "kubernetes_deployment_v1" "perhonen-deployment" {
  depends_on = [
    kubernetes_cluster_role_v1.perhonen-cluster-role,
    kubernetes_cluster_role_binding_v1.perhonen-cluster-role-binding,
    kubernetes_service_account_v1.perhonen-service-account,
  ]
  metadata {
    name      = "perhonen"
    namespace = var.namespace
    labels    = { app = "perhonen" }
  }
  spec {
    replicas = var.replicas
    selector {
      match_labels = { app = "perhonen" }
    }
    template {
      metadata {
        labels = {
          app                            = "perhonen"
          "openai.com/prometheus-target" = "true"
        }
      }
      spec {
        container {
          name  = "main"
          image = "${var.acr}/brix/perhonen:${var.perhonen_version}"
          port {
            name           = "http"
            container_port = 8000
          }

          port {
            name           = "metrics"
            container_port = 8139
          }

          resources {
            limits = { cpu = var.cpu, memory = var.memory }
          }
          env {
            name  = "RUST_LOG"
            value = var.log_level
          }
          env {
            name  = "CLUSTER"
            value = var.cluster
          }
          env {
            name  = "DRY_RUN"
            value = var.dry_run
          }
          env {
            name  = "PERHONEN_ENABLE_EVICTION"
            value = var.enable_eviction
          }
          env {
            name  = "PERHONEN_USE_UNALLOCATED_NODES"
            value = var.use_unallocated
          }
          image_pull_policy = "Always"

          volume_mount {
            name       = "cores"
            mount_path = "/tmp/cores"
          }

          liveness_probe {
            http_get {
              path = "/health"
              port = "http"
            }
          }
        }

        volume {
          name = "cores"
          empty_dir {}
        }

        service_account_name = "perhonen"
        toleration {
          key      = "openai.com/maintenance"
          operator = "Exists"
          effect   = "NoSchedule"
        }
        toleration {
          key      = "openai.com/team"
          operator = "Equal"
          value    = "infra"
          effect   = "NoSchedule"
        }
        affinity {
          node_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 100
              preference {
                match_expressions {
                  key      = "openai.com/maintenance-status"
                  operator = "NotIn"
                  values   = ["requested"]
                }
              }
            }
          }
        }
        priority_class_name = "system-cluster-critical"
      }
    }
    strategy {
      type = "Recreate"
    }
  }
}

resource "random_password" "perhonen_password" {
  length           = var.password_length
  special          = true
  override_special = "?!&-.;"
}

resource "kubernetes_secret" "perhonen-backend" {
  metadata {
    namespace = var.namespace
    name      = "perhonen-backend"
  }
  type = "Opaque"
  data = {
    auth = "perhonen:${bcrypt(random_password.perhonen_password.result)}"
  }

  lifecycle {
    # Ignore any changes in the computed "auth" field that occur due to the non‐deterministic bcrypt() function.
    ignore_changes = [
      data["auth"]
    ]

    # But if the underlying random password changes, then force resource replacement.
    replace_triggered_by = [
      random_password.perhonen_password.result
    ]
  }
}
