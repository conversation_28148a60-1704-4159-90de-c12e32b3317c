locals {
  # Regions where observability resources should be deployed
  regions = {
    "uksouth"        = {}
    "southcentralus" = {}
    "eastus2"        = {}
    "westus2"        = {}
    "australiaeast"  = {}
    "centralus"      = {}
    "canadacentral"  = {}
  }

  regions_to_provision = {
  }

  # Services used for observability
  # Map from DNS prefix to service IP/cluster
  services = {
    "lemon.orange-7" = {
      cluster_ip   = "************"
      cluster_name = "prod-uksouth-7"
    }
    "lemonservice.orange-7" = {
      cluster_ip   = "************"
      cluster_name = "prod-uksouth-7"
    }
    "lemonservice.orange-8" = {
      cluster_ip   = "************"
      cluster_name = "prod-uksouth-8"
    }
    "lemonservice.orange-15" = {
      cluster_ip   = "************"
      cluster_name = "prod-uksouth-15"
    }
    "lemonservice.orange-stage-southcentralus-hpe-1" = {
      cluster_ip   = "************"
      cluster_name = "stage-southcentralus-hpe-1"
    }
    "lemonservice.orange-prod-southcentralus-hpe-2" = {
      cluster_ip   = "**********"
      cluster_name = "prod-southcentralus-hpe-2"
    }
    "lemon.orange-prod-southcentralus-hpe-2" = {
      cluster_ip   = "***********"
      cluster_name = "prod-southcentralus-hpe-2"
    }
    "lemonservice.orange-prod-southcentralus-hpe-3" = {
      cluster_ip   = "***********"
      cluster_name = "prod-southcentralus-hpe-3"
    }
    "lemonservice.orange-prod-southcentralus-hpe-4" = {
      cluster_ip   = "***********"
      cluster_name = "prod-southcentralus-hpe-4"
    }
    "lemonservice.orange-prod-southcentralus-hpe-5" = {
      cluster_ip   = "************"
      cluster_name = "prod-southcentralus-hpe-5"
    }
    "lemon.orange-prod-southcentralus-hpe-5" = {
      cluster_ip   = "***********"
      cluster_name = "prod-southcentralus-hpe-5"
    }
    "lemonservice.orange-prod-westus2-19" = {
      cluster_ip   = "************"
      cluster_name = "prod-westus2-19"
    }
    "gosb.orange-7" = {
      cluster_ip   = "**********"
      cluster_name = "prod-uksouth-7"
    }
    "asyncmetrics.orange-7" = {
      cluster_ip   = "***********"
      cluster_name = "prod-uksouth-7"
    }
    "grafana.orange-7" = {
      cluster_ip   = "**********"
      cluster_name = "prod-uksouth-7"
    }
    "lemon-grafana.orange-7" = {
      cluster_ip   = "***********"
      cluster_name = "prod-uksouth-7"
    }
    "lemon-grafana.orange-8" = {
      cluster_ip   = "***********"
      cluster_name = "prod-uksouth-8"
    }
    "lemon-grafana.orange-15" = {
      cluster_ip   = "************"
      cluster_name = "prod-uksouth-15"
    }
    "lemon-grafana.orange-stage-southcentralus-hpe-1" = {
      cluster_ip   = "*********"
      cluster_name = "stage-southcentralus-hpe-1"
    }
    "lemon-grafana.orange-prod-southcentralus-hpe-2" = {
      cluster_ip   = "***********"
      cluster_name = "prod-southcentralus-hpe-2"
    }
    "lemon-grafana.orange-prod-southcentralus-hpe-3" = {
      cluster_ip   = "**********"
      cluster_name = "prod-southcentralus-hpe-3"
    }
    "lemon-grafana.orange-prod-southcentralus-hpe-4" = {
      cluster_ip   = "************"
      cluster_name = "prod-southcentralus-hpe-4"
    }
    "lemon-grafana.orange-prod-southcentralus-hpe-5" = {
      cluster_ip   = "************"
      cluster_name = "prod-southcentralus-hpe-5"
    }
    "lemon-grafana.orange-prod-westus2-19" = {
      cluster_ip   = "***********"
      cluster_name = "prod-westus2-19"
    }
    "lemon.orange-8" = {
      cluster_ip   = "***********"
      cluster_name = "prod-uksouth-8"
    }
  }
}
