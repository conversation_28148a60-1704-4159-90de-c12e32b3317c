module "azure-naming" {
  source = "../azure-naming"
}

locals {
  regional_storage_accounts = {
    for region, _ in merge(local.regions, local.regions_to_provision) : region => {
      storage_account_name            = "orngsnowflake${module.azure-naming.regions[region].short_name}"
      events_dev_storage_account_name = "orngeventsdev${module.azure-naming.regions[region].short_name}"
      # TODO: Add prod version of events storage account
      resource_group_name = "orange-observability-${region}"
      region_abbrev       = module.azure-naming.regions[region].short_name
    }
  }

  # NEW Local: Define a map containing only the ACTIVE regions' data
  active_regional_storage_accounts = {
    for region, _ in local.regions : region => { # Only iterate over active regions
      storage_account_name            = "orngsnowflake${module.azure-naming.regions[region].short_name}"
      events_dev_storage_account_name = "orngeventsdev${module.azure-naming.regions[region].short_name}"
      # TODO: Add prod version of events storage account
      resource_group_name = "orange-observability-${region}"
      region_abbrev       = module.azure-naming.regions[region].short_name
    }
  }
}

output "regions" {
  description = "Map from region to the regional observability storage account"
  value       = local.regional_storage_accounts
}

# This will be used by orange-users-vnet for data lookups.
output "active_regions" {
  description = "Map from region to the regional observability storage account (ACTIVE regions only)"
  value       = local.active_regional_storage_accounts
}

output "services" {
  description = "Map from service name to the cluster it belongs to & service IP for observability services"
  value       = local.services
}
