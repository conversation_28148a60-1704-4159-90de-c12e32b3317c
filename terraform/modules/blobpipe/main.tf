resource "helm_release" "blobpipe" {
  name      = "blobpipe"
  namespace = var.namespace

  chart = "${path.module}/chart"

  set {
    name  = "cluster_name"
    value = var.cluster_name
  }
  set {
    name  = "imageRepository"
    value = "iridiumsdc.azurecr.io/infra/blobpipe"
  }
  set {
    name  = "imageTag"
    value = "1127125"
    type = "string"
  }
  set {
    name  = "storage_configuration.tensor_logs.default.storage_account"
    value = var.storage_account_name
  }
}
