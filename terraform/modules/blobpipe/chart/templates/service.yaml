apiVersion: v1
kind: Service
metadata:
  name: {{ include "blobpipe" . }}
  labels:
    app: {{ include "blobpipe" . }}
    prometheus-target: "true"
spec:
  selector:
    app: {{ include "blobpipe" . }}
  clusterIP: None
  type: ClusterIP
  ports:
  - name: http
    protocol: TCP
    port: 80
    targetPort: http
  - name: grpc
    protocol: TCP
    port: 9000
    targetPort: grpc
  - name: web
    port: 9001


