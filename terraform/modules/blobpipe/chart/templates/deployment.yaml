apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "blobpipe" . }}
  labels:
    app: {{ include "blobpipe" . }}
spec:
  replicas: {{ get .Values.replica_count .Values.cluster_name | default 2  }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: {{ include "blobpipe" . }}
  template:
    metadata:
      labels:
        app: {{ include "blobpipe" . }}
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            app: {{ include "blobpipe" . }}
      terminationGracePeriodSeconds: 300
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.avoidSelector | quote }}
                operator: NotIn
                values: ["gpu"]
      containers:
      - name: blobpipeinst
        image: {{ printf "%s:%s" .Values.imageRepository .Values.imageTag }}
        imagePullPolicy: Always
        command: [
            "blobpipe-server",
            "--grpc-port",
            "9000",
            "--http-port",
            "8080",
            "--metrics-port",
            "9001",
            "--max-file-size-mb",
            "{{ get .Values.max_file_size .Values.cluster_name | default 1024 }}",
            "--max-buffers",
            "{{ get .Values.max_buffers .Values.cluster_name | default 2  }}",
            {{- $default_storage_config := get .Values.storage_configuration.tensor_logs "default" -}}
            {{- $cluster_config := get .Values.storage_configuration.tensor_logs .Values.cluster_name -}}
            {{- with $cluster_config | default $default_storage_config }}
            "--az-storage-account",
            "{{ tpl .storage_account $ }}",
            "--az-storage-container",
            "{{ tpl .storage_container $ }}",
            "--az-storage-prefix",
            "{{ tpl .storage_prefix $ }}",
            {{- end}}
            ]
        envFrom:
        - secretRef:
            name: azure-service-principal
        - configMapRef:
            name: azure-storage-account
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9000
          name: grpc
        - containerPort: 9001
          name: metrics
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        lifecycle:
          preStop:
            exec:
              command: ["/bin/bash", "-c", "sleep 120"]
        resources:
          limits:
            cpu: {{ .Values.cpu | quote }}
            memory: {{ .Values.memory | quote }}
            ephemeral-storage: {{ .Values.disk | quote }}
          requests:
            cpu: {{ .Values.cpu | quote }}
            memory: {{ .Values.memory | quote }}
            ephemeral-storage: {{ .Values.disk | quote }}
      priorityClassName: team-critical
      tolerations:
      - key: openai.com/team
        operator: Equal
        value: platform-services
        effect: NoSchedule

