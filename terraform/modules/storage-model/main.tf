resource "azurerm_resource_group" "rg" {
  location = var.location
  name     = "iridium-models-${var.location}"
  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      tags["oai-policy-in-scope"]
    ]
  }
}

# Initial implementation of model storage accounts for storing models
# This is not the way we will setup models in the future, but since we moved
# some models here it is preserved untill we finish setting up the correct
# way for storage accounts to store models
# TODO: Remove when we the models copied into new storage account
resource "azurerm_storage_account" "model_storage" {
  name                          = "iridiummodelssa${var.location}"
  resource_group_name           = azurerm_resource_group.rg.name
  location                      = azurerm_resource_group.rg.location
  account_tier                  = "Standard"
  account_replication_type      = "LRS"

  is_hns_enabled                   = true
  cross_tenant_replication_enabled = false
  allow_nested_items_to_be_public  = false
  shared_access_key_enabled        = false

  public_network_access_enabled    = true
  network_rules {
    default_action = "Deny"
    bypass         = ["None"]
    ip_rules       = var.builder-pool-ip-addresses
  }

  lifecycle {
    prevent_destroy = true
  }
}
data "azapi_resource" "model_storage_blob" {
    type = "Microsoft.Storage/storageAccounts/blobServices@2021-04-01"
    resource_id = "${azurerm_storage_account.model_storage.id}/blobServices/default"
}
module "model_storage_audit" {
  source = "../../modules/audit"
  target_resource_id = data.azapi_resource.model_storage_blob.id
  location           = var.location
}

# Moving forward we will create model storage accounts per region
# each tenant will have their own storage account for the models to be stored in
# This will allow us to have better control over the models and their access
resource "azurerm_storage_account" "cresco_model_store" {
  name                          = "irdcresco${var.location}"
  resource_group_name           = azurerm_resource_group.rg.name
  location                      = azurerm_resource_group.rg.location
  account_tier                  = "Standard"
  account_replication_type      = "LRS"

  cross_tenant_replication_enabled = false
  allow_nested_items_to_be_public  = false
  shared_access_key_enabled        = false

  public_network_access_enabled    = true
  network_rules {
    default_action = "Deny"
    bypass         = ["None"]
    ip_rules       = var.builder-pool-ip-addresses
  }

  lifecycle {
    prevent_destroy = true
  }
}
data "azapi_resource" "cresco_model_store_blob" {
    type = "Microsoft.Storage/storageAccounts/blobServices@2021-04-01"
    resource_id = "${azurerm_storage_account.cresco_model_store.id}/blobServices/default"
}
module "cresco_model_store_audit" {
  source = "../../modules/audit"
  target_resource_id = data.azapi_resource.cresco_model_store_blob.id
  location           = var.location
}

# Regional storage accounts, in the future they will be in each region
# Then we can replicate data into them from the global storage account
# This will be done using sciclone to follow the OAI architecture
resource "azurerm_storage_account" "regional_storage_account" {
  name                          = "iridiumoai${var.location}"
  resource_group_name           = azurerm_resource_group.rg.name
  location                      = azurerm_resource_group.rg.location
  account_tier                  = "Standard"
  account_replication_type      = "LRS"

  cross_tenant_replication_enabled = false
  allow_nested_items_to_be_public  = false
  shared_access_key_enabled        = false

  public_network_access_enabled    = true
  network_rules {
    default_action = "Deny"
    bypass         = ["None"]
    ip_rules       = var.builder-pool-ip-addresses
  }

  lifecycle {
    prevent_destroy = true
  }
}
