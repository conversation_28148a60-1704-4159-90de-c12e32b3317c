variable "location" {
  type        = string
  description = "Azure location for models storage resources"
}

variable "builder-pool-ip-addresses" {
  type        = set(string)
  description = "Ip address list for our build machines"
}

variable "private_dns_zones" {
  description = "A map of private DNS zones with their configurations"
  default     = {}
  type = map(object({
    short_name       = string
    resource_group   = string
    subresource_name = string
  }))
}

variable "virtual_networks" {
  description = "A list of virtual networks with their configurations"
  default     = {}
  type = map(object({
    virtual_network_id   = string
    virtual_network_name = string
    subnet_id            = string
    subnet_name          = string
    resource_group_name  = string
    location             = string
  }))
}
