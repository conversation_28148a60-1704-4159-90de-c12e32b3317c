data "azurerm_private_dns_zone" "private_dns_zone" {
  for_each            = var.private_dns_zones
  name                = each.key
  resource_group_name = each.value.resource_group
}

locals {
  # TODO: Please suggest a better way to validate this object
  allowed_subresource_names = { for v in ["blob", "queue", "table", "file"] : v => v }

  # Create a permutation map of private DNS zones per virtual network with their configurations
  private_dns_zone_virtual_network = { for x in flatten([
    for vent_key, vnet in var.virtual_networks : [
      for zone_key, dns in var.private_dns_zones : {
        key                      = "${vent_key}-${zone_key}"
        virtual_network_id       = vnet.virtual_network_id
        virtual_network_name     = vnet.virtual_network_name
        subnet_id                = vnet.subnet_id
        subnet_name              = vnet.subnet_name
        vent_rg_name             = vnet.resource_group_name
        virtual_network_location = vnet.location

        dns_zone_id         = data.azurerm_private_dns_zone.private_dns_zone[zone_key].id
        dns_zone_rg_name    = data.azurerm_private_dns_zone.private_dns_zone[zone_key].resource_group_name
        dns_zone_short_name = dns.short_name
        subresource_name    = local.allowed_subresource_names[dns.subresource_name]
      }
    ]
  ]) : x.key => x }
}

resource "azurerm_private_endpoint" "model_storage_private_endpoint" {
  for_each = local.private_dns_zone_virtual_network

  name                = "pe-model_storage-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
  location            = each.value.virtual_network_location
  resource_group_name = each.value.vent_rg_name
  subnet_id           = each.value.subnet_id

  private_service_connection {
    name                           = "conn-model_storage-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_connection_resource_id = azurerm_storage_account.model_storage.id
    subresource_names              = [each.value.subresource_name]
    is_manual_connection           = false
  }

  private_dns_zone_group {
    name                 = "dns-model_storage-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_dns_zone_ids = [each.value.dns_zone_id]
  }
}

resource "azurerm_private_endpoint" "cresco_model_store_private_endpoint" {
  for_each = local.private_dns_zone_virtual_network

  name                = "pe-cresco_model_store-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
  location            = each.value.virtual_network_location
  resource_group_name = each.value.vent_rg_name
  subnet_id           = each.value.subnet_id

  private_service_connection {
    name                           = "conn-cresco_model_store-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_connection_resource_id = azurerm_storage_account.cresco_model_store.id
    subresource_names              = [each.value.subresource_name]
    is_manual_connection           = false
  }

  private_dns_zone_group {
    name                 = "dns-cresco_model_store-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_dns_zone_ids = [each.value.dns_zone_id]
  }
}

resource "azurerm_private_endpoint" "regional_storage_account_private_endpoint" {
  for_each = local.private_dns_zone_virtual_network

  name                = "pe-regional_storage_account-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
  location            = each.value.virtual_network_location
  resource_group_name = each.value.vent_rg_name
  subnet_id           = each.value.subnet_id

  private_service_connection {
    name                           = "conn-regional_storage_account-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_connection_resource_id = azurerm_storage_account.regional_storage_account.id
    subresource_names              = [each.value.subresource_name]
    is_manual_connection           = false
  }

  private_dns_zone_group {
    name                 = "dns-regional_storage_account-${each.value.virtual_network_name}-${each.value.dns_zone_short_name}"
    private_dns_zone_ids = [each.value.dns_zone_id]
  }
}
