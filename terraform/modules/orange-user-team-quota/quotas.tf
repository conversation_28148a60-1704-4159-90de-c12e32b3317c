locals {
  skus = {
    gpu1 = {
      selector           = "metadata.node.openai.com/sku=gpu1,!openai.com/banned"
      resources_per_node = 8  # Each gpu1 node has 8 GPUs
    }
    cpu1 = {
      selector           = "metadata.node.openai.com/sku=cpu1,!openai.com/banned"
      // This is the vCPU count per node. For the uksouth 7..9 clusters,
      // the reported vCPUs are 14/14/29. Using the lowest value for now.
      // Until we find out if this matters at all.
      resources_per_node = 14  # How many resources per unit for cpu skus?
    }
    gpu11 = {
      selector           = "metadata.node.openai.com/sku=gpu11,!openai.com/banned"
      resources_per_node = 4  # Each gpu11 node has 4 GPUs (GB200)
    }    
  }

  // *****************
  // I M P O R T A N T:
  // We are moving away from the Terraform configuration flow for Perhonen quota. Now quota is configured via the Singularity control plane,
  // based on Capacity Reservations. Browse the CRs for a sample cluster here: 
  // https://sing-devops-enebecbfccgxg5b7.wus2.grafana.azure.com/d/a6623c2e-c691-4710-8d86-febca9141568/single-cluster-view?orgId=1&var-FederationId=azml-prod-southcentralus-hpe-2&var-CapacityId=HPE_BM_GPUH100_NvidiaGpu-Units&var-nodeName=All&from=now-2d&to=now&viewPanel=13896
  // After the transition completes, Terraform will manage only the placeholder Quota resources with zero quota for the "guest" teams in each cluster
  // (teams without an explicit quota assignment). See \terraform\modules\orange-cluster-guest-quota.
  // *****************

  // Specify quotas for each team, detailing allocations for every cluster.
  //
  // NOTE:
  // Team names must be in lowercase since they are used as `name` of quota resources in Kubernetes.
  // Kubernetes enforces lowercase name as per its guidelines:
  // https://kubernetes.io/docs/concepts/overview/working-with-objects/names/
  //
  // Quota definition:
  //  - count: number of nodes
  //  - priority: higher number means prioritized access to the healthy capacity and allowance for more usage over quota (by preemptable 
  //    jobs)
  // PM-managed source of truth: https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4583/Orange-Getting-Started
  //
  // Not all the quota defined here is enforced yet. It's meant for Perhonen and publishes to its configmap, but Brix makes the decision
  // about whether to do its own (legacy) scheduling or defer to Perhonen. We control this via the Quota resource. To opt out of Perhonen,
  // set this label:
  //    brix.openai.com/scheduler: brix
  team_quotas = {
  }

  cluster_names = distinct(flatten([
    for team, clusters in local.team_quotas : keys(clusters)
  ]))

  // Transform team quotas into the desired output structure for each cluster.
  // Group by cluster and compute resourceCount using sku multipliers.
  //
  // The team name is fixed as "Orange users" for all entries, aligning with
  // Perhonen's quota management approach. Subteam is utilized to enforce quotas.
  // Refer to the documentation for more details:
  // https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6018/Configuring-Perhonen-for-the-Orange-clusters
  formatted_team_allocations = {
    for cluster in local.cluster_names : cluster => [
      for team, team_clusters in local.team_quotas : {
        team = "Orange users"
        allocations = [
          for sku_name, quota in team_clusters[cluster] : {
            subteam       = team
            selector      = local.skus[sku_name].selector
            count         = quota.count
            resourceCount = quota.count * local.skus[sku_name].resources_per_node
            priority      = quota.priority
          }
        ]
      } if contains(keys(team_clusters), cluster)
    ]
  }

  # Calculate various statistics per team per cluster
  team_cluster_stats = {
    for team, clusters in local.team_quotas : team => {
      for cluster, quota_assignments in clusters : cluster => {
        total_gpu_count = sum([
          for sku, quota in quota_assignments : quota.count if !can(regex("cpu", lower(sku)))
        ])
        total_cpu_count = sum([
          for sku, quota in quota_assignments : quota.count if can(regex("cpu", lower(sku)))
        ])
        total_gpu_resources = sum([
          for sku, quota in quota_assignments : quota.count * try(local.skus[sku].resources_per_node, 1) if !can(regex("cpu", lower(sku)))
        ])
        total_cpu_resources = sum([
          for sku, quota in quota_assignments : quota.count * try(local.skus[sku].resources_per_node, 1) if can(regex("cpu", lower(sku)))
        ])
        total_resource_count = sum([
          for sku, quota in quota_assignments : quota.count * try(local.skus[sku].resources_per_node, 1)
        ])
        average_priority = length(quota_assignments) > 0 ? sum([
          for sku, quota in quota_assignments : quota.priority
        ]) / length(quota_assignments) : 0
        skus_allocated = length(quota_assignments)
      }
    }
  }

  # Calculate cluster-wide statistics
  cluster_stats = {
    for cluster in local.cluster_names : cluster => {
      teams_with_allocations = length([
        for team, clusters in local.team_quotas : team
        if lookup(clusters, cluster, null) != null
      ])
      total_gpu_allocations = sum([
        for team, clusters in local.team_quotas : 
          sum([for sku, quota in try(clusters[cluster], {}) : quota.count if !can(regex("cpu", lower(sku)))])
        if lookup(clusters, cluster, null) != null
      ])
      total_cpu_allocations = sum([
        for team, clusters in local.team_quotas : 
          sum([for sku, quota in try(clusters[cluster], {}) : quota.count if can(regex("cpu", lower(sku)))])
        if lookup(clusters, cluster, null) != null
      ])
      total_gpu_resources = sum([
        for team, clusters in local.team_quotas : 
          sum([for sku, quota in try(clusters[cluster], {}) : quota.count * try(local.skus[sku].resources_per_node, 1) if !can(regex("cpu", lower(sku)))])
        if lookup(clusters, cluster, null) != null
      ])
      total_cpu_resources = sum([
        for team, clusters in local.team_quotas : 
          sum([for sku, quota in try(clusters[cluster], {}) : quota.count * try(local.skus[sku].resources_per_node, 1) if can(regex("cpu", lower(sku)))])
        if lookup(clusters, cluster, null) != null
      ])
    }
  }
  
  # Add teams from teams.tf that don't have explicit quotas
  # This ensures all teams from teams.tf are represented
  all_teams = merge(
    # Start with existing team quotas
    { for team, clusters in local.team_quotas : team => clusters },
    
    # Add teams from teams.tf that don't have quotas defined
    { for team_name, team in var.cluster_access_groups : 
      team_name => {
        for cluster in team.cluster_access : cluster => {}
      } if lookup(local.team_quotas, team_name, null) == null
    }
  )
}
