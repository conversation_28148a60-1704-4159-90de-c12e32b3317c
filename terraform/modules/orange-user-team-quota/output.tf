output "orange-teams" {
  value = {
    for team_name, team_details in var.cluster_access_groups : team_name => {
      cluster_access         = team_details.cluster_access
      storage_access         = team_details.storage_access
      parent_security_groups = team_details.parent_security_groups
    }
  }
}

# MARK: Quota
# Output formatted allocations for each cluster
output "team_allocations" {
  value = local.formatted_team_allocations
  description = "Formatted team allocations per cluster in the required JSON structure"
}

# Output team statistics for each cluster
output "team_stats" {
  value = local.team_cluster_stats
  description = "Statistics for each team's allocations per cluster"
}

# Output cluster-wide statistics
output "cluster_stats" {
  value = local.cluster_stats
  description = "Aggregated statistics for each cluster"
}

# Output per-team total resource allocation across all clusters
output "team_total_resources" {
  value = {
    for team, clusters in local.team_quotas : team => {
      total_gpu_count = sum([
        for cluster, skus in clusters : sum([
          for sku, quota in skus : quota.count if !can(regex("cpu", lower(sku)))
        ])
      ])
      total_cpu_count = sum([
        for cluster, skus in clusters : sum([
          for sku, quota in skus : quota.count if can(regex("cpu", lower(sku)))
        ])
      ])
      total_gpu_resources = sum([
        for cluster, skus in clusters : sum([
          for sku, quota in skus : quota.count * try(local.skus[sku].resources_per_node, 1) if !can(regex("cpu", lower(sku)))
        ])
      ])
      total_cpu_resources = sum([
        for cluster, skus in clusters : sum([
          for sku, quota in skus : quota.count * try(local.skus[sku].resources_per_node, 1) if can(regex("cpu", lower(sku)))
        ])
      ])
      clusters_with_allocations = length(keys(clusters))
    }
  }
  description = "Total resource allocation for each team across all clusters"
}

# Output list of all teams that have quotas assigned
output "teams_with_quotas" {
  value = keys(local.team_quotas)
  description = "List of all teams that have quotas assigned"
}

# Output list of all teams that don't have quotas assigned
# IMPORTANT: teams_with_quotas and teams_without_quotas are used by terraform/orange-user-cluster-onboarding/users.tf to gather the names
# of all teams, to create zero-quota Quota resources to enable Perhonen to report (low-priority) usage for guest users. We'll need at least
# one of these variables still flown to the other TF configuration even after quota allocations are not managed through TF anymore.
output "teams_without_quotas" {
  value = distinct(flatten([
    for access_group_name, access_group in var.cluster_access_groups : [
      for team_name in access_group.parent_security_groups : team_name
      if !contains(keys(local.team_quotas), team_name)
    ]
  ]))
  description = "Teams that don't have quotas assigned"
}
