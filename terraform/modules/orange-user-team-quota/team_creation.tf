resource "azuread_group" "teams" {
  for_each = var.cluster_access_groups

  display_name     = each.key
  security_enabled = true // Enable security for the group
  owners = toset(concat(
    [for owner in each.value.owners : data.azuread_user.owners[owner].object_id],
    [data.azuread_client_config.current.object_id]
  ))
}

data "azuread_client_config" "current" {}

// Fetch Azure AD user details for all team owners
data "azuread_user" "owners" {
  for_each = toset(flatten([
    for team in var.cluster_access_groups : team.owners
  ]))

  user_principal_name = "${each.key}@green.microsoft.com"
}
