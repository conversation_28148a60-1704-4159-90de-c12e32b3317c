locals {
  white_list = {
    "77c6e7fb-afb4-4b32-ad8b-286b09b53ca4" = "bolian"
    "82c6a1dc-25f1-4356-bec0-46d8d3cba124" = "liuming"
    "ccbc6afb-80a9-4f70-a661-78a8b34801de" = "ragarg"
    "8704a234-c2a2-430c-81c6-307f68d142fb" = "aiailiji"
    "37efc4a7-8072-4ae9-b649-a874e1723ab6" = "divya"
    "30af7537-44b7-4df3-bd05-99bf9a29c0c2" = "swen"
    "45f4231d-4845-4984-8d24-ce8ec0b3e58e" = "vipulm"


    "6099caef-afe3-4128-8ceb-fd56ad72c7ad" = "nmecklenburg"
    "3fec39cf-2da7-4a2f-825f-68722e7f3b04" = "ashisjai"
    "2289ee2c-4a2b-4a8b-a1fe-6192111da404" = "marici"
    "080c545c-5c37-4a00-bce2-dd9cbcf2e6ad" = "savitam"
    "dc7b9b9f-1512-4e16-a7ad-d33998bb4510" = "srsaggam"
  }
}


resource "kubernetes_namespace" "manifold" {
  metadata {
    name = "manifold"
  }
}

resource "kubernetes_manifest" "selfsigned_issuer" {
  manifest = {
    apiVersion = "cert-manager.io/v1"
    kind       = "Issuer"
    metadata = {
      name      = "selfsigned-issuer"
      namespace = kubernetes_namespace.manifold.metadata[0].name
    }
    spec = {
      selfSigned = {}
    }
  }
}

resource "kubernetes_manifest" "job_mutator_webhook_cert" {
  manifest = {
    apiVersion = "cert-manager.io/v1"
    kind       = "Certificate"
    metadata = {
      name      = "job-mutator-webhook-cert"
      namespace = kubernetes_namespace.manifold.metadata[0].name
    }
    spec = {
      secretName = "job-mutator-webhook-cert"
      dnsNames = [
        "${kubernetes_manifest.job_mutator_service.manifest["metadata"]["name"]}.${kubernetes_namespace.manifold.metadata[0].name}.svc"
      ]
      issuerRef = {
        name = kubernetes_manifest.selfsigned_issuer.manifest["metadata"]["name"]
        kind = "Issuer"
      }
      usages = [
        "server auth",
        "key encipherment"
      ]
    }
  }
}


resource "kubernetes_manifest" "job_mutating_webhook_sa" {
  manifest = {
    apiVersion = "v1"
    kind       = "ServiceAccount"
    metadata = {
      name      = "job-mutating-webhook"
      namespace = kubernetes_namespace.manifold.metadata[0].name
    }
  }
}

resource "kubernetes_manifest" "job_mutator_clusterrole" {
  manifest = {
    apiVersion = "rbac.authorization.k8s.io/v1"
    kind       = "ClusterRole"
    metadata = {
      name = "manifold-job-mutator-role"
    }
    rules = [
      {
        apiGroups = [""]
        resources = ["secrets", "configmaps"]
        verbs     = ["get", "list", "watch", "create", "update", "patch", "delete"]
      },
      {
        apiGroups = ["batch"]
        resources = ["jobs"]
        verbs     = ["get", "list", "watch", "create", "update", "patch", "delete"]
      }
    ]
  }
}

resource "kubernetes_manifest" "job_mutator_rolebinding" {
  manifest = {
    apiVersion = "rbac.authorization.k8s.io/v1"
    kind       = "ClusterRoleBinding"
    metadata = {
      name = "job-mutator-sa-rolebinding"
    }
    subjects = [
      {
        kind      = "ServiceAccount"
        name      = kubernetes_manifest.job_mutating_webhook_sa.manifest["metadata"]["name"]
        namespace = kubernetes_namespace.manifold.metadata[0].name
      }
    ]
    roleRef = {
      kind     = "ClusterRole"
      name     = kubernetes_manifest.job_mutator_clusterrole.manifest["metadata"]["name"]
      apiGroup = "rbac.authorization.k8s.io"
    }
  }
}

resource "kubernetes_manifest" "job_mutator_clusterrolebinding" {
  manifest = {
    apiVersion = "rbac.authorization.k8s.io/v1"
    kind       = "ClusterRoleBinding"
    metadata = {
      name = "job-mutator"
    }
    subjects = [
      {
        kind      = "ServiceAccount"
        name      = kubernetes_manifest.job_mutating_webhook_sa.manifest["metadata"]["name"]
        namespace = kubernetes_namespace.manifold.metadata[0].name
      }
    ]
    roleRef = {
      kind     = "ClusterRole"
      name     = kubernetes_manifest.job_mutator_clusterrole.manifest["metadata"]["name"]
      apiGroup = "rbac.authorization.k8s.io"
    }
  }
}

resource "kubernetes_manifest" "job_mutator_webhook" {
  manifest = {
    apiVersion = "admissionregistration.k8s.io/v1"
    kind       = "MutatingWebhookConfiguration"
    metadata = {
      name = "job-mutator.k8s.io"
      annotations = {
        "cert-manager.io/inject-ca-from" = "${kubernetes_namespace.manifold.metadata[0].name}/job-mutator-webhook-cert"
      }
    }
    webhooks = [
      {
        name                    = "job-mutator.manifold.svc"
        admissionReviewVersions = ["v1"]
        objectSelector = {
          matchExpressions = [
            {
              key      = "AISC_JOB_INSTANCE_ID"
              operator = "Exists"
            }
          ]
        }
        clientConfig = {
          service = {
            name      = kubernetes_manifest.job_mutator_service.manifest["metadata"]["name"]
            namespace = kubernetes_namespace.manifold.metadata[0].name
            path      = "/mutate-jobs"
            port      = 443
          }
        }
        rules = [
          {
            operations  = ["CREATE"]
            apiGroups   = ["batch"]
            apiVersions = ["v1"]
            resources   = ["jobs"]
          }
        ]
        timeoutSeconds = 30
        failurePolicy  = "Fail"
        sideEffects    = "None"
      }
    ]
  }
}

resource "kubernetes_manifest" "job_mutator_deployment" {
  manifest = {
    apiVersion = "apps/v1"
    kind       = "Deployment"
    metadata = {
      name      = "job-mutator"
      namespace = kubernetes_namespace.manifold.metadata[0].name
      labels = {
        app = "job-mutator"
      }
    }
    spec = {
      replicas = 1
      selector = {
        matchLabels = {
          app = "job-mutator"
        }
      }
      template = {
        metadata = {
          labels = {
            app = "job-mutator"
          }
        }
        spec = {
          serviceAccountName = kubernetes_manifest.job_mutating_webhook_sa.manifest["metadata"]["name"]
          containers = [
            {
              name            = "webhook"
              image           = "iridiumsdc.azurecr.io/test/manifold-job-hook"
              imagePullPolicy = "Always"
              env = [
                {
                  name  = "CLUSTER_NAME"
                  value = var.cluster_name
                },
                {
                  name  = "NS_WHITELIST"
                  value = join(";", [for k, v in local.white_list : "${k}:${v}"])
                }
              ]
              ports = [
                {
                  containerPort = 9443
                }
              ]
              volumeMounts = [
                {
                  name      = "cert"
                  mountPath = "/tmp/k8s-webhook-server/serving-certs"
                  readOnly  = true
                }
              ]
            }
          ]
          volumes = [
            {
              name = "cert"
              secret = {
                secretName = kubernetes_manifest.job_mutator_webhook_cert.manifest["spec"]["secretName"]
              }
            }
          ]
        }
      }
    }
  }
}

resource "kubernetes_manifest" "job_mutator_service" {
  manifest = {
    apiVersion = "v1"
    kind       = "Service"
    metadata = {
      name      = "job-mutator-service"
      namespace = kubernetes_namespace.manifold.metadata[0].name
      labels = {
        app = "job-mutator"
      }
    }
    spec = {
      selector = {
        app = "job-mutator"
      }
      ports = [
        {
          port       = 443
          targetPort = 9443
        }
      ]
    }
  }
}
