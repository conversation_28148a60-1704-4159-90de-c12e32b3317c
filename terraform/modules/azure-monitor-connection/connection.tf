locals {
  location = var.loganalytics_location != null ? var.loganalytics_location : var.cluster.location
}

data "azurerm_resource_group" "rg" {
  provider = azurerm.common
  name     = "azuremonitor-${local.location}"
}

data "azurerm_monitor_workspace" "azuremonitorworkspace" {
  # Currently the name and resource group is by convention
  provider            = azurerm.common
  name                = "azuremonitorworkspace-${local.location}"
  resource_group_name = data.azurerm_resource_group.rg.name
}

resource "azurerm_monitor_data_collection_endpoint" "dce" {
  name                = "MSProm-${data.azurerm_resource_group.rg.location}-${var.cluster.name}"
  resource_group_name = var.cluster.resource_group
  location            = data.azurerm_resource_group.rg.location
  kind                = "Linux"
}

resource "azurerm_monitor_data_collection_rule" "dcr" {
  name                        = "MSProm-${data.azurerm_resource_group.rg.location}-${var.cluster.name}"
  resource_group_name         = var.cluster.resource_group
  location                    = data.azurerm_resource_group.rg.location
  data_collection_endpoint_id = azurerm_monitor_data_collection_endpoint.dce.id
  kind                        = "Linux"

  destinations {
    monitor_account {
      monitor_account_id = data.azurerm_monitor_workspace.azuremonitorworkspace.id
      name               = "MonitoringAccount1"
    }
  }

  data_flow {
    streams      = ["Microsoft-PrometheusMetrics"]
    destinations = ["MonitoringAccount1"]
  }

  data_sources {
    prometheus_forwarder {
      streams = ["Microsoft-PrometheusMetrics"]
      name    = "PrometheusDataSource"
    }
  }

  description = "DCR for Azure Monitor Metrics Profile (Managed Prometheus)"
  depends_on = [
    azurerm_monitor_data_collection_endpoint.dce
  ]
}

resource "azurerm_monitor_data_collection_rule_association" "dcra" {
  name                    = "MSProm-${data.azurerm_resource_group.rg.location}-${var.cluster.name}"
  target_resource_id      = var.cluster.id
  data_collection_rule_id = azurerm_monitor_data_collection_rule.dcr.id
  description             = "Association of data collection rule. Deleting this association will break the data collection for this AKS Cluster."
  depends_on = [
    azurerm_monitor_data_collection_rule.dcr
  ]
}


## Log Analytics integration
data "azurerm_log_analytics_workspace" "loganalytics" {
  provider            = azurerm.common
  name                = "loganalytics-${local.location}"
  resource_group_name = data.azurerm_resource_group.rg.name
}

resource "azurerm_monitor_data_collection_rule" "dcr-loganalytics" {
  name                = "LogAnalytics-${data.azurerm_resource_group.rg.location}-${var.cluster.name}"
  resource_group_name = var.cluster.resource_group
  location            = data.azurerm_resource_group.rg.location
  kind                = "Linux"

  destinations {
    log_analytics {
      workspace_resource_id = data.azurerm_log_analytics_workspace.loganalytics.id
      name                  = "LogAnalytics1"
    }
  }

  data_flow {
    streams      = ["Microsoft-ContainerInsights-Group-Default"]
    destinations = ["LogAnalytics1"]
  }


  data_sources {
    extension {
      # The same settings that is created when enabling the log analytics extension for AKS
      streams        = ["Microsoft-ContainerInsights-Group-Default"]
      extension_name = "ContainerInsights"
      extension_json = jsonencode({
        "dataCollectionSettings" : {
          "interval" : "1m",
          "namespaceFilteringMode" : "Off",
          "enableContainerLogV2" : true
        }
      })
      name = "ContainerInsightsExtension"
    }
  }

  description = "DCR for Azure Log Analytics"
}

resource "azurerm_monitor_data_collection_rule_association" "dcra-loganalytics" {
  name                    = "LogAnalytics-${data.azurerm_resource_group.rg.location}-${var.cluster.name}"
  target_resource_id      = var.cluster.id
  data_collection_rule_id = azurerm_monitor_data_collection_rule.dcr-loganalytics.id
  description             = "Association of data collection rule. Deleting this association will break the data collection for this AKS Cluster."
  depends_on = [
    azurerm_monitor_data_collection_rule.dcr-loganalytics
  ]
}
