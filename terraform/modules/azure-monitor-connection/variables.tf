variable "cluster" {
  type = object({
    name           = string
    id             = string
    location       = string
    resource_group = string
  })
  description = "The AKS cluster"
}

variable "loganalytics_location" {
  type        = string
  description = "The location of the Log Analytics workspace"
  default     = null
}

variable "grafana_workspace_name" {
  type        = string
  description = "The name of the Grafana workspace"
  default     = "" 
}