# This assumes there is already a storage account and container are already created

locals {
  user_map = {
    for username, user_attrs in var.users : user_attrs.storage_acount_name => {
      home    = "az://${user_attrs.storage_acount_name}"
      homes   = { "${user_attrs.storage_acount_region}" = "az://${user_attrs.storage_acount_name}" }
      regions = { "${user_attrs.storage_acount_region}" = "az://${user_attrs.storage_acount_name}" }
      type    = "storage-set"
    }
  }

  template = jsondecode(file("${path.module}/template/storage-map.json"))

  # Merge user_map into the 'sources' key of the template
  storage_map = merge(
    local.template,
    {
      sources = merge(
        local.template.sources, // Original sources from the template
        local.user_map          // Add user_map to sources
      )
    }
  )
}

data "azurerm_storage_account" "storage_account" {
  name                = var.storage_map.storage_account_name
  resource_group_name = var.storage_map.storage_resource_group
}

data "azurerm_storage_container" "storage_map" {
  name                 = "storage-map"
  storage_account_name = data.azurerm_storage_account.storage_account.name
}


resource "azurerm_role_assignment" "read-storage-map" {
  for_each = var.users

  scope                = data.azurerm_storage_account.storage_account.id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = each.value.app_object_id

}

data "json-formatter_format_json" "storage_map" {
  json = jsonencode(local.storage_map)
}

resource "azurerm_storage_blob" "storage_map" {
  name                   = "storage-map.json"
  storage_account_name   = data.azurerm_storage_account.storage_account.name
  storage_container_name = data.azurerm_storage_container.storage_map.name

  content_type   = "application/json"
  type           = "Block"
  source_content = data.json-formatter_format_json.storage_map.result
}

