resource "kubernetes_namespace" "ns" {
  metadata {
    name = "brix-bastion"
  }
}


locals {
  chart = "brix-bastion"
  yamlparam = yamlencode({
    image = {
      repository = "iridiumsdc.azurecr.io/brix/brix-bastion"
      # fork build branch https://dev.azure.com/project-argos/Mimco/_git/brix?path=%2F&version=GBbolian%2Fmsproxy&_a=contents
      tag        = "v0.16.4-577184de-dirty-cloudtest-amd64"
    }
    # see brix/pkg/bastion/azure/auth_dialer.go
    azure = {
      tenantId       = var.tenant_id
      applicationUri = "api://brix-bastion-aad-oauth-dev-${var.cluster_name}/"
      applicationId  = var.aad_app_id
    }

    domainName = "bastion.brix.${var.cluster_name}.dev.openai.org"
  })

}

resource "helm_release" "brix-bastion" {
  name       = "brix-bastion"
  namespace  = kubernetes_namespace.ns.id
  repository = "${path.module}/charts"
  chart      = local.chart


  values = [
    local.yamlparam,
    yamlencode({ hash = sha1(join("", [
      for f in fileset("${path.module}/charts/${local.chart}", "**") : filesha256("${path.module}/charts/${local.chart}/${f}")])
    ) })
  ]
}
