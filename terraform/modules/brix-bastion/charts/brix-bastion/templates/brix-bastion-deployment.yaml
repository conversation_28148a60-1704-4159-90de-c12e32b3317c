apiVersion: apps/v1
kind: Deployment
metadata:
  name: brix-bastion
  namespace: {{ .Release.Namespace }}
  labels:
    app: brix
    component: bastion
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: brix
      component: bastion
  template:
    metadata:
      labels:
        app: brix
        component: bastion
    spec:
      affinity: 
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: kubernetes.azure.com/mode
                operator: In
                values:
                - system
            weight: 100
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.azure.com/cluster
                operator: Exists
              - key: type
                operator: NotIn
                values:
                - virtual-kubelet
              - key: kubernetes.io/os
                operator: In
                values:
                - linux        
      serviceAccountName: brix-bastion
      containers:
      - name: main
        image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
        imagePullPolicy: Always
        args:
        # The following command is for testing purposes only.
        command:
        - "/opt/brix-bastion"
        - "--azure-tenant-id"
        - {{ .Values.azure.tenantId }}
        - "--azure-application-uri"
        - {{ .Values.azure.applicationUri }}
        - "--azure-application-id"
        - {{ .Values.azure.applicationId }}
        - "--trace"
        ports:
        - containerPort: 10000
          name: http
        - containerPort: 1080
          name: socks5
      restartPolicy: Always
