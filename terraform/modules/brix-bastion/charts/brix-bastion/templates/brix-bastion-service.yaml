apiVersion: v1
kind: Service
metadata:
  name: brix-bastion
  namespace: {{ .Release.Namespace }}
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
    # see def here terraform/modules/stage-apps/ingress.tf:26~27
    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "brix-ing-subnet"  
    external-dns.alpha.kubernetes.io/hostname: {{ .Values.domainName }}
  labels:
    app: brix
    component: bastion
spec:
  type: LoadBalancer
  selector:
    app: brix
    component: bastion
  ports:
  - name: http
    port: 10000
  - name: socks5
    port: 1080
