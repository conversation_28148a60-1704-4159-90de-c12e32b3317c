# Sentinel data collection rule
resource "azurerm_monitor_data_collection_rule" "sentinel_dcr" {
  name                = "dataCollectionRule1"
  resource_group_name = var.sentinel_config.dcr_resource_group
  location            = var.sentinel_config.dcr_location

  data_sources {
    syslog {
      streams = [
        "Microsoft-Syslog"
      ]
      facility_names = [
        "auth",
        "authpriv",
        "cron",
        "daemon",
        "mark",
        "kern",
        "local0",
        "local1",
        "local2",
        "local3",
        "local4",
        "local5",
        "local6",
        "local7",
        "lpr",
        "mail",
        "news",
        "syslog",
        "user",
        "uucp"
      ]
      log_levels = [
        "Debug",
        "Info",
        "Notice",
        "Warning",
        "Error",
        "Critical",
        "Alert",
        "Emergency"
      ]
      name = "sysLogsDataSource"
    }
  }

  destinations {
    log_analytics {
      workspace_resource_id = var.sentinel_config.log_analytics_workspace_id
      name                  = "sentinel-log-analytics-workspace"
    }
  }

  data_flow {
    streams = [
      "Microsoft-Syslog"
    ]
    destinations = [
      "sentinel-log-analytics-workspace"
    ]
  }
}
