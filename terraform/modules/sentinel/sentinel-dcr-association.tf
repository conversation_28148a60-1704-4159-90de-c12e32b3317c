data "azurerm_virtual_machine_scale_set" "node_pool" {
  for_each            = var.sentinel_config.unmanaged_gpu_node_pool_names
  name                = each.value
  resource_group_name = var.sentinel_config.unmanaged_gpu_node_pools_resource_group
}

resource "azurerm_monitor_data_collection_rule_association" "sentinel-dcr-association" {
  for_each                = data.azurerm_virtual_machine_scale_set.node_pool
  name                    = "sentinel-dcr-association-${each.key}"
  target_resource_id      = each.value.id
  data_collection_rule_id = azurerm_monitor_data_collection_rule.sentinel_dcr.id
}
