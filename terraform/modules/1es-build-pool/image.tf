resource "azapi_resource" "pool_image" {
  depends_on = [
    azurerm_role_assignment.network_contributor_assignment_1es,
    azurerm_role_assignment.reader_assignment_1es,
  ]

  type      = "Microsoft.CloudTest/images@2020-05-07"
  name      = "${var.pool_name}-image-v1.0.0"
  location  = var.pool_location
  parent_id = azurerm_resource_group.build_pool.id

  schema_validation_enabled = false
  body = {
    properties = {
      imageType = "Managed"
      baseImage = var.base_image
      artifacts = [
        { name = "linux-azcli" },
        { name = "linux-install-packages-standalone", parameters = { packages = "jq build-essential pigz unzip just" } },
        { name = "linux-install-docker" },
        { name = "linux-docker-use-ssd" },
        { name = "linux-kubelogin" },
        { name = "linux-bash-command", parameters = { "command" = "curl -fsSL https://tailscale.com/install.sh | sh" } },
        { name = "linux-disable-unattendedupgrades" }
      ]
      publishingProfile = {
        targetRegions = [
          { name = var.pool_location, replicas = 2 }
        ]
      }
      scheduleProfile = {
        # Run every Wednesday at 6:00 AM
        cronExpression = "0 6 * * 3"
      }
    }
  }

  timeouts {
    create = "2h"
    update = "2h"
  }
}
