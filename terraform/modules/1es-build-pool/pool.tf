resource "azapi_resource" "hosted_pool" {
  depends_on = [
    azapi_resource.pool_image,
    azurerm_user_assigned_identity.user_identity,
  ]
  type                      = "Microsoft.CloudTest/hostedpools@2020-05-07"
  name                      = var.pool_name
  location                  = var.pool_location
  parent_id                 = azurerm_resource_group.build_pool.id
  schema_validation_enabled = false

  body = {
    identity = {
      type = "UserAssigned"
      userAssignedIdentities = {
        (azurerm_user_assigned_identity.user_identity.id) = {}
      }
    }
    properties = {
      organization = var.organization
      projects     = var.projects
      contacts     = var.contacts
      sku = {
        tier = "Standard"
        name = var.pool_sku
      }
      images = [
        {
          imageName            = azapi_resource.pool_image.name
          poolBufferPercentage = "100"
        }
      ]
      vmProviderProperties = {
        vssAdminPermissions = "CreatorOnly"
      }
      maxPoolSize = var.max_pool_size
      agentProfile = {
        type = "Stateless"
      }
      networkProfile = {
        subnetId = azurerm_subnet.build_pool_subnet.id
      }
    }
  }
}
