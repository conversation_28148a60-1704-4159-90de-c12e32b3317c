resource "azurerm_public_ip" "nat_gateway_ip" {
  count               = var.enable_nat_gateway ? 1 : 0
  name                = "natgateway-ip"
  location            = var.pool_location
  resource_group_name = azurerm_resource_group.build_pool.name
  allocation_method   = "Static"
  sku                 = "Standard"
}

resource "azurerm_nat_gateway" "pool_nat_gateway" {
  count               = var.enable_nat_gateway ? 1 : 0
  name                = "pool-nat-gateway"
  location            = var.pool_location
  resource_group_name = azurerm_resource_group.build_pool.name

  sku_name = "Standard"
}

resource "azurerm_nat_gateway_public_ip_association" "nat_gateway_ip_association" {
  count                = var.enable_nat_gateway ? 1 : 0
  nat_gateway_id       = azurerm_nat_gateway.pool_nat_gateway[0].id
  public_ip_address_id = azurerm_public_ip.nat_gateway_ip[0].id
}

resource "azurerm_subnet_nat_gateway_association" "subnet_nat_gateway" {
  count          = var.enable_nat_gateway ? 1 : 0
  subnet_id      = azurerm_subnet.build_pool_subnet.id
  nat_gateway_id = azurerm_nat_gateway.pool_nat_gateway[0].id
}
