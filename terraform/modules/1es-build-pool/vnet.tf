# create a vnet and subnet for the build pool
resource "azurerm_virtual_network" "build_pool_vnet" {
  name                = "${var.pool_name}-vnet"
  resource_group_name = azurerm_resource_group.build_pool.name
  location            = azurerm_resource_group.build_pool.location
  address_space       = ["********/16"]
}

resource "azurerm_subnet" "build_pool_subnet" {
  name                 = "${var.pool_name}-subnet"
  resource_group_name  = azurerm_resource_group.build_pool.name
  virtual_network_name = azurerm_virtual_network.build_pool_vnet.name
  address_prefixes     = ["********/24"]

  delegation {
    name = "${var.pool_name}-subnet-delegation"

    service_delegation {
      name = "Microsoft.CloudTest/hostedpools"
      actions = [
        "Microsoft.Network/virtualNetworks/subnets/join/action"
      ]
    }
  }
}

resource "azurerm_subnet" "build_pool_resources_subnet" {
  name                 = "${var.pool_name}-resources-subnet"
  resource_group_name  = azurerm_resource_group.build_pool.name
  virtual_network_name = azurerm_virtual_network.build_pool_vnet.name
  address_prefixes     = ["********/24"]
}
