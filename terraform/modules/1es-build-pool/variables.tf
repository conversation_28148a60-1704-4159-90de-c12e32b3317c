variable "pool_name" {
  description = "The name of the hosted pool."
  type        = string
}

variable "pool_location" {
  description = "The Azure region of the hosted pool."
  type        = string
}

variable "max_pool_size" {
  description = "The maximum size of the hosted pool."
  type        = number
}

variable "base_image" {
  description = "The managed base image for the pool."
  type        = string
}

variable "pool_sku" {
  description = "The SKU of the hosted pool."
  type        = string
}

variable "organization" {
  description = "The organization of the hosted pool."
  type        = string
}

variable "projects" {
  description = "The projects of the hosted pool."
  type        = set(string)
}

variable "contacts" {
  description = "The contacts of the hosted pool. Corp email addresses."
  type        = set(string)
  default     = []
}

variable "storage_private_endpoints" {
  description = "A list of private endpoints for storage accounts."
  type = list(object({
    name                 = string
    storage_account_id   = string
    is_manual_connection = bool
  }))
  default = []
}

variable "keyvault_private_endpoints" {
  description = "A list of private endpoints for Key Vaults."
  type = list(object({
    name                 = string
    keyvault_id          = string
    is_manual_connection = bool
  }))
  default = []
}

variable "snowflake_private_endpoint" {
  description = "A private endpoint for the snowflake account."
  type = object({
    name                 = string
    snowflake_id         = string
    is_manual_connection = bool
  })
  default = null
}

variable "roles_assignments" {
  description = "A list of role assignments to be applied to the pool."
  type = list(object({
    role         = string
    scope        = string
  }))
  default = []
}

variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for the pool."
  type        = bool
  default     = false
}

variable "wandb_private_endpoint" {
  description = "A private endpoint for the wandb instance."
  type = object({
    name                             = string
    private_link_resource_id         = string
    is_manual_connection             = bool
  })
  default = null
}