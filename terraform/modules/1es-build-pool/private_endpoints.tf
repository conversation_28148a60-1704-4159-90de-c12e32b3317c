resource "azurerm_private_endpoint" "storage" {
  for_each = { for pe in var.storage_private_endpoints : pe.name => pe }

  name                = each.value.name
  resource_group_name = azurerm_resource_group.build_pool.name
  location            = azurerm_resource_group.build_pool.location
  subnet_id           = azurerm_subnet.build_pool_resources_subnet.id

  private_service_connection {
    name                           = "${each.value.name}-conn"
    private_connection_resource_id = each.value.storage_account_id
    is_manual_connection           = each.value.is_manual_connection
    subresource_names              = ["blob"] # Change based on your need (e.g., blob, file, table, queue)
  }

  private_dns_zone_group {
    name                 = "${var.pool_name}-${each.value.name}-dns"
    private_dns_zone_ids = [azurerm_private_dns_zone.private-dns_zones["blob"].id]
  }
}


resource "azurerm_private_endpoint" "keyvault" {
  for_each = { for pe in var.keyvault_private_endpoints : pe.name => pe }

  name                = each.value.name
  resource_group_name = azurerm_resource_group.build_pool.name
  location            = azurerm_resource_group.build_pool.location
  subnet_id           = azurerm_subnet.build_pool_resources_subnet.id

  private_service_connection {
    name                           = "${each.value.name}-conn"
    private_connection_resource_id = each.value.keyvault_id
    is_manual_connection           = each.value.is_manual_connection
    subresource_names              = ["vault"]
  }

  private_dns_zone_group {
    name                 = "${var.pool_name}-${each.value.name}-dns"
    private_dns_zone_ids = [azurerm_private_dns_zone.private-dns_zones["vault"].id]
  }
}

resource "azurerm_private_endpoint" "snowflake" {
  count = var.snowflake_private_endpoint == null ? 0 : 1

  name                = var.snowflake_private_endpoint.name
  resource_group_name = azurerm_resource_group.build_pool.name
  location            = azurerm_resource_group.build_pool.location
  subnet_id           = azurerm_subnet.build_pool_resources_subnet.id

  private_service_connection {
    name                              = "${var.snowflake_private_endpoint.name}-conn"
    private_connection_resource_alias = var.snowflake_private_endpoint.snowflake_id
    is_manual_connection              = var.snowflake_private_endpoint.is_manual_connection
    request_message                   = "Private-endpoint connection request"

  }

  private_dns_zone_group {
    name                 = "${var.pool_name}-${var.snowflake_private_endpoint.name}-dns"
    private_dns_zone_ids = [azurerm_private_dns_zone.private-dns_zones["snowflake"].id]
  }
}

resource "azurerm_private_endpoint" "wandb" {
  count = var.wandb_private_endpoint == null ? 0 : 1
  
  name                = var.wandb_private_endpoint.name
  location            = azurerm_resource_group.build_pool.location
  resource_group_name = azurerm_resource_group.build_pool.name
  subnet_id           = azurerm_subnet.build_pool_resources_subnet.id

  private_service_connection {
    name                           = "${var.wandb_private_endpoint.name}-conn"
    private_connection_resource_id = var.wandb_private_endpoint.private_link_resource_id
    is_manual_connection           = var.wandb_private_endpoint.is_manual_connection
    subresource_names              = [module.global.private_dns_zones["wandb"].subresource_name]
    request_message                = "Requesting Private Link connection for Application Gateway"
  }

  private_dns_zone_group {
    name                 = "${var.pool_name}-${var.wandb_private_endpoint.name}-dns"
    private_dns_zone_ids = [azurerm_private_dns_zone.private-dns_zones["wandb"].id]
  }
}