// A map of tenant ids to the 1ES managment service principal ids
locals {
  tenant_to_1es_management_sp = {
    // Green tenant
    "8b9ebe14-d942-49e7-ace9-14496d0caff0" = "41220d26-5f52-4c68-b849-95e5b06dc641"
    // AME tenant
    "33e01921-4d64-4f8c-a055-5bdaffd5e33d" = "5a152836-f9e8-4817-992a-5e09dfa87aab"
  }
}

data "azurerm_client_config" "current" {}

resource "azurerm_role_assignment" "reader_assignment_1es" {
  scope                = azurerm_virtual_network.build_pool_vnet.id
  role_definition_name = "Reader"
  principal_id         = local.tenant_to_1es_management_sp[data.azurerm_client_config.current.tenant_id]
}

resource "azurerm_role_assignment" "network_contributor_assignment_1es" {
  scope                = azurerm_virtual_network.build_pool_vnet.id
  role_definition_name = "Network Contributor"
  principal_id         = local.tenant_to_1es_management_sp[data.azurerm_client_config.current.tenant_id]
}
