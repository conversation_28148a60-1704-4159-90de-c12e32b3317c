resource "azurerm_role_assignment" "role_assignments" {
  # Create one assignment for each item in the roles_assignments list
  for_each = {
    for ra in var.roles_assignments :
    # Use a unique key for each element, e.g. "<scope>_<role>"
    "${ra.scope}_${ra.role}" => ra
  }

  scope                = each.value.scope
  role_definition_name = each.value.role
  principal_id         = azurerm_user_assigned_identity.user_identity.principal_id
}

# Give the pool managed identity network contributor access to its resource group
# The builder sometimes needs to create private endpoints and DNS entries for new resources while applying.
# See the orange-builder-access module.
resource "azurerm_role_assignment" "network_contributor_assignment" {
  scope                = azurerm_resource_group.build_pool.id
  role_definition_name = "Network Contributor"
  principal_id         = azurerm_user_assigned_identity.user_identity.principal_id
}
