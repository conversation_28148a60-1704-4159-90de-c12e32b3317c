output "pool_id" {
  value = azapi_resource.hosted_pool.id
}

output "pool_vnet_id" {
  value = azurerm_virtual_network.build_pool_vnet.id
}

output "pool_subnet_id" {
  value = azurerm_subnet.build_pool_subnet.id
}

output "pool_ip" {
  value = var.enable_nat_gateway ? azurerm_public_ip.nat_gateway_ip[0].ip_address : ""
}

output "resource_group_name" {
  value = azurerm_resource_group.build_pool.name
}

output "resources_subnet_id" {
  value = azurerm_subnet.build_pool_resources_subnet.id
}

output "msi_principal_id" {
  value = azurerm_user_assigned_identity.user_identity.principal_id
}