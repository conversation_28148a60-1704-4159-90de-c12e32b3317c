# 1ES Build Pool

A generic module to provision a stateless [1ES hosted Azure DevOps pool](https://eng.ms/docs/cloud-ai-platform/developer-services/one-engineering-system-1es/1es-docs/1es-hosted-azure-devops-pools/overview) to automate build/release processes in an Azure DevOps project.

## Note on `azapi` Provider

This module uses the `azapi` provider because the `Microsoft.CloudTest` resources are internal-only and are not available in the `azurerm` provider. The `azapi` provider complements the `azurerm` provider by enabling the management of Azure resources that are not yet or may never be supported in the `azurerm` provider, including private/public preview services and features. For more details, see the [azapi provider documentation](https://registry.terraform.io/providers/Azure/azapi/latest/docs).

## Resources

The module provisions the following resources:

1. **Resource Group**: Contains all the pool and associated resources. The resource group name is based on the pool name.
2. **[1ES Managed Image](https://eng.ms/docs/cloud-ai-platform/developer-services/one-engineering-system-1es/1es-docs/1es-managed-images/overview)**: Pre-configured with build pipeline requirements.
3. **[Stateless 1ES Hosted Azure DevOps Pool](https://eng.ms/docs/cloud-ai-platform/developer-services/one-engineering-system-1es/1es-docs/1es-hosted-azure-devops-pools/overview)**: Provisioned using the 1ES managed image and connected to the specified Azure DevOps project.
4. **User-Managed Identity**: Assigned to the pool machines for secure operations.
5. **Virtual Network and Subnet**: A virtual network and a subnet delegated to the 1ES hosted pool.
6. **Public IP Address**: Associated with the hosted pool subnet to route all outbound traffic.
7. **Managed Gateway**: Associated with the subnet and public IP address.


### Note on using AzA

## Pool Customizations

The image is based on **Ubuntu 22.04** and enables the following customizations:

### Azure CLI Installation

- Provides tools for managing Azure resources directly from the pool machines.

### Docker Support

- Includes Docker installation and configuration to utilize the machine's SSD if available, improving container performance.

### Essential Packages

- Pre-installs critical development and runtime packages such as `jq`, `build-essential`, `pigz`, and `unzip`.

### Kubernetes Support

- Installs `kubelogin` to enable authentication with Kubernetes clusters.

### Tailscale Installation

- Installs Tailscale during image setup to enable secure networking:

    ```bash
    curl -fsSL https://tailscale.com/install.sh | sh
    ```

### Disabling Unattended Upgrades

- Disables unattended upgrades to prevent pipeline disruptions caused by apt locks during package installations.

### Image Updates

The image is rebuilt and refreshed every Wednesday at 6:00 AM to ensure all tools and packages remain up-to-date.

### Enabling Artifacts

These customizations are powered by artifacts, which are reusable components that simplify the image setup process. For more details, refer to the [1ES Managed Images artifacts documentation](https://eng.ms/docs/cloud-ai-platform/devdiv/one-engineering-system-1es/1es-docs/1es-managed-images/artifacts).

### Important Notes

**1ES Image Creation Requirements:**

1ES image creation requires **100 cores** to be available in the `StandardDSv3Family`. If the cores are unavailable, the deployment may fail with an error similar to the following:

```powershell
The deployment 'xxx' failed with error(s). Showing 1 out of 1 error(s). Status Message: The request has been completed with result Failed. Please check details with more information. (Code: )  - SKU family standardDSv3Family is not available in location westus2! 1ES will work with Azure capacity team to get the SKU whitelisted. Click the 'Follow' button to track progress at https://mseng.visualstudio.com/Domino/_workitems/edit/1974240. You can choose to use a different region if you are able to, otherwise no action is necessary from you at the moment. (Code: FailedToRetrieveCoresLimit)    CorrelationId: 4d3e3034-6d0c-4639-ad5a-1db7b0534f5e
```
