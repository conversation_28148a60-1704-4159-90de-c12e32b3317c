module "global" {
  source = "../global_settings"
}

resource "azurerm_private_dns_zone" "private-dns_zones" {
  for_each            = module.global.private_dns_zones
  name                = each.value.name
  resource_group_name = azurerm_resource_group.build_pool.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "link-private-dns_zones" {
  depends_on = [azurerm_private_dns_zone.private-dns_zones]
  for_each   = module.global.private_dns_zones
  name                  = "link-${each.value.short_name}"
  resource_group_name   = azurerm_resource_group.build_pool.name
  virtual_network_id    = azurerm_virtual_network.build_pool_vnet.id
  private_dns_zone_name = each.value.name
}

# add records for the snowflake private endpoint
resource "azurerm_private_dns_a_record" "snowflake_a_records" {
  for_each = toset(values(module.global.private_dns_zones["snowflake"].snowflake_a_records))

  name                = each.value
  zone_name           = module.global.private_dns_zones["snowflake"].name
  resource_group_name = azurerm_resource_group.build_pool.name
  ttl                 = 300
  records             = [azurerm_private_endpoint.snowflake[0].private_service_connection[0].private_ip_address]

  # Make explicit
  depends_on = [azurerm_private_dns_zone.private-dns_zones]
}


# add records for the snowflake private endpoint
resource "azurerm_private_dns_a_record" "wandb_a_records" {
  name                = module.global.private_dns_zones["wandb"].dns_name_a_record
  zone_name           = module.global.private_dns_zones["wandb"].name
  resource_group_name = azurerm_resource_group.build_pool.name
  ttl                 = 300
  records             = [azurerm_private_endpoint.wandb[0].private_service_connection[0].private_ip_address]
  # Make explicit
  depends_on = [azurerm_private_dns_zone.private-dns_zones]
}


moved {
  from = azurerm_private_dns_zone.azure-storage
  to   = azurerm_private_dns_zone.private-dns_zones["blob"]
}

moved {
  from = azurerm_private_dns_zone_virtual_network_link.azure-storage
  to   = azurerm_private_dns_zone_virtual_network_link.link-private-dns_zones["blob"]
}

moved {
  from = azurerm_private_dns_zone.azure-key-vault
  to   = azurerm_private_dns_zone.private-dns_zones["vault"]
}

moved {
  from = azurerm_private_dns_zone_virtual_network_link.azure-key-vault
  to   = azurerm_private_dns_zone_virtual_network_link.link-private-dns_zones["vault"]
}
