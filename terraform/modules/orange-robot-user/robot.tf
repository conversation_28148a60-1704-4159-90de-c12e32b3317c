locals {
  pool_name            = "${var.robot_name}-robot-builder"
  tailscale_vault_name = "orangetailscalekeys"
  tailscale_vault_id   = "/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a/resourceGroups/orange-tailscale/providers/Microsoft.KeyVault/vaults/orangetailscalekeys"
}

module "robot-builder" {
  source = "../1es-build-pool"

  pool_name     = local.pool_name
  pool_location = "eastus2"

  max_pool_size = 5
  base_image    = "/canonical/ubuntu-24_04-lts/server/latest"
  pool_sku      = "Standard_D8ds_v4"

  organization       = "https://dev.azure.com/project-argos"
  projects           = ["Mimco"]
  contacts           = ["<EMAIL>"]
  enable_nat_gateway = false

  # Access to orangetailscalekeys is needed for the pipeline to connect as an ephemeral tailscale node

  keyvault_private_endpoints = [
    {
      name                 = local.tailscale_vault_name
      keyvault_id          = local.tailscale_vault_id
      is_manual_connection = false
    }
  ]

  roles_assignments = [
    {
      scope = local.tailscale_vault_id
      role  = "Key Vault Secrets User"
    }
  ]

  storage_private_endpoints = []
}
