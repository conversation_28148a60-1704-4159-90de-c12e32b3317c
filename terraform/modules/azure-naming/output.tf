output "regions" {
  description = "Map of Azure regions and their shortnames"
  value = {
    "australiacentral" = {
      name       = "Australia Central"
      short_name = "acl"
      aliases    = ["acl", "ac"]
    }
    "australiacentral2" = {
      name       = "Australia Central 2"
      short_name = "acl2"
      aliases    = ["acl2", "cbr2"]
    }
    "australiaeast" = {
      name       = "Australia East"
      short_name = "ae"
      aliases    = ["aeu", "eau", "ae"]
    }
    "australiasoutheast" = {
      name       = "Australia Southeast"
      short_name = "ase"
      aliases    = ["seau", "sau", "ase"]
    }
    "brazilsouth" = {
      name       = "Brazil South"
      short_name = "brs"
      aliases    = ["sbr", "cq", "brs"]
    }
    "brazilsoutheast" = {
      name       = "Brazil Southeast"
      short_name = "bse"
      aliases    = ["bse", "brse"]
    }
    "brazilus" = {
      name       = "Brazil US"
      short_name = "bru"
      aliases    = []
    }
    "canadacentral" = {
      name       = "Canada Central"
      short_name = "cnc"
      aliases    = ["cca", "cac", "ccan", "cnc", "cc"]
    }
    "canadaeast" = {
      name       = "Canada East"
      short_name = "cne"
      aliases    = ["eca", "cae", "cne"]
    }
    "centralindia" = {
      name       = "Central India"
      short_name = "inc"
      aliases    = ["cin", "cid", "inc"]
    }
    "centralus" = {
      name       = "Central US"
      short_name = "cus"
      aliases    = ["cus"]
    }
    "centraluseuap" = {
      name       = "Central US EUAP"
      short_name = "ccy"
      aliases    = ["cuse", "ccy"]
    }
    "eastasia" = {
      name       = "East Asia"
      short_name = "ea"
      aliases    = ["eas", "ea"]
    }
    "eastus" = {
      name       = "East US"
      short_name = "eus"
      aliases    = ["eus"]
    }
    "eastus2" = {
      name       = "East US 2"
      short_name = "eus2"
      aliases    = ["eus2"]
    }
    "eastus2euap" = {
      name       = "East US 2 EUAP"
      short_name = "ecy"
      aliases    = ["eus2e", "ecy"]
    }
    "eastusstg" = {
      name       = "East US STG"
      short_name = ""
      aliases    = []
    }
    "francecentral" = {
      name       = "France Central"
      short_name = "frc"
      aliases    = ["cfr", "par", "frc", "fc"]
    }
    "francesouth" = {
      name       = "France South"
      short_name = "frs"
      aliases    = ["sfr", "frs", "mrs"]
    }
    "germanynorth" = {
      name       = "Germany North"
      short_name = "gn"
      aliases    = ["nde", "gn"]
    }
    "germanywestcentral" = {
      name       = "Germany West Central"
      short_name = "gwc"
      aliases    = ["wcde", "dewc", "gwc"]
    }
    "israelcentral" = {
      name       = "Israel Central"
      short_name = "ilc"
      aliases    = []
    }
    "italynorth" = {
      name       = "Italy North"
      short_name = "itn"
      aliases    = []
    }
    "japaneast" = {
      name       = "Japan East"
      short_name = "jpe"
      aliases    = ["ejp", "jpe"]
    }
    "japanwest" = {
      name       = "Japan West"
      short_name = "jpw"
      aliases    = ["wjp", "jpw"]
    }
    "jioindiacentral" = {
      name       = "Jio India Central"
      short_name = "jic"
      aliases    = []
    }
    "jioindiawest" = {
      name       = "Jio India West"
      short_name = "jiw"
      aliases    = []
    }
    "koreacentral" = {
      name       = "Korea Central"
      short_name = "krc"
      aliases    = ["ckr", "se", "krc", "kc"]
    }
    "koreasouth" = {
      name       = "Korea South"
      short_name = "krs"
      aliases    = ["skr", "krs", "ps"]
    }
    "mexicocentral" = {
      name       = "Mexico Central"
      short_name = "mxc"
      aliases    = []
    }
    "newzealandnorth" = {
      name       = "New Zealand North"
      short_name = "nzn"
      aliases    = []
    }
    "northcentralus" = {
      name       = "North Central US"
      short_name = "ncus"
      aliases    = ["ncus"]
    }
    "northeurope" = {
      name       = "North Europe"
      short_name = "ne"
      aliases    = ["neu", "ne"]
    }
    "norwayeast" = {
      name       = "Norway East"
      short_name = "nwe"
      aliases    = ["eno", "noe", "nwe"]
    }
    "norwaywest" = {
      name       = "Norway West"
      short_name = "nww"
      aliases    = ["wno", "nww", "now"]
    }
    "polandcentral" = {
      name       = "Poland Central"
      short_name = "plc"
      aliases    = []
    }
    "qatarcentral" = {
      name       = "Qatar Central"
      short_name = "qac"
      aliases    = []
    }
    "southafricanorth" = {
      name       = "South Africa North"
      short_name = "san"
      aliases    = ["nza", "san"]
    }
    "southafricawest" = {
      name       = "South Africa West"
      short_name = "saw"
      aliases    = ["wza", "saw"]
    }
    "southcentralus" = {
      name       = "South Central US"
      short_name = "scus"
      aliases    = ["scus"]
    }
    "southcentralusstg" = {
      name       = "South Central US STG"
      short_name = ""
      aliases    = []
    }
    "southindia" = {
      name       = "South India"
      short_name = "ins"
      aliases    = ["sin", "ins", "ma"]
    }
    "southeastasia" = {
      name       = "Southeast Asia"
      short_name = "sea"
      aliases    = ["seas", "sasia", "sea"]
    }
    "spaincentral" = {
      name       = "Spain Central"
      short_name = "esc"
      aliases    = []
    }
    "swedencentral" = {
      name       = "Sweden Central"
      short_name = "sdc"
      aliases    = ["sdc"]
    }
    "swedensouth" = {
      name       = "Sweden South"
      short_name = "sds"
      aliases    = []
    }
    "switzerlandnorth" = {
      name       = "Switzerland North"
      short_name = "szn"
      aliases    = ["nch", "swn", "chn", "szn"]
    }
    "switzerlandwest" = {
      name       = "Switzerland West"
      short_name = "szw"
      aliases    = ["wch", "szw", "stzw"]
    }
    "uaecentral" = {
      name       = "UAE Central"
      short_name = "uac"
      aliases    = ["cae", "uac", "auh"]
    }
    "uaenorth" = {
      name       = "UAE North"
      short_name = "uan"
      aliases    = ["nae", "dxb", "uan", "uaen"]
    }
    "uksouth" = {
      name       = "UK South"
      short_name = "uks"
      aliases    = ["suk", "uks"]
    }
    "ukwest" = {
      name       = "UK West"
      short_name = "ukw"
      aliases    = ["wuk", "ukw", "cw"]
    }
    "westcentralus" = {
      name       = "West Central US"
      short_name = "wcus"
      aliases    = ["wcus"]
    }
    "westeurope" = {
      name       = "West Europe"
      short_name = "we"
      aliases    = ["weu", "we"]
    }
    "westindia" = {
      name       = "West India"
      short_name = "inw"
      aliases    = ["inw"]
    }
    "westus" = {
      name       = "West US"
      short_name = "wus"
      aliases    = ["wus"]
    }
    "westus2" = {
      name       = "West US 2"
      short_name = "wus2"
      aliases    = ["wus2"]
    }
    "westus3" = {
      name       = "West US 3"
      short_name = "wus3"
      aliases    = ["wus3", "usw3"]
    }
  }
}
