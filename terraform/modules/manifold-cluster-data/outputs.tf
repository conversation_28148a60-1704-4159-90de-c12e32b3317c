output "manifold_cluster_data" {
  value = {
    cluster_rg               = data.azurerm_resource_group.cluster_rg
    cluster_aks              = data.azurerm_kubernetes_cluster.cluster_aks
    cluster_identity         = data.azurerm_user_assigned_identity.cluster_identity
    unmanaged_pools_identity = data.azurerm_user_assigned_identity.unmanaged_pools_identity
    acr_pull_identity        = data.azurerm_user_assigned_identity.acr_pull_identity
    cluster_vnet             = data.azurerm_virtual_network.cluster_vnet
    cluster_k8s_subnet       = data.azurerm_subnet.cluster_k8s_subnet
    cluster_firewall         = data.azurerm_firewall.cluster_firewall
    cluster_firewall_policy  = data.azurerm_firewall_policy.cluster_firewall_policy
  }
}
