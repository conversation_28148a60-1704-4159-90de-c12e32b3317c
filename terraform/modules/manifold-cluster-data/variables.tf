variable "cluster_config" {
  description = "Configuration for the existing manifold cluster."
  type = object({
    resource_group_name           = string
    cluster_aks_name              = string
    vnet_name                     = string
    subnet_name                   = string
    firewall_name                 = string
    firewall_policy_name          = string
    cluster_identity_name         = string
    unmanaged_pools_identity_name = string
    acr_pull_identity_name        = string
  })
}
