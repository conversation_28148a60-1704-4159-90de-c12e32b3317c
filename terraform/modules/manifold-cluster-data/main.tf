data "azurerm_resource_group" "cluster_rg" {
  name = var.cluster_config.resource_group_name
}

data "azurerm_kubernetes_cluster" "cluster_aks" {
  name                = var.cluster_config.cluster_aks_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_virtual_network" "cluster_vnet" {
  name                = var.cluster_config.vnet_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_subnet" "cluster_k8s_subnet" {
  name                 = var.cluster_config.subnet_name
  virtual_network_name = data.azurerm_virtual_network.cluster_vnet.name
  resource_group_name  = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_firewall" "cluster_firewall" {
  name                = var.cluster_config.firewall_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_firewall_policy" "cluster_firewall_policy" {
  name                = var.cluster_config.firewall_policy_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_user_assigned_identity" "cluster_identity" {
  name                = var.cluster_config.cluster_identity_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_user_assigned_identity" "unmanaged_pools_identity" {
  name                = var.cluster_config.unmanaged_pools_identity_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}

data "azurerm_user_assigned_identity" "acr_pull_identity" {
  name                = var.cluster_config.acr_pull_identity_name
  resource_group_name = data.azurerm_resource_group.cluster_rg.name
}
