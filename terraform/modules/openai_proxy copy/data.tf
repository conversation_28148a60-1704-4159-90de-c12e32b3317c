locals {
  cluster_name = var.cluster_name
  namespace    = var.namespace

  image = {
    app = {
      repository = "iridiumsdc.azurecr.io/openai_proxy/openai_proxy_msft"
      # The build pipeline can be found at:
      # https://dev.azure.com/project-argos/Mimco/_build/results?buildId=${tag}
      tag        = "1309662"
    }
  }

  # Calculate the host based on namespace
  hostname_prefix = local.namespace == "openai-proxy" ?  "openai-proxy" : "openai-proxy-${var.namespace}"
  host_name = "${local.hostname_prefix}.int.${var.cluster_name}.dev.openai.org"
}