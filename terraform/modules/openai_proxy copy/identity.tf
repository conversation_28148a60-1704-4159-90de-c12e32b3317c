resource "azurerm_user_assigned_identity" "openai_proxy_identity" {
  name                = "openai-proxy-identity"
  resource_group_name = var.cluster_rg.name
  location            = var.cluster_rg.location
}

resource "azurerm_role_assignment" "openai_proxy_monitoring_reader" {
  scope                = var.cluster_rg.id
  role_definition_name = "Monitoring Reader"
  principal_id         = azurerm_user_assigned_identity.openai_proxy_identity.principal_id
}

resource "azurerm_role_assignment" "openai_proxy_monitoring_data_reader" {
  scope                = "/subscriptions/fbbd0f8a-b594-4fee-b381-3713acf07e7e/resourceGroups/orange-${var.prometheus_env}-azure-monitor-workspace_rg/providers/Microsoft.Monitor/accounts/orange-${var.prometheus_env}-amw"
  role_definition_name = "Monitoring Data Reader"
  principal_id         = azurerm_user_assigned_identity.openai_proxy_identity.principal_id
}

resource "azurerm_federated_identity_credential" "openai_proxy_federated_identity_credential" {
  name                = "openai-proxy-federated-identity-credential"
  resource_group_name = var.cluster_rg.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.openai_proxy_identity.id
  subject             = "system:serviceaccount:${local.namespace}:openai-proxy"
}

resource "kubernetes_service_account" "openai_proxy_service_account" {
  metadata {
    name      = "openai-proxy"
    namespace = local.namespace
    annotations = {
      "azure.workload.identity/client-id" = azurerm_user_assigned_identity.openai_proxy_identity.client_id
    }
  }
}
