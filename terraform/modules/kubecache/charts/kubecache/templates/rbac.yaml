apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "kubecache" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kubecache" . }}-read
rules:
- apiGroups: [""]
  resources: ["pods", "nodes"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kubecache" . }}-read-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kubecache" . }}-read
subjects:
- kind: ServiceAccount
  name: {{ include "kubecache" . }}
  namespace: {{ .Release.Namespace }}
