apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kubecache" . }}
  labels:
    app: {{ include "kubecache" . }}
spec:
{{- $default_server_config := get .Values.server_configuration "default" -}}
{{- $server_config := get .Values.server_configuration .Values.cluster_name -}}
{{- with $server_config | default $default_server_config }}
  replicas: {{ .replicas }}
{{- end}}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: {{ include "kubecache" . }}
  template:
    metadata:
      labels:
        app: {{ include "kubecache" . }}
    spec:
      serviceAccountName: {{ include "kubecache" . }}
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            app: {{ include "kubecache" . }}
      terminationGracePeriodSeconds: 30
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.avoidSelector | quote }}
                operator: NotIn
                values: ["gpu"]
      {{- if .Values.imagePullSecret }}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecret | quote }}
      {{- end }}
      containers:
      - name: cache
        envFrom:
          - secretRef:
              name: azure-service-principal
          - configMapRef:
              name: azure-storage-account
        env:
          - name: RUST_BACKTRACE
            value: "1"
          - name: RUST_LOG
            value: "info"
          - name: KUBE_POD_NAME
            valueFrom: {fieldRef: {fieldPath: metadata.name}}
          - name: KUBE_NAMESPACE
            valueFrom: {fieldRef: {fieldPath: metadata.namespace}}
          - name: KUBE_POD_APP
            value: {{ include "kubecache" . }}
        image: {{ printf "%s:%s" .Values.imageRepository .Values.imageTag }}
        
{{- $default_server_config := get .Values.server_configuration "default" -}}
{{- $server_config := get .Values.server_configuration .Values.cluster_name -}}
{{- with $server_config | default $default_server_config }}
        imagePullPolicy: Always
        command: [
          "kubecache",
          {{- range $index, $port := .http_ports }}
          "--ports", "{{ $port }}",
          {{- end }}
          
          "--metrics-port", "{{ .metrics_port }}",
          "--tokio-thread-count", "{{ .assigned_cores }}",
          {{- end}}
            {{- $cluster_config := get .Values.storage_configuration .Values.cluster_region -}}
            {{- with $cluster_config }}
            "--blobstore-path",
            "{{ tpl .blobstore_path $ }}",
            {{- end}}
        ]
{{- $default_server_config := get .Values.server_configuration "default" -}}
{{- $server_config := get .Values.server_configuration .Values.cluster_name -}}
{{- with $server_config | default $default_server_config }}
        ports:
        {{- range $index, $port := .http_ports }}
        - containerPort: {{ $port }}
          name: http-{{ $index }}
        {{- end }}
        - containerPort: {{ .metrics_port }}
          name: metrics
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ first .http_ports }}
          initialDelaySeconds: 60
          failureThreshold: 10
          timeoutSeconds: 5
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /healthz
            port: {{ first .http_ports }}
          failureThreshold: 10
          timeoutSeconds: 5
          initialDelaySeconds: 60
          periodSeconds: 10
        lifecycle:
          preStop:
            exec:
              command: ["/bin/bash", "-c", "sleep 30"]
        resources:
          limits:
            cpu: {{ .assigned_cores | quote }}
            memory: {{ .memory | quote }}
          requests:
            cpu: {{ .assigned_cores | quote }}
            memory: {{ .memory | quote }}
      priorityClassName: team-critical
      tolerations:
      - key: openai.com/team
        operator: Equal
        value: platform-services
        effect: NoSchedule
      hostNetwork: {{ .host_network }}
{{- end}}