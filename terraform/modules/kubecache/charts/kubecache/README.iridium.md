# Kubecache helm chart

This is a modified version of OpenAI's helm chart for kubecache.
It remains structurally similar, so hopefully upstream changes can be easy to bring over in the future.

Differences of note:
* OpenAI's per-region/cluster settings have been removed. Any effect from those would be unintentional and confusing.
* We create and specify a service account, and give it a cluster role allowing kubecache to read the resources it is interested in (across all namespaces). It's not clear how this was being handled on the OpenAI side.
* Settings like `RUST_BACKTRACE=1` for useful logs.
