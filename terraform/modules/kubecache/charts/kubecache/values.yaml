# Values are set in kubecache/main.tf
imageRepository: ""
imageTag: ""
imagePullSecret: ""
# Need to ensure this label exists on all orange gpu nodes to avoid them
avoidSelector: singularity.azure.com/processing-unit
storage_configuration:
  dummy_region:
    blobstore_path: "az://{{.Values.storage_account_name}}/kubecache-store/{{.Values.cluster_name}}"
server_configuration:
  default:
    http_ports: [80, 10541]
    metrics_port: 9001
    assigned_cores: 4
    memory: 24G
    host_network: false
    replicas: 3