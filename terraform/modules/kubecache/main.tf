resource "helm_release" "kubecache" {
  depends_on = [
    kubernetes_config_map.azure-storage-account,
    kubernetes_secret.azure-service-principal,
  ]
  name      = "kubecache"
  namespace = var.namespace

  repository = "${path.module}/charts"
  chart      = "kubecache"

  set {
    name  = "imageRepository"
    value = "iridiumsdc.azurecr.io/infra/kubecache"
  }

  set {
    name  = "imageTag"
    value = "1119939"
    type  = "string"
  }

  set {
    name  = "cluster_name"
    value = var.cluster_name
  }

  set {
    name  = "cluster_region"
    value = "dummy_region"
  }

  set {
    name  = "storage_account_name"
    value = var.storage_account_name
  }
}
