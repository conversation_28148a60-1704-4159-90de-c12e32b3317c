variable "namespace" {
  description = "The namespace to create and deploy the Kubecache Helm chart into"
  type        = string
}

variable "tenant_id" {
  description = "The tenant id to use for the Kubecache Helm chart"
  type        = string
}

variable "storage_account_name" {
  description = "The storage account name to use for the Kubecache Helm chart"
  type        = string
}

variable "cluster_name" {
  description = "The name of the AKS cluster"
  type        = string
}

variable "infra_sync_keyvault" {
  type = object({
    id                = string
    subscription_name = string
    subscription_id   = string
    tenant_id         = string
  })
}

# This is the same as the user app sync sp used for user cluster onboarding
variable "infra_app_sync_sp_client_id" {
  type        = string
  description = "Client ID of the service principal used to sync infra apps."
}

variable "oidc_token_file" {
  type        = string
  description = "Path to the OIDC token file for the federated credentials."
  default     = "/tmp/oidc_token"
}

variable "client_id_name" {
  description = "Name of the secret containing the client id"
  type        = string
  default     = "scaling-infra-clientid"
}

variable "client_secret_name" {
  description = "Name of the secret containing the client password"
  type        = string
  default     = "scaling-infra-secret"
}
