variable "config" {
  type = object({
    # Azure
    location = string # Azure location for the cluster

    # Kubernetes
    external_model_storage_private_endpoints = map(object({
      private_connection_resource_id = string
      subnet_name                    = optional(string) # subnet to add the private endpoint to, defaults to the cluster subnet if not specified
    })) # List of external storage accounts that need to be added into the virtual network using private endpoints
    storage_queue_private_endpoints = map(object({
      private_connection_resource_id = string
      subnet_name                    = optional(string) # subnet to add the private endpoint to, defaults to the cluster subnet if not specified
    })) # List storage queues that need to be added into the virtual network using private endpoints
    services_private_endpoints = map(object({
      service_alias          = optional(string)
      service_resource_id    = optional(string)
      subnet_name            = string
      dns_zone_name          = string
      override_endpoint_name = optional(string)
      custom_a_record        = optional(bool, false)
      custom_a_records       = optional(bool, false)
      records                = optional(any, [])
      is_manual_connection   = optional(bool, true)
      subresource_names      = optional(set(string), [])
      cnames                 = optional(set(string), [])
      srv_record = optional(object({
        name     = string
        port     = optional(number, 80)
        weight   = optional(number, 10)
        priority = optional(number, 10)
      }))
    })) # List of services that need to be added into the virtual network using private endpoints
    storage_access_group = optional(string, "") # The name of the storage access group
    acr_pull_repositories = map(object({
      name                = string
      resource_group_name = string
    })) # List of ACR registries to add AcrPull role assignments for the cluster identity

    # Resources
    cluster_rg               = any # Data or Resource for Azure resource group for the AKS cluster
    cluster_aks              = any # Data or Resource for Azure AKS cluster
    unmanaged_pools_identity = any # Data or Resource for Azure managed identity for unmanaged Kubelet agent pools
    cluster_identity         = any # Data or Resource for Azure managed identity for AKS cluster
    acr_pull_identity        = any # Data or Resource for Azure managed identity used for pulling images from ACRs
    cluster_vnet             = any # Data or Resource for Cluster virtual network
    cluster_k8s_subnet       = any # Data or Resource for Kubernetes subnet on the cluster virtual network
    cluster_firewall         = any # Data or Resource for Azure Firewall for the cluster
    cluster_firewall_policy  = any # Data or Resource for Azure Firewall Policy for the cluster

  })
  description = "Configuration for the connections stage"
}
