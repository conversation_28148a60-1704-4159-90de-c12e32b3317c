# Need to create a DNAT rule allowing the AME builder to access the AKS API server
# via the cluster firewall. There are two possible configurations, and we handle both automatically:
# - Firewall has an IP configuration with a public IP address resource. This public IP is the destination of the DNAT rule.
# - Firewall has a virtual hub. The virtual hub's IP address is the destination of the DNAT rule.
# The DNAT rule will allow and translate the destination IP address to the AKS API server's FQDN.

locals {
  firewall_ip_type = length(var.config.cluster_firewall.ip_configuration) > 0 ? "public_ip" : "virtual_hub"

  first_public_ip_address_id = (
    local.firewall_ip_type == "public_ip" ?
    var.config.cluster_firewall.ip_configuration[0].public_ip_address_id :
    null
  )

  last_virtual_hub_ip_address = (
    local.firewall_ip_type == "virtual_hub" ?
    var.config.cluster_firewall.virtual_hub[0].public_ip_addresses[length(var.config.cluster_firewall.virtual_hub[0].public_ip_addresses) - 1] :
    null
  )

  firewall_ip_address = (
    local.firewall_ip_type == "public_ip" ?
    data.azurerm_public_ip.firewall-ip[0].ip_address :
    local.last_virtual_hub_ip_address
  )
}

data "azurerm_public_ip" "firewall-ip" {
  count = local.firewall_ip_type == "public_ip" ? 1 : 0

  name                = provider::azurerm::parse_resource_id(local.first_public_ip_address_id).resource_name
  resource_group_name = provider::azurerm::parse_resource_id(local.first_public_ip_address_id).resource_group_name
}

resource "azurerm_firewall_policy_rule_collection_group" "firewall-apiserver-access" {
  name               = "orange-cluster-firewall-allow-builder"
  firewall_policy_id = var.config.cluster_firewall_policy.id
  priority           = 100

  nat_rule_collection {
    name     = "ORANGE-APISERVER"
    priority = 100
    action   = "Dnat"
    rule {
      name                = "ORANGE-APISERVER"
      protocols           = ["TCP"]
      source_addresses    = [module.global.ame_builder.ip]
      destination_address = local.firewall_ip_address
      destination_ports   = ["443"]
      translated_fqdn     = var.config.cluster_aks.fqdn
      translated_port     = "443"
    }
  }
}
