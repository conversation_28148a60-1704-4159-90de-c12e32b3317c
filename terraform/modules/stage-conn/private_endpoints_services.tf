locals {
  dns_names = toset([for _, value in var.config.services_private_endpoints : value.dns_zone_name])
  vnet_settings = {
    virtual_network_name = var.config.cluster_vnet.name
    virtual_network_id   = var.config.cluster_vnet.id
    resource_group_name  = var.config.cluster_rg.name
    location             = var.config.cluster_vnet.location
  }

  subnets = distinct([for _, value in var.config.services_private_endpoints : value.subnet_name])

  srv_record_prep = {
    for key, value in var.config.services_private_endpoints : "${value.dns_zone_name}/${value.srv_record.name}" =>
    merge(value.srv_record, { target = "${key}.${value.dns_zone_name}" })...
    if try(value.srv_record.name, null) != null && try(value.srv_record.name, "") != ""
  }

  srv_records_group = {
    for key, value in local.srv_record_prep : key => {
      dns_zone    = split("/", key)[0]
      name        = split("/", key)[1]
      srv_records = { for x in value : x.target => x }
    }
  }

  service_private_dns_zones = {
    for zone in flatten([
      [
        azurerm_private_dns_zone.sites_private_dns_zone, 
        azurerm_private_dns_zone.caas_private_dns_zone, 
        azurerm_private_dns_zone.caas_azure_private_dns_zone,
        azurerm_private_dns_zone.redis_enterprise_private_dns_zone,
        azurerm_private_dns_zone.keyvault_private_dns_zone,
        azurerm_private_dns_zone.postgresql_private_dns_zone,
        azurerm_private_dns_zone.redis_private_dns_zone,
        azurerm_private_dns_zone.snowflake_private_dns_zone,
        azurerm_private_dns_zone.aoai_private_dns_zone,
        azurerm_private_dns_zone.wandb_private_dns_zone,
      ]
    ]) : zone.name => zone
  }

  missing_dns_zones = [for name in local.dns_names : name if !contains(keys(local.service_private_dns_zones), name)]
}

check "service_private_dns_zones" {
  assert {
    condition     = local.missing_dns_zones == []
    error_message = "Missing service private DNS zone(s):\n${jsonencode(local.missing_dns_zones)}"
  }
}

data "azurerm_subnet" "link_subnets" {
  for_each             = toset(local.subnets)
  name                 = each.key
  virtual_network_name = var.config.cluster_vnet.name
  resource_group_name  = var.config.cluster_rg.name
}

module "custom_services" {
  source   = "../../modules/custom_service_connection"
  for_each = var.config.services_private_endpoints

  dns_name            = each.key
  service_alias       = each.value.service_alias
  service_resource_id = each.value.service_resource_id
  virtual_network = merge(
    local.vnet_settings,
    {
      subnet_name = data.azurerm_subnet.link_subnets[each.value.subnet_name].name
      subnet_id   = data.azurerm_subnet.link_subnets[each.value.subnet_name].id
    }
  )
  private_dns_zone       = local.service_private_dns_zones[each.value.dns_zone_name]
  subresource_names      = each.value.subresource_names
  cnames                 = each.value.cnames
  custom_a_record        = each.value.custom_a_record
  custom_a_records       = each.value.custom_a_records
  records                = each.value.records
  is_manual_connection   = each.value.is_manual_connection
  override_endpoint_name = each.value.override_endpoint_name
}

resource "azurerm_private_dns_srv_record" "dns-srv-record" {
  depends_on          = [module.custom_services]
  for_each            = local.srv_records_group
  name                = each.value.name
  zone_name           = local.service_private_dns_zones[each.value.dns_zone].name
  resource_group_name = local.service_private_dns_zones[each.value.dns_zone].resource_group_name
  ttl                 = 300

  dynamic "record" {
    for_each = each.value.srv_records
    iterator = srv
    content {
      priority = srv.value.priority
      weight   = srv.value.weight
      port     = srv.value.port
      target   = srv.value.target
    }
  }
}
