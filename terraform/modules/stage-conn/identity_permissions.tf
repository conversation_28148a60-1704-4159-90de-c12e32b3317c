resource "azurerm_role_assignment" "builder_cluster_admin" {
  scope                = var.config.cluster_aks.id
  role_definition_name = "Azure Kubernetes Service RBAC Cluster Admin"
  principal_id         = module.global.ame_builder.identity.principal_id
}

data "azurerm_container_registry" "cluster_identity_acr_pull" {
  provider            = azurerm.common
  for_each            = var.config.acr_pull_repositories
  name                = each.value.name
  resource_group_name = each.value.resource_group_name
}

resource "azurerm_role_assignment" "acr_pull_repositories_acr_pull" {
  for_each             = data.azurerm_container_registry.cluster_identity_acr_pull
  scope                = each.value.id
  role_definition_name = "AcrPull"
  principal_id         = var.config.acr_pull_identity.principal_id
}

resource "azurerm_role_assignment" "unmanaged_pools_identity" {
  scope                = var.config.cluster_rg.id
  role_definition_name = "Azure Kubernetes Service Cluster User Role"
  principal_id         = var.config.unmanaged_pools_identity.principal_id
}

data "azurerm_container_registry" "acr_repositories" {
  provider            = azurerm.common
  for_each            = var.config.acr_pull_repositories
  name                = each.value.name
  resource_group_name = each.value.resource_group_name
}

resource "azurerm_role_assignment" "kubelet-acr-pull" {
  for_each = data.azurerm_container_registry.acr_repositories

  scope                = each.value.id
  role_definition_name = "AcrPull"
  principal_id         = var.config.unmanaged_pools_identity.principal_id
}

data "azuread_group" "storage-access-group" {
  count        = length(var.config.storage_access_group) > 0 ? 1 : 0
  display_name = var.config.storage_access_group
}

resource "azuread_group_member" "unmanaged_pools_identity" {
  count            = length(data.azuread_group.storage-access-group) > 0 ? 1 : 0
  group_object_id  = data.azuread_group.storage-access-group[0].id
  member_object_id = var.config.unmanaged_pools_identity.principal_id
}

resource "azuread_group_member" "cluster" {
  count            = length(data.azuread_group.storage-access-group) > 0 ? 1 : 0
  group_object_id  = data.azuread_group.storage-access-group[0].id
  member_object_id = var.config.cluster_identity.principal_id
}
