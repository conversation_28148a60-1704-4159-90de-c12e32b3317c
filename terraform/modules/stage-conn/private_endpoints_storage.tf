# TODO: Refactor to support multiple types for a single resource
# example ["blob", "queue", "file"]

# Subnet IDs for each endpoint
data "azurerm_subnet" "storage_subnet" {
  for_each             = var.config.external_model_storage_private_endpoints
  name                 = each.value.subnet_name != null ? each.value.subnet_name : var.config.cluster_k8s_subnet.name
  virtual_network_name = var.config.cluster_vnet.name
  resource_group_name  = var.config.cluster_rg.name
}

# Manually-approved private endpoints, for external storage accounts
resource "azurerm_private_endpoint" "external_model_storage_private_endpoints" {
  for_each            = var.config.external_model_storage_private_endpoints
  name                = "pe-${each.key}"
  location            = var.config.cluster_vnet.location
  resource_group_name = var.config.cluster_rg.name
  subnet_id           = data.azurerm_subnet.storage_subnet[each.key].id

  private_service_connection {
    name                           = "conn-${each.key}"
    private_connection_resource_id = each.value.private_connection_resource_id
    subresource_names              = ["blob"]
    is_manual_connection           = true
    request_message                = "Orange terraform endpoint request"
  }

  private_dns_zone_group {
    name                 = "conn-${each.key}"
    private_dns_zone_ids = [data.azurerm_private_dns_zone.model_storage_blob_private_dns_zone.id]
  }
}
