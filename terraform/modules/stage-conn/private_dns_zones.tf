## TODO this file exists because Singularity clusters come with some, but not all, of the privatelink private DNS zones we need.
## For iridium, we created these all in stage-aks/private_dns_zones.tf
## But for orange, stage-aks stage is skipped, and we use a mix of resoure and data here to get the same set of zones.

# Caas.net
# TBD clean up after the migration
resource "azurerm_private_dns_zone" "caas_private_dns_zone" {
  name                = "caas.net"
  resource_group_name = var.config.cluster_rg.name
}
resource "azurerm_private_dns_zone_virtual_network_link" "caas_private_dns_zone_vnet_link" {
  name                  = "azuresitesvnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.caas_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# caas.azure.com
resource "azurerm_private_dns_zone" "caas_azure_private_dns_zone" {
  name                = "privatelink.caas.azure.com"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "caas_azure_private_dns_zone_vnet_link" {
  name                  = "caasazurevnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.caas_azure_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# Azure Websites
resource "azurerm_private_dns_zone" "sites_private_dns_zone" {
  name                = "privatelink.azurewebsites.net"
  resource_group_name = var.config.cluster_rg.name
}
resource "azurerm_private_dns_zone_virtual_network_link" "sites_private_dns_zone_vnet_link" {
  name                  = "azuresitesvnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.sites_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# https://learn.microsoft.com/en-us/azure/private-link/private-endpoint-dns#security
resource "azurerm_private_dns_zone" "keyvault_private_dns_zone" {
  name                = "privatelink.vaultcore.azure.net"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "keyvault_private_dns_zone_vnet_link" {
  name                  = "keyvaultvnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.keyvault_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# PostgreSQL
resource "azurerm_private_dns_zone" "postgresql_private_dns_zone" {
  name                = "privatelink.postgres.database.azure.com"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "postgresql_private_dns_zone_vnet_link" {
  name                  = "postgresqlvnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.postgresql_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# Azure Cache for Redis
resource "azurerm_private_dns_zone" "redis_private_dns_zone" {
  name                = "privatelink.redis.cache.windows.net"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "redis_private_dns_zone_vnet_link" {
  name                  = "redisvnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.redis_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# Redis Enterprise
resource "azurerm_private_dns_zone" "redis_enterprise_private_dns_zone" {
  name                = "privatelink.redisenterprise.cache.azure.net"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "redis_enterprise_private_dns_zone_vnet_link" {
  name                  = "redisenterprisevnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.redis_enterprise_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# Blob storage
# (this one is created on the Singularity side)
data "azurerm_private_dns_zone" "model_storage_blob_private_dns_zone" {
  name                = "privatelink.blob.core.windows.net"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone" "storage_queue_private_dns_zone" {
  name                = "privatelink.queue.core.windows.net"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "storage_queue_private_dns_zone_vnet_link" {
  name                  = "storagequeuevnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.storage_queue_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# Wandb
resource "azurerm_private_dns_zone" "wandb_private_dns_zone" {
  name                = "wandb.io"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "wandb_private_dns_zone_vnet_link" {
  name                  = "msaip-private-terraform"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.wandb_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
  
  depends_on = [azurerm_private_dns_zone.wandb_private_dns_zone]
}

# Azure OpenAI Service
resource "azurerm_private_dns_zone" "aoai_private_dns_zone" {
  name                = "privatelink.openai.azure.com"
  resource_group_name = var.config.cluster_rg.name
}
resource "azurerm_private_dns_zone_virtual_network_link" "aoai_private_dns_zone_vnet_link" {
  name                  = "aoaivnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.aoai_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}

# Snowflake
resource "azurerm_private_dns_zone" "snowflake_private_dns_zone" {
  name                = "privatelink.snowflakecomputing.com"
  resource_group_name = var.config.cluster_rg.name
}

resource "azurerm_private_dns_zone_virtual_network_link" "snowflake_private_dns_zone_vnet_link" {
  name                  = "snowflakevnetlink"
  resource_group_name   = var.config.cluster_rg.name
  private_dns_zone_name = azurerm_private_dns_zone.snowflake_private_dns_zone.name
  virtual_network_id    = var.config.cluster_vnet.id
}