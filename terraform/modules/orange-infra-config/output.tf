module "azure-naming" {
  source = "../azure-naming"
}

locals {
  regional_storage_accounts = {
    for region, _ in local.regions : region => {
      storage_account_name = "ornginfra${module.azure-naming.regions[region].short_name}"
      resource_group_name  = "orange-infra-${region}"
      region_abbrev        = module.azure-naming.regions[region].short_name
    }
  }
}

output "regions" {
  description = "Map from region to the regional infra storage account"
  value       = local.regional_storage_accounts
}
