# # Get the ip address of the server service
# data "kubernetes_service" "trellis_server" {
#   depends_on = [helm_release.trellis]
#   metadata {
#     name      = "trellis-server"
#     namespace = kubernetes_namespace.ns.metadata[0].name
#   }
# }

# output "trellis_server_ip" {
#   description = "The external IP of the Trellis server"
#   value       = data.kubernetes_service.trellis_server.status[0].load_balancer[0].ingress[0].ip
# }

# # Get the ip address of the app service
# data "kubernetes_service" "trellis_app" {
#   depends_on = [helm_release.trellis]
#   metadata {
#     name      = "trellis-app"
#     namespace = kubernetes_namespace.ns.metadata[0].name
#   }
# }
# output "trellis_app_ip" {
#   description = "The external IP of the Trellis app"
#   value       = data.kubernetes_service.trellis_app.status[0].load_balancer[0].ingress[0].ip
# }
