resource "kubernetes_stateful_set" "redis" {
  wait_for_rollout = false

  metadata {
    name      = "redis"
    namespace = local.namespace
    labels = {
      app = "redis"
    }
  }

  spec {
    service_name = "redis"
    replicas     = 1

    selector {
      match_labels = {
        app = "redis"
      }
    }

    template {
      metadata {
        labels = {
          app = "redis"
        }
      }

      spec {
        container {
          name  = "redis"
          image = "${local.image.redis.repository}:${local.image.redis.tag}"

          port {
            container_port = 12234
          }

          readiness_probe {
            exec {
              command = ["redis-cli", "-a", "$(REDISCLI_AUTH)",  "-p", "12234", "ping"]
            }
            initial_delay_seconds = 5
            period_seconds        = 10
            timeout_seconds       = 5
          }

          liveness_probe {
            exec {
              command = ["redis-cli", "-a", "$(REDISCLI_AUTH)",  "-p", "12234", "ping"]
            }
            initial_delay_seconds = 30
            period_seconds        = 30
            timeout_seconds       = 5
          }

          resources {
            requests = {
              cpu    = "100m"
              memory = "128Mi"
            }
            limits = {
              cpu    = "500m"
              memory = "512Mi"
            }
          }

          volume_mount {
            name       = "redis-data"
            mount_path = "/data"
          }

          env {
            name = "REDISCLI_AUTH"
            value_from {
              secret_key_ref {
                name = kubernetes_secret.trellis_secrets.metadata[0].name
                key  = "redis_password"
              }
            }
          }

          command = ["sh", "-c"]
          args    = ["redis-server --port 12234 --requirepass \"$REDISCLI_AUTH\" --appendonly yes"]
        }

        affinity {
          node_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 1
              preference {
                match_expressions {
                  key      = "singularity.azure.com/processing-unit"
                  operator = "In"
                  values   = ["cpu", "system"]
                }
              }
            }
          }
        }
      }
    }

    volume_claim_template {
      metadata {
        name = "redis-data"
      }
      spec {
        access_modes       = ["ReadWriteOnce"]
        storage_class_name = "managed-premium"
        resources {
          requests = {
            storage = "16Gi"
          }
        }
      }
    }
  }

  depends_on = [
    kubernetes_secret.trellis_secrets
  ]
}

# Create a headless service for Redis StatefulSet.
resource "kubernetes_service" "redis" {
  metadata {
    name      = "redis"
    namespace = local.namespace
  }

  spec {
    cluster_ip              = "None"
    type                    = "ClusterIP"
    internal_traffic_policy = "Cluster"
    ip_families             = ["IPv4"]
    ip_family_policy        = "SingleStack"

    port {
      port        = 12234
      target_port = 12234
    }

    selector = {
      app = "redis"
    }
  }

  depends_on = [
    kubernetes_stateful_set.redis
  ]
}
