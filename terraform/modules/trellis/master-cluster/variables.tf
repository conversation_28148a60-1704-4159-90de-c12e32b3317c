variable "environment" {
  description = "The trellis environment (dev | test | prod), commonly used as a suffix for resource names"
  type        = string
}

variable "cluster_name" {
  description = "The name of the Azure Kubernetes Service (AKS) cluster where Trellis will be deployed"
  type        = string
}

variable "aks_namespace" {
  description = "The Kubernetes namespace for <PERSON>relli<PERSON> in the AKS cluster"
  type        = string
}

variable "postgresql_password" {
  description = "The postgresql admin password for the Trellis database"
  type        = string
}

variable "redis_password" {
  description = "The password for the Redis cache server used by Trellis"
  type        = string
}

variable "trellis_sp_id" {
  description = "The service principal ID for Trellis, used for authentication and authorization"
  type        = string
}

variable "trellis_sp_password" {
  description = "The service principal password for <PERSON><PERSON><PERSON>, used for authentication and authorization"
  type        = string
}