locals {
  image = {
    server = {
      repository = "iridiumsdc.azurecr.io/trellis/trellis_server"
      tag        = "1270449" # https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1270449&view=results
    }
    app = {
      repository = "iridiumsdc.azurecr.io/trellis/trellis_app"
      tag        = "1261757" # https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1261757&view=results
    }
    redis = {
      repository = "mcr.microsoft.com/mirror/docker/library/redis"
      tag        = "7.2"
    }
  }
  secrets = {
      postgresql_password = var.postgresql_password
      redis_password      = var.redis_password
      trellis_sp_id       = var.trellis_sp_id
      trellis_sp_password = var.trellis_sp_password
    }
  cluster_name = var.cluster_name
  environment  = var.environment
  namespace    = var.aks_namespace
}
