# This file defines the Kubernetes deployment for the Trellis app and server.
# Both the app and server are exposed via a Tailscale load balancer service to allow access from Tailscale users.
# https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror/commit/52996b0f55147b9fc0204b2634b5a1b05aa28f96?path=/project/trellis_app/trellis_deployment.yaml&_a=contents

# Create the trellis app and server deployment in Kubernetes.
resource "kubernetes_deployment" "trellis_app_and_server" {
  wait_for_rollout = false

  metadata {
    name      = "trellis-app-and-server"
    namespace = local.namespace
    labels = {
      app = "trellis-app-and-server"
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        app = "trellis-app-and-server"
      }
    }

    template {
      metadata {
        labels = {
          app = "trellis-app-and-server"
        }
      }

      spec {
        container {
          name  = "trellis-server"
          image = "${local.image.server.repository}:${local.image.server.tag}"

          port {
            container_port = 8000
          }

          readiness_probe {
            failure_threshold = 5
            http_get {
              path   = "/readyz"
              port   = 8000
              scheme = "HTTP"
            }
            initial_delay_seconds = 30
            period_seconds        = 10
            success_threshold     = 1
            timeout_seconds       = 5
          }

          resources {
            requests = {
              cpu    = "8"
              memory = "16Gi"
            }
            limits = {
              cpu    = "8"
              memory = "64Gi"
            }
          }

          env {
            name  = "TRELLIS_SERVER_IS_LOCAL"
            value = "0"
          }

          env {
            name  = "TRELLIS_SERVER_USE_PRODUCTION_DB"
            value = "1"
          }

          env {
            name = "PGPASSWORD"
            value_from {
              secret_key_ref {
                name = kubernetes_secret.trellis_secrets.metadata[0].name
                key  = "postgresql_password"
              }
            }
          }

          env {
            name = "REDIS_SERVER_PASSWORD"
            value_from {
              secret_key_ref {
                name = kubernetes_secret.trellis_secrets.metadata[0].name
                key  = "redis_password"
              }
            }
          }

          env {
            name  = "REDIS_URL"
            value = "redis.trellis-prod.svc.cluster.local:12234" # This points to the headless service for Redis.
          }

          # env {
          #   name = "STRAWBERRYACE_CENTRALUS_TOKEN" # TODO: What is this for?
          #   value_from {
          #     secret_key_ref {
          #       name = "trellis-strawberryace-centralus-token"
          #       key  = "token"
          #     }
          #   }
          # }

          # env {
          #   name = "CAAS_API_KEY" # TODO: What is this for?
          #   value_from {
          #     secret_key_ref {
          #       name = "trellis-caas-api-key"
          #       key  = "token"
          #     }
          #   }
          # }

          # Pretend to be rcall so that sciclone works.
          # env {
          #   name  = "RCALL_KUBE_CLUSTER"
          #   value = "narwhal"
          # }

          # This must be a valid user because mini tries to create a container
          # named `oai{OPENAI_USER}` when it is imported.
          env {
            name  = "OPENAI_USER"
            value = "zhaogao"
          }
        }

        container {
          name  = "trellis-app"
          image = "${local.image.app.repository}:${local.image.app.tag}"

          port {
            container_port = 3000
          }

          readiness_probe {
            failure_threshold = 5
            http_get {
              path   = "/"
              port   = 3000
              scheme = "HTTP"
            }
            initial_delay_seconds = 5
            period_seconds        = 10
            success_threshold     = 1
            timeout_seconds       = 5
          }

          resources {
            requests = {
              cpu    = "8"
              memory = "2Gi"
            }
            limits = {
              cpu    = "8"
              memory = "8Gi"
            }
          }

          env {
            name  = "TRELLIS_BASE_URL"
            value = "http://localhost:8000/"
          }
        }

        affinity {
          node_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 1
              preference {
                match_expressions {
                  key      = "singularity.azure.com/processing-unit"
                  operator = "In"
                  values   = ["cpu", "system"]
                }
              }
            }
          }
        }
      }
    }
  }

  depends_on = [
        kubernetes_secret.trellis_secrets
  ]
}

# Create a CLUSTERIP service to expose the Trellis server to the callers from the same cluster.
resource "kubernetes_service" "trellis_cluster_ip" {
    metadata {
        name      = "trellis-cluster-ip"
        namespace = local.namespace
    }

    spec {
        cluster_ip              = "None"
        type                    = "ClusterIP"
        internal_traffic_policy = "Cluster"
        ip_families             = ["IPv4"]
        ip_family_policy        = "SingleStack"

        port {
            port        = 8000
            target_port = 8000
        }

        selector = {
            app = "trellis-app-and-server"
        }
    }

    depends_on = [
        kubernetes_deployment.trellis_app_and_server
    ]
}

# Create a tailscale load balancer service to expose the Trellis app and server to tailscale users.
resource "kubernetes_service" "trellis_tailscale_lb" {
    metadata {
        name      = "trellis-tailscale-lb"
        namespace = local.namespace
    }

    spec {
        load_balancer_class = "tailscale"
        type                = "LoadBalancer"
        
        selector = {
            app = "trellis-app-and-server"
        }

        port {
            name        = "app"
            port        = 3000
            protocol    = "TCP"
            target_port = 3000
        }

        port {
            name        = "server"
            port        = 8000
            protocol    = "TCP"
            target_port = 8000
        }
    }

    depends_on = [kubernetes_deployment.trellis_app_and_server]
}