# Create a Kubernetes Ingress resource for the Trellis app.
resource "kubernetes_ingress_v1" "trellis_app_ingress" {
  metadata {
    name      = "trellis-app-ingress"
    namespace = local.namespace
    
    annotations = {
      "kubernetes.io/ingress.global-static-ip-name"         = "${local.cluster_name}-kubernetes-ingress"
      "nginx.ingress.kubernetes.io/auth-cache-key"          = "$oauth_cookie_cache_key$http_authorization"
      "nginx.ingress.kubernetes.io/auth-response-headers"   = "Authorization, x-auth-request-access-token, X-Auth-Request-Email"
      "nginx.ingress.kubernetes.io/auth-url"                = "https://oai-azure-auth-proxy.int.${local.cluster_name}.dev.openai.org/oauth2/auth"
      "nginx.ingress.kubernetes.io/auth-signin"             = "https://oauth2-proxy-core-group.aad-auth.int.${local.cluster_name}.dev.openai.org/oauth2/start"
      "nginx.ingress.kubernetes.io/proxy-buffer-size"       = "128k"
      "nginx.ingress.kubernetes.io/proxy-busy-buffers-size" = "512k"
      "nginx.ingress.kubernetes.io/proxy-buffers-number"    = "4"
      "nginx.ingress.kubernetes.io/configuration-snippet"   = <<-EOT
        proxy_set_header X-User-Email $upstream_http_x_auth_request_email;
        add_header Cache-Control "no-store";
      EOT
      "nginx.ingress.kubernetes.io/backend-protocol"        = "HTTP"
    }
  }

  spec {
    ingress_class_name = "internal-nginx"
    
    rule {
      host = "trellis.int.${local.cluster_name}.dev.openai.org"
      
      http {
        path {
          path      = "/"
          path_type = "Prefix"
          
          backend {
            service {
              name = "trellis-tailscale-lb"
              port {
                number = 3000
              }
            }
          }
        }
      }
    }
    
    tls {
      hosts = ["trellis.int.${local.cluster_name}.dev.openai.org"]
    }
  }

  depends_on = [
    kubernetes_service.trellis_tailscale_lb
  ]
}