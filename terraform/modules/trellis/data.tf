# Read trellis configuration
module "trellis-config" {
  source = "../orange-trellis-config"
}

module "orange-global-settings" {
  source = "../global_settings"
}

locals {
  cluster_name                          = var.cluster_name
  # This is a key vault that holds the copy of <PERSON><PERSON><PERSON> secrests since the cluster deployment
  # identity does not have access to the orng-trellis-prod key vault in the Green tenant.
  keyvault_id                           = module.orange-global-settings.infra_sync.keyvault.id
  resource_group                        = module.trellis-config.global_resource_config.resource_group
  subscription_id                       = module.trellis-config.global_resource_config.subscription_id
  environment                           = module.trellis-config.global_resource_config.environment
  postgresql_admin_password_secret_name = module.trellis-config.global_resource_config.keyvault.secret_names.postgresql_admin_password
  redis_password_secret_name            = module.trellis-config.global_resource_config.keyvault.secret_names.redis_password
  trellis_sp_id_secret_name             = module.trellis-config.global_resource_config.keyvault.secret_names.trellis_sp_id
  trellis_sp_password_secret_name       = module.trellis-config.global_resource_config.keyvault.secret_names.trellis_sp_password
  trellis_aks_namespace                 = module.trellis-config.global_resource_config.aks_namespace
  orange-datatool-admins                = module.trellis-config.global_resource_config.admin_group.name
}

data "azurerm_key_vault_secret" "postgresql_password" {
  name         = local.postgresql_admin_password_secret_name
  key_vault_id = local.keyvault_id
  provider     = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "redis_password" {
  name         = local.redis_password_secret_name
  key_vault_id = local.keyvault_id
  provider     = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "trellis_sp_id" {
  name         = local.trellis_sp_id_secret_name
  key_vault_id = local.keyvault_id
  provider     = azurerm.infra-secret-reader
}

data "azurerm_key_vault_secret" "trellis_sp_password" {
  name         = local.trellis_sp_password_secret_name
  key_vault_id = local.keyvault_id
  provider     = azurerm.infra-secret-reader
}