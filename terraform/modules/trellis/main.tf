locals {
  is_master = local.cluster_name == "prod-southcentralus-hpe-3" # All trellis servers are deployed in the prod-southcentralus-hpe-3 cluster.
  is_follower = !local.is_master
}

# Create the Kubernetes namespace for Trellis in each AKS cluster.
resource "kubernetes_namespace" "trellis_namespace" {
  metadata {
    name = local.trellis_aks_namespace
  }
}

# Create trellis resources in the master cluster.
module "trellis_master_cluster" {
  count  = local.is_master ? 1 : 0
  source = "./master-cluster"
  environment = local.environment
  cluster_name = local.cluster_name
  aks_namespace = kubernetes_namespace.trellis_namespace.metadata[0].name
  postgresql_password = data.azurerm_key_vault_secret.postgresql_password.value
  redis_password = data.azurerm_key_vault_secret.redis_password.value
  trellis_sp_id = data.azurerm_key_vault_secret.trellis_sp_id.value
  trellis_sp_password = data.azurerm_key_vault_secret.trellis_sp_password.value
}

# Create the routing service for follower cluster to access the trellis service in the master cluster.
resource "kubernetes_service" "trellis_proxy_service" {
  count  = local.is_follower ? 1 : 0
  metadata {
    name = "trellis-proxy-service"
    namespace = kubernetes_namespace.trellis_namespace.metadata[0].name
    annotations = {
      "tailscale.com/tailnet-fqdn" = "trellis-prod-trellis-tailscale-lb.tailefd4cb.ts.net"
    }
  }
  spec {
    type          = "ExternalName"
    external_name = "unused"
  }
  lifecycle {
    ignore_changes = [spec[0].external_name]
  }
}