apiVersion: v1
kind: ServiceAccount
metadata:
  name: externaldns
  namespace: {{ .Release.Namespace }}
  annotations:
    azure.workload.identity/client-id: "{{ .Values.userAssignedIdentityID }}"  
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: externaldns
rules:
- apiGroups: [""]
  resources: ["services","endpoints","pods"]
  verbs: ["get","watch","list"]
- apiGroups: ["extensions","networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get","watch","list"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: externaldns-viewer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: externaldns
subjects:
- kind: ServiceAccount
  name: externaldns
  namespace: {{ .Release.Namespace }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: externaldns
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: externaldns
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: externaldns
        azure.workload.identity/use: "true"
        azure.workload.identity/client-id: "{{ .Values.userAssignedIdentityID }}"  
    spec:
      affinity: 
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: kubernetes.azure.com/mode
                operator: In
                values:
                - system
            weight: 100
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.azure.com/cluster
                operator: Exists
              - key: type
                operator: NotIn
                values:
                - virtual-kubelet
              - key: kubernetes.io/os
                operator: In
                values:
                - linux    
      serviceAccountName: externaldns
      containers:
      - name: externaldns
        image: mcr.microsoft.com/oss/v2/kubernetes/external-dns:v0.15.0
        args:
        - --source=service
        - --service-type-filter=LoadBalancer
        - --source=ingress
        - --ingress-class=internal-nginx
        - --domain-filter={{ .Values.domainSuffix }}
        - --provider=azure-private-dns
        - --policy=sync
        - --fqdn-template={{"{{"}}.Name{{"}}"}}.{{"{{"}}.Namespace{{"}}"}}.svc.{{ .Values.domainSuffix }}
        - --zone-name-filter=openai.org
        - --registry=noop
        - --events
        - --provider-cache-time=1h
        volumeMounts:
        - name: azure-config-file
          mountPath: /etc/kubernetes
          readOnly: true
      volumes:
      - name: azure-config-file
        secret:
          secretName: azure-config-file
---
apiVersion: v1
kind: Secret
metadata:
  name: azure-config-file
  namespace: {{ .Release.Namespace }}
type: Opaque
stringData:
  azure.json: |
    {
      "tenantId": "{{ .Values.tenantId }}",
      "subscriptionId": "{{ .Values.subscriptionId }}",
      "resourceGroup": "{{ .Values.resourceGroup }}",
      "useWorkloadIdentityExtension": true,
      "userAssignedIdentityID": "{{ .Values.userAssignedIdentityID }}"
    }