variable "cluster_name" {
  type = string
}

variable "cluster_aks" {
  type        = any
  description = "Data or Resource for Azure AKS cluster"
}

variable "cluster_resource_group" {
  type = string
  description = "value of the resource group name where the cluster is deployed"
}

variable "cluster_region" {
  type = string
  description = "value of the region where the cluster is deployed"
}

variable "dnszone_tenant_id" {
  type    = string
  description = "Tenant ID of the subscription where the openai.org DNS zone is located"
}

variable "dnszone_resource_group" {
  type    = string
  description = "Name of the resource group where the openai.org DNS zone is located"
}

variable "dnszone_subscription_id" {
  type    = string
  description = "Subscription ID of the subscription where the openai.org DNS zone is located"
}