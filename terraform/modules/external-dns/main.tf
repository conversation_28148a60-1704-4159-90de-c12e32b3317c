terraform {
  required_providers {
    azurerm = {
      version = "4.19.0"
    }
    kubernetes = {
      source = "hashicorp/kubernetes"
    }
    kubectl = {
      source = "alekc/kubectl"
    }
  }
}

locals {
  zone             = "openai.org"
  chart            = "external-dns"
  cluster_sub_zone = "dev.${local.zone}" # e.g. dev.openai.org
}

resource "kubernetes_namespace" "external-dns" {
  metadata {
    name = "external-dns"
  }
}

resource "azurerm_user_assigned_identity" "external-dns-identity" {
  name                = "aksexternaldns"
  resource_group_name = var.cluster_resource_group
  location            = var.cluster_region
  tags = {
  }
}

resource "azurerm_role_assignment" "private-dns-zone-contributor" {
  scope                = "/subscriptions/${var.dnszone_subscription_id}/resourceGroups/${var.dnszone_resource_group}/providers/Microsoft.Network/privateDnsZones/${local.zone}"
  role_definition_name = "Private DNS Zone Contributor"
  principal_id         = azurerm_user_assigned_identity.external-dns-identity.principal_id

}

resource "azurerm_federated_identity_credential" "external-dns-identity-workload-identity" {
  name                = "externaldns"
  resource_group_name = var.cluster_resource_group
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_aks.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.external-dns-identity.id
  subject             = "system:serviceaccount:${kubernetes_namespace.external-dns.id}:externaldns"
}

locals {
  yamlparam = yamlencode({
    domainSuffix           = "${var.cluster_name}.${local.cluster_sub_zone}" # e.g. prod-uksouth-15.dev.openai.org
    userAssignedIdentityID = azurerm_user_assigned_identity.external-dns-identity.client_id
    tenantId               = var.dnszone_tenant_id
    resourceGroup          = var.dnszone_resource_group
    subscriptionId         = var.dnszone_subscription_id
  })
}


resource "helm_release" "external-dns" {
  depends_on = [
    kubernetes_namespace.external-dns,
  ]

  name      = "external-dns"
  namespace = kubernetes_namespace.external-dns.id

  repository = "${path.module}/charts"
  chart      = local.chart

  values = [
    local.yamlparam,
    yamlencode({ hash = sha1(join("", [
      for f in fileset("${path.module}/charts/${local.chart}", "**") : filesha256("${path.module}/charts/${local.chart}/${f}")])
    ) })
  ]
}
