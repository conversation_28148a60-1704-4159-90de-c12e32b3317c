terraform {
  required_providers {
    kubernetes = {
      source = "hashicorp/kubernetes"
    }
  }
}

resource "kubernetes_config_map" "team_resource_manager_config" {
  metadata {
    name      = "team-resource-manager-config"
    namespace = "system"
  }

  data = {
    "team_allocations" = jsonencode(var.team_allocations)
  }
}

# Generate the Brix Quota resources for each subteam in team allocations. (A subteam here corresponds to a Moonfire real team.)
# We need these to give <PERSON><PERSON><PERSON> the placeholder for reporting quota status.
resource "kubernetes_manifest" "subteam_quotas" {
  # Create a flattened map of all subteams across all allocations
  for_each = {
    for alloc in flatten([
      for team_idx, team in var.team_allocations : [
        for alloc_idx, alloc in team.allocations : [
          {
            subteam  = alloc.subteam
            sku = try(
              # Example: selector = "metadata.node.openai.com/sku=gpu1,!openai.com/banned",
              regex("sku=(\\w+)", alloc.selector)[0],
              "gpu1"
            )
            priority = alloc.priority
            quotaCount = alloc.resourceCount
            nodeCount = alloc.count
          }
        ]
        if !strcontains(alloc.selector, "sku=cpu") # We are not tracking the CPU nodes initially. 
      ]
    ]) : alloc.subteam => alloc
  }

  manifest = {
    apiVersion = "brix.openai.com/v1alpha1"
    kind       = "Quota"
    metadata = {
      name = each.key
      labels = {
        "brix.openai.com/scheduler" = "perhonen"
      }
    }
    spec = {
      allocations = [
        {
          cluster     = var.cluster_name
          dedicated   = false
          sku         = each.value.sku
          displayName = each.value.sku # no nicer name available
          priority    = each.value.priority
          quotaCount  = each.value.quotaCount
          quotaNode   = each.value.nodeCount
        }
      ]
    }
  }

  field_manager {
    name = "orange-quota-cluster-config"
    # Overwrite the manually created Quota resources.
    force_conflicts = true
  }  
}

# For all the teams without quota allocations in the cluster, create a placeholder Quota resource with zero quota. This is to enable Perhonen 
# to report low-priority usage for any users who have access to the cluster.
resource "kubernetes_manifest" "placeholder_quotas" {
  for_each = setsubtract(var.all_teams, keys(kubernetes_manifest.subteam_quotas))
  manifest = {
    apiVersion = "brix.openai.com/v1alpha1"
    kind       = "Quota"
    metadata = {
      name = each.key
      labels = {
        "brix.openai.com/scheduler" = "perhonen"
      }
    }
    spec = {
      allocations = [
        {
          cluster     = var.cluster_name
          dedicated   = false
          sku         = "gpu1"
          displayName = "gpu1"
          priority    = 1 # Probably doesn't matter. Teams with quota are at priority 5.
          quotaCount  = 0
          quotaNode   = 0
        }
      ]
    }
  }
}


