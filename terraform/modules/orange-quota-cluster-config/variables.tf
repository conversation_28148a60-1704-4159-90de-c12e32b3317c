variable "cluster_name" {
  type        = string
}

// See https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6018/Configuring-Perhonen-for-the-Orange-clusters
variable "team_allocations" {
  description = "Team allocations for the cluster"
  type = list(object({
    team = string
    allocations = list(object({
      subteam       = string
      priority      = number
      count         = number # count of nodes
      resourceCount = number # GPU or vCPU count
      selector      = string # node label selector
    }))
  }))
}

variable "all_teams" {
  description = "All known teams, not only the ones with quota allocations in the given cluster"
  type = set(string)
}