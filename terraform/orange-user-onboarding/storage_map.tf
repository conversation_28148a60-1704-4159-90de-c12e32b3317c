locals {
  user_storage_map = {
    for user_alias, user_data in local.all_users : user_alias => {
      storage_acount_name = module.storage[user_alias].storage_account_name
      app_object_id       = module.user-app[user_alias].app.object_id
      user_object_id      = user_data.object_id
    }
  }
}

module "storage-map" {
  providers = {
    azurerm = azurerm.orange-oai-assets-provider
  }
  source = "../modules/orange-storage-map"

  users       = local.user_storage_map
  storage_map = var.storage_map
}
