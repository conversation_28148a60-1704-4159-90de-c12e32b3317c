locals {
  # Get actual storage account names from the storage module
  storage_account_names = {
    for user in keys(local.all_users) : user => module.storage[user].storage_account_name
  }
  
  # Add storage account names to the existing unified users structure
  all_users_with_storage = {
    for user_alias, user_data in local.all_users : user_alias => merge(user_data, {
      storage_account_name = local.storage_account_names[user_alias]
    })
  }
  
  orange_users_json = jsonencode(local.all_users_with_storage)
}

data "azurerm_storage_account" "orange_users_storage" {
  provider            = azurerm.orange-oai-assets-provider
  name                = var.orange-user-access-storage.storage_account_name
  resource_group_name = var.orange-user-access-storage.storage_resource_group
}

data "azurerm_storage_container" "orange_users_container" {
  provider             = azurerm.orange-oai-assets-provider
  name                 = var.orange-user-access-storage.storage_container_name
  storage_account_name = data.azurerm_storage_account.orange_users_storage.name
}

resource "azurerm_storage_blob" "orange_users_blob" {
  provider               = azurerm.orange-oai-assets-provider
  name                   = "users.json"
  storage_account_name   = data.azurerm_storage_account.orange_users_storage.name
  storage_container_name = data.azurerm_storage_container.orange_users_container.name

  content_type   = "application/json"
  type           = "Block"
  source_content = local.orange_users_json
}
