variable "onboarding-sp" {
  type = object({
    client_id                    = string
    tenant_id                    = string
    keyvault_name                = string
    keyvault_resource_group_name = string
    certificate_name             = string
  })
}

variable "orange-user-sp-sync" {
  type = object({
    client_id = string
    object_id = string
  })
}

variable "user-resources-group" {
  type = string
}

variable "storage_region" {
  type = string
}

variable "storage_map" {
  type = object({
    storage_account_name   = string
    storage_resource_group = string
  })
}

variable "storage_account_prefix" {
  type        = string
  description = "Prefix for the storage account name."
}

variable "builder_msi_principal_id" {
  type        = string
  description = "The principal ID of the managed identity running the automation."
}


variable "orange-user-access-storage" {
  type = object({
    storage_resource_group = string
    storage_account_name   = string
    storage_container_name = string
  })
  description = "The storage account and container where the user access JSON file will be stored."
}


variable "snowflake_account_info" {
  type = object({
    snowflake_organization_name = string
    snowflake_account_name      = string
  })
}
