terraform {

  required_providers {
    azuread = "~> 3.0.2"
    azurerm = "4.19.0"

    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
    snowflake = {
      source = "snowflakedb/snowflake"
      version = "2.3.0"
    }
  }
}

provider "azuread" {
  alias              = "onboarding-sp"
  client_id          = var.onboarding-sp.client_id
  tenant_id          = var.onboarding-sp.tenant_id
  client_certificate = data.azurerm_key_vault_secret.onboarding-sp-cert.value
}

provider "azurerm" {
  alias                           = "orange-users-provider"
  storage_use_azuread             = true
  subscription_id                 = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS https://microsoftservicetree.com/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8
  resource_provider_registrations = "none"


  features {
    storage {
      # We create storage accounts with azurerm_storage_account that are private access only,
      # but we don't create a corresponding private endpoint + dns on the pipeline builder's vnet.
      # This setting prevents that resource from trying to query the inaccessible data-plane
      # (*.blob.core.windows.net) endpoint for e.g. static website settings.
      # Note that changing this setting might be problematic w.r.t. needing to recreate resources.
      data_plane_available = false
    }
  }
}

provider "azurerm" {
  alias                           = "orange-oai-assets-provider"
  storage_use_azuread             = true
  subscription_id                 = "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e" # AIPLATFORM-ORANGE-OAI-ASSESTS
  resource_provider_registrations = "none"


  features {
    storage {
      data_plane_available = false
    }
  }
}

# default infra provider
provider "azurerm" {
  storage_use_azuread             = true
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA https://microsoftservicetree.com/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a
  resource_provider_registrations = "none"

  features {
    storage {
      data_plane_available = false
    }
  }
}

provider "azurerm" {
  alias             = "auditing-sub"
  subscription_id   = "f91c4126-3a46-4ddc-b984-6e9e4c1625ee" # AIPLATFORM-MUMFORD-SECMON-GREEN-DEV
  resource_provider_registrations = "none"

  features {}
}

data "azurerm_client_config" "current" {}

provider "azapi" {
  subscription_id = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS https://microsoftservicetree.com/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8
}

provider "snowflake" {
  organization_name = var.snowflake_account_info.snowflake_organization_name
  account_name      = var.snowflake_account_info.snowflake_account_name
  role              = "ACCOUNTADMIN"
  user              = data.azurerm_key_vault_secret.snowflake_username.value
  password          = data.azurerm_key_vault_secret.snowflake_password.value
}

data "azurerm_key_vault" "onboarding-sp-keyvault" {
  name                = var.onboarding-sp.keyvault_name
  resource_group_name = var.onboarding-sp.keyvault_resource_group_name
}

data "azurerm_key_vault_secret" "onboarding-sp-cert" {
  name         = var.onboarding-sp.certificate_name
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}


data "azurerm_key_vault_secret" "snowflake_username" {
  name         = "snowflake-username"
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}

data "azurerm_key_vault_secret" "snowflake_password" {
  name         = "snowflake-password"
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}
