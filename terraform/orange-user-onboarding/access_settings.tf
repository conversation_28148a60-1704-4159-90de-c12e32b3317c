module "orange-team-onboarding-remote-state" {
  source = "../modules/orange-remote-state"
  providers = {
    azurerm = azurerm
  }
  state-key = "orange-team-onboarding"
}

# Import robot users from remote state
module "orange-robot-users-remote-state" {
  source = "../modules/orange-remote-state"
  providers = {
    azurerm = azurerm
  }
  state-key = "orange-robot-users"
}

module "user-settings" {
  source = "../modules/orange-user-settings-aad"
  providers = {
    azuread = azuread.onboarding-sp,
    azurerm = azurerm
  }
  orange-teams = module.orange-team-onboarding-remote-state.remote-state-outputs.orange-teams
  moonfire_admin_team_cluster_access = module.orange-team-onboarding-remote-state.remote-state-outputs.moonfire_admin_team_cluster_access
}

locals {
  robot_users_raw = try(module.orange-robot-users-remote-state.remote-state-outputs.robots, [])
    robot_user_entries = {
    for robot in local.robot_users_raw : robot.name => {
      cluster_access = ["prod-uksouth-9", "stage-southcentralus-hpe-1"]
      storage_access = ["base", "cresco"]
      teams          = ["orange-robots"]
      is_robot       = true
      object_id      = robot.msi_principal_id
    }
  }
  
  enhanced_human_users = {
    for alias, settings in module.user-settings.users : alias => merge(settings, {
      is_robot  = false
      object_id = data.azuread_user.user[alias].object_id
    })
  }
  
  all_users = merge(local.enhanced_human_users, local.robot_user_entries)
}

data "azuread_user" "user" {
  provider            = azuread.onboarding-sp
  for_each            = module.user-settings.users
  user_principal_name = "${each.key}@green.microsoft.com"
}
