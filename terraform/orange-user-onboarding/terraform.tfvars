# This is the service principal that has the extra permissions to create apps/serivce principals for users.
onboarding-sp = {
  client_id                    = "17eb5cd6-98da-4e7a-bab6-1adefcafa5de"
  tenant_id                    = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green tenant
  keyvault_name                = "orange-onboarding-app-kv"
  keyvault_resource_group_name = "orange-onboarding"
  certificate_name             = "orange-onboarding-app-auth"
}

# This is the multi-tenant app (homed in AME) used for syncing user SP secrets into clusters.
# We give its Green tenant service principal access to a keyvault.
orange-user-sp-sync = {
  client_id = "79f57bc6-dd99-4067-b1f1-f6abb241be4b"
  # SP OID in Green tenant
  object_id = "6a76b261-89e4-4280-a599-695e6b22e6d5"
}

user-resources-group = "orange-user-resources"

storage_region = "uksouth"

storage_map = {
  storage_account_name   = "orngoaiartifacts"
  storage_resource_group = "orange-resources"
}

storage_account_prefix = "oaiorange"

builder_msi_principal_id = "82cb6bbd-45fc-4139-9980-bf074453ab80"

orange-user-access-storage = {
  storage_resource_group = "orange-resources"
  storage_account_name   = "orngoaiartifacts" # Use the same storage account used for storing storage map
  storage_container_name = "storage-map"
}

snowflake_account_info = {
  snowflake_organization_name = "aomgsqf"
  snowflake_account_name      = "wya76377.privatelink"
}
