/*
--------------------------------------------------------------------------------
End of Green Tenant boundary.
The users and team allocations defined here are output values, which are then
exported by the Azure DevOps pipeline as an artifact. This artifact is later
downloaded by the AME builder and applied on the AME side:
https://dev.azure.com/project-argos/Mimco/_git/bootstrapping?path=/terraform/orange-user-cluster-onboarding/users.tf

The order of execution can be found in this pipeline:
https://dev.azure.com/project-argos/Mimco/_git/bootstrapping?path=/.azuredevops/orange-onboarding.yaml
https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1167667&view=results
--------------------------------------------------------------------------------
*/
output "users" {
  value       = local.all_users_with_storage  # Now includes both human and robot users with storage account names
  description = "User settings for all users (human and robot) in orange with teams and access settings"
}

# Used to assign quotas to teams
output "team_allocations" {
  value       = module.orange-team-onboarding-remote-state.remote-state-outputs.team_allocations
  description = "Team allocations for users in orange"
}

# These are only for debugging purposes, should not be used for anything else
output "team_stats" {
  value = module.orange-team-onboarding-remote-state.remote-state-outputs.team_stats
}

output "cluster_stats" {
  value = module.orange-team-onboarding-remote-state.remote-state-outputs.cluster_stats
}

output "team_total_resources" {
  value = module.orange-team-onboarding-remote-state.remote-state-outputs.team_total_resources
}

output "teams_with_quotas" {
  value = module.orange-team-onboarding-remote-state.remote-state-outputs.teams_with_quotas
}

output "teams_without_quotas" {
  value = module.orange-team-onboarding-remote-state.remote-state-outputs.teams_without_quotas
}

output "moonfire_admin_groups_for_clusters" {
  value       = module.user-settings.moonfire_admin_groups_for_clusters
  description = "Map of moonfire team admin Azure AD group names to their object IDs (only for groups that have cluster access)"
}

output "moonfire_team_admin_cluster_access" {
  value       = module.user-settings.moonfire_team_admin_cluster_access
  description = "Map of moonfire team admin groups to the clusters they should have access to"
}
