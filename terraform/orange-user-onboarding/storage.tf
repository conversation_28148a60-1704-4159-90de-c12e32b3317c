locals {
  # create a map of user aliases to app objects id for all users (human and robot)
  user_group_object_ids = { for user in keys(local.all_users) : user => module.user-app[user].group.object_id }
}

module "storage" {
  providers = {
    azurerm = azurerm.orange-users-provider
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  depends_on = [azurerm_resource_group.user-resources-group]
  source     = "../modules/orange-user-storage"
  for_each   = local.user_group_object_ids

  storage_resource_group = azurerm_resource_group.user-resources-group.name
  storage_region         = var.storage_region
  storage_account_prefix = var.storage_account_prefix
  user_alias             = each.key
  group_object_id        = each.value
}
