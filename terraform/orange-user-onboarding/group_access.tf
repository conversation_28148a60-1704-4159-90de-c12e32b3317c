module "global" {
  source = "../modules/global_settings"
}

locals {
  user_access_list = merge([
    for user_alias, user_data in local.all_users : {
      for tent in user_data.storage_access : "${user_alias}-${tent}" => {
        user_name     = user_alias
        user_group_id = module.user-app[user_alias].group.object_id

        access_group = tent
      }
    }
  ]...)
}

data "azuread_group" "group" {
  provider = azuread.onboarding-sp
  for_each = module.global.security_groups

  display_name = each.value
}

resource "azuread_group_member" "group_access" {
  provider = azuread.onboarding-sp
  for_each = local.user_access_list

  group_object_id  = data.azuread_group.group[each.value.access_group].object_id
  member_object_id = each.value.user_group_id
}
