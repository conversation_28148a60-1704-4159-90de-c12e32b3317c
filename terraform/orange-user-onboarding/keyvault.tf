# We store the secrets for the per-user SPs into a keyvault.
# An AME-side pipeline stage will copy these into k8s secrets.

resource "azurerm_key_vault" "orange-user-sp-sync-kv" {
  provider                  = azurerm.orange-users-provider
  name                      = "orange-user-sp-sync-kv"
  resource_group_name       = azurerm_resource_group.user-resources-group.name
  location                  = "westus3"
  enable_rbac_authorization = true

  sku_name  = "standard"
  tenant_id = data.azurerm_client_config.current.tenant_id

  soft_delete_retention_days    = 90
  purge_protection_enabled      = true
  public_network_access_enabled = false
}

module "builder-access" {
  source   = "../modules/orange-builder-access"

  name        = "orange-user-sp-sync-kv"
  dns_zone    = module.global.private_dns_zones["vault"]
  resource_id = azurerm_key_vault.orange-user-sp-sync-kv.id
  role_assignments = [
    "Key Vault Secrets Officer" 

  ]
}

module "orange_user_sp_sync_kv_secmon_audit" {
  source = "../modules/aoai-secmon-audit"
  providers = {
    azurerm              = azurerm.orange-users-provider
    azurerm.auditing-sub = azurerm.auditing-sub
  }

  target_resource_id = azurerm_key_vault.orange-user-sp-sync-kv.id
}

# AME side needs to read the secrets
resource "azurerm_role_assignment" "orange-user-sp-sync-keyvault-secret-user" {
  provider             = azurerm.orange-users-provider
  principal_id         = var.orange-user-sp-sync.object_id
  role_definition_name = "Key Vault Secrets User"
  scope                = azurerm_key_vault.orange-user-sp-sync-kv.id
}

# AME side also needs metadata access for some reason
resource "azurerm_role_assignment" "orange-user-sp-sync-keyvault-reader" {
  provider             = azurerm.orange-users-provider
  principal_id         = var.orange-user-sp-sync.object_id
  role_definition_name = "Key Vault Reader"
  scope                = azurerm_key_vault.orange-user-sp-sync-kv.id
}

