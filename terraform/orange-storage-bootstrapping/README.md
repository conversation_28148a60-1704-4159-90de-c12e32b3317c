# Orange Storage Bootstrapping

This terraform configuration builds global & regional storage for OAI models/data/code in the `AIPLATFORM-ORANGE-OAI-ASSESTS` subscription.
New accounts, regions, and tents should be added in `./terraform.tfvars`.
Resources within the green tenant that are needed to access or use this storage, such as keyvaults and private links, should also go here.

Note that we take a general meaning of 'storage' here - stateful things with data inside.
Besides blob storage accounts, we also create e.g. managed Redis instances.