# This creates a Redis Enterprise cluster (and related keyvault) for "harmony".
# Currently, these is a single global instance, and we consider it part of the "base" tent (widely accessible vs. e.g. Cresco).
# See https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/lib/harmony_redis/harmony_redis/redis_utils.py&version=GBmsft/staging/20250114&_a=contents

resource "azurerm_redis_enterprise_cluster" "harmony_cluster" {
  name                = "orngharmony"
  resource_group_name = azurerm_resource_group.orange-resources.name
  location            = azurerm_resource_group.orange-resources.location
  sku_name            = "Enterprise_E10-8"
  zones               = ["1", "2", "3"]

  tags = {}
  lifecycle { prevent_destroy = true }
}

resource "azurerm_redis_enterprise_database" "harmony_cluster_db" {
  name = "default"

  cluster_id        = azurerm_redis_enterprise_cluster.harmony_cluster.id
  client_protocol   = "Encrypted"
  clustering_policy = "OSSCluster"
  eviction_policy   = "NoEviction"
  port              = 10000
}

module "harmony-vault" {
  source = "../modules/vault-base"

  name                = "orngharmony-vault"
  resource_group_name = azurerm_resource_group.orange-resources.name
  location            = azurerm_resource_group.orange-resources.location
  tenant_id           = data.azurerm_subscription.current.tenant_id

  providers = {
    azurerm              = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
    azurerm.infra        = azurerm.infra
  }

  group_role_assignments = {
    "Key Vault Secrets User" = [
      data.azuread_group.group["base"].object_id
    ]
  }

  # Grant orange-infra-sp-sync (Green tenant SP) access to read secrets and vault metadata
  role_assignments = {
    "Key Vault Secrets User" = [
      var.orange-infra-sp-sync.object_id
    ],
    "Key Vault Reader" = [
      var.orange-infra-sp-sync.object_id
    ]
  }

  tags = {
    "TENT"            = "BASE"
    "oai-sensitivity" = "critical"
  }
}

# Builder needs a private-endpoint + role assignment to store the redis secret
module "harmony-vault-builder-access" {
  providers = {
    azurerm = azurerm.infra
  }
  source = "../modules/orange-builder-access"

  name        = module.harmony-vault.vault.name
  dns_zone    = module.global.private_dns_zones["vault"]
  resource_id = module.harmony-vault.vault.id
  role_assignments = [
    "Key Vault Secrets Officer"
  ]
}

resource "azurerm_key_vault_secret" "harmony_redis_secret" {
  depends_on = [module.harmony-vault-builder-access]
  # `OAIHARMONY_REDIS_PARAMS_SECRET` in OAI code
  name         = "redis-oaiharmony"
  value        = "${azurerm_redis_enterprise_cluster.harmony_cluster.hostname}:${azurerm_redis_enterprise_database.harmony_cluster_db.port}:${azurerm_redis_enterprise_database.harmony_cluster_db.primary_access_key}"
  key_vault_id = module.harmony-vault.vault.id
}
