# This creates a global Event Hub (Kafka) cluster and related keyvault.
# This is a single global instance, part of the "base" tent (widely accessible).

# Event Hub Namespace with Kafka endpoint enabled
resource "azurerm_eventhub_namespace" "kafka_cluster" {
  name                = "orngkafka"
  location            = azurerm_resource_group.orange-resources.location
  resource_group_name = azurerm_resource_group.orange-resources.name
  sku                 = "Premium"
  capacity            = 4
  
  tags = {
    "TENT"            = "BASE"
    "oai-sensitivity" = "critical"
  }
  
  lifecycle { prevent_destroy = true }
}

# Create Event Hub Topic (metrics only)
resource "azurerm_eventhub" "kafka_topics" {
  name              = "metrics"
  namespace_id      = azurerm_eventhub_namespace.kafka_cluster.id
  partition_count   = 8
  message_retention = 7
}

# Create Consumer Group
resource "azurerm_eventhub_consumer_group" "default" {
  name                = "default-consumer"
  eventhub_name       = azurerm_eventhub.kafka_topics.name
  namespace_name      = azurerm_eventhub_namespace.kafka_cluster.name
  resource_group_name = azurerm_resource_group.orange-resources.name
  user_metadata       = "Default consumer group for metrics topic"
}

# Create a keyvault for storing Event Hub secrets
module "kafka-vault" {
  source = "../modules/vault-base"

  name                = "orngkafka-vault"
  resource_group_name = azurerm_resource_group.orange-resources.name
  location            = azurerm_resource_group.orange-resources.location
  tenant_id           = data.azurerm_subscription.current.tenant_id

  providers = {
    azurerm              = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
    azurerm.infra        = azurerm.infra
  }

  group_role_assignments = {
    "Key Vault Secrets User" = [
      data.azuread_group.group["base"].object_id
    ]
  }
    # Grant orange-infra-sp-sync (Green tenant SP) access to read secrets and vault metadata
  role_assignments = {
    "Key Vault Secrets User" = [
      var.orange-infra-sp-sync.object_id
    ],
    "Key Vault Reader" = [
      var.orange-infra-sp-sync.object_id
    ]
  }

  tags = {
    "TENT"            = "BASE"
    "oai-sensitivity" = "critical"
  }
}

# Create authorization rule for sending and listening
resource "azurerm_eventhub_namespace_authorization_rule" "kafka_auth" {
  name                = "orngkafka-auth"
  namespace_name      = azurerm_eventhub_namespace.kafka_cluster.name
  resource_group_name = azurerm_resource_group.orange-resources.name
  
  listen = true
  send   = true
  manage = true
}

# Builder needs a private-endpoint + role assignment to store the kafka secret
module "kafka-vault-builder-access" {
  providers = {
    azurerm = azurerm.infra
  }
  source = "../modules/orange-builder-access"

  name        = module.kafka-vault.vault.name
  dns_zone    = module.global.private_dns_zones["vault"]
  resource_id = module.kafka-vault.vault.id
  role_assignments = [
    "Key Vault Secrets Officer"
  ]
}


# Store connection string in Key Vault
resource "azurerm_key_vault_secret" "kafka_connection_string" {
  depends_on = [module.kafka-vault-builder-access]
  name         = "kafka-orngkafka"
  value        = azurerm_eventhub_namespace_authorization_rule.kafka_auth.primary_connection_string
  key_vault_id = module.kafka-vault.vault.id
}

# Also store separate key and endpoint for flexibility
resource "azurerm_key_vault_secret" "kafka_key" {
  depends_on = [module.kafka-vault-builder-access]
  name         = "kafka-orngkafka-key"
  value        = azurerm_eventhub_namespace_authorization_rule.kafka_auth.primary_key
  key_vault_id = module.kafka-vault.vault.id
}

resource "azurerm_key_vault_secret" "kafka_endpoint" {
  depends_on = [module.kafka-vault-builder-access]
  name         = "kafka-orngkafka-endpoint"
  value        = azurerm_eventhub_namespace.kafka_cluster.default_primary_connection_string
  key_vault_id = module.kafka-vault.vault.id
}