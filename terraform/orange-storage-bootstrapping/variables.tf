variable "onboarding-sp" {
  type = object({
    client_id                    = string
    tenant_id                    = string
    keyvault_name                = string
    keyvault_resource_group_name = string
    certificate_name             = string
  })
}

variable "extra_storage_principal_names" {
  description = "A list SP names to grant access to regional storage."
  type        = list(string)
  default     = []
}

variable "orange-infra-sp-sync" {
  description = "Cross-tenant service principal for syncing infrastructure secrets"
  type = object({
    client_id = string
    object_id = string # Green tenant object ID
  })
}
