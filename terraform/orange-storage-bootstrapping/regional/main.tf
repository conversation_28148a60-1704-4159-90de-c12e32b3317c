module "azure-naming" {
  source = "../../modules/azure-naming"
}

locals {
  region_abbrev = module.azure-naming.regions[var.region].short_name
}

resource "azurerm_resource_group" "orange-regional-resources" {
  name     = var.rg_name
  location = var.region
}

# Create a regional account for each tent
module "regional-storage" {
  providers = {
    azurerm.infra        = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }
  source   = "../../modules/storage-base"
  for_each = var.tents

  name                = "orng${local.region_abbrev}${each.key}" # eg, orngukscresco
  resource_group_name = azurerm_resource_group.orange-regional-resources.name
  location            = azurerm_resource_group.orange-regional-resources.location

  group_role_assignments = {
    "Storage Blob Data Contributor" = [
      var.groups[each.key].object_id
    ],
    "Reader"                        = [
      var.groups[each.key].object_id
    ]
  }

  role_assignments = {
    "Storage Blob Data Contributor" = var.extra_principal_object_ids
  }

  tags = {
    "TENT"            = upper(each.key)
    "oai-sensitivity" = "critical"
  }
}
