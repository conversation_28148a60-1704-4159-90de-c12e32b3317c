variable "rg_name" {
  description = "name of the regional resource group to create storage accounts in"
  type        = string
}

variable "region" {
  description = "full region name to create storage accounts for"
  type        = string
}

variable "tents" {
  description = "set of tents to create regional storage accounts for"
  type        = map(object({}))
}

variable "groups" {
  description = "List of group objects with tent name as key"
  type = map(object({
    object_id = string
  }))
}

variable "extra_principal_object_ids" {
  description = "A list of Object IDs for principals needing storage access."
  type        = list(string)
  default     = []
}
