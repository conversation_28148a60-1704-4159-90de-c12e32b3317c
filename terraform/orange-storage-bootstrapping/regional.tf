# Create accounts for each region
module "regional" {
  providers = {
    azurerm.infra        = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }
  source = "./regional"
  # for_each = module.orange-storage-config.regions
  for_each = merge(module.orange-storage-config.regions, module.orange-storage-config.regions_to_provision)

  rg_name                    = module.orange-storage-config.regional_rg_names[each.key]
  region                     = each.key
  tents                      = module.orange-storage-config.tents
  groups                     = data.azuread_group.group
  extra_principal_object_ids = [for sp in data.azuread_service_principal.extra_storage_principals : sp.object_id]
}
