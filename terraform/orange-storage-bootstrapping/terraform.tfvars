onboarding-sp = {
  client_id                    = "17eb5cd6-98da-4e7a-bab6-1adefcafa5de"
  tenant_id                    = "8b9ebe14-d942-49e7-ace9-14496d0caff0" # Green tenant
  keyvault_name                = "orange-onboarding-app-kv"
  keyvault_resource_group_name = "orange-onboarding"
  certificate_name             = "orange-onboarding-app-auth"
}

extra_storage_principal_names = [
  "scaling-infra-orange-app"
]

# This is the multi-tenant app (homed in AME) used for syncing infrastructure secrets into clusters.
# We give its Green tenant service principal access to key vaults.
orange-infra-sp-sync = {
  client_id = "79f57bc6-dd99-4067-b1f1-f6abb241be4b"
  object_id = "6a76b261-89e4-4280-a599-695e6b22e6d5" # SP OID in Green tenant
}
