resource "azurerm_resource_group" "orange-resources" {
  name     = module.orange-storage-config.global_rg_name
  location = "uksouth"
}

# Per-tent stores
module "tented-global-storage" {
  providers = {
    azurerm.infra        = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }
  source   = "../modules/storage-base"
  for_each = module.orange-storage-config.tents

  name                = "orng${each.key}" # eg, orngcresco
  resource_group_name = azurerm_resource_group.orange-resources.name
  location            = azurerm_resource_group.orange-resources.location

  group_role_assignments = {
    "Storage Blob Data Contributor" = [
      data.azuread_group.group[each.key].object_id
    ],
    "Reader" = [
      data.azuread_group.group[each.key].object_id
    ]
  }

  tags = {
    "TENT"            = upper(each.key)
    "oai-sensitivity" = "critical"
  }
}

# Ad-hoc storage accounts
module "extra-global-storage" {
  providers = {
    azurerm.infra        = azurerm.infra
    azurerm.auditing-sub = azurerm.auditing-sub
  }
  source   = "../modules/storage-base"
  for_each = module.orange-storage-config.extra_global_accounts

  name                = each.key
  resource_group_name = azurerm_resource_group.orange-resources.name
  location            = try(each.value.location, azurerm_resource_group.orange-resources.location)

  # For orngoaiartifacts, give read-only access to the group
  # For all other storage accounts, keep the existing write access
  group_role_assignments = each.key == "orngoaiartifacts" ? {
    "Storage Blob Data Reader" = [
      data.azuread_group.group[each.value.tent].object_id
    ],
    "Reader" = [
      data.azuread_group.group[each.value.tent].object_id
    ]
    } : {
    "Storage Blob Data Contributor" = [
      data.azuread_group.group[each.value.tent].object_id
    ],
    "Reader" = [
      data.azuread_group.group[each.value.tent].object_id
    ]
  }

  # For orngoaiartifacts, grant write access to specific maintainers only
  # For all other storage accounts, no individual role assignments
  role_assignments = each.key == "orngoaiartifacts" ? {
    "Storage Blob Data Contributor" = [
      for user in keys(data.azuread_user.orngoaiartifacts_maintainers) :
      data.azuread_user.orngoaiartifacts_maintainers[user].object_id
    ]
  } : {}

  builder_access                   = try(each.value.builder_access, false)
  containers                       = try(each.value.containers, {})
  versioning_enabled               = try(each.value.versioning_enabled, false)
  change_feed_enabled              = try(each.value.change_feed_enabled, false)
  cross_tenant_replication_enabled = try(each.value.cross_tenant_replication_enabled, false)

  tags = {
    "TENT"            = upper(each.value.tent)
    "oai-sensitivity" = "critical"
  }
}

data "azurerm_subscription" "current" {}

# Maintainers for orngoaiartifacts storage account - only these users will have write access
data "azuread_user" "orngoaiartifacts_maintainers" {
  provider = azuread.onboarding-sp
  for_each = toset([
    "bolian",
    "liuming",
    "aosama",
    "randydodgen",
    "shivenraina",
    "aiailiji",
    "swen",
    "yshahin",
    "vipulm",
    "sebastko",
    "luw",
    "wxiao"
  ])
  user_principal_name = "${each.key}@green.microsoft.com"
}
module "tented-global-vaults" {
  source   = "../modules/vault-base"
  for_each = module.orange-storage-config.tents

  providers = {
    azurerm              = azurerm
    azurerm.auditing-sub = azurerm.auditing-sub
    azurerm.infra        = azurerm.infra
  }

  name                = "orng${each.key}-vault" # eg, orngcresco-vault
  resource_group_name = azurerm_resource_group.orange-resources.name
  location            = azurerm_resource_group.orange-resources.location
  tenant_id           = data.azurerm_subscription.current.tenant_id

  group_role_assignments = {
    "Key Vault Crypto Service Encryption User" = [
      data.azuread_group.group[each.key].object_id
    ]
  }

  tags = {
    "TENT"            = upper(each.key)
    "oai-sensitivity" = "critical"
  }
}

module "builder-access" {
  providers = {
    azurerm = azurerm.infra
  }
  source   = "../modules/orange-builder-access"
  for_each = module.tented-global-vaults

  name        = each.value.vault.name
  dns_zone    = module.global.private_dns_zones["vault"]
  resource_id = each.value.vault.id
  role_assignments = [
    "Key Vault Crypto Officer"
  ]
}

resource "azurerm_key_vault_key" "key_wrapping_asymmetric_key" {
  depends_on   = [module.builder-access]
  for_each     = module.tented-global-vaults
  name         = "thekey" # Mumford infra name
  key_vault_id = each.value.vault.id
  key_type     = "RSA"
  key_size     = 2048

  key_opts = [
    "unwrapKey",
    "wrapKey",
  ]

  lifecycle {
    prevent_destroy = true
  }
}

import {
  to = module.extra-global-storage["orngoaiartifacts"].azurerm_storage_container.container["storage-map"]
  id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngoaiartifacts/blobServices/default/containers/storage-map"
}

import {
  to = module.extra-global-storage["orngoaiartifacts"].azurerm_storage_container.container["data-gym"]
  id = "/subscriptions/92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e/resourceGroups/orange-resources/providers/Microsoft.Storage/storageAccounts/orngoaiartifacts/blobServices/default/containers/data-gym"
}

moved {
  from = module.extra-global-storage["orngcaas"].azurerm_role_assignment.container_permissions["70e98d21-8300-46f8-b30d-9bfa045fe9a7-Storage Blob Data Reader"]
  to = module.extra-global-storage["orngcaas"].azurerm_role_assignment.container_permissions["caas-data-reader"]
}

moved {
  from = module.extra-global-storage["orngoaiartifacts"].azurerm_role_assignment.container_permissions["c5b25592-4c39-4d18-ac13-1c99e8b87471-Storage Blob Data Reader"]
  to = module.extra-global-storage["orngoaiartifacts"].azurerm_role_assignment.container_permissions["LemonInfraStorageMap"]
}

moved {
  from = module.extra-global-storage["orngtransfer"].azurerm_role_assignment.container_permissions["54d2df28-dcb5-4216-acba-1fd664fba2e2-Storage Blob Data Reader"]
  to = module.extra-global-storage["orngtransfer"].azurerm_role_assignment.container_permissions["StorageBrokerDevault"]
}
