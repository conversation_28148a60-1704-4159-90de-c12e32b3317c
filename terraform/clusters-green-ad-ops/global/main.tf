module "oidc-proxy-ad-ops" {
  providers = {
    azuread = azuread.onboarding-sp
    azurerm = azurerm
  }
  source = "../../modules/oidc-proxy-ad-ops"

  cluster_name    = local.cluster_name
  service_tree_id = local.service_tree_id
}

module "oauth-proxy-ad-ops" {
  providers = {
    azuread = azuread.onboarding-sp
    azurerm = azurerm
  }
  source = "../../modules/oauth-proxy-ad-ops"

  service_tree_id = local.service_tree_id
}
