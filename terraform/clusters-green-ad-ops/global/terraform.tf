terraform {

  backend "azurerm" {
    resource_group_name  = "orange-terraform"
    storage_account_name = "orangetfstate"
    container_name       = "tfstate"
    key                  = "prod-uksouth-7.green-ad"
    subscription_id      = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
    use_azuread_auth     = true
  }

  required_providers {
    azuread = "~> 2.37.0"
    azurerm = "4.19.0"
    kubernetes = {
      version = "2.31.0"
    }
  }
}


data "azurerm_key_vault" "onboarding-sp-keyvault" {
  name                = "orange-onboarding-app-kv"
  resource_group_name = "orange-onboarding"
}

data "azurerm_key_vault_secret" "onboarding-sp-cert" {
  name         = "orange-onboarding-app-auth"
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}

provider "azuread" {
  alias              = "onboarding-sp"
  tenant_id          = "8b9ebe14-d942-49e7-ace9-14496d0caff0"
  client_id          = "17eb5cd6-98da-4e7a-bab6-1adefcafa5de"
  client_certificate = data.azurerm_key_vault_secret.onboarding-sp-cert.value
}


# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
  resource_provider_registrations = "none"
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azurerm" {
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a"
  resource_provider_registrations = "none"
  alias                           = "common"
  features {
    virtual_machine_scale_set {
      roll_instances_when_required = false # or true, which is the default if this block is omitted
    }
  }
}

provider "azuread" {
  tenant_id = "8b9ebe14-d942-49e7-ace9-14496d0caff0"
}
