locals {
  resource_group        = "caas-data-reader"
  location              = "eastus2" # align with the region of `orngcaas`

  caas_cluster_federated_identity_settings = {
    stamps = {
      "eastus2-07" = {
        oidc_issuer_url = "https://eastus2.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/158925ce-ff7f-4356-b551-dbb5ffd4023c/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "eastus2-08" = {
        oidc_issuer_url = "https://eastus2.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/0b00118a-d9c2-43ae-8159-0e3c6acc84f0/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "eastus2-09" = {
        oidc_issuer_url = "https://eastus2.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/b4000cbb-8718-4315-9a65-497bd657292a/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "southcentralus-02" = {
        oidc_issuer_url = "https://southcentralus.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/d8b6c554-e586-4e4d-ba51-0b117e1bee9c/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "southcentralus-03" = {
        oidc_issuer_url = "https://southcentralus.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/455e986c-168f-4d19-84e9-f85b820a65eb/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "southcentralus-04" = {
        oidc_issuer_url = "https://southcentralus.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/243b82ad-777b-45da-af7b-ee49310e0c30/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "westus2-02" = {
        oidc_issuer_url = "https://westus2.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/a92f066e-2fe5-4fc4-8a9c-01bff02cbb4f/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
      "westus2-halo-02-ame" = {
        oidc_issuer_url = "https://westus2.oic.prod-aks.azure.com/33e01921-4d64-4f8c-a055-5bdaffd5e33d/dd615033-0359-429e-a5db-25a139687a84/"
        subject         = "system:serviceaccount:openai:wi-blob-fuse-sa"
      }
    }
  }
}

resource "azurerm_resource_group" "caas_reader_identity_resource_group" {
  name     = local.resource_group
  location = local.location
}

resource "azurerm_user_assigned_identity" "caas_reader_identity" {
  name                = "caas-data-reader-identity"
  resource_group_name = local.resource_group
  location            = local.location
}

resource "azurerm_federated_identity_credential" "caas_reader_federated" {
  for_each              = local.caas_cluster_federated_identity_settings.stamps

  name                = "${each.key}-wi"
  resource_group_name = local.resource_group
  audience            = ["api://AzureADTokenExchange"]
  issuer              = each.value.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.caas_reader_identity.id
  subject             = each.value.subject
}
