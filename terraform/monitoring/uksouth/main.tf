terraform {

  backend "azurerm" {
    resource_group_name  = "iridium-terraform"
    storage_account_name = "iridiumtfstate"
    container_name       = "tfstate"
    subscription_id      = "64467a16-0cdd-4a44-ad9b-83b5540828ac"

    key = "azuremonitor-uksouth"
  }
  required_providers {
    azuread = "~> 2.23.0"
    azurerm = "4.19.0"
  }
}

# PROVIDER SETUP

provider "azurerm" {
  subscription_id                 = "64467a16-0cdd-4a44-ad9b-83b5540828ac"
  resource_provider_registrations = "none"
  features {}
}

module "azuremonitor" {
  source                    = "../../modules/azure-monitor"
  location                  = "uksouth"
  grafana_admins_group_id   = "ada32895-185f-4af1-baa5-02e1be13f826"
  grafana_editors_group_ids = ["ada32895-185f-4af1-baa5-02e1be13f826"]
}
