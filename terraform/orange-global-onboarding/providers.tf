terraform {

  required_providers {
    azuread = "~> 3.0.2"
    azurerm = "4.19.0"

    azapi = {
      source  = "Azure/azapi"
      version = ">= 2.1.0"
    }
  }
}

provider "azuread" {
  alias              = "onboarding-sp"
  client_id          = var.onboarding-sp.client_id
  tenant_id          = var.onboarding-sp.tenant_id
  client_certificate = data.azurerm_key_vault_secret.onboarding-sp-cert.value
}

provider "azurerm" {
  alias                           = "orange-users-provider"
  resource_provider_registrations = "none"
  storage_use_azuread             = true
  subscription_id                 = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS https://microsoftservicetree.com/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8

  features {}
}

provider "azurerm" {
  alias                           = "orange-oai-assets-provider"
  resource_provider_registrations = "none"
  storage_use_azuread             = true
  subscription_id                 = "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e" # AIPLATFORM-ORANGE-OAI-ASSESTS

  features {}
}

# default infra provider
provider "azurerm" {
  resource_provider_registrations = "none"
  storage_use_azuread             = true
  subscription_id                 = "57cd9edb-cae4-478d-9c6a-81ac38d30c4a" # AIPLATFORM-ORANGE-INFRA https://microsoftservicetree.com/subscriptions/57cd9edb-cae4-478d-9c6a-81ac38d30c4a

  features {}
}

provider "azapi" {
  subscription_id = "da7f69c1-646c-4df5-91ac-6cca3d502bd8" # AIPLATFORM-ORANGE-USERS https://microsoftservicetree.com/subscriptions/da7f69c1-646c-4df5-91ac-6cca3d502bd8
}

data "azurerm_key_vault" "onboarding-sp-keyvault" {
  name                = var.onboarding-sp.keyvault_name
  resource_group_name = var.onboarding-sp.keyvault_resource_group_name
}

data "azurerm_key_vault_secret" "onboarding-sp-cert" {
  name         = var.onboarding-sp.certificate_name
  key_vault_id = data.azurerm_key_vault.onboarding-sp-keyvault.id
}


