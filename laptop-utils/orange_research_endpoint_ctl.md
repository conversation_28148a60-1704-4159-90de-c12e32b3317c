# Orange Publish Research Endpoint - Documentation

## Overview
orange_research_endpoint_ctl is a command-line utility to manage the research endpoints

## Requirements
- `kubectl` command-line tool configured with access to your Kubernetes clusters

## Usage
### Endpoint
```bash
orange_research_endpoint_ctl publish <pod-name> <port> <url path>
orange_research_endpoint_ctl delete <pod-name> <port> <url path>
orange_research_endpoint_ctl list

# examples
# publish
orange_research_endpoint_ctl publish test-twapi-0 5122 /v1/inference

# delete
orange_research_endpoint_ctl delete test-twapi-0 5122 /v1/inference

# list
orange_research_endpoint_ctl list
```

### Endpoint user OID Allowed List
If OID allowed list is added to an endpoint, the user needs to be in the required moonfire group and in this oid list to access to this endpoint
```bash
orange_research_endpoint_ctl oid add <pod-name> <port> <comma separated oid list>
orange_research_endpoint_ctl oid remove <pod-name> <port> <comma separated oid list>
orange_research_endpoint_ctl oid remove-all <pod-name> <port>
orange_research_endpoint_ctl oid list <pod-name> <port>

# examples
# add
orange_research_endpoint_ctl oid add test-twapi-0 5122 "oid1, oid2"

# remove
orange_research_endpoint_ctl oid remove test-twapi-0 5122 "oid2"

# remove-all
orange_research_endpoint_ctl oid remove-all test-twapi-0 5122

# list
orange_research_endpoint_ctl oid list test-twapi-0 5122

```

### Endpoint Managed Identity Allowed List
Only the managed identities in the endpoint MI allowed list can access to the endpoint
```bash
orange_research_endpoint_ctl mi add <pod-name> <port> <comma separated managed identity oid list>
orange_research_endpoint_ctl mi remove <pod-name> <port> <comma separated managed identity oid list>
orange_research_endpoint_ctl mi remove-all <pod-name> <port>
orange_research_endpoint_ctl mi list <pod-name> <port>

# examples
# add
orange_research_endpoint_ctl mi add test-twapi-0 5122 "mi1, mi2"

# remove
orange_research_endpoint_ctl mi remove test-twapi-0 5122 "mi2"

# remove-all
orange_research_endpoint_ctl mi remove-all test-twapi-0 5122

# list
orange_research_endpoint_ctl mi list test-twapi-0 5122
```

## Supported Orange production clusters
- azml-prod-uksouth-azhub-7
- azml-prod-uksouth-azhub-8
- azml-prod-uksouth-azhub-15
- azml-prod-southcentralus-hpe-2
- azml-prod-southcentralus-hpe-3
- azml-prod-southcentralus-hpe-4
- azml-prod-southcentralus-hpe-5
- azml-prod-westus2-azhub-19