# Orange Get Quota Utility

A command-line utility to view and analyze Quota information in the Orange cluster.

## Overview

`orange_get_quota` is a command-line tool that provides a clear, formatted view of quota information for the particular Orange cluster, including assigned quotas, usable quotas, and availability by priority level. The tool presents the information in three tables:

1. **Quota Overview**: Shows high-level quota information per team for the cluster, if specified, or the default cluster in the current kubectl context.
2. **Quota Usage**: Displays how quota is being used by specific users.
3. **Quota Availability**: Details available quota by team and pool/job priority level.


## Usage

### Basic Usage

```bash
# Get quota information for all teams in the current Orange cluster that is set in the active Kubernetes context.
orange_get_quota
```

### Specifying Clusters

```bash
# Get quota information for a specific cluster without changing your current context
orange_get_quota -c prod-uksouth-15
```

### Filtering by Team

```bash
# Show quota information for a specific team only
orange_get_quota -t team-moonfire-dri

# Use partial team names
orange_get_quota -t dri

# Combine with cluster selection
orange_get_quota -c prod-uksouth-15 -t dri
```

## Command-line Options

| Option | Long Form | Description |
|--------|-----------|-------------|
| `-c CLUSTER` | `--cluster CLUSTER` | Kubernetes cluster context to use |
| `-t TEAM` | `--team TEAM` | Filter results by team name, partial matches are supported and are case-insensitive |

## Example Output

When you run `orange_get_quota`, you'll see output similar to the following, the Quota values are the GPU counts:

```
==================================================
               QUOTA OVERVIEW
==================================================

+-------------------+---------------------------+------------------+----------------+
| Team Name         | Cluster                   |   Assigned Quota |   Usable Quota |
+===================+===========================+==================+================+
| team-moonfire-dri | prod-southcentralus-hpe-5 |               96 |             96 |
+-------------------+---------------------------+------------------+----------------+
| team-moonfire-mai | prod-southcentralus-hpe-5 |             1792 |           1792 |
+-------------------+---------------------------+------------------+----------------+


==================================================
               QUOTA USAGE
==================================================

+---------------------------+-------------------+------------+-------------+---------+
| Cluster                   | Team Name         | Priority   | User        |   Count |
+===========================+===================+============+=============+=========+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Critical   | ynagaraj    |      48 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Critical   | mipark      |       8 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Low        | alexsanchez |      32 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Low        | ravichak    |      32 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Low        | wukai       |       8 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | Critical   | wxiao       |      48 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | Critical   | adelgiorno  |      16 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | High       | adelgiorno  |      80 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | High       | khurams     |      16 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | Low        | zelin       |      80 |
+---------------------------+-------------------+------------+-------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | Low        | rupaljain   |      16 |
+---------------------------+-------------------+------------+-------------+---------+

USAGE SUMMARY BY WORKLOAD PRIORITY

+---------------------------+------------+---------------+
| Cluster                   | Priority   |   Total Count |
+===========================+============+===============+
| prod-southcentralus-hpe-5 | Critical   |           120 |
+---------------------------+------------+---------------+
| prod-southcentralus-hpe-5 | High       |            96 |
+---------------------------+------------+---------------+
| prod-southcentralus-hpe-5 | Low        |           168 |
+---------------------------+------------+---------------+

TOTAL USAGE SUMMARY:
  prod-southcentralus-hpe-5: 384


==================================================
             QUOTA AVAILABILITY
==================================================

Team: team-moonfire-dri

+---------------------------+-------------------+------------+---------+
| Cluster                   | Team Name         | Priority   |   Count |
+===========================+===================+============+=========+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Critical   |      40 |
+---------------------------+-------------------+------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-dri | High       |      40 |
+---------------------------+-------------------+------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-dri | Low        |       0 |
+---------------------------+-------------------+------------+---------+

--------------------------------------------------

Team: team-moonfire-mai

+---------------------------+-------------------+------------+---------+
| Cluster                   | Team Name         | Priority   |   Count |
+===========================+===================+============+=========+
| prod-southcentralus-hpe-5 | team-moonfire-mai | Critical   |    1728 |
+---------------------------+-------------------+------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | High       |    1632 |
+---------------------------+-------------------+------------+---------+
| prod-southcentralus-hpe-5 | team-moonfire-mai | Low        |    1536 |
+---------------------------+-------------------+------------+---------+

```

## Notes

- Negative values in the Quota Availability table indicate overallocation (more quota is being used than was initially allocated for that priority)
- The tool requires `kubectl` and proper Kubernetes configuration to query cluster information directly
- When using team filtering with `-t`, partial matches are supported and are case-insensitive