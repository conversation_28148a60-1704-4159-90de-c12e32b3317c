#!/bin/bash

export PATH=$PATH:$HOME/.openai/bin

ACTION=$1

# Supported actions: publish, delete, list
if [ "$ACTION" != "publish" ] && [ "$ACTION" != "delete" ] && [ "$ACTION" != "list" ] && [ "$ACTION" != "oid" ] && [ "$ACTION" != "mi" ]; then
  echo "Supported actions: publish, delete, list, oid, mi"
  exit 1
fi


# Replace the associative array with a simpler format for compatibility with bash versions < 4
CLUSTER_IP_MAP="prod-uksouth-7:************* prod-uksouth-8:*********** prod-uksouth-15:************* stage-southcentralus-hpe-1:************* prod-southcentralus-hpe-2:************ prod-southcentralus-hpe-3:*************** prod-southcentralus-hpe-4:************* prod-southcentralus-hpe-5:********** prod-westus2-19:***************"

# Function to get the IP for a given cluster
get_cluster_ip() {
  local cluster=$1
  for entry in $CLUSTER_IP_MAP; do
    local key="${entry%%:*}"
    local value="${entry##*:}"
    if [ "$key" = "$cluster" ]; then
      echo "$value"
      return
    fi
  done
  echo ""
}

# Function to check if a value is in a comma-separated list
is_in_csv_list() {
  local list="$1"
  local value="$2"
  # Remove all spaces
  local clean_list=$(echo "$list" | tr -d ' ')
  # Check if the value is in the list
  if [[ ",$clean_list," == *",$value,"* ]]; then
    return 0
  else
    return 1
  fi
}

# Function to add a value to a comma-separated list and clean up the result
add_to_csv_list() {
  local list="$1"
  local value="$2"
  # Remove all spaces
  local clean_list=$(echo "$list" | tr -d ' ')
  # Check if the value is already in the list
  if is_in_csv_list "$clean_list" "$value"; then
    echo "$clean_list"
  else
    # Append the new value to the list
    if [ -z "$clean_list" ]; then
      echo "$value"
    else
      echo "$clean_list,$value"
    fi
  fi
}

# Function to remove a value from a comma-separated list and clean up the result
remove_from_csv_list() {
  local list="$1"
  local value="$2"
  # Remove all spaces
  local clean_list=$(echo "$list" | tr -d ' ')
  # Escape sed delimiter and other special characters in value
  local esc_value=$(printf '%s' "$value" | sed -e 's/[\&/]/\\&/g')
  # Remove all occurrences of the value, with or without leading/trailing commas
  local result=$(echo ",$clean_list," | sed -E "s/,$esc_value,/,/g" | sed -E 's/^,|,$//g')
  echo "$result"
}

CURRENT_CLUSTER=$(kubectl config current-context)

# Ensure CURRENT_CLUSTER is not empty and matches a key in CLUSTER_IP_MAP
if [ -z "$CURRENT_CLUSTER" ]; then
  echo "Error: Current cluster context is not set."
  exit 1
fi

# Public action
# Create a new service for the pod with different port and path
if [ "$ACTION" = "publish" ]; then
  PROXY_IP=$(get_cluster_ip "$CURRENT_CLUSTER")

  if [ -z "$PROXY_IP" ]; then
    echo "Error: Proxy IP for cluster $CURRENT_CLUSTER is not defined in CLUSTER_IP_MAP."
    exit 1
  fi
  
  POD_NAME=$2
  PORT=$3
  URLPATH=$4

  if [ -z "$POD_NAME" ] || [ -z "$PORT" ] || [ -z "$URLPATH" ]; then
    echo "Usage: $0 publish <pod-name> <port> <url path>"
    exit 1
  fi

  if ! kubectl get pod $POD_NAME > /dev/null 2>&1; then
    echo "Pod $POD_NAME does not exist."
    exit 1
  fi
  
  SERVICE_NAME="${POD_NAME}-svc-${PORT}"

  if kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
    echo "Service $SERVICE_NAME already exists. Add the new path to the existing service."
    # get the annotation exposed-research-ep-paths=$URLPATH
    EXISTING_PATHS=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-paths}')
    # check if the URLPATH is already in the existing paths
    if is_in_csv_list "$EXISTING_PATHS" "$URLPATH" ; then
      echo "Path $URLPATH already exists in the service $SERVICE_NAME."
      exit 1
    fi
    # append the new path to the existing paths
    NEW_PATHS=$(add_to_csv_list "$EXISTING_PATHS" "$URLPATH")
    kubectl annotate svc $SERVICE_NAME exposed-research-ep-paths=$NEW_PATHS --overwrite
  else
    echo "Creating new service $SERVICE_NAME."
    kubectl expose pod $POD_NAME --type=ClusterIP --name=$SERVICE_NAME --port=$PORT --target-port=$PORT
    kubectl label svc $SERVICE_NAME expose-research-ep=true
    kubectl annotate svc $SERVICE_NAME exposed-research-ep-ports=$PORT exposed-research-ep-paths=$URLPATH
  fi

  NAMESPACE=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.namespace}')

  SERVICE_URL="http://$SERVICE_NAME.$NAMESPACE.svc.cluster.local:$PORT"

  echo "Service URL: $SERVICE_URL"
  echo "Proxy IP: $PROXY_IP"

  echo "Usage:"
  echo "1.Get access-token:"
  echo "(Linux) access_token=\$(az account get-access-token --resource 2a750fd4-529b-4678-b332-6331e201131c --query accessToken -o tsv)"
  echo "(Powershell) \$access_token = az account get-access-token --query accessToken --resource api://2a750fd4-529b-4678-b332-6331e201131c -o tsv"
  echo "2.Call the endpoint:"
  echo "curl -k \"https://$PROXY_IP:443$URLPATH\" -H \"Authorization: Bearer \$access_token\" -H \"SERVICE_URL:$SERVICE_URL\" -d \"{}\""
  echo "Please note that we may need to configure the HTTP method explicitly in the curl command."
  echo "For example, if the HTTP method is GET, we need to add -X GET to the curl command."

  exit 0
fi

# Delete action
if [ "$ACTION" = "delete" ]; then
  POD_NAME=$2
  PORT=$3
  URLPATH=$4

  if [ -z "$POD_NAME" ] || [ -z "$PORT" ] || [ -z "$URLPATH" ]; then
    echo "Usage: $0 delete <pod-name> <port> <url path>"
    exit 1
  fi

  LEGACY_SERVICE_NAME="${POD_NAME}-re-svc"
  if kubectl get svc $LEGACY_SERVICE_NAME > /dev/null 2>&1; then
    echo "Deleting legacy service $LEGACY_SERVICE_NAME."
    kubectl delete svc $LEGACY_SERVICE_NAME
    exit 0
  fi

  SERVICE_NAME="${POD_NAME}-svc-${PORT}"

  if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
    echo "Service $SERVICE_NAME does not exist."
    exit 1
  fi

  EXISTING_PATHS=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-paths}')
  if is_in_csv_list $EXISTING_PATHS $URLPATH; then
    # remove the path from the existing paths
    NEW_PATHS=$(remove_from_csv_list $EXISTING_PATHS $URLPATH)
    kubectl annotate svc $SERVICE_NAME exposed-research-ep-paths="$NEW_PATHS" --overwrite
    echo "Path $URLPATH removed from the service $SERVICE_NAME."

    # If no paths left, delete the service
    if [ -z "$NEW_PATHS" ]; then
      kubectl delete svc $SERVICE_NAME
      echo "Service $SERVICE_NAME deleted."
    fi
  else
    echo "Path $URLPATH does not exist in the service $SERVICE_NAME."
    exit 1
  fi

  exit 0
fi

# List action
if [ "$ACTION" = "list" ]; then
  # List all services with the label expose-research-ep=true
  kubectl get svc -l expose-research-ep=true -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.metadata.namespace}{"\t"}{.spec.ports[0].port}{"\t"}{.metadata.annotations.exposed-research-ep-paths}{"\n"}{end}'
fi

# OID action
if [ "$ACTION" = "oid" ]; then
  SUB_ACTION=$2
  POD_NAME=$3
  PORT=$4
  OIDLIST=$5

  if [ -z "$SUB_ACTION" ] || ([ "$SUB_ACTION" != "add" ] && [ "$SUB_ACTION" != "remove" ] && [ "$SUB_ACTION" != "remove-all" ] && [ "$SUB_ACTION" != "list" ]); then
    echo "Supported OID allowed-list actions: add, remove, remove-all, list"
    exit 1
  fi

  if [ "$SUB_ACTION" = "add" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ] || [ -z "$OIDLIST" ]; then
      echo "Usage: $0 oid add <pod-name> <port> <oid list>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    EXISTING_OIDLIST=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-oid-allowed-list}')
    
    # OIDLIST is a comma separated list of OIDs
    # check if the each element in the OIDLIST is already in the existing OID list
    # if not, append it to the existing OID list
    IFS=',' read -r -a OID_ARRAY <<< "$OIDLIST"
    for OID in "${OID_ARRAY[@]}"; do
      # trim the space of OID
      OID=$(echo $OID | sed 's| *||g')
      if is_in_csv_list "$EXISTING_OIDLIST" "$OID"; then
        echo "OID $OID already exists in the service $SERVICE_NAME."
        continue
      fi
      # append the new OID to the existing OID list
      EXISTING_OIDLIST=$(add_to_csv_list "$EXISTING_OIDLIST" "$OID")
    done

    # append the new OID to the existing OID list
    kubectl annotate svc $SERVICE_NAME exposed-research-ep-allowed-list="$EXISTING_OIDLIST" --overwrite
  fi

  if [ "$SUB_ACTION" = "remove" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ] || [ -z "$OIDLIST" ]; then
      echo "Usage: $0 oid remove <pod-name> <port> <oid list>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    EXISTING_OIDLIST=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-allowed-list}')
    
    IFS=',' read -r -a OID_ARRAY <<< "$OIDLIST"
    for OID in "${OID_ARRAY[@]}"; do
      # trim the space of OID
      OID=$(echo $OID | sed 's| *||g')
      if ! is_in_csv_list "$EXISTING_OIDLIST" "$OID"; then
        echo "OID $OID does not exist in the service $SERVICE_NAME."
        continue
      fi
      # remove the OID from the existing OID list
      EXISTING_OIDLIST=$(remove_from_csv_list "$EXISTING_OIDLIST" "$OID")
    done

    if [ -z "$EXISTING_OIDLIST" ]; then
      kubectl annotate svc $SERVICE_NAME exposed-research-ep-allowed-list- --overwrite
      exit 0
    fi

    kubectl annotate svc $SERVICE_NAME exposed-research-ep-allowed-list="$EXISTING_OIDLIST" --overwrite
  fi

  if [ "$SUB_ACTION" = "remove-all" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ]; then
      echo "Usage: $0 oid remove-all <pod-name> <port>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    kubectl annotate svc $SERVICE_NAME exposed-research-ep-allowed-list- --overwrite
  fi

  if [ "$SUB_ACTION" = "list" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ]; then
      echo "Usage: $0 oid list <pod-name> <port>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    EXISTING_OIDLIST=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-allowed-list}')
    echo "OID allowed list: $EXISTING_OIDLIST"
  fi

  exit 0
fi

# Managed Identity action
if [ "$ACTION" = "mi" ]; then
  SUB_ACTION=$2
  POD_NAME=$3
  PORT=$4
  OIDLIST=$5

  if [ -z "$SUB_ACTION" ] || ([ "$SUB_ACTION" != "add" ] && [ "$SUB_ACTION" != "remove" ] && [ "$SUB_ACTION" != "remove-all" ] && [ "$SUB_ACTION" != "list" ]); then
    echo "Supported Managed Identity allowed-list actions: add, remove, remove-all, list"
    exit 1
  fi

  if [ "$SUB_ACTION" = "add" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ] || [ -z "$OIDLIST" ]; then
      echo "Usage: $0 mi add <pod-name> <port> <managed identity oid list>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    EXISTING_OIDLIST=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-mi-allowed-list}')

    IFS=',' read -r -a OID_ARRAY <<< "$OIDLIST"
    for OID in "${OID_ARRAY[@]}"; do
      # trim the space of OID
      OID=$(echo $OID | sed 's| *||g')
      if is_in_csv_list "$EXISTING_OIDLIST" "$OID"; then
        echo "Managed Identity $OID already exists in the service $SERVICE_NAME."
        continue
      fi
      # append the new OID to the existing OID list
      EXISTING_OIDLIST=$(add_to_csv_list "$EXISTING_OIDLIST" "$OID")
    done

    # append the new OID to the existing OID list
    kubectl annotate svc $SERVICE_NAME exposed-research-ep-mi-allowed-list="$EXISTING_OIDLIST" --overwrite
  fi

  if [ "$SUB_ACTION" = "remove" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ] || [ -z "$OIDLIST" ]; then
      echo "Usage: $0 mi remove <pod-name> <port> <managed identity oid list>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    EXISTING_OIDLIST=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-mi-allowed-list}')

    IFS=',' read -r -a OID_ARRAY <<< "$OIDLIST"
    for OID in "${OID_ARRAY[@]}"; do
      # trim the space of OID
      OID=$(echo $OID | sed 's| *||g')
      if ! is_in_csv_list "$EXISTING_OIDLIST" "$OID"; then
        echo "OID $OID does not exist in the service $SERVICE_NAME."
        continue
      fi
      # remove the OID from the existing OID list
      EXISTING_OIDLIST=$(remove_from_csv_list "$EXISTING_OIDLIST" "$OID")
    done

    if [ -z "$EXISTING_OIDLIST" ]; then
      kubectl annotate svc $SERVICE_NAME exposed-research-ep-mi-allowed-list- --overwrite
      exit 0
    fi

    kubectl annotate svc $SERVICE_NAME exposed-research-ep-mi-allowed-list="$EXISTING_OIDLIST" --overwrite
  fi

  if [ "$SUB_ACTION" = "remove-all" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ]; then
      echo "Usage: $0 mi remove-all <pod-name> <port>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    kubectl annotate svc $SERVICE_NAME exposed-research-ep-mi-allowed-list- --overwrite
  fi

  if [ "$SUB_ACTION" = "list" ]; then
    if [ -z "$POD_NAME" ] || [ -z "$PORT" ]; then
      echo "Usage: $0 mi list <pod-name> <port>"
      exit 1
    fi

    SERVICE_NAME="${POD_NAME}-svc-${PORT}"

    if ! kubectl get svc $SERVICE_NAME > /dev/null 2>&1; then
      echo "Service $SERVICE_NAME does not exist."
      exit 1
    fi

    EXISTING_OIDLIST=$(kubectl get svc $SERVICE_NAME -o jsonpath='{.metadata.annotations.exposed-research-ep-mi-allowed-list}')
    echo "Managed Identity allowed list: $EXISTING_OIDLIST"
  fi

  exit 0
fi