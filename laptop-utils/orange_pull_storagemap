#!/bin/zsh

set -e

local_cache=~/.cache/openai_clusters/storage-map.json
[ ! -z "$APPLIED_STORAGE_MAP" ] && { local_cache=~/.cache/openai_clusters/storage-map-applied.json; }

echo "Downloading storage map..."
bbb cp az://orngoaiartifacts/storage-map/storage-map.json ~/.cache/openai_clusters/storage-map.json

if [ -f "$MONOREPO_ROOT/_msft/rcall_overrides/orange_laptop.sh" ]; then
    echo "Running code version-specific initialization on the laptop..."
    source "$MONOREPO_ROOT/_msft/rcall_overrides/orange_laptop.sh"
else
    echo "Code version-specific initialization not found. Skipping..."
fi