apiVersion: v1
items:
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"iridium-pilot"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":160,"quotaNode":20,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-10T05:43:32Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: iridium-pilot
    resourceVersion: "172155458"
    uid: a0f8dd7c-99c3-4028-88f7-514083ac8214
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 160
      quotaNode: 20
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"team-moonfire-bic"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":640,"quotaNode":80,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-30T22:41:40Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: team-moonfire-bic
    resourceVersion: "238426238"
    uid: e7899b72-5e61-49b0-9dc7-d7b069ecba0e
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 640
      quotaNode: 80
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"team-moonfire-dri"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":160,"quotaNode":20,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-11T19:58:28Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: team-moonfire-dri
    resourceVersion: "175339841"
    uid: 0406cafe-be05-4d9e-8126-b4b8ed760993
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 160
      quotaNode: 20
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"team-moonfire-genai"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":640,"quotaNode":80,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-11T19:59:13Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: team-moonfire-genai
    resourceVersion: "175341609"
    uid: 3cb6807c-bf6f-4e7e-b58e-c3469df33be7
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 640
      quotaNode: 80
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"team-moonfire-github"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":640,"quotaNode":80,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-25T18:29:39Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: team-moonfire-github
    resourceVersion: "220491942"
    uid: 6c782902-80f0-42ee-8503-9e872d621bee
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 640
      quotaNode: 80
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"team-moonfire-mai"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":640,"quotaNode":80,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-22T21:26:38Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: team-moonfire-mai
    resourceVersion: "210953264"
    uid: 3efbc4a9-0db5-4e3e-b874-02b5b53e5b46
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 640
      quotaNode: 80
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
- apiVersion: brix.openai.com/v1alpha1
  kind: Quota
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Quota","metadata":{"annotations":{},"labels":{"brix.openai.com/scheduler":"brix"},"name":"team-moonfire-security"},"spec":{"allocations":[{"cluster":"prod-southcentralus-hpe-2","dedicated":false,"displayName":"HPE GPU","quotaCount":640,"quotaNode":80,"sku":"gpu1"}]}}
    creationTimestamp: "2025-04-30T22:41:24Z"
    generation: 1
    labels:
      brix.openai.com/scheduler: brix
    name: team-moonfire-security
    resourceVersion: "238426240"
    uid: ffe21438-f59a-49c1-ab9f-a15ccf11f815
  spec:
    allocations:
    - cluster: prod-southcentralus-hpe-2
      dedicated: false
      displayName: HPE GPU
      priority: 20
      quotaCount: 640
      quotaNode: 80
      sku: gpu1
  status:
    allocations:
    - allocatedCount: 0
      allocatedNode: 0
      availability:
        Critical: 0
        High: 0
        Low: 0
        Mid: 0
      quotaStatusPerLocation:
      - allocatedQuota: 0
        availabilityPerPriority:
          Critical: 0
          High: 0
          Low: 0
          Mid: 0
        location: {}
        userUsageEntry: []
      quotaUsage: {}
kind: List
metadata:
  resourceVersion: ""
  selfLink: ""
