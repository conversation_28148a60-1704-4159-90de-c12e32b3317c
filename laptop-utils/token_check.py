import os, json, time

TENANT_ID_AME = "33e01921-4d64-4f8c-a055-5bdaffd5e33d"
TENANT_ID_GREEN = "8b9ebe14-d942-49e7-ace9-14496d0caff0"
TENANT_ID_CORP = "72f988bf-86f1-41af-91ab-2d7cd011db47"

TENANTS = {
    TENANT_ID_AME: "AME",
    TENANT_ID_CORP: "CORP",
    TENANT_ID_GREEN: "GREEN"
}

def read_token_cache():
    token_cache = os.path.expanduser("~/.azure/msal_token_cache.json")
    if os.path.isfile(token_cache):
        with open(token_cache, "r") as file:
            data = json.load(file)
            return data
    else:
        raise RuntimeError("can't find " + token_cache)


def check_expiration(token):
    tenant_id = token.get("realm")  # Get tenant ID from the token
    if tenant_id not in TENANTS:
        print(f"⚠️ Unknown tenant ID: {tenant_id}")
        print("-------------------------------------------------------")
        return

    tenant_name = TENANTS[tenant_id]  # Retrieve tenant name from mapping
    expiration_time = int(token["expires_on"])
    expiration_in_UTC = time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime(expiration_time))
    current_time = time.time()

    # Determine status based on expiration
    if expiration_time < current_time:
        status = "❌ EXPIRED"
    elif expiration_time - current_time < 600:  # 10-minute warning
        status = "⚠️ NEAR EXPIRATION (check soon!)"
    else:
        status = "✅ VALID (not yet expired)"

    # Display token details with tenant information
    print(f"🔒 Token for:          {token['target']}")
    print(f"   🏢 Tenant:          {tenant_name} ({tenant_id})")
    print(f"   ⏳ Expires on:       {expiration_in_UTC} UTC")
    print(f"   {status}")
    print("-------------------------------------------------------")


if __name__ == "__main__":
    cache = read_token_cache()
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime(time.time()))
    print(f"🌐 Current time:        {current_time} UTC")
    print("-------------------------------------------------------")
    for token in cache["AccessToken"].values():
        check_expiration(token)