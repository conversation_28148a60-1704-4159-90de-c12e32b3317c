# Orange Get Job InQueue - Documentation

## Overview
orange_get_pendingjob is a command-line utility that helps you view and manage pending GPU jobs in Kubernetes clusters. This tool queries pool objects with GPU requirements that are currently waiting to be scheduled.

## Installation

1. Ensure the script is executable:
   ```bash
   chmod +x /path/to/orange_get_pendingjob
   ```

2. For convenient access from anywhere, consider adding the script's directory to your PATH:
   ```bash
   echo 'export PATH=$PATH:/home/<USER>/code/bootstrapping/laptop-utils/pending_job' >> ~/.bashrc
   source ~/.bashrc
   ```

## Requirements

- Python 3
- `kubectl` command-line tool configured with access to your Kubernetes clusters
- Required Python packages: `argparse`, `yaml`, `subprocess`

## Usage

```bash
orange_get_pendingjob [OPTIONS]
```

### Options

| Option | Description |
|--------|-------------|
| `-h, --help` | Show the help message and exit |
| `-f FILE, --file FILE` | Path to a YAML file containing pool objects (from `kubectl get pool -A -o yaml`) |
| `-c CONTEXT, --context CONTEXT` | Kubernetes context to use (if not specified, uses the current context) |
| `-v, --verbose` | Enable verbose logging |

### Examples

1. Using the current Kubernetes context:
   ```bash
   orange_get_pendingjob
   ```

2. Using a specific Kubernetes context:
   ```bash
   orange_get_pendingjob -c my-cluster-context
   ```

3. Loading from a pre-saved YAML file:
   ```bash
   orange_get_pendingjob -f my-pools-dump.yaml
   ```

4. With verbose logging:
   ```bash
   orange_get_pendingjob -v
   ```

## Output

The tool displays a table with the following columns:

- **Team Name**: Team associated with the job
- **Priority**: Job's priority class
- **Creation Timestamp**: When the job was created
- **Total Node**: Number of nodes required
- **Namespace**: Kubernetes namespace
- **Pool Name**: Name of the pool resource

## Example Output

Below is an example of the output generated by the `orange_get_pendingjob` tool:

```plaintext
% ./orange_get_pendingjob
This table lists all pending jobs in this cluster
Team Name            Priority        Creation Timestamp        Total Node      Namespace       Pool Name
TEAM-MOONFIRE-GENAI  team-critical   2025-04-11T19:08:11Z      1               luw             luw-ps7-controller-w0
TEAM-MOONFIRE-GENAI  team-critical   2025-04-11T19:08:21Z      1               luw             luw-ps7-rollout-worker-w0
iridium-pilot        low-priority    2025-04-09T16:32:55Z      2               naraymad        devbox
iridium-pilot        low-priority    2025-04-11T11:36:52Z      2               visahanmohan    devbox
iridium-pilot        low-priority    2025-04-14T21:10:20Z      2               agoswami        devbox
iridium-pilot        low-priority    2025-04-21T10:24:21Z      2               ajaypalsingh    devbox
iridium-pilot        low-priority    2025-04-25T05:57:56Z      2               vaibhavmadan    vmdevbox
iridium-pilot        team-critical   2025-03-16T03:37:49Z      1               asaipulla       asdev
team-moonfire-dri    team-mild       2025-04-16T11:39:16Z      1               hnamburi        hnamburi-d16-0312-1-train-w0
team-moonfire-genai  low-priority    2025-04-17T00:17:20Z      2               yiyoulin        devbox
```

## How It Works

The script filters Kubernetes pool objects that:
1. Have GPU requirements (via `brix.openai.com/resource-limit-gpu` annotation)
2. Are pending (where `scheduledReplicas = 0`)

It then extracts relevant information and displays it in a formatted table.

## Troubleshooting

- If you get a "command not found" error, make sure the script is in your PATH or specify the full path.
- If you get a Python script not found error, verify the Python script exists in the same directory.
- Make sure your kubectl configuration is valid and you have access to the cluster.
