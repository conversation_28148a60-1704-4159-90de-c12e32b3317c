apiVersion: v1
items:
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: amrh
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 757cfb1a-0ceb-4951-bed5-dde90c65da98
    resourceVersion: "********"
    uid: 8b751b1d-eaa9-433d-ab07-161d0df17ed7
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:11Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:11Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: aosama
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: db4bd534-14c7-4861-8276-6f423872c1fd
    resourceVersion: "********"
    uid: ********-bea4-4428-8e46-e5dae3049f9e
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:13Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:13Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: aupadhyay
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 8f0069d9-a95f-498b-bda8-ac763b2f96db
    resourceVersion: "********"
    uid: a4c922f1-f4e9-4738-a692-6159ad2235df
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:22:07Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:50Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: bolian
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 72bdeec3-593f-4682-b657-2057c6948bad
    resourceVersion: "********"
    uid: 7049e452-97a1-466f-9501-979cb4e6cb82
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-15T00:51:56Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 30
    labels:
      brix.openai.com/scheduler: perhonen
      brix.openai.com/task-id: devbox1
      brix.openai.com/workload: devbox1
    name: devbox1
    namespace: bolian
    resourceVersion: "********"
    uid: 4faf5fa5-40f4-4b48-a667-2fb8995575f0
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: false
    extensions: {}
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1
          creationTimestamp: null
          labels:
            brix.openai.com/git: devbox1
            brix.openai.com/scheduler: perhonen
            brix.openai.com/task-id: devbox1
            brix.openai.com/workload: devbox1
          namespace: bolian
        spec:
          containers:
          - name: main
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
            securityContext:
              privileged: false
            stdin: true
          restartPolicy: Always
          schedulerName: perhonen
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:20:09Z"
      lastTransitionTime: "2025-04-29T20:20:09Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-29T20:20:09Z"
      lastTransitionTime: "2025-04-29T20:20:09Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:20:09Z"
      lastTransitionTime: "2025-04-29T20:20:09Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-29T20:20:09Z"
      lastTransitionTime: "2025-04-15T00:51:57Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:20:09Z"
      lastTransitionTime: "2025-04-15T00:51:57Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:20:09Z"
      lastTransitionTime: "2025-04-15T00:51:57Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 30
    revision: 5c5bcfc865
    workers:
      currentReplicas: 0
      failedReplicas: 1
      message: 0/0/0
      phases: F
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:48Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: changov
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: af3898ea-fede-4916-99dc-fcfd302d7880
    resourceVersion: "********"
    uid: 316c03f8-a07e-4065-b44f-ed0ce96f5bfd
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:22:07Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:48Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:48Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:48Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangechangov/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","log_relpath":"devbox14-low-12cpu"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"devbox14-low-12cpu","job_id":"job-250424072217X63XFAJH","openai.com/job-type":"interactive","torchflow.openai.com/component":"ray-devbox"},"name":"devbox14-low-12cpu","namespace":"changov"},"spec":{"coordinator":true,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":0,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangechangov/rcall/results/devbox14-low-12cpu","brix.openai.com/target-quotas":"iridium-pilot","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"devbox14-low-12cpu","rcall_call_timestamp":"1745479337.9427238"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"devbox14-low-12cpu","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"devbox14-low-12cpu","job_id":"job-250424072217X63XFAJH","openai.com/job-type":"interactive","session_id":"91ema4ghj20i","torchflow.openai.com/component":"ray-devbox"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["changov"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250424072217X63XFAJH"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":9090,"name":"ts-oai-app","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor7","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/sys","name":"sysfs","readOnly":true},{"mountPath":"/host/oai-hwhealth","name":"oai-hwhealth","readOnly":true},{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"low-priority","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/sys","type":"Directory"},"name":"sysfs"},{"hostPath":{"path":"/scratch/host-mountable/oai-hwhealth","type":"DirectoryOrCreate"},"name":"oai-hwhealth"},{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: devbox14-low-12cpu
      rayfract.openai.com/ray_done: "no"
    creationTimestamp: "2025-04-24T07:22:21Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 10
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: devbox14-low-12cpu
      experiment_name: devbox14-low-12cpu
      job_id: job-250424072217X63XFAJH
      openai.com/job-type: interactive
      torchflow.openai.com/component: ray-devbox
    name: devbox14-low-12cpu
    namespace: changov
    resourceVersion: "26992318"
    uid: 223a5f60-cb63-47f5-9374-68c625d12804
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: true
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 0
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangechangov/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangechangov/rcall/results/devbox14-low-12cpu
            brix.openai.com/target-quotas: iridium-pilot
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: devbox14-low-12cpu
            rcall_call_timestamp: "1745479337.9427238"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: devbox14-low-12cpu
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: devbox14-low-12cpu
            cilium.openai.com/network-policy: strict
            experiment_name: devbox14-low-12cpu
            job_id: job-250424072217X63XFAJH
            openai.com/job-type: interactive
            session_id: 91ema4ghj20i
            torchflow.openai.com/component: ray-devbox
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - changov
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250424072217X63XFAJH
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 9090
              name: ts-oai-app
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor7
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/sys
              name: sysfs
              readOnly: true
            - mountPath: /host/oai-hwhealth
              name: oai-hwhealth
              readOnly: true
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: low-priority
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /sys
              type: Directory
            name: sysfs
          - hostPath:
              path: /scratch/host-mountable/oai-hwhealth
              type: DirectoryOrCreate
            name: oai-hwhealth
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:20:39Z"
      lastTransitionTime: "2025-04-29T20:20:39Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-29T20:20:39Z"
      lastTransitionTime: "2025-04-29T20:20:39Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:20:39Z"
      lastTransitionTime: "2025-04-29T19:55:23Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-29T20:20:39Z"
      lastTransitionTime: "2025-04-24T07:22:21Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:20:39Z"
      lastTransitionTime: "2025-04-24T07:22:21Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:20:39Z"
      lastTransitionTime: "2025-04-24T07:22:21Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 10
    revision: 65484594cb
    workers:
      currentReplicas: 0
      failedReplicas: 1
      message: 0/0/0
      phases: F
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangechangov/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","log_relpath":"devbox16"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"devbox16","job_id":"job-250429071422KLSYTKPG","openai.com/job-type":"interactive","torchflow.openai.com/component":"ray-devbox"},"name":"devbox16","namespace":"changov"},"spec":{"coordinator":true,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":0,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":2,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangechangov/rcall/results/devbox16","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"devbox16","rcall_call_timestamp":"1745910862.8980966"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"devbox16","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"devbox16","job_id":"job-250429071422KLSYTKPG","openai.com/job-type":"interactive","session_id":"xdec52a3uyl5","torchflow.openai.com/component":"ray-devbox"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["changov"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429071422KLSYTKPG"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":9090,"name":"ts-oai-app","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor7","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/sys","name":"sysfs","readOnly":true},{"mountPath":"/host/oai-hwhealth","name":"oai-hwhealth","readOnly":true},{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-high","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/sys","type":"Directory"},"name":"sysfs"},{"hostPath":{"path":"/scratch/host-mountable/oai-hwhealth","type":"DirectoryOrCreate"},"name":"oai-hwhealth"},{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: devbox16
      rayfract.openai.com/ray_done: "no"
    creationTimestamp: "2025-04-29T07:14:24Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 4
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: devbox16
      experiment_name: devbox16
      job_id: job-250429071422KLSYTKPG
      openai.com/job-type: interactive
      torchflow.openai.com/component: ray-devbox
    name: devbox16
    namespace: changov
    resourceVersion: "26468108"
    uid: d78d4871-1bc2-4083-ab49-9d9172f78624
  spec:
    allocated: true
    allocatedReplicas: 2
    coordinator: true
    extensions: {}
    podDeletionPolicy: Always
    suspend: true
    ttlSecondsAfterFinished: 0
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 2
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangechangov/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangechangov/rcall/results/devbox16
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: devbox16
            rcall_call_timestamp: "1745910862.8980966"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: devbox16
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: devbox16
            cilium.openai.com/network-policy: strict
            experiment_name: devbox16
            job_id: job-250429071422KLSYTKPG
            openai.com/job-type: interactive
            session_id: xdec52a3uyl5
            torchflow.openai.com/component: ray-devbox
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - changov
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429071422KLSYTKPG
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 9090
              name: ts-oai-app
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor7
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/sys
              name: sysfs
              readOnly: true
            - mountPath: /host/oai-hwhealth
              name: oai-hwhealth
              readOnly: true
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-high
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /sys
              type: Directory
            name: sysfs
          - hostPath:
              path: /scratch/host-mountable/oai-hwhealth
              type: DirectoryOrCreate
            name: oai-hwhealth
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T07:26:57Z"
      lastTransitionTime: "2025-04-29T07:26:42Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-29T07:26:57Z"
      lastTransitionTime: "2025-04-29T07:26:42Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-29T07:26:57Z"
      lastTransitionTime: "2025-04-29T07:26:08Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-29T07:26:57Z"
      lastTransitionTime: "2025-04-29T07:26:57Z"
      message: Pool is suspended
      reason: Suspended
      status: "True"
      type: Suspended
    - lastProbeTime: "2025-04-29T07:26:57Z"
      lastTransitionTime: "2025-04-29T07:14:24Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T07:26:57Z"
      lastTransitionTime: "2025-04-29T07:14:24Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 4
    revision: 574f656768
    workers:
      currentReplicas: 0
      failedReplicas: 2
      message: 0/0/0
      phases: FF
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangechangov/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","log_relpath":"devbox3"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"devbox3","job_id":"job-2504151749483PXA2K65","openai.com/job-type":"interactive","torchflow.openai.com/component":"ray-devbox"},"name":"devbox3","namespace":"changov"},"spec":{"coordinator":true,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":0,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1122624","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"12","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangechangov/rcall/results/devbox3","brix.openai.com/target-quotas":"iridium-pilot","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"devbox3","rcall_call_timestamp":"1744739388.4748156"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"devbox3","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"devbox3","job_id":"job-2504151749483PXA2K65","openai.com/job-type":"interactive","session_id":"zox8dowbiloy","torchflow.openai.com/component":"ray-devbox"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["changov"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-2504151749483PXA2K65"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1122624","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":9090,"name":"ts-oai-app","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor7","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/sys","name":"sysfs","readOnly":true},{"mountPath":"/host/oai-hwhealth","name":"oai-hwhealth","readOnly":true},{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":null,"restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/sys","type":"Directory"},"name":"sysfs"},{"hostPath":{"path":"/scratch/host-mountable/oai-hwhealth","type":"DirectoryOrCreate"},"name":"oai-hwhealth"},{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: devbox3
      rayfract.openai.com/ray_done: "no"
    creationTimestamp: "2025-04-15T17:49:50Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 31
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: devbox3
      experiment_name: devbox3
      job_id: job-2504151749483PXA2K65
      openai.com/job-type: interactive
      torchflow.openai.com/component: ray-devbox
    name: devbox3
    namespace: changov
    resourceVersion: "26992000"
    uid: 34cad8ff-4fad-4c69-923c-b9694b3a2696
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: true
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 0
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangechangov/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1122624
            brix.openai.com/instances: gpu1
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: "12"
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangechangov/rcall/results/devbox3
            brix.openai.com/target-quotas: iridium-pilot
            brix.openai.com/target-skus: gpu1
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: devbox3
            rcall_call_timestamp: "1744739388.4748156"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: devbox3
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: devbox3
            cilium.openai.com/network-policy: strict
            experiment_name: devbox3
            job_id: job-2504151749483PXA2K65
            openai.com/job-type: interactive
            session_id: zox8dowbiloy
            torchflow.openai.com/component: ray-devbox
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - changov
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-2504151749483PXA2K65
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1122624
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 9090
              name: ts-oai-app
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor7
              protocol: TCP
            resources:
              limits:
                cpu: "12"
                memory: "242266170215"
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "12"
                memory: "242266170215"
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/sys
              name: sysfs
              readOnly: true
            - mountPath: /host/oai-hwhealth
              name: oai-hwhealth
              readOnly: true
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: null
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /sys
              type: Directory
            name: sysfs
          - hostPath:
              path: /scratch/host-mountable/oai-hwhealth
              type: DirectoryOrCreate
            name: oai-hwhealth
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:20:11Z"
      lastTransitionTime: "2025-04-29T20:20:11Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-29T20:20:11Z"
      lastTransitionTime: "2025-04-29T20:20:11Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:20:11Z"
      lastTransitionTime: "2025-04-29T20:19:57Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-29T20:20:11Z"
      lastTransitionTime: "2025-04-15T17:49:50Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:20:11Z"
      lastTransitionTime: "2025-04-15T17:49:50Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:20:11Z"
      lastTransitionTime: "2025-04-15T17:49:50Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 31
    revision: 7cf99c6c86
    workers:
      currentReplicas: 0
      failedReplicas: 1
      message: 0/0/0
      phases: F
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangechangov/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","log_relpath":"devbox9-high-irpi"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"devbox9-high-irpi","job_id":"job-25042404100065YR7IMH","openai.com/job-type":"interactive","torchflow.openai.com/component":"ray-devbox"},"name":"devbox9-high-irpi","namespace":"changov"},"spec":{"coordinator":true,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":0,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":2,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangechangov/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangechangov/rcall/results/devbox9-high-irpi","brix.openai.com/target-quotas":"iridium-pilot","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"devbox9-high-irpi","rcall_call_timestamp":"1745467800.4098673"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"devbox9-high-irpi","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"devbox9-high-irpi","job_id":"job-25042404100065YR7IMH","openai.com/job-type":"interactive","session_id":"o2yijkv0c29d","torchflow.openai.com/component":"ray-devbox"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["changov"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-25042404100065YR7IMH"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":9090,"name":"ts-oai-app","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor7","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/sys","name":"sysfs","readOnly":true},{"mountPath":"/host/oai-hwhealth","name":"oai-hwhealth","readOnly":true},{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-high","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/sys","type":"Directory"},"name":"sysfs"},{"hostPath":{"path":"/scratch/host-mountable/oai-hwhealth","type":"DirectoryOrCreate"},"name":"oai-hwhealth"},{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: devbox9-high-irpi
      rayfract.openai.com/ray_done: "no"
    creationTimestamp: "2025-04-24T04:10:02Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 6
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: devbox9-high-irpi
      experiment_name: devbox9-high-irpi
      job_id: job-25042404100065YR7IMH
      openai.com/job-type: interactive
      torchflow.openai.com/component: ray-devbox
    name: devbox9-high-irpi
    namespace: changov
    resourceVersion: "22520859"
    uid: 1d771371-a568-471c-a25a-62b61351ac60
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: true
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 0
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 2
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangechangov/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangechangov/rcall/results/devbox9-high-irpi
            brix.openai.com/target-quotas: iridium-pilot
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: devbox9-high-irpi
            rcall_call_timestamp: "1745467800.4098673"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: devbox9-high-irpi
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: devbox9-high-irpi
            cilium.openai.com/network-policy: strict
            experiment_name: devbox9-high-irpi
            job_id: job-25042404100065YR7IMH
            openai.com/job-type: interactive
            session_id: o2yijkv0c29d
            torchflow.openai.com/component: ray-devbox
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - changov
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-25042404100065YR7IMH
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 9090
              name: ts-oai-app
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor7
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/sys
              name: sysfs
              readOnly: true
            - mountPath: /host/oai-hwhealth
              name: oai-hwhealth
              readOnly: true
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-high
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /sys
              type: Directory
            name: sysfs
          - hostPath:
              path: /scratch/host-mountable/oai-hwhealth
              type: DirectoryOrCreate
            name: oai-hwhealth
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-25T03:43:26Z"
      lastTransitionTime: "2025-04-25T03:43:25Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-25T03:43:26Z"
      lastTransitionTime: "2025-04-25T03:43:25Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-25T03:43:26Z"
      lastTransitionTime: "2025-04-25T03:42:36Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-25T03:43:26Z"
      lastTransitionTime: "2025-04-24T04:10:02Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-25T03:43:26Z"
      lastTransitionTime: "2025-04-24T04:10:02Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-25T03:43:26Z"
      lastTransitionTime: "2025-04-24T04:10:02Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 6
    revision: 55b986dcc
    workers:
      currentReplicas: 0
      failedReplicas: 2
      message: 0/0/0
      phases: FF
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: clmiller
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 0aea190a-0d59-4199-a54b-1a6f9d29c7cd
    resourceVersion: "********"
    uid: bc418511-fccd-4545-9bdf-8d9b321b7d80
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: dguna
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 5bfccabf-9432-4f18-94b2-273f5475d285
    resourceVersion: "********"
    uid: 2fe212fa-65df-463b-95c6-953c071c699f
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:10Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:10Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-17T19:28:57Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: hnamburi
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 595ad26b-**************-cc9187ee5005
    resourceVersion: "********"
    uid: cf5ca545-ba5c-4e39-b524-f83a36bd06c0
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-19T18:16:38Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-19T18:16:38Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-19T18:17:23Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-17T19:28:58Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-17T19:28:58Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-17T19:28:58Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:50Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: jkadupitige
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 5673cf71-a72d-4757-9503-d703e28a1af3
    resourceVersion: "********"
    uid: 67e02878-645a-44ab-b756-887821ad06cc
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:21:13Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:21:13Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-19T18:22:07Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:07Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:48Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: liuming
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 504384a7-9d3a-4163-9fff-f91592733857
    resourceVersion: "********"
    uid: 8a31b676-288e-4b1a-9ba3-59e289c7e1cd
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:12Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:12Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"devbox0429b"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"devbox0429b","job_id":"job-250429201924PDCD7S3Z","openai.com/job-type":"interactive","torchflow.openai.com/component":"ray-devbox"},"name":"devbox0429b","namespace":"liuming"},"spec":{"coordinator":true,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":0,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/devbox0429b","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"devbox0429b","rcall_call_timestamp":"1745957964.276296"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"devbox0429b","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"devbox0429b","job_id":"job-250429201924PDCD7S3Z","openai.com/job-type":"interactive","session_id":"0qpmordfxim0","torchflow.openai.com/component":"ray-devbox"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429201924PDCD7S3Z"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":9090,"name":"ts-oai-app","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor7","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/sys","name":"sysfs","readOnly":true},{"mountPath":"/host/oai-hwhealth","name":"oai-hwhealth","readOnly":true},{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"low-priority","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/sys","type":"Directory"},"name":"sysfs"},{"hostPath":{"path":"/scratch/host-mountable/oai-hwhealth","type":"DirectoryOrCreate"},"name":"oai-hwhealth"},{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: devbox0429b
      rayfract.openai.com/ray_done: "no"
    creationTimestamp: "2025-04-29T20:19:26Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 6
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: devbox0429b
      experiment_name: devbox0429b
      job_id: job-250429201924PDCD7S3Z
      openai.com/job-type: interactive
      torchflow.openai.com/component: ray-devbox
    name: devbox0429b
    namespace: liuming
    resourceVersion: "27807366"
    uid: b87fad79-2594-4b86-8f98-ecd459d5b9cc
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: true
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 0
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/devbox0429b
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: devbox0429b
            rcall_call_timestamp: "1745957964.276296"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: devbox0429b
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: devbox0429b
            cilium.openai.com/network-policy: strict
            experiment_name: devbox0429b
            job_id: job-250429201924PDCD7S3Z
            openai.com/job-type: interactive
            session_id: 0qpmordfxim0
            torchflow.openai.com/component: ray-devbox
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429201924PDCD7S3Z
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 9090
              name: ts-oai-app
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor7
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/sys
              name: sysfs
              readOnly: true
            - mountPath: /host/oai-hwhealth
              name: oai-hwhealth
              readOnly: true
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: low-priority
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /sys
              type: Directory
            name: sysfs
          - hostPath:
              path: /scratch/host-mountable/oai-hwhealth
              type: DirectoryOrCreate
            name: oai-hwhealth
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:42:22Z"
      lastTransitionTime: "2025-04-30T16:42:22Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-30T16:42:22Z"
      lastTransitionTime: "2025-04-30T16:42:22Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:42:22Z"
      lastTransitionTime: "2025-04-30T16:41:38Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-30T16:42:22Z"
      lastTransitionTime: "2025-04-29T20:19:26Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:42:22Z"
      lastTransitionTime: "2025-04-29T20:19:26Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:42:22Z"
      lastTransitionTime: "2025-04-29T20:19:26Z"
      message: Coordinator is not finished
      reason: CoordinatorUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 6
    revision: 7b8fcc744f
    workers:
      currentReplicas: 0
      failedReplicas: 1
      message: 0/0/0
      phases: F
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0425ab-controller-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-beam0425ab-controller-w0","job_id":"job-250425065610JCNGMUZQ","rapid_id":"liuming-beam0425ab","rapid_index_in_pool":"0","rapid_pool":"controller","torchflow.openai.com/component":"worker"},"name":"liuming-beam0425ab-controller-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0425ab-controller-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0425ab-controller-w0","rcall_call_timestamp":"1745564170.020292"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0425ab-controller-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0425ab-controller-w0","job_id":"job-250425065610JCNGMUZQ","rapid_id":"liuming-beam0425ab","rapid_index_in_pool":"0","rapid_pool":"controller","session_id":"09ymro9s0hlq","torchflow.openai.com/component":"worker"}},"spec":{"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250425065610JCNGMUZQ"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":5000,"name":"int-lemon","protocol":"TCP"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[]}}}}}
      log_relpath: liuming-beam0425ab-controller-w0
    creationTimestamp: "2025-04-25T06:56:12Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-beam0425ab-controller-w0
      experiment_name: liuming-beam0425ab-controller-w0
      job_id: job-250425065610JCNGMUZQ
      rapid_id: liuming-beam0425ab
      rapid_index_in_pool: "0"
      rapid_pool: controller
      torchflow.openai.com/component: worker
    name: liuming-beam0425ab-controller-w0
    namespace: liuming
    resourceVersion: "26986111"
    uid: 2aac305a-77a7-45ad-9869-7bc45f4502ca
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0425ab-controller-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0425ab-controller-w0
            rcall_call_timestamp: "1745564170.020292"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0425ab-controller-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-beam0425ab-controller-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0425ab-controller-w0
            job_id: job-250425065610JCNGMUZQ
            rapid_id: liuming-beam0425ab
            rapid_index_in_pool: "0"
            rapid_pool: controller
            session_id: 09ymro9s0hlq
            torchflow.openai.com/component: worker
        spec:
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250425065610JCNGMUZQ
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 5000
              name: int-lemon
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts: []
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes: []
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:12:18Z"
      lastTransitionTime: "2025-04-29T20:05:34Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:12:18Z"
      lastTransitionTime: "2025-04-29T20:05:34Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:12:18Z"
      lastTransitionTime: "2025-04-29T20:12:18Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:12:18Z"
      lastTransitionTime: "2025-04-25T06:56:12Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:12:18Z"
      lastTransitionTime: "2025-04-25T06:56:12Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:12:18Z"
      lastTransitionTime: "2025-04-25T06:56:12Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: bb9cffdbf
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0425ab-rollout-worker-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"liuming-beam0425ab-rollout-worker-w0","experiment_name":"liuming-beam0425ab-rollout-worker-w0","job_id":"job-250425065619J4E3Y6JP","rapid_id":"liuming-beam0425ab","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","torchflow.openai.com/component":"worker"},"name":"liuming-beam0425ab-rollout-worker-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0425ab-rollout-worker-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0425ab-rollout-worker-w0","rcall_call_timestamp":"1745564179.8961139"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0425ab-rollout-worker-w0","brix.openai.com/service":"liuming-beam0425ab-rollout-worker-w0","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0425ab-rollout-worker-w0","job_id":"job-250425065619J4E3Y6JP","rapid_id":"liuming-beam0425ab","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","session_id":"09ymro9s0hlq","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250425065619J4E3Y6JP"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-beam0425ab-rollout-worker-w0
    creationTimestamp: "2025-04-25T06:56:21Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: liuming-beam0425ab-rollout-worker-w0
      brix.openai.com/task-id: liuming-beam0425ab-rollout-worker-w0
      experiment_name: liuming-beam0425ab-rollout-worker-w0
      job_id: job-250425065619J4E3Y6JP
      rapid_id: liuming-beam0425ab
      rapid_index_in_pool: "0"
      rapid_pool: rollout-worker
      torchflow.openai.com/component: worker
    name: liuming-beam0425ab-rollout-worker-w0
    namespace: liuming
    resourceVersion: "26990503"
    uid: 27c105b6-40e1-446d-b137-4a6944742f08
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0425ab-rollout-worker-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0425ab-rollout-worker-w0
            rcall_call_timestamp: "1745564179.8961139"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0425ab-rollout-worker-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: liuming-beam0425ab-rollout-worker-w0
            brix.openai.com/task-id: liuming-beam0425ab-rollout-worker-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0425ab-rollout-worker-w0
            job_id: job-250425065619J4E3Y6JP
            rapid_id: liuming-beam0425ab
            rapid_index_in_pool: "0"
            rapid_pool: rollout-worker
            session_id: 09ymro9s0hlq
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250425065619J4E3Y6JP
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:18:09Z"
      lastTransitionTime: "2025-04-29T20:12:12Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:18:09Z"
      lastTransitionTime: "2025-04-29T20:12:12Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:18:09Z"
      lastTransitionTime: "2025-04-29T20:18:09Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:18:09Z"
      lastTransitionTime: "2025-04-25T06:56:21Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:18:09Z"
      lastTransitionTime: "2025-04-25T06:56:21Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:18:09Z"
      lastTransitionTime: "2025-04-25T06:56:21Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 867b45b698
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0425ab-train-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-beam0425ab-train-w0","job_id":"job-250425065628GGJ2YEN4","rapid_id":"liuming-beam0425ab","rapid_index_in_pool":"0","rapid_pool":"train","torchflow.openai.com/component":"worker"},"name":"liuming-beam0425ab-train-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0425ab-train-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0425ab-train-w0","rcall_call_timestamp":"1745564188.6717272"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0425ab-train-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0425ab-train-w0","job_id":"job-250425065628GGJ2YEN4","rapid_id":"liuming-beam0425ab","rapid_index_in_pool":"0","rapid_pool":"train","session_id":"09ymro9s0hlq","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250425065628GGJ2YEN4"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-beam0425ab-train-w0
    creationTimestamp: "2025-04-25T06:56:30Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-beam0425ab-train-w0
      experiment_name: liuming-beam0425ab-train-w0
      job_id: job-250425065628GGJ2YEN4
      rapid_id: liuming-beam0425ab
      rapid_index_in_pool: "0"
      rapid_pool: train
      torchflow.openai.com/component: worker
    name: liuming-beam0425ab-train-w0
    namespace: liuming
    resourceVersion: "26991622"
    uid: 4ae757ed-35a5-4010-befd-991638a65e58
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0425ab-train-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0425ab-train-w0
            rcall_call_timestamp: "1745564188.6717272"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0425ab-train-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-beam0425ab-train-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0425ab-train-w0
            job_id: job-250425065628GGJ2YEN4
            rapid_id: liuming-beam0425ab
            rapid_index_in_pool: "0"
            rapid_pool: train
            session_id: 09ymro9s0hlq
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250425065628GGJ2YEN4
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:19:46Z"
      lastTransitionTime: "2025-04-29T20:13:54Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:19:46Z"
      lastTransitionTime: "2025-04-29T20:14:22Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:19:46Z"
      lastTransitionTime: "2025-04-29T20:19:46Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:19:46Z"
      lastTransitionTime: "2025-04-25T06:56:30Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:19:46Z"
      lastTransitionTime: "2025-04-25T06:56:30Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:19:46Z"
      lastTransitionTime: "2025-04-25T06:56:30Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 85758b697
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0429a-controller-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-beam0429a-controller-w0","job_id":"job-250429201952NZFXKEAV","rapid_id":"liuming-beam0429a","rapid_index_in_pool":"0","rapid_pool":"controller","torchflow.openai.com/component":"worker"},"name":"liuming-beam0429a-controller-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0429a-controller-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0429a-controller-w0","rcall_call_timestamp":"1745957992.403299"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0429a-controller-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0429a-controller-w0","job_id":"job-250429201952NZFXKEAV","rapid_id":"liuming-beam0429a","rapid_index_in_pool":"0","rapid_pool":"controller","session_id":"q7u820w3rx8x","torchflow.openai.com/component":"worker"}},"spec":{"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429201952NZFXKEAV"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":5000,"name":"int-lemon","protocol":"TCP"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[]}}}}}
      log_relpath: liuming-beam0429a-controller-w0
    creationTimestamp: "2025-04-29T20:19:54Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-beam0429a-controller-w0
      experiment_name: liuming-beam0429a-controller-w0
      job_id: job-250429201952NZFXKEAV
      rapid_id: liuming-beam0429a
      rapid_index_in_pool: "0"
      rapid_pool: controller
      torchflow.openai.com/component: worker
    name: liuming-beam0429a-controller-w0
    namespace: liuming
    resourceVersion: "26994375"
    uid: aa7b9951-7106-4734-8fa7-64d432990e21
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0429a-controller-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0429a-controller-w0
            rcall_call_timestamp: "1745957992.403299"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0429a-controller-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-beam0429a-controller-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0429a-controller-w0
            job_id: job-250429201952NZFXKEAV
            rapid_id: liuming-beam0429a
            rapid_index_in_pool: "0"
            rapid_pool: controller
            session_id: q7u820w3rx8x
            torchflow.openai.com/component: worker
        spec:
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429201952NZFXKEAV
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 5000
              name: int-lemon
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts: []
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes: []
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:23:43Z"
      lastTransitionTime: "2025-04-29T20:19:57Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:23:43Z"
      lastTransitionTime: "2025-04-29T20:20:39Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:23:43Z"
      lastTransitionTime: "2025-04-29T20:23:43Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:23:43Z"
      lastTransitionTime: "2025-04-29T20:19:54Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:23:43Z"
      lastTransitionTime: "2025-04-29T20:19:54Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:23:43Z"
      lastTransitionTime: "2025-04-29T20:19:54Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 745bb776c5
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0429a-rollout-worker-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"liuming-beam0429a-rollout-worker-w0","experiment_name":"liuming-beam0429a-rollout-worker-w0","job_id":"job-250429201959ZBHE6MDS","rapid_id":"liuming-beam0429a","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","torchflow.openai.com/component":"worker"},"name":"liuming-beam0429a-rollout-worker-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0429a-rollout-worker-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0429a-rollout-worker-w0","rcall_call_timestamp":"1745957999.677221"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0429a-rollout-worker-w0","brix.openai.com/service":"liuming-beam0429a-rollout-worker-w0","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0429a-rollout-worker-w0","job_id":"job-250429201959ZBHE6MDS","rapid_id":"liuming-beam0429a","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","session_id":"q7u820w3rx8x","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429201959ZBHE6MDS"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-beam0429a-rollout-worker-w0
    creationTimestamp: "2025-04-29T20:20:01Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: liuming-beam0429a-rollout-worker-w0
      brix.openai.com/task-id: liuming-beam0429a-rollout-worker-w0
      experiment_name: liuming-beam0429a-rollout-worker-w0
      job_id: job-250429201959ZBHE6MDS
      rapid_id: liuming-beam0429a
      rapid_index_in_pool: "0"
      rapid_pool: rollout-worker
      torchflow.openai.com/component: worker
    name: liuming-beam0429a-rollout-worker-w0
    namespace: liuming
    resourceVersion: "26995905"
    uid: 54f97124-147b-4c33-8ac2-3caea7b3a57c
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0429a-rollout-worker-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0429a-rollout-worker-w0
            rcall_call_timestamp: "1745957999.677221"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0429a-rollout-worker-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: liuming-beam0429a-rollout-worker-w0
            brix.openai.com/task-id: liuming-beam0429a-rollout-worker-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0429a-rollout-worker-w0
            job_id: job-250429201959ZBHE6MDS
            rapid_id: liuming-beam0429a
            rapid_index_in_pool: "0"
            rapid_pool: rollout-worker
            session_id: q7u820w3rx8x
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429201959ZBHE6MDS
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:25:58Z"
      lastTransitionTime: "2025-04-29T20:20:05Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:25:58Z"
      lastTransitionTime: "2025-04-29T20:20:09Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:25:58Z"
      lastTransitionTime: "2025-04-29T20:25:58Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:25:58Z"
      lastTransitionTime: "2025-04-29T20:20:01Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:25:58Z"
      lastTransitionTime: "2025-04-29T20:20:01Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:25:58Z"
      lastTransitionTime: "2025-04-29T20:20:01Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 5cddb68b84
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0429a-train-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-beam0429a-train-w0","job_id":"job-250429202007D7HTYQRS","rapid_id":"liuming-beam0429a","rapid_index_in_pool":"0","rapid_pool":"train","torchflow.openai.com/component":"worker"},"name":"liuming-beam0429a-train-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0429a-train-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0429a-train-w0","rcall_call_timestamp":"1745958007.1567192"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0429a-train-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0429a-train-w0","job_id":"job-250429202007D7HTYQRS","rapid_id":"liuming-beam0429a","rapid_index_in_pool":"0","rapid_pool":"train","session_id":"q7u820w3rx8x","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429202007D7HTYQRS"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-beam0429a-train-w0
    creationTimestamp: "2025-04-29T20:20:08Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-beam0429a-train-w0
      experiment_name: liuming-beam0429a-train-w0
      job_id: job-250429202007D7HTYQRS
      rapid_id: liuming-beam0429a
      rapid_index_in_pool: "0"
      rapid_pool: train
      torchflow.openai.com/component: worker
    name: liuming-beam0429a-train-w0
    namespace: liuming
    resourceVersion: "26995687"
    uid: 59b64357-155f-4c8e-a66c-4d73b8aacfaf
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0429a-train-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0429a-train-w0
            rcall_call_timestamp: "1745958007.1567192"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0429a-train-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-beam0429a-train-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0429a-train-w0
            job_id: job-250429202007D7HTYQRS
            rapid_id: liuming-beam0429a
            rapid_index_in_pool: "0"
            rapid_pool: train
            session_id: q7u820w3rx8x
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429202007D7HTYQRS
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:25:40Z"
      lastTransitionTime: "2025-04-29T20:20:11Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:25:40Z"
      lastTransitionTime: "2025-04-29T20:20:11Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:25:40Z"
      lastTransitionTime: "2025-04-29T20:25:40Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:25:40Z"
      lastTransitionTime: "2025-04-29T20:20:08Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:25:40Z"
      lastTransitionTime: "2025-04-29T20:20:08Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:25:40Z"
      lastTransitionTime: "2025-04-29T20:20:08Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 58bcdc475c
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0429b-controller-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-beam0429b-controller-w0","job_id":"job-250429202856G35PE7F7","rapid_id":"liuming-beam0429b","rapid_index_in_pool":"0","rapid_pool":"controller","torchflow.openai.com/component":"worker"},"name":"liuming-beam0429b-controller-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0429b-controller-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0429b-controller-w0","rcall_call_timestamp":"1745958536.001766"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0429b-controller-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0429b-controller-w0","job_id":"job-250429202856G35PE7F7","rapid_id":"liuming-beam0429b","rapid_index_in_pool":"0","rapid_pool":"controller","session_id":"za3xsdmpghz4","torchflow.openai.com/component":"worker"}},"spec":{"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429202856G35PE7F7"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":5000,"name":"int-lemon","protocol":"TCP"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[]}}}}}
      log_relpath: liuming-beam0429b-controller-w0
    creationTimestamp: "2025-04-29T20:28:57Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-beam0429b-controller-w0
      experiment_name: liuming-beam0429b-controller-w0
      job_id: job-250429202856G35PE7F7
      rapid_id: liuming-beam0429b
      rapid_index_in_pool: "0"
      rapid_pool: controller
      torchflow.openai.com/component: worker
    name: liuming-beam0429b-controller-w0
    namespace: liuming
    resourceVersion: "27000542"
    uid: 7d3be8f2-e5ee-4c43-9213-ed8b11e2d89b
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0429b-controller-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0429b-controller-w0
            rcall_call_timestamp: "1745958536.001766"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0429b-controller-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-beam0429b-controller-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0429b-controller-w0
            job_id: job-250429202856G35PE7F7
            rapid_id: liuming-beam0429b
            rapid_index_in_pool: "0"
            rapid_pool: controller
            session_id: za3xsdmpghz4
            torchflow.openai.com/component: worker
        spec:
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429202856G35PE7F7
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 5000
              name: int-lemon
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts: []
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes: []
  status:
    conditions:
    - lastProbeTime: "2025-04-29T20:32:50Z"
      lastTransitionTime: "2025-04-29T20:29:00Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T20:32:50Z"
      lastTransitionTime: "2025-04-29T20:29:46Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T20:32:50Z"
      lastTransitionTime: "2025-04-29T20:32:50Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T20:32:50Z"
      lastTransitionTime: "2025-04-29T20:28:57Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T20:32:50Z"
      lastTransitionTime: "2025-04-29T20:28:57Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T20:32:50Z"
      lastTransitionTime: "2025-04-29T20:28:57Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 86944cb79f
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0429b-rollout-worker-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"liuming-beam0429b-rollout-worker-w0","experiment_name":"liuming-beam0429b-rollout-worker-w0","job_id":"job-250429202902O7LNJJEY","rapid_id":"liuming-beam0429b","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","torchflow.openai.com/component":"worker"},"name":"liuming-beam0429b-rollout-worker-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0429b-rollout-worker-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0429b-rollout-worker-w0","rcall_call_timestamp":"1745958542.4617221"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0429b-rollout-worker-w0","brix.openai.com/service":"liuming-beam0429b-rollout-worker-w0","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0429b-rollout-worker-w0","job_id":"job-250429202902O7LNJJEY","rapid_id":"liuming-beam0429b","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","session_id":"za3xsdmpghz4","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429202902O7LNJJEY"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-beam0429b-rollout-worker-w0
    creationTimestamp: "2025-04-29T20:29:04Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 5
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: liuming-beam0429b-rollout-worker-w0
      brix.openai.com/task-id: liuming-beam0429b-rollout-worker-w0
      experiment_name: liuming-beam0429b-rollout-worker-w0
      job_id: job-250429202902O7LNJJEY
      rapid_id: liuming-beam0429b
      rapid_index_in_pool: "0"
      rapid_pool: rollout-worker
      torchflow.openai.com/component: worker
    name: liuming-beam0429b-rollout-worker-w0
    namespace: liuming
    resourceVersion: "27027149"
    uid: 9f088e83-637c-4cb4-887e-519e95206ba8
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0429b-rollout-worker-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0429b-rollout-worker-w0
            rcall_call_timestamp: "1745958542.4617221"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0429b-rollout-worker-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: liuming-beam0429b-rollout-worker-w0
            brix.openai.com/task-id: liuming-beam0429b-rollout-worker-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0429b-rollout-worker-w0
            job_id: job-250429202902O7LNJJEY
            rapid_id: liuming-beam0429b
            rapid_index_in_pool: "0"
            rapid_pool: rollout-worker
            session_id: za3xsdmpghz4
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429202902O7LNJJEY
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T21:11:44Z"
      lastTransitionTime: "2025-04-29T21:09:31Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T21:11:44Z"
      lastTransitionTime: "2025-04-29T21:09:31Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T21:11:44Z"
      lastTransitionTime: "2025-04-29T21:11:44Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T21:11:44Z"
      lastTransitionTime: "2025-04-29T20:29:04Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T21:11:44Z"
      lastTransitionTime: "2025-04-29T20:29:04Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T21:11:44Z"
      lastTransitionTime: "2025-04-29T20:29:04Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 5
    revision: 768d6b686d
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-beam0429b-train-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-beam0429b-train-w0","job_id":"job-250429202909SOLC5A6N","rapid_id":"liuming-beam0429b","rapid_index_in_pool":"0","rapid_pool":"train","torchflow.openai.com/component":"worker"},"name":"liuming-beam0429b-train-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-beam0429b-train-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-beam0429b-train-w0","rcall_call_timestamp":"1745958549.573827"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-beam0429b-train-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-beam0429b-train-w0","job_id":"job-250429202909SOLC5A6N","rapid_id":"liuming-beam0429b","rapid_index_in_pool":"0","rapid_pool":"train","session_id":"za3xsdmpghz4","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250429202909SOLC5A6N"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-beam0429b-train-w0
    creationTimestamp: "2025-04-29T20:29:10Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 7
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-beam0429b-train-w0
      experiment_name: liuming-beam0429b-train-w0
      job_id: job-250429202909SOLC5A6N
      rapid_id: liuming-beam0429b
      rapid_index_in_pool: "0"
      rapid_pool: train
      torchflow.openai.com/component: worker
    name: liuming-beam0429b-train-w0
    namespace: liuming
    resourceVersion: "27071406"
    uid: 160638bb-54f4-41f6-a721-476ad79640e0
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-beam0429b-train-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-beam0429b-train-w0
            rcall_call_timestamp: "1745958549.573827"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-beam0429b-train-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-beam0429b-train-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-beam0429b-train-w0
            job_id: job-250429202909SOLC5A6N
            rapid_id: liuming-beam0429b
            rapid_index_in_pool: "0"
            rapid_pool: train
            session_id: za3xsdmpghz4
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250429202909SOLC5A6N
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-29T22:17:33Z"
      lastTransitionTime: "2025-04-29T22:15:24Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-29T22:17:33Z"
      lastTransitionTime: "2025-04-29T22:15:24Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-29T22:17:33Z"
      lastTransitionTime: "2025-04-29T22:17:33Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-29T22:17:33Z"
      lastTransitionTime: "2025-04-29T20:29:10Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-29T22:17:33Z"
      lastTransitionTime: "2025-04-29T20:29:10Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-29T22:17:33Z"
      lastTransitionTime: "2025-04-29T20:29:10Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 7
    revision: 66867fd48f
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-d16-m26-tst-beam-0429a-controller-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-d16-m26-tst-beam-0429a-controller-w0","job_id":"job-250430164132IBCPVCBR","rapid_id":"liuming-d16-m26-tst-beam-0429a","rapid_index_in_pool":"0","rapid_pool":"controller","torchflow.openai.com/component":"worker"},"name":"liuming-d16-m26-tst-beam-0429a-controller-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429a-controller-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-d16-m26-tst-beam-0429a-controller-w0","rcall_call_timestamp":"1746031292.853832"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-d16-m26-tst-beam-0429a-controller-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-d16-m26-tst-beam-0429a-controller-w0","job_id":"job-250430164132IBCPVCBR","rapid_id":"liuming-d16-m26-tst-beam-0429a","rapid_index_in_pool":"0","rapid_pool":"controller","session_id":"wof54xc6658u","torchflow.openai.com/component":"worker"}},"spec":{"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250430164132IBCPVCBR"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":5000,"name":"int-lemon","protocol":"TCP"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[]}}}}}
      log_relpath: liuming-d16-m26-tst-beam-0429a-controller-w0
    creationTimestamp: "2025-04-30T16:41:36Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 3
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429a-controller-w0
      experiment_name: liuming-d16-m26-tst-beam-0429a-controller-w0
      job_id: job-250430164132IBCPVCBR
      rapid_id: liuming-d16-m26-tst-beam-0429a
      rapid_index_in_pool: "0"
      rapid_pool: controller
      torchflow.openai.com/component: worker
    name: liuming-d16-m26-tst-beam-0429a-controller-w0
    namespace: liuming
    resourceVersion: "27808845"
    uid: 2ff7020c-21f2-451c-8bd5-81d29c8384e6
  spec:
    allocated: true
    allocatedReplicas: 1
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429a-controller-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-d16-m26-tst-beam-0429a-controller-w0
            rcall_call_timestamp: "1746031292.853832"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-d16-m26-tst-beam-0429a-controller-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429a-controller-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-d16-m26-tst-beam-0429a-controller-w0
            job_id: job-250430164132IBCPVCBR
            rapid_id: liuming-d16-m26-tst-beam-0429a
            rapid_index_in_pool: "0"
            rapid_pool: controller
            session_id: wof54xc6658u
            torchflow.openai.com/component: worker
        spec:
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250430164132IBCPVCBR
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 5000
              name: int-lemon
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts: []
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes: []
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:44:31Z"
      lastTransitionTime: "2025-04-30T16:41:42Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-30T16:44:31Z"
      lastTransitionTime: "2025-04-30T16:42:22Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:44:31Z"
      lastTransitionTime: "2025-04-30T16:44:31Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-30T16:44:31Z"
      lastTransitionTime: "2025-04-30T16:41:36Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:44:31Z"
      lastTransitionTime: "2025-04-30T16:41:36Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:44:31Z"
      lastTransitionTime: "2025-04-30T16:41:36Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 3
    revision: 6bdffd47cf
    workers:
      currentReplicas: 1
      failedReplicas: 0
      message: 1/1/1
      phases: R
      readyReplicas: 1
      replicas: 1
      scheduledReplicas: 1
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","experiment_name":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","job_id":"job-250430164143JISY5FXT","rapid_id":"liuming-d16-m26-tst-beam-0429a","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","torchflow.openai.com/component":"worker"},"name":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","rcall_call_timestamp":"1746031303.600189"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","brix.openai.com/service":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-d16-m26-tst-beam-0429a-rollout-worker-w0","job_id":"job-250430164143JISY5FXT","rapid_id":"liuming-d16-m26-tst-beam-0429a","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","session_id":"wof54xc6658u","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250430164143JISY5FXT"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
    creationTimestamp: "2025-04-30T16:41:46Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 2
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
      brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
      experiment_name: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
      job_id: job-250430164143JISY5FXT
      rapid_id: liuming-d16-m26-tst-beam-0429a
      rapid_index_in_pool: "0"
      rapid_pool: rollout-worker
      torchflow.openai.com/component: worker
    name: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
    namespace: liuming
    resourceVersion: "27806957"
    uid: 6caa9638-6b55-4914-8a13-1a7c1439332b
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
            rcall_call_timestamp: "1746031303.600189"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
            brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-d16-m26-tst-beam-0429a-rollout-worker-w0
            job_id: job-250430164143JISY5FXT
            rapid_id: liuming-d16-m26-tst-beam-0429a
            rapid_index_in_pool: "0"
            rapid_pool: rollout-worker
            session_id: wof54xc6658u
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250430164143JISY5FXT
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:41:46Z"
      lastTransitionTime: "2025-04-30T16:41:46Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-30T16:41:46Z"
      lastTransitionTime: "2025-04-30T16:41:46Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:41:46Z"
      lastTransitionTime: "2025-04-30T16:41:46Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-30T16:41:46Z"
      lastTransitionTime: "2025-04-30T16:41:46Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:41:46Z"
      lastTransitionTime: "2025-04-30T16:41:46Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:41:46Z"
      lastTransitionTime: "2025-04-30T16:41:46Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 2
    revision: 645f77467d
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      phases: _
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-d16-m26-tst-beam-0429a-train-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-d16-m26-tst-beam-0429a-train-w0","job_id":"job-250430164153THWVP64N","rapid_id":"liuming-d16-m26-tst-beam-0429a","rapid_index_in_pool":"0","rapid_pool":"train","torchflow.openai.com/component":"worker"},"name":"liuming-d16-m26-tst-beam-0429a-train-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":1,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429a-train-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-d16-m26-tst-beam-0429a-train-w0","rcall_call_timestamp":"1746031313.551948"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-d16-m26-tst-beam-0429a-train-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-d16-m26-tst-beam-0429a-train-w0","job_id":"job-250430164153THWVP64N","rapid_id":"liuming-d16-m26-tst-beam-0429a","rapid_index_in_pool":"0","rapid_pool":"train","session_id":"wof54xc6658u","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250430164153THWVP64N"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-d16-m26-tst-beam-0429a-train-w0
    creationTimestamp: "2025-04-30T16:41:56Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 2
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429a-train-w0
      experiment_name: liuming-d16-m26-tst-beam-0429a-train-w0
      job_id: job-250430164153THWVP64N
      rapid_id: liuming-d16-m26-tst-beam-0429a
      rapid_index_in_pool: "0"
      rapid_pool: train
      torchflow.openai.com/component: worker
    name: liuming-d16-m26-tst-beam-0429a-train-w0
    namespace: liuming
    resourceVersion: "27807076"
    uid: b7d6668e-37c3-41b7-aa23-8be25499b84f
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 1
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429a-train-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-d16-m26-tst-beam-0429a-train-w0
            rcall_call_timestamp: "1746031313.551948"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-d16-m26-tst-beam-0429a-train-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429a-train-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-d16-m26-tst-beam-0429a-train-w0
            job_id: job-250430164153THWVP64N
            rapid_id: liuming-d16-m26-tst-beam-0429a
            rapid_index_in_pool: "0"
            rapid_pool: train
            session_id: wof54xc6658u
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250430164153THWVP64N
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:41:56Z"
      lastTransitionTime: "2025-04-30T16:41:56Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-30T16:41:56Z"
      lastTransitionTime: "2025-04-30T16:41:56Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:41:56Z"
      lastTransitionTime: "2025-04-30T16:41:56Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-30T16:41:56Z"
      lastTransitionTime: "2025-04-30T16:41:56Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:41:56Z"
      lastTransitionTime: "2025-04-30T16:41:56Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:41:56Z"
      lastTransitionTime: "2025-04-30T16:41:56Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 2
    revision: 697b56fbd4
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      phases: __
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-d16-m26-tst-beam-0429b-controller-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-d16-m26-tst-beam-0429b-controller-w0","job_id":"job-250430164249J4QN72MM","rapid_id":"liuming-d16-m26-tst-beam-0429b","rapid_index_in_pool":"0","rapid_pool":"controller","torchflow.openai.com/component":"worker"},"name":"liuming-d16-m26-tst-beam-0429b-controller-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429b-controller-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-d16-m26-tst-beam-0429b-controller-w0","rcall_call_timestamp":"1746031369.8004968"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-d16-m26-tst-beam-0429b-controller-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-d16-m26-tst-beam-0429b-controller-w0","job_id":"job-250430164249J4QN72MM","rapid_id":"liuming-d16-m26-tst-beam-0429b","rapid_index_in_pool":"0","rapid_pool":"controller","session_id":"q0obet4rly0l","torchflow.openai.com/component":"worker"}},"spec":{"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250430164249J4QN72MM"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":5000,"name":"int-lemon","protocol":"TCP"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[]}}}}}
      log_relpath: liuming-d16-m26-tst-beam-0429b-controller-w0
    creationTimestamp: "2025-04-30T16:42:52Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 2
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429b-controller-w0
      experiment_name: liuming-d16-m26-tst-beam-0429b-controller-w0
      job_id: job-250430164249J4QN72MM
      rapid_id: liuming-d16-m26-tst-beam-0429b
      rapid_index_in_pool: "0"
      rapid_pool: controller
      torchflow.openai.com/component: worker
    name: liuming-d16-m26-tst-beam-0429b-controller-w0
    namespace: liuming
    resourceVersion: "27807741"
    uid: 10036da5-201b-4b26-8767-9bfca8fca191
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429b-controller-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-d16-m26-tst-beam-0429b-controller-w0
            rcall_call_timestamp: "1746031369.8004968"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-d16-m26-tst-beam-0429b-controller-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429b-controller-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-d16-m26-tst-beam-0429b-controller-w0
            job_id: job-250430164249J4QN72MM
            rapid_id: liuming-d16-m26-tst-beam-0429b
            rapid_index_in_pool: "0"
            rapid_pool: controller
            session_id: q0obet4rly0l
            torchflow.openai.com/component: worker
        spec:
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250430164249J4QN72MM
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 5000
              name: int-lemon
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts: []
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes: []
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:42:52Z"
      lastTransitionTime: "2025-04-30T16:42:52Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-30T16:42:52Z"
      lastTransitionTime: "2025-04-30T16:42:52Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:42:52Z"
      lastTransitionTime: "2025-04-30T16:42:52Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-30T16:42:52Z"
      lastTransitionTime: "2025-04-30T16:42:52Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:42:52Z"
      lastTransitionTime: "2025-04-30T16:42:52Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:42:52Z"
      lastTransitionTime: "2025-04-30T16:42:52Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 2
    revision: 7f95774fc6
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      phases: _
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","experiment_name":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","job_id":"job-250430164259NX2RSTEA","rapid_id":"liuming-d16-m26-tst-beam-0429b","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","torchflow.openai.com/component":"worker"},"name":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":0,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","rcall_call_timestamp":"1746031379.421567"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","brix.openai.com/service":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-d16-m26-tst-beam-0429b-rollout-worker-w0","job_id":"job-250430164259NX2RSTEA","rapid_id":"liuming-d16-m26-tst-beam-0429b","rapid_index_in_pool":"0","rapid_pool":"rollout-worker","session_id":"q0obet4rly0l","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250430164259NX2RSTEA"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
    creationTimestamp: "2025-04-30T16:43:01Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 2
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
      brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
      experiment_name: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
      job_id: job-250430164259NX2RSTEA
      rapid_id: liuming-d16-m26-tst-beam-0429b
      rapid_index_in_pool: "0"
      rapid_pool: rollout-worker
      torchflow.openai.com/component: worker
    name: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
    namespace: liuming
    resourceVersion: "27807853"
    uid: 0e3f703b-62b1-4621-8fa6-dfa6d4cc9308
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 0
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
            rcall_call_timestamp: "1746031379.421567"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
            brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-d16-m26-tst-beam-0429b-rollout-worker-w0
            job_id: job-250430164259NX2RSTEA
            rapid_id: liuming-d16-m26-tst-beam-0429b
            rapid_index_in_pool: "0"
            rapid_pool: rollout-worker
            session_id: q0obet4rly0l
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250430164259NX2RSTEA
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:43:02Z"
      lastTransitionTime: "2025-04-30T16:43:01Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-30T16:43:02Z"
      lastTransitionTime: "2025-04-30T16:43:01Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:43:02Z"
      lastTransitionTime: "2025-04-30T16:43:01Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-30T16:43:02Z"
      lastTransitionTime: "2025-04-30T16:43:01Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:43:02Z"
      lastTransitionTime: "2025-04-30T16:43:01Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:43:02Z"
      lastTransitionTime: "2025-04-30T16:43:01Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 2
    revision: 579678c747
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      phases: _
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      blobstore_base: az://oaiorangeliuming/rcall
      brix.openai.com/created-by: <EMAIL>
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"brix.openai.com/v1alpha1","kind":"Pool","metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","log_relpath":"liuming-d16-m26-tst-beam-0429b-train-w0"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/queue-name":"","brix.openai.com/queue-namespace":"","brix.openai.com/service":"rcall","experiment_name":"liuming-d16-m26-tst-beam-0429b-train-w0","job_id":"job-250430164309YU63MNUP","rapid_id":"liuming-d16-m26-tst-beam-0429b","rapid_index_in_pool":"0","rapid_pool":"train","torchflow.openai.com/component":"worker"},"name":"liuming-d16-m26-tst-beam-0429b-train-w0","namespace":"liuming"},"spec":{"coordinator":null,"podDeletionPolicy":"Always","suspend":false,"ttlSecondsAfterFinished":604800,"workers":{"deleteFailed":true,"persistentVolumeClaims":[],"replicas":1,"spareReplicas":1,"template":{"metadata":{"annotations":{"blobstore_base":"az://oaiorangeliuming/rcall","brix.openai.com/agent-breakpoint-on-exit":"never","brix.openai.com/azure":"enabled","brix.openai.com/banned-nodes-policy":"avoid","brix.openai.com/bash":"enabled","brix.openai.com/ca":"enabled","brix.openai.com/dns":"enabled","brix.openai.com/filesetup":"enabled","brix.openai.com/git":"enabled","brix.openai.com/git-alternates":"enabled","brix.openai.com/infiniband":"enabled","brix.openai.com/infra":"enabled","brix.openai.com/infra-image":"iridiumsdc.azurecr.io/rcall:crow-1147178","brix.openai.com/instances":"gpu1,gpu","brix.openai.com/kubectl":"enabled","brix.openai.com/limits":"enabled","brix.openai.com/log-export":"enabled","brix.openai.com/nvidia":"enabled","brix.openai.com/persistence":"enabled","brix.openai.com/ptrace":"enabled","brix.openai.com/pvc":"enabled","brix.openai.com/resource-limit-cpu":"auto","brix.openai.com/resource-limit-gpu":"8","brix.openai.com/resource-limit-memory":"auto","brix.openai.com/resource-limit-nvidia-gpu":"8","brix.openai.com/rma-nodes-policy":"avoid","brix.openai.com/scheduling":"enabled","brix.openai.com/service":"enabled","brix.openai.com/ssh":"enabled","brix.openai.com/storage":"az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429b-train-w0","brix.openai.com/target-quotas":"team-moonfire-dri","brix.openai.com/target-skus":"gpu1,gpu","brix.openai.com/target-skus-override":"true","brix.openai.com/tmpfs":"enabled","brix.openai.com/watchdog":"enabled","log_relpath":"liuming-d16-m26-tst-beam-0429b-train-w0","rcall_call_timestamp":"1746031389.261867"},"labels":{"app":"rcall","backend":"brix","brix.openai.com/git":"liuming-d16-m26-tst-beam-0429b-train-w0","brix.openai.com/service":"rcall","cilium.openai.com/network-policy":"strict","experiment_name":"liuming-d16-m26-tst-beam-0429b-train-w0","job_id":"job-250430164309YU63MNUP","rapid_id":"liuming-d16-m26-tst-beam-0429b","rapid_index_in_pool":"0","rapid_pool":"train","session_id":"q0obet4rly0l","torchflow.openai.com/component":"worker"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"metadata.node.openai.com/persistent-blobcache","operator":"In","values":["liuming"]}]},"weight":50}]}},"containers":[{"args":["/opt/oai/bin/python3","-u","-m","rcall.commands.entrypoint","/var/brix/share/rcall/entrypoint.zpickle"],"env":[{"name":"RCALL_DISABLE_LOG_UPLOAD","value":"0"},{"name":"BRIX_JOB_ID","value":"job-250430164309YU63MNUP"}],"env_from":[],"image":"iridiumsdc.azurecr.io/rcall:crow-1147178","name":"main","ports":[{"containerPort":31338,"name":"brix-ssh"},{"containerPort":2112,"name":"ts-api","protocol":"TCP"},{"containerPort":2113,"name":"ts-api-br0","protocol":"TCP"},{"containerPort":2114,"name":"ts-api-br1","protocol":"TCP"},{"containerPort":2115,"name":"ts-api-br2","protocol":"TCP"},{"containerPort":2116,"name":"ts-api-br3","protocol":"TCP"},{"containerPort":2117,"name":"ts-api-br4","protocol":"TCP"},{"containerPort":2118,"name":"ts-api-br5","protocol":"TCP"},{"containerPort":2119,"name":"ts-api-br6","protocol":"TCP"},{"containerPort":2120,"name":"ts-api-br7","protocol":"TCP"},{"containerPort":11090,"name":"ts-tw-actor-0","protocol":"TCP"},{"containerPort":11091,"name":"ts-tw-actor-1","protocol":"TCP"},{"containerPort":11092,"name":"ts-tw-actor-2","protocol":"TCP"},{"containerPort":11093,"name":"ts-tw-actor-3","protocol":"TCP"},{"containerPort":11094,"name":"ts-tw-actor-4","protocol":"TCP"},{"containerPort":11095,"name":"ts-tw-actor-5","protocol":"TCP"},{"containerPort":11096,"name":"ts-tw-actor-6","protocol":"TCP"},{"containerPort":11097,"name":"ts-tw-actor-7","protocol":"TCP"},{"containerPort":9090,"name":"metrics","protocol":"TCP"}],"resources":{"limits":{"nvidia.com/hca":1000}},"securityContext":{"capabilities":{"add":["SYSLOG"]},"privileged":false},"stdin":true,"volumeMounts":[{"mountPath":"/host/blobcache","name":"blobcache"}],"workingDir":"/root"}],"enableServiceLinks":false,"hostNetwork":false,"priorityClassName":"team-critical","restartPolicy":"OnFailure","terminationGracePeriodSeconds":5,"volumes":[{"hostPath":{"path":"/scratch/host-mountable/blobcache","type":"DirectoryOrCreate"},"name":"blobcache"}]}}}}}
      log_relpath: liuming-d16-m26-tst-beam-0429b-train-w0
    creationTimestamp: "2025-04-30T16:43:11Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 2
    labels:
      app: rcall
      backend: brix
      brix.openai.com/queue-name: ""
      brix.openai.com/queue-namespace: ""
      brix.openai.com/scheduler: perhonen
      brix.openai.com/service: rcall
      brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429b-train-w0
      experiment_name: liuming-d16-m26-tst-beam-0429b-train-w0
      job_id: job-250430164309YU63MNUP
      rapid_id: liuming-d16-m26-tst-beam-0429b
      rapid_index_in_pool: "0"
      rapid_pool: train
      torchflow.openai.com/component: worker
    name: liuming-d16-m26-tst-beam-0429b-train-w0
    namespace: liuming
    resourceVersion: "27807971"
    uid: c9712791-7bd1-4e06-9ccc-e75a5fe1bffc
  spec:
    allocated: false
    allocatedReplicas: 0
    coordinator: false
    extensions: {}
    podDeletionPolicy: Always
    suspend: false
    ttlSecondsAfterFinished: 604800
    workers:
      deleteFailed: true
      persistentVolumeClaims: []
      replicas: 1
      spareReplicas: 1
      template:
        metadata:
          annotations:
            blobstore_base: az://oaiorangeliuming/rcall
            brix.openai.com/agent-breakpoint-on-exit: never
            brix.openai.com/azure: enabled
            brix.openai.com/banned-nodes-policy: avoid
            brix.openai.com/bash: enabled
            brix.openai.com/ca: enabled
            brix.openai.com/dns: enabled
            brix.openai.com/filesetup: enabled
            brix.openai.com/git: enabled
            brix.openai.com/git-alternates: enabled
            brix.openai.com/infiniband: enabled
            brix.openai.com/infra: enabled
            brix.openai.com/infra-image: iridiumsdc.azurecr.io/rcall:crow-1147178
            brix.openai.com/instances: gpu1,gpu
            brix.openai.com/kubectl: enabled
            brix.openai.com/limits: enabled
            brix.openai.com/log-export: enabled
            brix.openai.com/nvidia: enabled
            brix.openai.com/persistence: enabled
            brix.openai.com/ptrace: enabled
            brix.openai.com/pvc: enabled
            brix.openai.com/resource-limit-cpu: auto
            brix.openai.com/resource-limit-gpu: "8"
            brix.openai.com/resource-limit-memory: auto
            brix.openai.com/resource-limit-nvidia-gpu: "8"
            brix.openai.com/rma-nodes-policy: avoid
            brix.openai.com/scheduling: enabled
            brix.openai.com/service: enabled
            brix.openai.com/ssh: enabled
            brix.openai.com/storage: az://oaiorangeliuming/rcall/results/liuming-d16-m26-tst-beam-0429b-train-w0
            brix.openai.com/target-quotas: team-moonfire-dri
            brix.openai.com/target-skus: gpu1,gpu
            brix.openai.com/target-skus-override: "true"
            brix.openai.com/tmpfs: enabled
            brix.openai.com/watchdog: enabled
            log_relpath: liuming-d16-m26-tst-beam-0429b-train-w0
            rcall_call_timestamp: "1746031389.261867"
          creationTimestamp: null
          labels:
            app: rcall
            backend: brix
            brix.openai.com/git: liuming-d16-m26-tst-beam-0429b-train-w0
            brix.openai.com/scheduler: perhonen
            brix.openai.com/service: rcall
            brix.openai.com/task-id: liuming-d16-m26-tst-beam-0429b-train-w0
            cilium.openai.com/network-policy: strict
            experiment_name: liuming-d16-m26-tst-beam-0429b-train-w0
            job_id: job-250430164309YU63MNUP
            rapid_id: liuming-d16-m26-tst-beam-0429b
            rapid_index_in_pool: "0"
            rapid_pool: train
            session_id: q0obet4rly0l
            torchflow.openai.com/component: worker
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - preference:
                  matchExpressions:
                  - key: metadata.node.openai.com/persistent-blobcache
                    operator: In
                    values:
                    - liuming
                weight: 50
          containers:
          - args:
            - /opt/oai/bin/python3
            - -u
            - -m
            - rcall.commands.entrypoint
            - /var/brix/share/rcall/entrypoint.zpickle
            env:
            - name: RCALL_DISABLE_LOG_UPLOAD
              value: "0"
            - name: BRIX_JOB_ID
              value: job-250430164309YU63MNUP
            env_from: []
            image: iridiumsdc.azurecr.io/rcall:crow-1147178
            name: main
            ports:
            - containerPort: 31338
              name: brix-ssh
              protocol: TCP
            - containerPort: 2112
              name: ts-api
              protocol: TCP
            - containerPort: 2113
              name: ts-api-br0
              protocol: TCP
            - containerPort: 2114
              name: ts-api-br1
              protocol: TCP
            - containerPort: 2115
              name: ts-api-br2
              protocol: TCP
            - containerPort: 2116
              name: ts-api-br3
              protocol: TCP
            - containerPort: 2117
              name: ts-api-br4
              protocol: TCP
            - containerPort: 2118
              name: ts-api-br5
              protocol: TCP
            - containerPort: 2119
              name: ts-api-br6
              protocol: TCP
            - containerPort: 2120
              name: ts-api-br7
              protocol: TCP
            - containerPort: 11090
              name: ts-tw-actor-0
              protocol: TCP
            - containerPort: 11091
              name: ts-tw-actor-1
              protocol: TCP
            - containerPort: 11092
              name: ts-tw-actor-2
              protocol: TCP
            - containerPort: 11093
              name: ts-tw-actor-3
              protocol: TCP
            - containerPort: 11094
              name: ts-tw-actor-4
              protocol: TCP
            - containerPort: 11095
              name: ts-tw-actor-5
              protocol: TCP
            - containerPort: 11096
              name: ts-tw-actor-6
              protocol: TCP
            - containerPort: 11097
              name: ts-tw-actor-7
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
            resources:
              limits:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
                nvidia.com/hca: 1k
              requests:
                cpu: "94"
                memory: 1853273112Ki
                nvidia.com/gpu: "8"
            securityContext:
              capabilities:
                add:
                - SYSLOG
              privileged: false
            stdin: true
            volumeMounts:
            - mountPath: /host/blobcache
              name: blobcache
            workingDir: /root
          enableServiceLinks: false
          hostNetwork: false
          priorityClassName: team-critical
          restartPolicy: OnFailure
          schedulerName: perhonen
          terminationGracePeriodSeconds: 5
          volumes:
          - hostPath:
              path: /scratch/host-mountable/blobcache
              type: DirectoryOrCreate
            name: blobcache
  status:
    conditions:
    - lastProbeTime: "2025-04-30T16:43:11Z"
      lastTransitionTime: "2025-04-30T16:43:11Z"
      message: Not enough workers are current
      reason: WorkersNotCurrent
      status: "False"
      type: Current
    - lastProbeTime: "2025-04-30T16:43:11Z"
      lastTransitionTime: "2025-04-30T16:43:11Z"
      message: Not enough workers are scheduled
      reason: WorkersNotScheduled
      status: "False"
      type: Scheduled
    - lastProbeTime: "2025-04-30T16:43:11Z"
      lastTransitionTime: "2025-04-30T16:43:11Z"
      message: Not enough workers are ready
      reason: WorkersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: "2025-04-30T16:43:11Z"
      lastTransitionTime: "2025-04-30T16:43:11Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-30T16:43:11Z"
      lastTransitionTime: "2025-04-30T16:43:11Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-30T16:43:11Z"
      lastTransitionTime: "2025-04-30T16:43:11Z"
      message: Workers are not finished
      reason: WorkersUnfinished
      status: "False"
      type: Failed
    manager:
      current: false
      ready: false
      scheduled: false
    observedGeneration: 2
    revision: 5bdf8f75bf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      phases: __
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:50Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: pandeyn
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 932680d9-e363-4af4-a139-75524530d8a6
    resourceVersion: "********"
    uid: 54ab19bf-bf6b-4ea5-80a6-e2fb0d2ed51e
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:11Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:11Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: randydodgen
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 4ad25041-bea0-431f-81b3-0f464e1353d3
    resourceVersion: "********"
    uid: 05d568bb-957c-40ab-be40-2ae195aaac18
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: rojingeorge
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 42dcd924-b693-491f-880d-100a8ef749fa
    resourceVersion: "********"
    uid: be037ba5-beda-423b-a81e-ae71b8d23d30
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:14Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:14Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-22T23:51:58Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: ruizh
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: bdfe3427-244a-4dff-b3d9-fae4f8849cdf
    resourceVersion: "********"
    uid: c2974b20-cc54-4515-bcc4-80ff8d77a835
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-22T23:53:15Z"
      lastTransitionTime: "2025-04-22T23:51:59Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-22T23:53:15Z"
      lastTransitionTime: "2025-04-22T23:52:02Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-22T23:53:15Z"
      lastTransitionTime: "2025-04-22T23:53:15Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-22T23:53:15Z"
      lastTransitionTime: "2025-04-22T23:51:59Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-22T23:53:15Z"
      lastTransitionTime: "2025-04-22T23:51:59Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-22T23:53:15Z"
      lastTransitionTime: "2025-04-22T23:51:59Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 0
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:49Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: shivenraina
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 1b5a2c10-9c40-4d21-951b-708e20ee5c22
    resourceVersion: "********"
    uid: a109b537-c552-414d-b25f-312be571553a
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:15Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:49Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-17T19:28:57Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: yiyoulin
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: f6691f86-0e6b-4c10-bbf1-0e847e881290
    resourceVersion: "********"
    uid: 13c38538-ddf8-4671-bf3b-21825c94b662
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-19T18:16:39Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-19T18:16:39Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-19T18:17:23Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-17T19:28:58Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-17T19:28:58Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:17:23Z"
      lastTransitionTime: "2025-04-17T19:28:58Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
- apiVersion: brix.openai.com/v1alpha1
  kind: Pool
  metadata:
    annotations:
      brix.openai.com/created-by: system:serviceaccount:system:brix-operator
    creationTimestamp: "2025-04-04T00:21:50Z"
    finalizers:
    - brix.openai.com/pool-pods
    generation: 1
    name: brix-git
    namespace: yshahin
    ownerReferences:
    - apiVersion: brix.openai.com/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: Git
      name: brix-git
      uid: 9ddd32ea-3fdc-4652-908f-d673b6c0b4ef
    resourceVersion: "********"
    uid: 7d310bd2-9512-4e50-831f-e787b606318c
  spec:
    allocated: true
    coordinator: false
    extensions: {}
    manager:
      deleteFailed: true
      template:
        metadata:
          annotations:
            brix.openai.com/git-max-requests: "8"
            brix.openai.com/git-request-timeout: 5m
          creationTimestamp: null
          labels:
            brix.openai.com/git: brix-git
        spec:
          containers:
          - name: main
            resources: {}
            volumeMounts:
            - mountPath: /root/code
              name: brix-git
          nodeSelector:
            singularity.azure.com/processing-unit: cpu
          priorityClassName: team-critical
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: openai.com/team
            operator: Exists
          volumes:
          - name: brix-git
            persistentVolumeClaim:
              claimName: brix-git
    podDeletionPolicy: OnSuccess
    suspend: false
    workers:
      deleteFailed: true
      replicas: 0
      spareReplicas: 0
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - name: main
            resources: {}
  status:
    conditions:
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:13Z"
      message: Pods are current
      reason: Current
      status: "True"
      type: Current
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:21:13Z"
      message: All pods are scheduled
      reason: Scheduled
      status: "True"
      type: Scheduled
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-19T18:22:06Z"
      message: All pods are ready
      reason: Ready
      status: "True"
      type: Ready
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Pool is not suspended
      reason: NotSuspended
      status: "False"
      type: Suspended
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Succeeded
    - lastProbeTime: "2025-04-19T18:22:06Z"
      lastTransitionTime: "2025-04-04T00:21:50Z"
      message: Manager has not finished
      reason: ManagerNotFinished
      status: "False"
      type: Failed
    manager:
      current: true
      phase: Running
      ready: true
      restarts: 1
      scheduled: true
    observedGeneration: 1
    revision: 8fcf46bbf
    workers:
      currentReplicas: 0
      failedReplicas: 0
      message: 0/0/0
      readyReplicas: 0
      replicas: 0
      scheduledReplicas: 0
      succeededReplicas: 0
      unhealthyReplicas: 0
kind: List
metadata:
  resourceVersion: ""
