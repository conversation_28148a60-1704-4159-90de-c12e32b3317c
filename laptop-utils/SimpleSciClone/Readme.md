# SimpleSciClone.sh

## Overview

`SimpleSciClone.sh` is a Bash script designed to synchronize the contents of a container between two Azure Blob Storage accounts. It uses Azure CLI and AzCopy to perform the synchronization, fetch container contents, summarize blob sizes, and identify differences between the source and destination containers.

## Features

- Synchronizes container contents between two Azure Blob Storage accounts.
- Generates Shared Access Signature (SAS) tokens for secure access.
- Fetches and summarizes blob content sizes in the source and destination containers.
- Compares the contents of the source and destination containers to identify differences.
- Automatically syncs differences if any are found.

## Prerequisites

1. **Azure CLI**: Ensure that Azure CLI is installed and authenticated.
2. **AzCopy**: Install AzCopy for efficient data transfer.
3. **Bash Shell**: The script is designed to run in a Bash shell environment.

## Usage
```bash

# Make the script executable
chmod +x SimpleSciClone.sh

# Run the script with the required arguments
bash SimpleSciClone.sh <SourceStorageAccountName> <DestinationStorageAccountName> <ContainerName> <DirectoryPath>"
```

### Example
```bash
bash SimpleSciClone.sh orngcresco orngwus2cresco data
bash SimpleSciClone.sh orngcresco orngwus2cresco data /Path
```

### Arguments
- `<SourceStorageAccountName>`: The name of the source Azure Storage account.
- `<DestinationStorageAccountName>`: The name of the destination Azure Storage account.
- `<ContainerName>`: The name of the container to be synchronized.
- `<DirectoryPath>`: The optional directory path under the container to be synced specifically, if not passed it will sync the complete container.

### Notes
- Ensure that you have the necessary permissions to access both the source and destination storage accounts.
- The script will prompt for authentication if required.
- Review the output logs for any errors or warnings during the synchronization process.