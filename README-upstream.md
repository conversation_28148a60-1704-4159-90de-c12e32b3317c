Upstream work
---

This document lists work items to upsteam currently hacked aspects in Iridium.

### `rcall.util.ensure_vpn`

Hard-coded `sci-tailscale-router` in function. Need to make it configurable.

### `rcall.brix.create_ingress`

Function `create_ingress` currently by-passed in `.rcall_config.py` since we do not support ingress yet.

### `ENCODER_GYM_BASE = clusters.localize_path("raz://oaidatasets2/encoder-gym")`

Hard-coded storage path for encoder data. Patch to allow env variable override:
```patch
diff --git a/deprecated/data_gym/data_gym/datasets/text/encoding.py b/deprecated/data_gym/data_gym/datasets/text/encoding.py
index 6b2040e0..47342b95 100644
--- a/deprecated/data_gym/data_gym/datasets/text/encoding.py
+++ b/deprecated/data_gym/data_gym/datasets/text/encoding.py
@@ -30,7 +30,10 @@ ENCODINGS_BASE = os.environ.get(
         azure=clusters.localize_path("raz://oaidatasets2/data-gym/encodings"),
     ),
)
-ENCODER_GYM_BASE = clusters.localize_path("raz://oaidatasets2/encoder-gym")
+ENCODER_GYM_BASE = os.environ.get(
+    "OPENAI_ENCODINGS_BASE_DATA_GYM",
+    clusters.localize_path("raz://oaidatasets2/encoder-gym"))
+

SpecialSequenceHandling = Literal["error", "special_token", "text"]
```