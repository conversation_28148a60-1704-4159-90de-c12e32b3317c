Iridium Clusters Bootstrapping
---

This repo contains scripts and documentation to setup iridium clusters on both service and client side.

## Introduction
Iridium clusters are a Microsoft + OAI effort to be able to run OAI training and finetuning code on Microsoft infrastructure with highest level of fidelity in terms of user experience and matching code. As most of OAI code and configs were purpose built, a bunch of scripts in this repo help bridge these gaps. Most of documentation will be within the scripts themselves. 

Our primary goal is to have zero patches to the upstream repos.

The fact that you are reading this file means that you are already in mimco tent and should have access to `torchflow-mirror` and `brix` repositories within this project.

### Repository layout
* **`.azuredevops/`**: Contains Azure pipelines that performs different actions.
* **`admission/`**: Contains Iridium cluster mutating admission controller source code. See [README](admission/README.md).
* **`docs/`**: Various documentation READMEs.
* **`hack/`**: Deprecated temporary test setup. Will be removed soon.
* **`images/`**: Iridium image building recipes.
* **`laptop-utils/`**: Various MacBook laptop utilies that get copied during onboarding.
* **`terraform/`**: Main terraform repo containing configs for cluster administration.
* **`scripts/`**: Python scripts used by Azure pipelines

### Getting started

Please see the [Penny Wiki](https://aka.ms/penny.wiki) concretely the [Orange: Getting Started](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4583/Orange-Getting-Started) page for installation instructions.

* [**Iridium Architecture**](README-iridium.md).
* [**Administration Guide**](README-admin.md).
* [**Users Guide and Getting Started**](README-laptop.md).
* [**OpenAI Packaging (`oaipkg`) Guide**](README-oaipkg.md).
* [**SciClone in Iridium**](README-sciclone.md).
* [**Upstream**](README-upstream.md).
* [**Terraform Guide**](terraform/README.md).
* [**Adding New Clusters Guide**](terraform/clusters/README.md)
