FROM mcr.microsoft.com/devcontainers/rust:bullseye AS builder
WORKDIR /src
COPY . /src
ARG CARGO_TOKEN
RUN RUSTFLAGS='-C target-feature=+crt-static' \
    CARGO_REGISTRIES_MIMCO_PUBLICPACKAGES_TOKEN="${CARGO_TOKEN}" \
    CARGO_REGISTRIES_MIMCO_PUBLICPACKAGES_CREDENTIAL_PROVIDER="cargo:token" \
    cargo build --release --target x86_64-unknown-linux-gnu

FROM scratch
COPY --from=builder /src/target/x86_64-unknown-linux-gnu/release/iridmission /
ENTRYPOINT [ "/iridmission" ]
