Iridium Admission Controller
---
This is a simple Kubernetes mutating admission controller implementation for Iridium clusters. It provides a generic and configurable way to mutate pods as they are created with various patches.

### Use cases
The main use cases is to be able to adapt brix created pods to work in Iridium environments. Here are some examples:
* Inject service principal certificates.
* Inject platform specific environment variables, namely `EXTRA_CLUSTERS_JSON`.

For up-to-date usage refer to the [terraform configuration](../terraform/modules/iridmission/configuration.yaml).

### Development Workflow

1. **Make Changes and Open a Pull Request (PR)**  
   
   Once you have made your changes, create a PR. Submitting a PR will automatically trigger the build pipeline.  
   
   **Pipeline URL:** [Build Pipeline](https://dev.azure.com/project-argos/Mimco/_build?definitionId=1661)

2. **Pipeline Execution**  
   
   After the PR is opened, the pipeline will run. On successful completion, it will build and **push** the new container image to the Azure Container Registry (ACR).

3. **Update Terraform with the New Version**  
   
   Once the image has been pushed, you need to update the Terraform configuration with the newly released version.  
   
   **File to Update:** [iridmission-version.tf](../terraform/modules/iridmission/iridmission-version.tf)

### Deployment
The controller is deployed using [this terraform module](../terraform/modules/iridmission/).

### Usage
The controller is deployed as part of terraform. An example configuration file is provided in `examples/` directory.
