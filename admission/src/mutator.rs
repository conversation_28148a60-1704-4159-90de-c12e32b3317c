use std::collections::HashMap;
use std::error::Error;
use std::fs::File;

use jsonptr::Pointer;
use kube::core::{admission::AdmissionResponse, DynamicObject, ResourceExt};
use serde::Deserialize;
use serde_yaml;
use tracing::*;

const OPERATION_ADD: &str = "add";
const OPERATION_REMOVE: &str = "remove";
const OPERATION_REPLACE: &str = "replace";

const OBJECT_KIND_POD: &str = "pod";
const OBJECT_KIND_DAEMONSET: &str = "daemonset";

#[derive(Deserialize, PartialEq, Debug, Clone)]
pub struct MutationOp {
    op: String,
    path: String,
    value: String,
}

#[derive(Deserialize, PartialEq, Debug)]
pub struct MutationConfig {
    #[serde(rename = "matchLabels")]
    match_labels: HashMap<String, String>,
    #[serde(rename = "matchAnnotations")]
    match_annotations: HashMap<String, String>,

    ops: Vec<MutationOp>,
}

#[derive(Deserialize, PartialEq, Debug)]
pub struct Config {
    #[serde(rename = "objectConfigs")]
    object_configs: HashMap<String, Vec<MutationConfig>>,
}

pub struct Mutator {
    config: Config,
}

impl Mutator {
    pub fn new(config_path: &str) -> Self {
        let file = File::open(config_path)
            .expect(format!("Unable to open config file {}", config_path).as_str());
        let config = serde_yaml::from_reader(file)
            .expect(format!("Unable to parse config file {}", config_path).as_str());

        Mutator::new_with_config(config)
    }

    pub fn new_with_config(config: Config) -> Self {
        Mutator { config }
    }

    pub fn mutate(
        &self,
        kind: &str,
        res: AdmissionResponse,
        obj: &DynamicObject,
    ) -> Result<AdmissionResponse, Box<dyn Error>> {
        let object_type = match kind {
            "Pod" => OBJECT_KIND_POD,
            "DaemonSet" => OBJECT_KIND_DAEMONSET,
            _ => {
                info!("skipping request other than pod or daemonset: {}", kind);
                return Ok(res);
            }
        };

        let mutation_configs = &self.config.object_configs[object_type];

        let mut all_patches = Vec::new();

        for mutation_config in mutation_configs {
            let label_match = mutation_config.match_labels.iter().all(|(label, value)| {
                obj.labels().get(label).map_or(false, |v| {
                    let rex = regex::Regex::new(value).unwrap();
                    return rex.is_match(v);
                })
            });

            let annotation_match = mutation_config
                .match_annotations
                .iter()
                .all(|(ann, value)| {
                    obj.annotations().get(ann).map_or(false, |v| {
                        let rex = regex::Regex::new(value).unwrap();
                        return rex.is_match(v);
                    })
                });

            if label_match && annotation_match {
                // This config applies to the current pod
                for op in mutation_config.ops.iter() {
                    let path = Pointer::parse(op.path.as_str())
                        .expect(format!("unable to parse json path {}", op.path).as_str());
                    let value = serde_json::from_str(&op.value)?;

                    let patch_op = match op.op.as_str() {
                        OPERATION_ADD => {
                            json_patch::PatchOperation::Add(json_patch::AddOperation {
                                path,
                                value,
                            })
                        }
                        OPERATION_REMOVE => {
                            json_patch::PatchOperation::Remove(json_patch::RemoveOperation { path })
                        }
                        OPERATION_REPLACE => {
                            json_patch::PatchOperation::Replace(json_patch::ReplaceOperation {
                                path,
                                value,
                            })
                        }
                        _ => panic!("unknown patch operation {}", op.op),
                    };

                    all_patches.push(patch_op);
                }
            }
        }

        if !all_patches.is_empty() {
            info!("applying patches: {:?}", all_patches);
            Ok(res.with_patch(json_patch::Patch(all_patches))?)
        } else {
            info!("no matching configs found, no patches applied");
            Ok(res)
        }
    }
}

#[cfg(test)]
mod test {
    use kube::core::{
        admission::{AdmissionResponse, AdmissionReview},
        DynamicObject,
    };
    use std::collections::HashMap;

    use crate::mutator::{MutationConfig, MutationOp, Mutator, OBJECT_KIND_POD, OBJECT_KIND_DAEMONSET, OPERATION_ADD, OPERATION_REMOVE};

    #[test]
    fn can_parse_dynamic_object_into_pod() -> Result<(), Box<dyn std::error::Error>> {
        const WEBHOOK_BODY: &str = r#"{"kind":"AdmissionReview","apiVersion":"admission.k8s.io/v1","request":{"uid":"0c9a8d74-9cb7-44dd-b98e-09fd62def2f4","kind":{"group":"","version":"v1","kind":"Pod"},"resource":{"group":"","version":"v1","resource":"pods"},"requestKind":{"group":"","version":"v1","kind":"Pod"},"requestResource":{"group":"","version":"v1","resource":"pods"},"name":"echo-pod","namespace":"colin-coder","operation":"CREATE","userInfo":{"username":"<EMAIL>","groups":["system:authenticated"],"extra":{"iam.gke.io/user-assertion":["REDACTED"],"user-assertion.cloud.google.com":["REDACTED"]}},"object":{"kind":"Pod","apiVersion":"v1","metadata":{"name":"echo-pod","namespace":"colin-coder","creationTimestamp":null,"labels":{"app":"echo-server"},"annotations":{"test":"annotation","sku":"gpu2,gpu","kubectl.kubernetes.io/last-applied-configuration":"{\"apiVersion\":\"v1\",\"kind\":\"Pod\",\"metadata\":{\"annotations\":{},\"labels\":{\"app\":\"echo-server\"},\"name\":\"echo-pod\",\"namespace\":\"colin-coder\"},\"spec\":{\"containers\":[{\"image\":\"jmalloc/echo-server\",\"name\":\"echo-server\",\"ports\":[{\"containerPort\":8080,\"name\":\"http-port\"}]}]}}\n"},"managedFields":[{"manager":"kubectl","operation":"Update","apiVersion":"v1","time":"2021-03-29T23:02:16Z","fieldsType":"FieldsV1","fieldsV1":{"f:metadata":{"f:annotations":{".":{},"f:kubectl.kubernetes.io/last-applied-configuration":{}},"f:labels":{".":{},"f:app":{}}},"f:spec":{"f:containers":{"k:{\"name\":\"echo-server\"}":{".":{},"f:image":{},"f:imagePullPolicy":{},"f:name":{},"f:ports":{".":{},"k:{\"containerPort\":8080,\"protocol\":\"TCP\"}":{".":{},"f:containerPort":{},"f:name":{},"f:protocol":{}}},"f:resources":{},"f:terminationMessagePath":{},"f:terminationMessagePolicy":{}}},"f:dnsPolicy":{},"f:enableServiceLinks":{},"f:restartPolicy":{},"f:schedulerName":{},"f:securityContext":{},"f:terminationGracePeriodSeconds":{}}}}]},"spec":{"volumes":[{"name":"default-token-rxbqq","secret":{"secretName":"default-token-rxbqq"}}],"containers":[{"name":"echo-server","image":"jmalloc/echo-server","ports":[{"name":"http-port","containerPort":8080,"protocol":"TCP"}],"resources":{},"volumeMounts":[{"name":"default-token-rxbqq","readOnly":true,"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount"}],"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","imagePullPolicy":"Always"}],"restartPolicy":"Always","terminationGracePeriodSeconds":30,"dnsPolicy":"ClusterFirst","serviceAccountName":"default","serviceAccount":"default","securityContext":{},"schedulerName":"default-scheduler","tolerations":[{"key":"node.kubernetes.io/not-ready","operator":"Exists","effect":"NoExecute","tolerationSeconds":300},{"key":"node.kubernetes.io/unreachable","operator":"Exists","effect":"NoExecute","tolerationSeconds":300}],"priority":0,"enableServiceLinks":true},"status":{}},"oldObject":null,"dryRun":false,"options":{"kind":"CreateOptions","apiVersion":"meta.k8s.io/v1"}}}"#;
        let rev = serde_json::from_str::<AdmissionReview<DynamicObject>>(WEBHOOK_BODY).unwrap();
        let request = rev.request.as_ref().unwrap();
        let obj = request.object.as_ref().unwrap();
        let res = AdmissionResponse::from(request);

        // test label matching positive
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([("app".to_string(), "echo-server".to_string())]),
                    match_annotations: HashMap::from([]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_some());

        // test label matching negative
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([(
                        "app".to_string(),
                        "echo-server-wrong".to_string(),
                    )]),
                    match_annotations: HashMap::from([]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_none());

        // test annotation matching positive
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([]),
                    match_annotations: HashMap::from([(
                        "test".to_string(),
                        "annotation".to_string(),
                    )]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_some());

        // test annotation matching negative
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([]),
                    match_annotations: HashMap::from([(
                        "test".to_string(),
                        "annotation-wrong".to_string(),
                    )]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_none());

        // test annotation regex matching any positive
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([]),
                    match_annotations: HashMap::from([(
                        "test".to_string(),
                        ".*".to_string(),
                    )]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_some());

        // test annotation regex matching negative
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([]),
                    match_annotations: HashMap::from([(
                        "test".to_string(),
                        "does-not-match.*".to_string(),
                    )]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_none());

        // test annotation gpu pod matching
        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_POD.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([]),
                    match_annotations: HashMap::from([(
                        "sku".to_string(),
                        "(gpu1|gpu2).*".to_string(),
                    )]),
                    ops: vec![MutationOp {
                        op: OPERATION_ADD.to_string(),
                        path: "/metadata/annotations".to_string(),
                        value: r#"{"test":"value"}"#.to_string(),
                    }],
                }],
            )]),
        });
        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_some());

        Ok(())
    }

    #[test]
    fn multiple_configs_one_matches_one_does_not() -> Result<(), Box<dyn std::error::Error>> {
        const WEBHOOK_BODY: &str = r#"{"kind":"AdmissionReview","apiVersion":"admission.k8s.io/v1","request":{"uid":"0c9a8d74-9cb7-44dd-b98e-09fd62def2f4","kind":{"group":"","version":"v1","kind":"Pod"},"resource":{"group":"","version":"v1","resource":"pods"},"requestKind":{"group":"","version":"v1","kind":"Pod"},"requestResource":{"group":"","version":"v1","resource":"pods"},"name":"echo-pod","namespace":"colin-coder","operation":"CREATE","userInfo":{"username":"<EMAIL>","groups":["system:authenticated"],"extra":{"iam.gke.io/user-assertion":["REDACTED"],"user-assertion.cloud.google.com":["REDACTED"]}},"object":{"kind":"Pod","apiVersion":"v1","metadata":{"name":"echo-pod","namespace":"colin-coder","creationTimestamp":null,"labels":{"app":"echo-server"},"annotations":{"test":"annotation","kubectl.kubernetes.io/last-applied-configuration":"{\"apiVersion\":\"v1\",\"kind\":\"Pod\",\"metadata\":{\"annotations\":{},\"labels\":{\"app\":\"echo-server\"},\"name\":\"echo-pod\",\"namespace\":\"colin-coder\"},\"spec\":{\"containers\":[{\"image\":\"jmalloc/echo-server\",\"name\":\"echo-server\",\"ports\":[{\"containerPort\":8080,\"name\":\"http-port\"}]}]}}\n"},"managedFields":[{"manager":"kubectl","operation":"Update","apiVersion":"v1","time":"2021-03-29T23:02:16Z","fieldsType":"FieldsV1","fieldsV1":{"f:metadata":{"f:annotations":{".":{},"f:kubectl.kubernetes.io/last-applied-configuration":{}},"f:labels":{".":{},"f:app":{}}},"f:spec":{"f:containers":{"k:{\"name\":\"echo-server\"}":{".":{},"f:image":{},"f:imagePullPolicy":{},"f:name":{},"f:ports":{".":{},"k:{\"containerPort\":8080,\"protocol\":\"TCP\"}":{".":{},"f:containerPort":{},"f:name":{},"f:protocol":{}}},"f:resources":{},"f:terminationMessagePath":{},"f:terminationMessagePolicy":{}}},"f:dnsPolicy":{},"f:enableServiceLinks":{},"f:restartPolicy":{},"f:schedulerName":{},"f:securityContext":{},"f:terminationGracePeriodSeconds":{}}}}]},"spec":{"volumes":[{"name":"default-token-rxbqq","secret":{"secretName":"default-token-rxbqq"}}],"containers":[{"name":"echo-server","image":"jmalloc/echo-server","ports":[{"name":"http-port","containerPort":8080,"protocol":"TCP"}],"resources":{},"volumeMounts":[{"name":"default-token-rxbqq","readOnly":true,"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount"}],"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","imagePullPolicy":"Always"}],"restartPolicy":"Always","terminationGracePeriodSeconds":30,"dnsPolicy":"ClusterFirst","serviceAccountName":"default","serviceAccount":"default","securityContext":{},"schedulerName":"default-scheduler","tolerations":[{"key":"node.kubernetes.io/not-ready","operator":"Exists","effect":"NoExecute","tolerationSeconds":300},{"key":"node.kubernetes.io/unreachable","operator":"Exists","effect":"NoExecute","tolerationSeconds":300}],"priority":0,"enableServiceLinks":true},"status":{}},"oldObject":null,"dryRun":false,"options":{"kind":"CreateOptions","apiVersion":"meta.k8s.io/v1"}}}"#;

        let rev = serde_json::from_str::<AdmissionReview<DynamicObject>>(WEBHOOK_BODY).unwrap();
        let request = rev.request.as_ref().unwrap();
        let obj = request.object.as_ref().unwrap();
        let res = AdmissionResponse::from(request);

        // This config matches the pod since it has app=echo-server
        let matching_config = MutationConfig {
            match_labels: HashMap::from([("app".to_string(), "echo-server".to_string())]),
            match_annotations: HashMap::from([]),
            ops: vec![MutationOp {
                op: OPERATION_ADD.to_string(),
                path: "/metadata/annotations".to_string(),
                value: r#"{"multi-test":"valueA"}"#.to_string(),
            }],
        };

        // This config does not match because the label is different
        let non_matching_config = MutationConfig {
            match_labels: HashMap::from([("app".to_string(), "does-not-match".to_string())]),
            match_annotations: HashMap::from([]),
            ops: vec![MutationOp {
                op: OPERATION_ADD.to_string(),
                path: "/metadata/annotations".to_string(),
                value: r#"{"multi-test2":"valueB"}"#.to_string(),
            }],
        };

        let mutator = crate::mutator::Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                crate::mutator::OBJECT_KIND_POD.to_string(),
                vec![matching_config, non_matching_config],
            )]),
        });

        let response = mutator.mutate("Pod", res.clone(), obj).unwrap();
        assert!(response.allowed);

        // We only expect a patch from the matching config, not from the non-matching one
        let patch = response.patch.as_ref().unwrap();
        let patch_str = std::str::from_utf8(patch)?;
        assert!(patch_str.contains("\"multi-test\":\"valueA\""));
        assert!(!patch_str.contains("\"multi-test2\":\"valueB\""));

        Ok(())
    }

    #[test]
    fn can_remove_field_from_daemonset() -> Result<(), Box<dyn std::error::Error>> {
        const WEBHOOK_BODY: &str = r#"{
            "kind": "AdmissionReview",
            "apiVersion": "admission.k8s.io/v1",
            "request": {
                "uid": "12345",
                "kind": { "group": "apps", "version": "v1", "kind": "DaemonSet" },
                "resource": { "group": "apps", "version": "v1", "resource": "daemonsets" },
                "name": "example-daemonset",
                "namespace": "kube-system",
                "operation": "CREATE",
                "userInfo": {
                    "username": "<EMAIL>",
                    "groups": ["system:authenticated"],
                    "extra": {
                        "iam.gke.io/user-assertion": ["REDACTED"],
                        "user-assertion.cloud.google.com": ["REDACTED"]
                    }
                },
                "object": {
                    "kind": "DaemonSet",
                    "apiVersion": "v1",
                    "metadata": {
                        "name": "example-daemonset",
                        "namespace": "kube-system",
                        "labels": { "component": "ama-metrics" }
                    },
                    "spec": {
                        "template": {
                            "metadata": {
                                "labels": { "dsName": "ama-metrics-node" }
                            },
                            "spec": {
                                "affinity": {
                                    "nodeAffinity": {
                                        "requiredDuringSchedulingIgnoredDuringExecution": {
                                            "nodeSelectorTerms": [
                                                {
                                                    "matchExpressions": [
                                                        {
                                                            "key": "kubernetes.io/os",
                                                            "operator": "In",
                                                            "values": ["linux"]
                                                        },
                                                        {
                                                            "key": "type",
                                                            "operator": "NotIn",
                                                            "values": ["virtual-kubelet"]
                                                        },
                                                        {
                                                            "key": "kubernetes.azure.com/cluster",
                                                            "operator": "Exists"
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    }
                                },
                                "containers": [
                                    {
                                        "name": "example-container",
                                        "image": "example-image"
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        }"#;

        let rev = serde_json::from_str::<AdmissionReview<DynamicObject>>(WEBHOOK_BODY).unwrap();
        let request = rev.request.as_ref().unwrap();
        let obj = request.object.as_ref().unwrap();
        let res = AdmissionResponse::from(request);

        let mutator = Mutator::new_with_config(crate::mutator::Config {
            object_configs: HashMap::from([(
                OBJECT_KIND_DAEMONSET.to_string(),
                vec![MutationConfig {
                    match_labels: HashMap::from([("component".to_string(), "ama-metrics".to_string())]),
                    match_annotations: HashMap::from([]),
                    ops: vec![MutationOp {
                        op: OPERATION_REMOVE.to_string(),
                        path: "/spec/affinity/nodeAffinity/requiredDuringSchedulingIgnoredDuringExecution/nodeSelectorTerms/0/matchExpressions/2".to_string(),
                        value: r#"{"key": "kubernetes.azure.com/cluster", "operator": "Exists"}"#.to_string(), // Value is ignored for REMOVE operations
                    }],
                }],
            )]),
        });

        let response = mutator.mutate("DaemonSet", res.clone(), obj).unwrap();
        assert!(response.allowed);
        assert!(response.patch.is_some());

        let patch = response.patch.as_ref().unwrap();
        let patch_str = std::str::from_utf8(patch)?;
        assert!(patch_str.contains(r#"{"op":"remove","path":"/spec/affinity/nodeAffinity/requiredDuringSchedulingIgnoredDuringExecution/nodeSelectorTerms/0/matchExpressions/2"}"#));

        Ok(())
    }
}
