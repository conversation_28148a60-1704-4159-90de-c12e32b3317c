use clap::Parser;
use iridmission::mutator::Mutator;
use kube::core::{
    admission::{AdmissionRequest, AdmissionResponse, AdmissionReview},
    DynamicObject, ResourceExt,
};
use std::convert::Infallible;
use std::sync::Arc;
use std::time::SystemTime;
use tracing::*;
use warp::{http::StatusCode, reply, Filter, Rejection, Reply};

#[derive(Clone)]
struct LivenessContext {
    cert_path: String,
    server_start_time: SystemTime,
}

#[derive(Debug, Parser)]
#[clap(name = "iridmission", version = "0.1.0")]
struct Args {
    /// Path to TLS key.
    #[arg(long)]
    tls_key_path: String,
    /// Path to TLS certificate.
    #[arg(long)]
    tls_cert_path: String,
    #[arg(long)]
    config_path: String,
    /// HTTP listen port.
    #[arg(long, default_value = "8080")]
    port: u16,
    /// HTTP mutate path
    #[arg(long, default_value = "mutate")]
    http_mutate_path: String,
    /// HTTP liveness path
    #[arg(long, default_value = "live")]
    http_liveness_path: String,
}

#[tokio::main]
async fn main() {
    tracing_subscriber::fmt::init();

    let args = Args::parse();

    let mutator = Arc::new(Mutator::new(&args.config_path));
    
    // Create liveness context for certificate freshness check
    let liveness_context = Arc::new(LivenessContext {
        cert_path: args.tls_cert_path.clone(),
        server_start_time: SystemTime::now(),
    });

    let mutation_route = warp::post().and(
        warp::path(args.http_mutate_path)
            .and(warp::body::json())
            .and(with_mutator(mutator))
            .and_then(mutate_handler)
            .with(warp::trace::request()),
    );
    
    let liveness_route = warp::get()
        .and(warp::path(args.http_liveness_path))
        .and(with_liveness_context(liveness_context.clone()))
        .and_then(liveness_handler);
        
    let addr = format!("0.0.0.0:{}", args.port);
    warp::serve(mutation_route.or(liveness_route))
        .tls()
        .cert_path(args.tls_cert_path)
        .key_path(args.tls_key_path)
        .run(addr.parse::<std::net::SocketAddr>().unwrap())
        .await;
}

fn with_mutator(
    mutator: Arc<Mutator>,
) -> impl Filter<Extract = (Arc<Mutator>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || mutator.clone())
}

fn with_liveness_context(
    context: Arc<LivenessContext>,
) -> impl Filter<Extract = (Arc<LivenessContext>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || context.clone())
}

async fn liveness_handler(
    context: Arc<LivenessContext>,
) -> Result<impl Reply, Rejection> {
    // Check if certificate file exists and get its metadata
    let metadata = match std::fs::metadata(&context.cert_path) {
        Ok(m) => m,
        Err(e) => {
            error!("Failed to read certificate metadata: {}", e);
            return Ok(reply::with_status("Certificate file not accessible", StatusCode::SERVICE_UNAVAILABLE));
        }
    };
    
    // Get certificate modification time
    let cert_modified = match metadata.modified() {
        Ok(t) => t,
        Err(e) => {
            error!("Failed to get certificate modification time: {}", e);
            return Ok(reply::with_status("Cannot determine certificate modification time", StatusCode::SERVICE_UNAVAILABLE));
        }
    };
    
    // Check if certificate was modified after server start
    if cert_modified > context.server_start_time {
        warn!("Certificate has been modified since server start. Server started at {:?}, certificate modified at {:?}", 
              context.server_start_time, cert_modified);
        // Return 503 Service Unavailable to trigger pod restart
        return Ok(reply::with_status("Certificate has been renewed, restart required", StatusCode::SERVICE_UNAVAILABLE));
    }
    
    // Certificate is still valid from server start
    debug!("Liveness check passed. Certificate unchanged since server start.");
    Ok(reply::with_status("OK", StatusCode::OK))
}

async fn mutate_handler(
    body: AdmissionReview<DynamicObject>,
    mutator: Arc<Mutator>,
) -> Result<impl Reply, Infallible> {
    // Parse incoming webhook AdmissionRequest first
    let req: AdmissionRequest<_> = match body.try_into() {
        Ok(req) => req,
        Err(err) => {
            error!("invalid request: {}", err.to_string());
            return Ok(reply::json(
                &AdmissionResponse::invalid(err.to_string()).into_review(),
            ));
        }
    };

    // Then construct a AdmissionResponse
    let mut res = AdmissionResponse::from(&req);
    // req.Object always exists for us, but could be None if extending to DELETE events
    if let Some(obj) = req.object {
        let name = obj.name_any(); // apiserver may not have generated a name yet
        res = match mutator.mutate(&req.kind.kind, res.clone(), &obj) {
            Ok(res) => {
                info!("accepted: {:?} on Pod {}", req.operation, name);
                res
            }
            Err(err) => {
                warn!("denied: {:?} on {} ({})", req.operation, name, err);
                res.deny(err.to_string())
            }
        };
    };
    // Wrap the AdmissionResponse wrapped in an AdmissionReview
    Ok(reply::json(&res.into_review()))
}
