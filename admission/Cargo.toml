[package]
name = "iridmission"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
jsonptr = "0.4.7"
kube = { version = "0.94.1", features = ["admission"] }
k8s-openapi = { version = "0.22.0", features = ["latest"] }
warp = { version = "0.3", default-features = false, features = ["tls"] }
json-patch = "2.0.0"
tokio = { version = "1.14.0", features = ["full"] }
tokio-macros = "2.4.0"
serde_json = "1.0.128"
tracing = "0.1.36"
tracing-subscriber = "0.3.17"
clap = { version = "4.5.17", features = ["derive"] }
serde = "1.0.210"
serde_yaml = "0.9.34"
regex = "1.3.9"
