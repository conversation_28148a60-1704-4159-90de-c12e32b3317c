objectConfigs:
  daemonset:
    - matchLabels:
        component: ama-metrics
      matchAnnotations: {}
      ops:
      - op: remove
        path: /spec/template/spec/affinity/nodeAffinity/requiredDuringSchedulingIgnoredDuringExecution/nodeSelectorTerms/0/matchExpressions/2
        value: |
          {
            "key":"kubernetes.azure.com/cluster",
            "operator":"Exists"
          }
    - matchLabels:
        component: ama-logs-agent
      matchAnnotations: {}
      ops:
      - op: remove
        path: /spec/template/spec/affinity/nodeAffinity/requiredDuringSchedulingIgnoredDuringExecution/nodeSelectorTerms/0/matchExpressions/1
        value: |
          {
            "key":"kubernetes.azure.com/cluster",
            "operator":"Exists"
          }
  pod:
    - matchLabels:
        label1: value1
      matchAnnotations:
        annotation1: value1
      ops:
      - op: add
        path: /spec/containers/0/env/-
        value: |
          {
            "name":"EXTRA_CLUSTERS_JSON",
            "value":"{\"name\":\"iridium-0\",\"region\":\"uksouth\"}"
          }
