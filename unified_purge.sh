#!/usr/bin/env bash
#
# Purge OAI artifacts installed by ./unified_setup.sh

# Display warning
echo "WARNING: This script will remove all OAI artifacts installed by ./unified_setup.sh."
echo "Do you wish to continue?"

# Prompt user for confirmation
read -r -p "Type yes/y to continue or no/n to exit: " choice

# Check user input
case "$choice" in
    yes|y)
        echo "You chose to continue..."
        ;;
    no|n)
        echo "Exiting script."
        exit 0
        ;;
    *)
        echo "Invalid input. Exiting..."
        exit 1
        ;;
esac

source ./util.sh
set_os_type_env_var

# Remove rcall config
rm $HOME/.rcall_config.py

# Remove Orange and Iridium profile
rm $HOME/.iridium_profile
rm $HOME/.orange_profile


# Remove brix
rm -rf $HOME/.brix

# Remove OpenAI dir
rm -rf $HOME/.openai

# Remove OpenAI venv
rm -fr $HOME/.virtualenvs/openai

# Remove sourcing of the orange profile and rc
sed -i '' '/orange_profile/d' ~/.zprofile
sed -i '' '/orange_rc/d' ~/.zshrc

