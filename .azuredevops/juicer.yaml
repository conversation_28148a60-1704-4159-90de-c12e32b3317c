pool: "iridium-builder"

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - src/juicer/*

steps:
  - checkout: git://Mimco/_git/bootstrapping

  - template: templates/az-setup.yaml

  - script: |
      az acr login --name iridiumsdc
    displayName: 'Login to ACR'

  - script: |
      git config --global user.email "${Build.QueuedBy}@microsoft.com"
      git config --global user.name "${Build.QueuedBy}"
      git tag "juicer-v0.1.0-$(git rev-parse --short HEAD)"
      cd src/juicer
      make ACR=iridiumsdc build
    displayName: 'Make build'

  - script: |
      cd src/juicer
      make ACR=iridiumsdc push
    displayName: 'Make push'
