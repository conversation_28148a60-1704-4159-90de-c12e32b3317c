
pool: 'iridium-builder'

# This is not triggered by any change in this repo, we build the blobpipe image on demand
# Make sure you update corresponding terraform version.
trigger: none

steps:
  # This pipeline is playing a few tricks to get the right version of torchflow-mirror:
  # 1. We are not cloning the correct branch since it triggers Supply Chain failures
  # 2. We however are cloning the research branch, in a shallow manner, only such that azure devops ensure we have the correct SYSTEM_ACCESSTOKEN set.
  # 3. We then use the SYSTEM_ACCESSTOKEN to clone the correct branch.
  # 4. We also patch the Dockerfile on the fly to pull from mcr mirror to get around the dockerhub rate limits.
  # 5. We manually pushed the cargo-chef image  version required to iridiumsdc.azurecr.io to get around the dockerhub rate limits, and we do a similar patch to the Dockerfile to pull from the mirror.
  - checkout: git://Mimco/_git/torchflow-mirror@microsoft/main-research
    fetchDepth: 1
    fetchTags: false

  - script: |
      git clone --branch orange/main --single-branch --depth 1 --no-tags --recurse-submodules=no https://$(System.AccessToken)@dev.azure.com/project-argos/Mimco/_git/torchflow-mirror $(System.DefaultWorkingDirectory)/torchflow-mirror
    displayName: 'Checkout torchflow-mirror'
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)

  - template: templates/az-setup.yaml

  - script: |
      az acr login --name iridiumsdc
    displayName: 'Login to ACR'

  # Temporary upgrade to Rust 1.81 to fix LazyLock stability issues
  # TODO(openai-update): Revert to stable Rust version once we get latest patch updates from openai
  # that addresses LazyLock compatibility with earlier Rust versions
  - script: |
      sed -i -e 's/debian:bookworm/mcr.microsoft.com\/mirror\/docker\/library\/debian:bookworm/g' project/scaling-infra/blobpipe/Dockerfile
      sed -i -e 's/lukemathwalker\/cargo-chef:latest-rust-1.80.1/iridiumsdc.azurecr.io\/mirror\/docker\/lukemathwalker\/cargo-chef:latest-rust-1.81.0/g' project/scaling-infra/blobpipe/Dockerfile
  
      docker build -f project/scaling-infra/blobpipe/Dockerfile -t iridiumsdc.azurecr.io/infra/blobpipe:$(Build.BuildId) .
    displayName: 'Build'
    workingDirectory: '$(System.DefaultWorkingDirectory)/torchflow-mirror'


  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - script: |
        docker push iridiumsdc.azurecr.io/infra/blobpipe:$(Build.BuildId)
      displayName: 'Push'
