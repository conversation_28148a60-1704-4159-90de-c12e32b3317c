
pool: 'iridium-builder'

trigger: none

pr:
  branches:
    include:
      - "*"
  paths:
    include:
      - .azuredevops/perhonen.yaml

steps:
  - checkout: git://Mimco/_git/brix
  
  - template: templates/az-setup.yaml

  - script: |
      az acr login --name iridiumsdc
    displayName: 'Login to ACR'

  - script: |
      git config --global user.email "${Build.QueuedBy}@microsoft.com"
      git config --global user.name "${Build.QueuedBy}"
      git tag "perhonen-v0.0.1-$(git rev-parse --short HEAD)"
      cd perhonen
      sed -i -e 's/debian:bookworm/mcr.microsoft.com\/mirror\/docker\/library\/debian:bookworm/g' Dockerfile
      sed -i -e 's/lukemathwalker\/cargo-chef:latest-rust-1.85.0/iridiumsdc.azurecr.io\/mirror\/docker\/lukemathwalker\/cargo-chef:latest-rust-1.85.0/g' Dockerfile 
      make IMAGE_REGISTRY=iridiumsdc.azurecr.io build
    displayName: 'Make build'

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - template: templates/az-setup.yaml 
    - script: |
        cd perhonen
        make IMAGE_REGISTRY=iridiumsdc.azurecr.io push
      displayName: 'Make push'
