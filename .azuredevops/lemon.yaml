parameters:
  - name: bootstrapping_branch
    type: string
    default: 'main'

  - name: torchflow_mirror_branch
    type: string
    default: 'orange/lemon/main-may'

pool: 'iridium-builder'

trigger:
  branches:
    include:
      - main
  paths:
    include:
    - images/lemon/*
stages:
  - stage: Build
    displayName: 'Fetch oaiartifact wheels'
    jobs:
      - job: Build
        displayName: 'Fetch oaiartifact wheels'
        pool: 'orange-builder'
        steps:
          - template: templates/orange-stage-setup.yaml
          - script: |
              az account set --subscription "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e"
            displayName: "Az account show"
          - script: |
              az storage blob download-batch --pattern "*.whl" --destination $BUILD_ARTIFACTSTAGINGDIRECTORY --account-name orngoaiartifacts --source wheels --auth-mode login
            displayName: "cp orngoaiartifacts/wheels"
          
          - task: PublishBuildArtifacts@1
            displayName: "Wheels as Artifact"
            inputs:
              pathToPublish: "$(Build.ArtifactStagingDirectory)"
              artifactName: "wheels"
  
  - stage: "Pushtoiridiumsdc"
    displayName: 'Build and Push Lemon Docker image to iridiumsdc'
    jobs:
      - job: Push
        displayName: 'Push Lemon'
        pool: 'iridium-builder'
        steps:
          - template: templates/az-setup.yaml
          - task: DownloadBuildArtifacts@0
            displayName: "Download Artifact"
            inputs:
              artifactName: "wheels"
              downloadPath: "$(System.DefaultWorkingDirectory)/wheels"

          - checkout: git://Mimco/_git/bootstrapping@${{ parameters.bootstrapping_branch }}
            fetchDepth: 1
            fetchTags: false

          - checkout: git://Mimco/_git/torchflow-mirror@microsoft/main-research
            fetchDepth: 1
            fetchTags: false

          - script: |
              git clone --branch ${{ parameters.torchflow_mirror_branch }} --single-branch --depth 1 --no-tags --recurse-submodules=no https://$(System.AccessToken)@dev.azure.com/project-argos/Mimco/_git/torchflow-mirror $(System.DefaultWorkingDirectory)/openai
            displayName: 'Checkout torchflow-mirror'
            env:
              SYSTEM_ACCESSTOKEN: $(System.AccessToken)
          
          - script: |
              cp -r /mnt/vss/_work/1/s/openai bootstrapping/images/lemon/
              cp -r /mnt/vss/_work/1/s/wheels bootstrapping/images/lemon/
            displayName: "List directory and copy to context"

          - script: |
              az acr login --name iridiumsdc
              DOCKER_BUILDKIT=1 docker build -t iridiumsdc.azurecr.io/infra/lemon:$(Build.BuildId) .
              docker push iridiumsdc.azurecr.io/infra/lemon:$(Build.BuildId)
            displayName: 'Push Lemon image'
            workingDirectory: ./bootstrapping/images/lemon