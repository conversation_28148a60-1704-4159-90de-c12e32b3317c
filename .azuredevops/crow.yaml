
pool: 'iridium-builder'

trigger:
  branches:
    include:
      - main
  paths:
    include:
    - images/crow/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
    - images/crow/*

steps:
  - script: |
      docker run --privileged --rm mcr.microsoft.com/mirror/docker/tonistiigi/binfmt:qemu-v9.2.2-52 --install all
    displayName: 'Setup qemu'

  - template: templates/az-setup.yaml
  - script: |
      az acr login --name iridiumsdc
      docker build --platform linux/amd64 -t iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)-amd64 .
      docker build --platform linux/arm64 -t iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)-arm64 .

    displayName: 'Build image'
    workingDirectory: images/crow

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - template: templates/az-setup.yaml

    - script: |
        docker push iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)-amd64
        docker push iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)-arm64

        docker manifest create iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId) \
          --amend iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)-amd64 \
          --amend iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)-arm64
        docker manifest push iridiumsdc.azurecr.io/rcall:crow-$(Build.BuildId)
      displayName: 'Push image'
