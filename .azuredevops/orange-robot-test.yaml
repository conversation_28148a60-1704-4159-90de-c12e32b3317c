name: Orange E2E Test (Robot User)

trigger: none

# We are the 'orng-test-bot' robot user.
# orange-robot-users creates this 1ES build pool, which has an msi attached (see below)
pool: 'orng-test-bot-robot-builder'

steps:
  - checkout: self

  - script: |
      # Client ID is for msi_orng-test-bot-robot-builder
      az login --identity --client-id e20273ca-a05c-456c-a591-9562358e7cc5 --allow-no-subscriptions
    displayName: 'Authenticate to Azure using Managed Identity (msi_orng-test-bot-robot-builder)'

  - template: templates/orange-tailscale-setup.yaml

  - script: |
      # TODO: Maybe this will be done by the laptop setup script instead.
      set -ex
      sudo curl -sSL "https://dl.k8s.io/release/v1.22.0/bin/linux/amd64/kubectl" \
        -o /usr/local/bin/kubectl
      sudo chmod ugo+x /usr/local/bin/kubectl
      kubectl version --client
    displayName: Install kubectl

  - script: |
      set -ex
      export KUBECONFIG="$(realpath .azuredevops/config/orange-robot-kubeconfig.yaml)"
      [ -f "$KUBECONFIG" ] || (echo "Kubeconfig not found at $KUBECONFIG"; exit 1)

      # prod-uksouth-9 has a role binding for this user so we can see this work.
      kubectl get namespace
    displayName: Poke a cluster
