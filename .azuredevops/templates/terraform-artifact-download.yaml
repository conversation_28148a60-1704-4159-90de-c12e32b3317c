parameters:
  - name: path # file path to place downloaded .tfplan file
    type: string
  - name: blob_prefix # optional prefix for the uploaded blob, must match prefix used in upload
    type: string
    default: 'TerraformPlan'
  - name: storage_account_name
    type: string
  - name: blob_container_name
    type: string

steps:
  - script: |
      BLOB_NAME='${{ parameters.blob_prefix }}-$(Build.BuildId)-$(System.JobAttempt).tfplan'
      echo "Downloading artifact from storage blob 'az://${{ parameters.storage_account_name }}/${{ parameters.blob_container_name }}/$BLOB_NAME'..."
      az storage blob download \
        --account-name '${{ parameters.storage_account_name }}' \
        --container-name '${{ parameters.blob_container_name }}' \
        --name "$BLOB_NAME" \
        --file '${{ parameters.path }}' \
        --auth-mode login \
        || { echo "If BlobNotFound error, this usually means you re-ran an Apply stage without re-running the Plan stage first. Try starting a new pipeline run instead."; exit 1; }
    displayName: 'Download Plan Artifact from Storage Blob'
