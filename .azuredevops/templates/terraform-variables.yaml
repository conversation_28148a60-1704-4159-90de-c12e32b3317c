parameters:
  - name: tenant
    type: string
    values:
      - ame
      - green

# variables to set on all terraform pipelines
variables:
  # AzureRM tf backend
  - name: ARM_TENANT_ID
    ${{ if eq(parameters.tenant, 'ame') }}:
      value: 33e01921-4d64-4f8c-a055-5bdaffd5e33d
    ${{ if eq(parameters.tenant, 'green') }}:
      value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
  - name: ARM_USE_AZUREAD
    value: true
  - name: ARM_USE_MSI
    value: true
  - name: ARM_STORAGE_USE_AZUREAD
    value: true
  # Terraform
  - name: TF_CLI_ARGS_plan
    value: -lock-timeout=30m
  - name: TF_CLI_ARGS_apply
    value: -lock-timeout=30m
  # Azure pipelines
  - name: PROCESS_SIGINT_TIMEOUT
    value: 60000 # wait 1 minute after sigint for lock release
  - name: USE_GRACEFUL_PROCESS_SHUTDOWN
    value: true
