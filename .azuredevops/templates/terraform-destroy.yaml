parameters:
  - name: path
    type: string

steps:
  - checkout: self

  - template: stage-setup.yaml

  - script: |
      terraform init
    displayName: 'Init Terraform'
    workingDirectory: ${{ parameters.path }}

  # Destroy Infra
  - script: |
      echo "Destroying infrastructure..."
      terraform destroy -auto-approve
    displayName: 'Destroy Infrastructure'
    workingDirectory: ${{ parameters.path }}