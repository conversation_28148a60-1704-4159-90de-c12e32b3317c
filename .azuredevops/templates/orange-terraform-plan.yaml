parameters:
  - name: path
    type: string
  - name: artifact_name
    type: string
    default: 'TerraformPlan-$(System.JobAttempt)'
  - name: skip_plan # Useful when we can't really do a proper plan in the CI pipeline but must do a validate
    type: boolean
    default: false
  - name: download_artifact_name
    type: string
    default: ''
  - name: download_artifact_path
    type: string
    default: ''
  - name: terraform_vars
    type: object
    default: []
  - name: terraform_flags
    type: string
    default: ''

steps:
  - checkout: self

  - template: orange-stage-setup.yaml

  # - ${{ if not(parameters.skip_plan) }}: # dont download artifact if we are not going to run a plan
  - template: download-artifact.yaml
    parameters:
      artifact: ${{ parameters.download_artifact_name }}
      targetPath: ${{ parameters.download_artifact_path }}

  # Step to initialize and validate terraform
  - script: |
      terraform init
      terraform validate
    displayName: 'Init & Validate Terraform'
    workingDirectory: ${{ parameters.path }}

  # Conditionally run plan and artifact upload steps only when skip_plan is false
  - ${{ if not(parameters.skip_plan) }}:
    - script: |
        TF_VAR_ARGS=""
        for var in $(echo $terraform_vars | jq -c '.[]'); do
          name=$(echo $var | jq -r '.name')
          value=$(echo $var | jq -r '.value')
          TF_VAR_ARGS+="-var=${name}=${value} "
        done
        terraform plan -input=false -out=plan.tfplan -detailed-exitcode $terraform_flags $TF_VAR_ARGS
        exit_code=$?

        case $exit_code in
          0)
            echo "No changes detected in the plan"
            echo "##vso[task.setvariable variable=plan_has_changes;isOutput=true]false"
            ;;
          1)
            echo "Terraform plan failed"
            exit 1
            ;;
          2)
            echo "Changes detected in the plan"
            echo "##vso[task.setvariable variable=plan_has_changes;isOutput=true]true"
            ;;
          *)
            echo "Unexpected exit code: $exit_code"
            exit $exit_code
            ;;
        esac
      displayName: 'Plan Terraform'
      name: plan
      workingDirectory: ${{ parameters.path }}
      env: # Preserve var name case
        TF_CLI_ARGS_plan: $(TF_CLI_ARGS_plan)
        terraform_vars: ${{ convertToJson(parameters.terraform_vars) }}
        terraform_flags: ${{ parameters.terraform_flags }}

    - template: terraform-artifact-upload.yaml
      parameters:
        path: '${{ parameters.path }}/plan.tfplan'
        storage_account_name: orangetfstate
        blob_container_name: tfplan
        blob_prefix: ${{ parameters.artifact_name }}

  - ${{ if and(eq(variables['Build.Reason'], 'PullRequest'), not(parameters.skip_plan)) }}:
    - template: terraform-post-comment.yaml
      parameters:
        planFile: 'plan.tfplan'
        path: ${{ parameters.path }}