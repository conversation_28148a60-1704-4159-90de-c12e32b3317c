# Note that this template expects 'az login' to have run already, with access to orangetailscalekeys
steps:
  - script: |
      # Retrieve client_id and client_secret from Key Vault
      CLIENT_ID=$(az keyvault secret show --subscription 57cd9edb-cae4-478d-9c6a-81ac38d30c4a --vault-name orangetailscalekeys --name orange-builder-tailscale-client-id --query value -o tsv)
      CLIENT_SECRET=$(az keyvault secret show --subscription 57cd9edb-cae4-478d-9c6a-81ac38d30c4a --vault-name orangetailscalekeys --name orange-builder-tailscale-client-secret --query value -o tsv)

      # Get ACCESS_TOKEN
      ACCESS_TOKEN=$(curl -s -d "client_id=${CLIENT_ID}" -d "client_secret=${CLIENT_SECRET}" "https://api.tailscale.com/api/v2/oauth/token" | jq -r .access_token)

      # Fetch AUTHKEY
      AUTHKEY=$(curl -s "https://api.tailscale.com/api/v2/tailnet/-/keys" \
        -u "${ACCESS_TOKEN}:" \
        -H "Content-Type: application/json" \
        --data-binary '
      {
        "capabilities": {
          "devices": {
            "create": {
              "reusable": false,
              "ephemeral": true,
              "preauthorized": true,
              "tags": [ "tag:orange-builder" ]
            }
          }
        }
      }' | jq -r .key)

      # Authenticate Tailscale
      sudo tailscale up --auth-key ${AUTHKEY} --accept-dns=true --accept-routes=true --hostname=$(hostname)-orange-robot

      tailscale status || { echo "Tailscale failed to start" ; exit 1; }
      sudo tailscale dns status
    displayName: 'Connect to Tailscale'

