parameters:
  - name: path
    type: string
    
  - name: planFile
    type: string
    default: 'plan.tfplan'

steps:
  - script: |
      cd ${{ parameters.path }}
      terraform show -json ${{ parameters.planFile }} > terraform_plan.json
      terraform show -no-color ${{ parameters.planFile }} > terraform_plan_diff.txt
    displayName: 'Generate Terraform Plan Outputs'

  - script: |
      echo "Installing uv..."
      curl -LsSf https://astral.sh/uv/install.sh | sh
      source $HOME/.local/bin/env
      echo "Running Terraform comment update script..."
      uv run $(System.DefaultWorkingDirectory)/scripts/post-terraform-plan-as-comment.py
    displayName: "Install uv & Run Terraform Comment Script"
    env:
      PLAN_JSON_PATH: "${{ parameters.path }}/terraform_plan.json"
      PLAN_DIFF_PATH: "${{ parameters.path }}/terraform_plan_diff.txt"
      AZDO_COLLECTION_URI: $(System.CollectionUri)
      AZDO_TEAM_PROJECT: $(System.TeamProject)
      AZDO_REPOSITORY_ID: $(Build.Repository.ID)
      AZDO_PULLREQUEST_ID: $(System.PullRequest.PullRequestId)
      AZDO_ACCESS_TOKEN: $(System.AccessToken)
