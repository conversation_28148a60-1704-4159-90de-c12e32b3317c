parameters:
  - name: path
    type: string
  - name: artifact_name
    type: string
    default: 'TerraformPlan-$(System.JobAttempt)'
  - name: download_artifact_name
    type: string
    default: ''
  - name: download_artifact_path
    type: string
    default: ''
  - name: terraform_vars
    type: object
    default: []

steps:
  - checkout: self

  - template: orange-stage-setup.yaml
  
  - template: download-artifact.yaml
    parameters:
      artifact: ${{ parameters.download_artifact_name }}
      targetPath: ${{ parameters.download_artifact_path }}

  # Download Plan Artifact
  - template: terraform-artifact-download.yaml
    parameters:
      path: '${{ parameters.path }}/plan.tfplan'
      storage_account_name: orangetfstate
      blob_container_name: tfplan
      blob_prefix: ${{ parameters.artifact_name }}

  - script: |
      terraform init
    displayName: 'Init Terraform'
    workingDirectory: ${{ parameters.path }}

  # Apply Plan
  - script: |
      TF_VAR_ARGS=""
      for var in $(echo $terraform_vars | jq -c '.[]'); do
        name=$(echo $var | jq -r '.name')
        value=$(echo $var | jq -r '.value')
        TF_VAR_ARGS+="-var=${name}=${value} "
      done
      echo "Applying plan..."
      terraform apply plan.tfplan $TF_VAR_ARGS
    displayName: 'Apply Plan'
    workingDirectory: ${{ parameters.path }}
    env: # Preserve var name case
      TF_CLI_ARGS_apply: $(TF_CLI_ARGS_apply)
      terraform_vars: ${{ convertToJson(parameters.terraform_vars) }}
