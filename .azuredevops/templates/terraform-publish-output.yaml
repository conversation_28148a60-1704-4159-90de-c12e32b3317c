parameters:
  - name: path
    type: string
  - name: oidc_enabled
    type: boolean
    default: false
  - name: artifact_name
    type: string
    default: 'TerraformPlan-$(System.JobAttempt)'
  - name: terraform_vars # Generic list of terraform variables
    type: object
    default: []

steps:
  - checkout: self

  - template: orange-stage-setup.yaml

  - script: |
      terraform init
    displayName: 'Init Terraform'
    workingDirectory: ${{ parameters.path }}

  - script: |
      # Generate the terraform output and save it to a file
      terraform output -json > output.json
    displayName: 'Export Terraform Output'
    name: plan_output
    workingDirectory: ${{ parameters.path }}

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Output Artifact'
    inputs:
      ArtifactName: ${{ parameters.artifact_name }}
      TargetPath: ${{ parameters.path }}/output.json