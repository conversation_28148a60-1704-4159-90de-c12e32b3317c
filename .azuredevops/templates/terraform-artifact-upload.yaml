parameters:
  - name: path # path to .tfplan file to upload
    type: string
  - name: blob_prefix # optional prefix for the uploaded blob, to distinguish between multiple plans in a single run
    type: string
    default: 'TerraformPlan'
  - name: storage_account_name
    type: string
  - name: blob_container_name
    type: string

steps:
  - script: |
      BLOB_NAME='${{ parameters.blob_prefix }}-$(Build.BuildId)-$(System.JobAttempt).tfplan'
      echo "Uploading artifact to storage blob 'az://${{ parameters.storage_account_name }}/${{ parameters.blob_container_name }}/$BLOB_NAME'..."
      az storage blob upload \
        --account-name '${{ parameters.storage_account_name }}' \
        --container-name '${{ parameters.blob_container_name }}' \
        --name "$BLOB_NAME" \
        --file '${{ parameters.path }}' \
        --auth-mode login \
        --overwrite false
    displayName: 'Upload Plan Artifact to Storage Blob'
