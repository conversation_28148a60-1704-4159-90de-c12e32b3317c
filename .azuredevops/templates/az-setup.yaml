parameters:
  - name: oidc_enabled
    type: boolean
    default: false


steps:
  # Authenticate to Azure using Managed Identity
  - script: |
      # The msi_iridium-builder object ID
      az login --identity --username 337eeb41-0bf2-4205-9eec-7a05b177ae37
      az account set --subscription "64467a16-0cdd-4a44-ad9b-83b5540828ac"
    displayName: 'Authenticate to Azure using Managed Identity (msi_iridium-builder)'
    
    
  # only run if oidc_enabled is true
  - ${{ if eq(parameters.oidc_enabled, true) }}:
    - script: |
        curl -s -H 'Metadata: true' 'http://***************/metadata/identity/oauth2/token?api-version=2021-01-01&resource=api://AzureAdTokenExchange' | jq .access_token -r > /tmp/oidc_token
      displayName: 'Generate Federated OIDC Token'