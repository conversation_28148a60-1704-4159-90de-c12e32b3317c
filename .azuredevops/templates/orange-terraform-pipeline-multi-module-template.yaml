# --------------------------------------------------------------------------------
#  Title: Multi-Module Terraform Pipeline Template
#  
#  Description:
#    This template defines two stages (Plan and Apply) for each module in your
#    Azure DevOps pipeline. It supports:
#      - Dynamic stage name creation (hyphens in module names are replaced with 
#        underscores to produce valid stage names)
#      - A Plan → Apply workflow per module
#      - Optional inter-module dependencies: if a module requires another module to 
#        finish its Apply stage before starting its Plan stage, list that dependency.
#
#  IMPORTANT:
#    - **Do not specify** the `dependsOn` property as an empty array to indicate no
#      dependencies. Doing so will cause an "Object reference not set to an instance
#      of an object" error.
#    - To run modules in parallel (i.e. with no dependencies), simply omit the 
#      `dependsOn` property for that module.
#    - If dependencies are needed, explicitly set `dependsOn` to an array of module 
#      names (using the raw module names). The template automatically transforms each
#      dependency into a reference to the corresponding Apply stage.
#
#  Usage Example:
#
#    In your main pipeline (azure-pipelines.yaml), reference this template:
#
#      trigger:
#        branches:
#          include:
#            - main
#
#      extends:
#        template: orange-terraform-pipeline-multi-module-template.yaml
#        parameters:
#          modules:
#            - name: orange_global_onboarding
#              path: infra/orange-global-onboarding
#              environment: orange_storage
#              # Do not set dependsOn if there are no dependencies.
#
#            - name: orange_storage_bootstrapping
#              path: infra/orange-storage-bootstrapping
#              environment: orange_storage
#              dependsOn:
#                - orange_global_onboarding  # Waits for Apply_orange_global_onboarding stage.
#
#  Note:
#    Ensure that your environment (e.g., "orange_storage") exists in Project Settings 
#    under Pipelines → Environments.
#
# --------------------------------------------------------------------------------

parameters:
  - name: modules
    type: object
    default: []

# Shared Variables
variables:
  - template: terraform-variables.yaml
    parameters:
      tenant: green

stages:
  # Loop over each module to create its Plan and Apply stages
  - ${{ each module in parameters.modules }}:
    # -------------------------------------------------
    # PLAN STAGE
    - stage: ${{ format('Plan_{0}', replace(module.name, '-', '_')) }}
      displayName: "Plan - ${{ module.name }}"

      # Insert 'dependsOn' only if module.dependsOn is non-null
      # NOTE: An empty list causes AzureDevOps to through object reference null errors
      ${{ if module.dependsOn }}:
        dependsOn:
          # Loop over each dependency in module.dependsOn
          ${{ each dependency in module.dependsOn }}:
            - ${{ format('Apply_{0}', replace(dependency, '-', '_')) }}
      ${{ else }}:
        dependsOn: []

      jobs:
        - job: Plan
          displayName: "Run Plan for ${{ module.name }}"
          pool: "orange-builder"
          steps:
            - template: orange-terraform-plan.yaml
              parameters:
                path: ${{ module.path }}
                artifact_name: ${{ module.name }}

    # -------------------------------------------------
    # APPLY STAGE
    - stage: ${{ format('Apply_{0}', replace(module.name, '-', '_')) }}
      displayName: "Apply - ${{ module.name }}"
      dependsOn: ${{ format('Plan_{0}', replace(module.name, '-', '_')) }}

      # Only run Apply if:
      # 1) Plan succeeded
      # 2) Plan shows changes
      # 3) Build reason & branch conditions are met
      # For reference see https://blogs.blackmarble.co.uk/rfennell/using-azure-devops-stage-dependency-variables-with-conditional-stage-and-job-execution/ 
      # And https://learn.microsoft.com/en-us/azure/devops/pipelines/process/expressions?view=azure-devops#dependency-syntax-overview
      condition: and(
        not(failed()), 
        not(canceled()),
        eq(dependencies['${{ format('Plan_{0}', replace(module.name, '-', '_')) }}'].outputs['Plan.plan.plan_has_changes'], 'true'),
        in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
        in(variables['Build.SourceBranch'], 'refs/heads/main'))

      jobs:
        - deployment: Apply
          displayName: "Run Apply for ${{ module.name }}"
          pool: "orange-builder"
          environment: ${{ module.environment }}
          strategy:
            runOnce:
              deploy:
                steps:
                  - template: orange-terraform-apply.yaml
                    parameters:
                      path: ${{ module.path }}
                      artifact_name: ${{ module.name }}
