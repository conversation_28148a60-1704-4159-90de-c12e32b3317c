parameters:
  - name: module
    type: string
  - name: path
    type: string

variables:
  - name: ARM_TENANT_ID
    value: 33e01921-4d64-4f8c-a055-5bdaffd5e33d
  - name: ARM_USE_AZUREAD
    value: true
  - name: ARM_USE_MSI
    value: true
  - name: ARM_STORAGE_USE_AZUREAD
    value: true

stages:
  # Combined Plan & Apply Stage
  - stage: Deploy
    displayName: "Deploy - ${{ parameters.module }}"
    jobs:
      - job: Deploy
        displayName: 'Run Deploy for ${{ parameters.module }}'
        pool: 'iridium-builder'
        steps:
          - checkout: self

          - template: stage-setup.yaml

          # step to validate and init terraform
          - script: |
              terraform init
              terraform validate
            displayName: 'Init & Validate Terraform'
            workingDirectory: ${{ parameters.path }}

          # step to plan terraform
          - script: |
                terraform plan -input=false -out=plan.tfplan
            displayName: 'Plan Terraform'
            name: plan
            workingDirectory: ${{ parameters.path }}
            env: # Preserve var name case
              TF_CLI_ARGS_plan: $(TF_CLI_ARGS_plan)

            # Upload Plan Artifact
          - template: terraform-artifact-upload.yaml
            parameters:
              path: '${{ parameters.path }}/plan.tfplan'
              storage_account_name: iridiumtfstate
              blob_container_name: tfplan
            displayName: 'Upload Plan Artifact to Storage Blob'

          # Apply Plan
          - script: |
              echo "Applying plan..."
              terraform apply plan.tfplan
            displayName: 'Apply Plan'
            workingDirectory: ${{ parameters.path }}
            env: # Preserve var name case
              TF_CLI_ARGS_apply: $(TF_CLI_ARGS_apply)

  # Destroy Stage
  - stage: Destroy
    displayName: "Destroy - ${{ parameters.module }}"
    dependsOn: ['Deploy']
    # Run destroy only if deploy succeeded, If not, It would be wise to debug what happened
    condition: succeeded('Deploy')
    jobs:
      - job: Destroy
        displayName: 'Run Destroy for ${{ parameters.module }}'
        pool: 'iridium-builder'
        steps:
          - template: terraform-destroy.yaml
            parameters:
              path: ${{ parameters.path }}