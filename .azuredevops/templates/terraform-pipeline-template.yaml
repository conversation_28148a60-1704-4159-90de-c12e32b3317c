parameters:
  - name: module
    type: string
  - name: environment
    type: string
  - name: path
    type: string
  - name: oidc_enabled
    type: boolean
    default: false

variables:
  - name: ARM_TENANT_ID
    value: 33e01921-4d64-4f8c-a055-5bdaffd5e33d
  - name: ARM_USE_AZUREAD
    value: true
  - name: ARM_USE_MSI
    value: true
  - name: ARM_STORAGE_USE_AZUREAD
    value: true

stages:
  # Plan Stage
  - stage: Plan
    displayName: "Plan - ${{ parameters.module }}"
    jobs:
      - job: Plan
        displayName: 'Run Plan for ${{ parameters.module }}'
        pool: 'iridium-builder'
        steps:
          - template: terraform-plan.yaml
            parameters:
              path: ${{ parameters.path }}
              oidc_enabled: ${{ parameters.oidc_enabled }}

  # Apply Stage
  - stage: Apply
    displayName: "Apply - ${{ parameters.module }}"
    dependsOn: Plan
    condition: and(succeeded(), eq(dependencies.Plan.outputs['Plan.plan.plan_has_changes'], 'true'), in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for ${{ parameters.module }}'
        pool: 'iridium-builder'
        environment: ${{ parameters.environment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - template: terraform-apply.yaml
                  parameters:
                    path: ${{ parameters.path }}
                    oidc_enabled: ${{ parameters.oidc_enabled }}
