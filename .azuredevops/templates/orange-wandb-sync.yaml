parameters:
  - name: users_artifact_name
    type: string
  - name: scripts_path
    type: string

steps:
  - script: |
      echo "Downloading and installing uv..."
      curl -Ls https://astral.sh/uv/install.sh | bash
      echo "Sourcing environment: $HOME/.local/bin/env"
      source "$HOME/.local/bin/env"
      echo "Verifying uv installation..."
      uv --version
      echo "Installation complete."
    displayName: "Install uv"

  - task: DownloadPipelineArtifact@2
    displayName: 'Download Published Users Artifact'
    inputs:
      artifactName: ${{ parameters.users_artifact_name }}
      targetPath: ${{ parameters.scripts_path }}

  - script: |
      source "$HOME/.local/bin/env"
      cd ${{ parameters.scripts_path }}
      ./sync_wandb_state.py --vault-url "$(AZURE_KEY_VAULT)" --user-path output.json --dry-run $(IS_DRY_RUN)
    displayName: "Run All WandB Stages"
