parameters:
  - name: module
    type: string
  - name: environment
    type: string
  - name: path
    type: string

variables:
  - name: ARM_TENANT_ID
    value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
  - name: ARM_USE_AZUREAD
    value: true
  - name: ARM_USE_MSI
    value: true
  - name: ARM_STORAGE_USE_AZUREAD
    value: true

stages:
  # Plan Stage
  - stage: Plan
    displayName: "Plan - ${{ parameters.module }}"
    jobs:
      - job: Plan
        displayName: 'Run Plan for ${{ parameters.module }}'
        pool: 'orange-builder'
        steps:
          - template: orange-terraform-plan.yaml
            parameters:
              path: ${{ parameters.path }}

  #  Apply Stage
  - stage: Apply
    displayName: "Apply - ${{ parameters.module }}"
    dependsOn: Plan
    condition: and(succeeded(), eq(dependencies.Plan.outputs['Plan.plan.plan_has_changes'], 'true'), in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for ${{ parameters.module }}'
        pool: 'orange-builder'
        environment: ${{ parameters.environment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - template: orange-terraform-apply.yaml
                  parameters:
                    path: ${{ parameters.path }}
