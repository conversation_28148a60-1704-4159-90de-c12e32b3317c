pool: 'iridium-builder'

trigger:
  branches:
    include:
      - main
      - tremo/brix-git-alternate-build  # Enable testing
  paths:
    include:
    - images/git-alternates/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
    - images/git-alternates/*

steps:
  - template: templates/az-setup.yaml
  
  - script: |
      docker run --privileged --rm mcr.microsoft.com/mirror/docker/tonistiigi/binfmt:qemu-v9.2.2-52 --install all
    displayName: 'Setup qemu'  
      
  # Checkout self (bootstrapping repo with our scripts)
  - checkout: self
  
  # Shallow checkout to ensure System.AccessToken is available (pattern used from other pipelines)
  - checkout: git://Mimco/_git/torchflow-mirror@microsoft/main-research
    fetchDepth: 1
    fetchTags: false
  
  - script: |
      bash build-git-alternates.sh
    displayName: 'Build and push git alternates image'
    workingDirectory: bootstrapping/images/git-alternates
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)