trigger:
  # Batch builds of the main branch to serlize the execution of the pipelines
  batch: true
  branches:
    include:
      - main
  paths:
    include:
      - terraform/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
      # Only trigger on cluster-related paths
      - terraform/clusters/**
      - terraform/clusters-green-ad-ops/**
      - terraform/orange-clusters-config/**
      # Include all modules - any module change should trigger validation
      - terraform/modules/**

extends:
  template: templates/terraform-pipeline-multi-module-template.yaml
  parameters:
    modules:
      - name: prod-uksouth-7_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-7/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-uksouth-7_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-7/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-uksouth-7_conn

      - name: prod-uksouth-8_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-8/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-uksouth-8_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-8/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-uksouth-8_conn

      - name: prod-uksouth-9_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-9/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-uksouth-9_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-9/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-uksouth-9_conn

      - name: prod-uksouth-15_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-15/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-uksouth-15_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-uksouth-15/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-uksouth-15_conn

      - name: prod-southcentralus-hpe-2_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-2/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-southcentralus-hpe-2_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-2/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-southcentralus-hpe-2_conn

      - name: prod-southcentralus-hpe-5_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-5/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-southcentralus-hpe-5_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-5/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-southcentralus-hpe-5_conn

      - name: prod-southcentralus-hpe-3_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-3/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-southcentralus-hpe-3_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-3/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-southcentralus-hpe-3_conn

      - name: prod-southcentralus-hpe-4_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-4/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-southcentralus-hpe-4_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-southcentralus-hpe-4/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-southcentralus-hpe-4_conn

      - name: prod-westus2-19_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-westus2-19/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-westus2-19_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-westus2-19/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-westus2-19_conn

      - name: prod-eastus2-30_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-eastus2-30/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: prod-eastus2-30_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/prod-eastus2-30/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - prod-eastus2-30_conn

      - name: stage-southcentralus-2_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/stage-southcentralus-2/deploy-stage-conn
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
      - name: stage-southcentralus-2_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/stage-southcentralus-2/deploy-stage-apps
        environment: 'iridium-user-onboarding'
        oidc_enabled: true
        dependsOn:
          - stage-southcentralus-2_conn