pool: 'iridium-builder'

trigger:
  branches:
    include:
      - main
      - bolian/orangebox  # Enable testing
  paths:
    include:
    - images/kube-orange-box/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
    - images/kube-orange-box/*

stages:
  - stage: GetOaiartifactWheels
    displayName: 'Fetch oaiartifact wheels'
    jobs:
      - job: Build
        displayName: 'Fetch oaiartifact wheels'
        pool: 'orange-builder'
        steps:
          - template: templates/orange-stage-setup.yaml
          - script: |
              az account set --subscription "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e"
            displayName: "Az account show"
          - script: |
              cd $BUILD_ARTIFACTSTAGINGDIRECTORY
              # az storage blob download-batch --pattern "*.whl" --destination $BUILD_ARTIFACTSTAGINGDIRECTORY --account-name orngoaiartifacts --source wheels --auth-mode login
              az storage blob download-batch --pattern "*cp312*linux_x86_64.whl" --destination $BUILD_ARTIFACTSTAGINGDIRECTORY --account-name orngoaiartifacts --source wheels --auth-mode login
              az storage blob download-batch --pattern "*none-any.whl" --destination $BUILD_ARTIFACTSTAGINGDIRECTORY --account-name orngoaiartifacts --source wheels --auth-mode login
              az storage blob download-batch --pattern "*py3*x86_64.whl" --destination $BUILD_ARTIFACTSTAGINGDIRECTORY --account-name orngoaiartifacts --source wheels --auth-mode login
              az storage blob download   --container-name wheels   --name public-wheels/connexion2/connexion2-2020.0.dev1-py2.py3-none-any.whl   --file connexion2-2020.0.dev1-py2.py3-none-any.whl   --account-name orngoaiartifacts --auth-mode login 
            displayName: "Download orngoaiartifacts/wheels"
          - task: PublishBuildArtifacts@1
            displayName: "Wheels as Artifact"
            inputs:
              pathToPublish: "$(Build.ArtifactStagingDirectory)"
              artifactName: "wheels"

  - stage: PushToIridiumsdc
    jobs:
      - job: Push
        displayName: 'Build and Push Orange Docker image to iridiumsdc'
        pool: 'iridium-builder'
        steps:
          - template: templates/az-setup.yaml
          - task: DownloadBuildArtifacts@0
            displayName: "Download Artifact"
            inputs:
              artifactName: "wheels"
              downloadPath: "$(System.DefaultWorkingDirectory)"

          # Checkout self (bootstrapping repo with our scripts)
          - checkout: self
            path: bootstrapping

          - checkout: git://Mimco/_git/torchflow-mirror@orange/main
            fetchDepth: 1
            fetchTags: false
            # path: bootstrapping/images/kube-orange-box/code/openai
            
          - checkout: git://Mimco/_git/brix@refs/tags/v0.40.0
            fetchDepth: 1
            fetchTags: true
            path: bootstrapping/images/kube-orange-box/code/brix

          - checkout: git://Mimco/_git/glass@main
            fetchDepth: 1
            fetchTags: false
            # path: bootstrapping/images/kube-orange-box/code/glass

          - script: |
              set -x

              git clone --single-branch --branch orange/main --no-tags https://${SYSTEM_ACCESSTOKEN}@dev.azure.com/project-argos/Mimco/_git/torchflow-mirror $(Agent.BuildDirectory)/bootstrapping/images/kube-orange-box/code/openai
              git -C $(Agent.BuildDirectory)/bootstrapping/images/kube-orange-box/code/openai remote set-url origin  https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror
              git clone --single-branch --branch main --no-tags https://${SYSTEM_ACCESSTOKEN}@dev.azure.com/project-argos/Mimco/_git/glass $(Agent.BuildDirectory)/bootstrapping/images/kube-orange-box/code/glass
              git -C $(Agent.BuildDirectory)/bootstrapping/images/kube-orange-box/code/glass remote set-url origin  https://dev.azure.com/project-argos/Mimco/_git/glass

              cp -r $(System.DefaultWorkingDirectory)/wheels/* wheel/
              mkdir -p cache
              python3 copy_wheels.py wheel/ cache
              az acr login --name iridiumsdc
              DOCKER_BUILDKIT=1 docker build -t iridiumsdc.azurecr.io/kube-orange-box:$(Build.BuildId) .
              docker tag iridiumsdc.azurecr.io/kube-orange-box:$(Build.BuildId) iridiumsdc.azurecr.io/kube-orange-box:latest
              docker push iridiumsdc.azurecr.io/kube-orange-box:$(Build.BuildId)
              docker push iridiumsdc.azurecr.io/kube-orange-box:latest
            displayName: 'Build and push image'
            workingDirectory: $(Agent.BuildDirectory)/bootstrapping/images/kube-orange-box
            env:
              SYSTEM_ACCESSTOKEN: $(System.AccessToken)           