
pool: 'iridium-builder'

trigger:
  branches:
    include:
      - main
  paths:
    include:
    - admission/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
    - admission/*

steps:
  - task: CargoAuthenticate@0
    inputs:
      configFile: admission/.cargo/config.toml

  - script: |
      docker build -t iridiumsdc.azurecr.io/iridmission:$(Build.BuildId) . --build-arg CARGO_TOKEN="${CARGO_REGISTRIES_MIMCO_PUBLICPACKAGES_TOKEN}"
    displayName: 'Build'
    workingDirectory: 'admission'

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - template: templates/az-setup.yaml

    - script: |
        az acr login --name iridiumsdc
        docker push iridiumsdc.azurecr.io/iridmission:$(Build.BuildId)
      displayName: 'Push'
      workingDirectory: 'admission'
