parameters:
  - name: build_trellis_app
    type: boolean
    default: false
  - name: build_trellis_server
    type: boolean
    default: false

pool: 'iridium-builder'

trigger: none
stages:
  - stage: FetchOAIArtifactWheels
    displayName: 'Fetch oaiartifact wheels'
    condition: eq('${{ parameters.build_trellis_server }}', 'true')
    jobs:
      - job: FetchOAIArtifactWheels
        displayName: 'Fetch oaiartifact wheels'
        pool: 'orange-builder'
        steps:
          - template: templates/orange-stage-setup.yaml
          - script: |
              az account set --subscription "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e"
            displayName: "Set subscription"
          - script: |
              set -eufx -o pipefail
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels
              az storage blob download-batch --pattern "*.whl" --destination $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels --account-name orngoaiartifacts --source wheels --auth-mode login
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels/nccl/Et8dHKle
              az storage blob download --name nccl/Et8dHKle/libnccl-private_2.18.3-openai+9d4b02d+cuda12.8_amd64.deb --file $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels/nccl/Et8dHKle/libnccl-private_2.18.3-openai+9d4b02d+cuda12.8_amd64.deb --account-name orngoaiartifacts --container-name wheels --auth-mode login
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels/cache_getenv/2025-04-04-07-00-33
              az storage blob download --name cache_getenv/2025-04-04-07-00-33/cache_getenv_x86_64.so --file $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels/cache_getenv/2025-04-04-07-00-33/cache_getenv_x86_64.so --account-name orngoaiartifacts --container-name wheels --auth-mode login
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/storage-map
              az storage blob download --name storage-map.json --file $BUILD_ARTIFACTSTAGINGDIRECTORY/storage-map/storage-map.json --account-name orngoaiartifacts --container-name storage-map --auth-mode login
            displayName: "Download orngoaiartifacts from Azure Storage"
          - task: PublishBuildArtifacts@1
            displayName: "Publish orngoaiartifacts as build artifact"
            inputs:
              pathToPublish: "$(Build.ArtifactStagingDirectory)"
              artifactName: "orngoaiartifacts"
 
  - stage: "PushToIridiumsdc"
    displayName: 'Build and Push Trellis App & Server Docker images to iridiumsdc'
    condition: or(eq('${{ parameters.build_trellis_app }}', 'true'), eq('${{ parameters.build_trellis_server }}', 'true'))
    jobs:
      - job: BuildAndPushImages
        displayName: 'Build and push Trellis App & Server Docker images'
        pool: 'iridium-builder'
        steps:
          - template: templates/az-setup.yaml
          - task: DownloadBuildArtifacts@0
            displayName: "Download Artifact"
            condition: eq('${{ parameters.build_trellis_server }}', 'true')
            inputs:
              artifactName: "orngoaiartifacts"
              # The artifact is downloaded to a subdirectory "orngoaiartifacts" in the target directory.
              downloadPath: "$(System.DefaultWorkingDirectory)"

          # We are using the bootstrapping branch to avoid triggering Supply Chain Analysis failures.
          - checkout: git://Mimco/_git/bootstrapping@main
            fetchDepth: 1
            fetchTags: false

          - checkout: git://Mimco/_git/torchflow-mirror@trellis/dev/20250618
            fetchDepth: 1
            fetchTags: false

          - script: |
              set -eufx -o pipefail
              cp -r ./torchflow-mirror ./bootstrapping/images/trellis/server/openai
              cp -r /mnt/vss/_work/1/s/orngoaiartifacts ./bootstrapping/images/trellis/server/openai/orngoaiartifacts
            displayName: "Copy files to docker build context"
            condition: eq('${{ parameters.build_trellis_server }}', 'true')

          - script: |
              set -eufx -o pipefail
              export MONOREPO_DIR=$(realpath .)
              # Install pyenv and Python 3.11.8 which is required by the OpenAI codebase.
              curl https://pyenv.run | bash
              export PYENV_ROOT="$HOME/.pyenv"
              export PATH="$PYENV_ROOT/bin:$PATH"
              eval "$(pyenv init --path)"
              eval "$(pyenv init -)"
              eval "$(pyenv virtualenv-init -)"

              sudo apt-get update
              sudo apt-get install -y make build-essential libssl-dev zlib1g-dev \
                libbz2-dev libreadline-dev libsqlite3-dev wget curl llvm \
                libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev

              pyenv install 3.11.8
              pyenv global 3.11.8

              python --version

              # Install oaipkg
              echo "Try to install oaipkg"
              python install.py
              echo "Installed oaipkg"
              # Update .dockerignore.
              # Hack to remove all missing dependencies
              sed -i '/"duo",/d' project/trellis_server/trellis_server/scripts/lock_docker_dependencies.py
              sed -i 's| duo | |g' project/trellis_server/Dockerfile
              sed -i '/agent_monitoring/d' project/caas_tasks/caas_adversarial/pyproject.toml
              # Generating project/trellis_server/Dockerfile.dockerignore
              python project/trellis_server/trellis_server/scripts/lock_docker_dependencies.py --project trellis_server

              # Hack the torchflow/run-torchflow-setup.py to avoid bbb command
              sed -i '/submit(download_so)/d' torchflow/run-torchflow-setup.py
              sed -i '/submit(download_storage_map)/d' torchflow/run-torchflow-setup.py
              sed -i 's|run("bbb cp https://oaiartifacts[^"]*")||g' torchflow/run-torchflow-setup.py
              sed -i 's|get_deb_path(artifact_paths=artifact_paths, cc=cc)|"/tmp/libnccl-private_2.18.3-openai+9d4b02d+cuda12.8_amd64.deb"|g' torchflow/run-torchflow-setup.py
              cat torchflow/run-torchflow-setup.py
            displayName: "Generate Dockerfile.dockerignore"
            condition: eq('${{ parameters.build_trellis_server }}', 'true')
            workingDirectory: ./bootstrapping/images/trellis/server/openai

          - script: |
              set -eufx -o pipefail
              export MONOREPO_DIR=$(realpath .)
              # Override the opanai Dockerfile with ours
              cp ../Dockerfile project/trellis_server/Dockerfile
              # Move the .dockerignore to the root of the build context
              mv project/trellis_server/Dockerfile.dockerignore .
              # Copy the custom wheels copy script to the build context
              cp ../../../lemon/copy_wheels.py .
              az acr login --name iridiumsdc
              export DOCKER_IMAGE=iridiumsdc.azurecr.io/trellis/trellis_server:$(Build.BuildId)
              export DOCKER_BUILDKIT=1
              docker buildx build "$MONOREPO_DIR" -f "$MONOREPO_DIR/project/trellis_server/Dockerfile" -t iridiumsdc.azurecr.io/trellis/trellis_server:$(Build.BuildId)
              docker push $DOCKER_IMAGE
            displayName: 'Build and push trellis server image'
            condition: eq('${{ parameters.build_trellis_server }}', 'true')
            workingDirectory: ./bootstrapping/images/trellis/server/openai

          - script: |
              set -eufx -o pipefail
              export MONOREPO_DIR=$(System.DefaultWorkingDirectory)/torchflow-mirror
              export GIT_COMMIT=$(git rev-parse HEAD)
              export GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
              if git diff-index --quiet HEAD --; then
                export GIT_CLEAN=true
              else
                export GIT_CLEAN=false
              fi

              # Hack the Dockerfile to be able to build in orange
              sed -i 's|node:22.14-bookworm-slim@sha256:bac8ff0b5302b06924a5e288fb4ceecef9c8bb0bb92515985d2efdc3a2447052|mcr.microsoft.com/mirror/docker/library/node:20@sha256:ba077fe891ce516b24bdbbd66d27d1e8e8c5a6e6b31ec7e7e559b45c3fca0643|g' $MONOREPO_DIR/project/trellis_app/Dockerfile

              export DOCKER_IMAGE=iridiumsdc.azurecr.io/trellis/trellis_app:$(Build.BuildId)
              export DOCKER_BUILDKIT=1
              docker buildx build \
                "$MONOREPO_DIR" \
                -f $(System.DefaultWorkingDirectory)/torchflow-mirror/project/trellis_app/Dockerfile \
                -t $DOCKER_IMAGE \
                --platform=linux/amd64 \
                --build-arg GIT_COMMIT=$GIT_COMMIT \
                --build-arg GIT_CLEAN=$GIT_CLEAN \
                --build-arg GIT_BRANCH=$GIT_BRANCH \
                --progress=plain
              az acr login --name iridiumsdc
              docker push $DOCKER_IMAGE
            displayName: 'Build and Push App Docker Image'
            condition: eq('${{ parameters.build_trellis_app }}', 'true')
            workingDirectory: $(System.DefaultWorkingDirectory)/torchflow-mirror/project/trellis_app/trellis_app/scripts

