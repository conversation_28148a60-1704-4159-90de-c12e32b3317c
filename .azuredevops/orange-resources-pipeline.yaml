trigger:
  # Batch builds of the main branch to serlize the execution of the pipelines
  batch: true
  branches:
    include:
      - main
  paths:
    include:
      - terraform/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
      # Only trigger on shared resources paths
      - terraform/orange-global-onboarding/**
      - terraform/orange-storage-bootstrapping/**
      - terraform/orange-observability/**
      - terraform/orange-infra-apps/**
      - terraform/orange-trellis-global/**
      - terraform/orange-robot-users/**
      - terraform/orange-storage-config/**
      - terraform/orange-builder/**
      - terraform/monitoring/**
      - terraform/clusters-green-ad-ops/**
      - terraform/orange-caas-dependencies/**
      # Include all modules - any module change should trigger validation
      - terraform/modules/**

extends:
  template: templates/orange-terraform-pipeline-multi-module-template.yaml
  parameters:
    modules:
      - name: orange-global-onboarding
        path: $(System.DefaultWorkingDirectory)/terraform/orange-global-onboarding
        environment: orange-user-onboarding
      - name: orange-storage-bootstrapping
        path: $(System.DefaultWorkingDirectory)/terraform/orange-storage-bootstrapping
        environment: orange-user-onboarding
      - name: orange-observability
        path: $(System.DefaultWorkingDirectory)/terraform/orange-observability
        environment: orange-user-onboarding
      - name: orange-infra-apps
        path: $(System.DefaultWorkingDirectory)/terraform/orange-infra-apps
        environment: orange-user-onboarding
      - name: orange-trellis-global
        path: $(System.DefaultWorkingDirectory)/terraform/orange-trellis-global
        environment: orange-user-onboarding
      - name: orange-cluster-ad-ops
        path: $(System.DefaultWorkingDirectory)/terraform/clusters-green-ad-ops/global
        environment: orange-user-onboarding
      - name: orange-caas-dependencies
        path: $(System.DefaultWorkingDirectory)/terraform/orange-caas-dependencies
        environment: orange-user-onboarding
