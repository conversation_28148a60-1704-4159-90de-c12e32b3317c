trigger:
  # Batch builds of the main branch to serlize the execution of the pipelines
  batch: true
  branches:
    include:
      - main
  paths:
    include:
      - terraform/*
pr:
  branches:
    include:
      - "*"
  paths:
    include:
      # Only trigger on user/team onboarding related paths
      - terraform/orange-user-onboarding/**
      - terraform/orange-team-onboarding/**
      - terraform/orange-users-membership/**
      - terraform/orange-private-endpoint-approval/**
      - terraform/orange-users-vnet/**
      - terraform/orange-user-cluster-onboarding/**
      - terraform/orange-user-cluster-onboarding-builder/**
      - terraform/orange-robot-users/**
      # Only include modules actually used by onboarding pipeline
      - terraform/modules/1es-build-pool/**
      - terraform/modules/aoai-secmon-audit/**
      - terraform/modules/cluster-onboarding/**
      - terraform/modules/global_settings/**
      - terraform/modules/moonfire-admin-rbac/**
      - terraform/modules/orange-builder-access/**
      - terraform/modules/orange-observability-config/**
      - terraform/modules/orange-quota-cluster-config/**
      - terraform/modules/orange-cluster-guest-quota/**
      - terraform/modules/orange-remote-state/**
      - terraform/modules/orange-robot-user/**
      - terraform/modules/orange-storage-config/**
      - terraform/modules/orange-storage-map/**
      - terraform/modules/orange-team-observability-azure/**
      - terraform/modules/orange-user-app/**
      - terraform/modules/orange-user-settings/**
      - terraform/modules/orange-user-settings-aad/**
      - terraform/modules/orange-user-storage/**
      - terraform/modules/orange-user-team-quota/**
      - terraform/modules/snowflake/**
      - terraform/modules/tailscale-subnet-router/**
      - terraform/modules/users-brix-cluster-access/**

variables:
  - template: templates/terraform-variables.yaml
    parameters:
      tenant: green

stages:
  # Plan Stage
  - stage: PlanOrangeTeamResources
    displayName: "Plan Orange team resources (Green)"
    jobs:
      - job: Plan
        displayName: 'Run Plan for orange-team-onboarding'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
          # This stage is refreshes the state for hundereds of resources,
          # since it has to go through the per user resources
          # We started getting throttling issues, so we are limiting the parallelism
          # │ {
          # │   "error": {
          # │     "code": "TooManyRequests",
          # │     "message": "The request is being throttled as the limit has been reached for operation type - Read_ObservationWindow_00:05:00. For more information, see - https://aka.ms/srpthrottlinglimits"
          # │   }
          # │ }
          # Default is 10, reducing to 5
          - name: TFE_PARALLELISM
            value: 5
        steps:
          - template: templates/orange-terraform-plan.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-team-onboarding
              artifact_name: 'orange-team-onboarding-plan'

  #  Apply Stage
  - stage: ApplyTeamOnboarding
    displayName: "Apply Orange team resources (Green)"
    dependsOn: PlanOrangeTeamResources
    condition: and(
      not(failed()), 
      not(canceled()),
      eq(dependencies.PlanOrangeTeamResources.outputs['Plan.plan.plan_has_changes'], 'true'),
      in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
      in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for orange-team-onboarding'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        environment: orange-user-onboarding
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/orange-terraform-apply.yaml
                  parameters:
                    path: $(System.DefaultWorkingDirectory)/terraform/orange-team-onboarding
                    artifact_name: 'orange-team-onboarding-plan'

  # Plan Stage
  - stage: PlanOrangeUserMembership
    displayName: "Plan Orange user membership to new security groups (Green)"
    ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), in(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      dependsOn: ApplyTeamOnboarding
      condition: and(
        not(failed()), 
        not(canceled()),
        in(dependencies.ApplyTeamOnboarding.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'))
    ${{ else }}:
      dependsOn: []
      condition: and(
        not(failed()), 
        not(canceled()))
    jobs:
      - job: Plan
        displayName: 'Run Plan for orange-users-membership'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
          # This stage is refreshes the state for hundereds of resources,
          # since it has to go through the per user resources
          # We started getting throttling issues, so we are limiting the parallelism
          # │ {
          # │   "error": {
          # │     "code": "TooManyRequests",
          # │     "message": "The request is being throttled as the limit has been reached for operation type - Read_ObservationWindow_00:05:00. For more information, see - https://aka.ms/srpthrottlinglimits"
          # │   }
          # │ }
          # Default is 10, reducing to 5
          - name: TFE_PARALLELISM
            value: 5
        steps:
          - template: templates/orange-terraform-plan.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-users-membership
              artifact_name: 'orange-users-membership-plan'

  #  Apply Stage
  - stage: ApplyOrangeUserMembership
    displayName: "Apply Orange user membership to new security groups (Green)"
    dependsOn: PlanOrangeUserMembership
    condition: and(
      not(failed()), 
      not(canceled()),
      eq(dependencies.PlanOrangeUserMembership.outputs['Plan.plan.plan_has_changes'], 'true'),
      in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
      in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for orange-users-membership'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        environment: orange-user-onboarding
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/orange-terraform-apply.yaml
                  parameters:
                    path: $(System.DefaultWorkingDirectory)/terraform/orange-users-membership
                    artifact_name: 'orange-users-membership-plan'

  # Plan Stage
  - stage: PlanOrangeUserResources
    displayName: "Plan Orange user resources (Green)"
    ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), in(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      dependsOn: ApplyOrangeUserMembership
      condition: and(
        not(failed()), 
        not(canceled()),
        in(dependencies.ApplyOrangeUserMembership.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'))
    ${{ else }}:
      dependsOn: []
      condition: and(
        not(failed()), 
        not(canceled()))
    jobs:
      - job: Plan
        displayName: 'Run Plan for orange-user-onboarding'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        steps:
          - template: templates/orange-terraform-plan.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-user-onboarding
              artifact_name: 'orange-user-onboarding-plan'
              ${{ if and(in(variables['Build.Reason'], 'PullRequest'), not(in(variables['Build.SourceBranch'], 'refs/heads/main'))) }}:
                terraform_flags: '-refresh=false'

  #  Apply Stage
  - stage: ApplyUserOnboarding
    displayName: "Apply Orange user resources (Green)"
    dependsOn: PlanOrangeUserResources
    condition: and(
      not(failed()), 
      not(canceled()),
      eq(dependencies.PlanOrangeUserResources.outputs['Plan.plan.plan_has_changes'], 'true'),
      in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
      in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for orange-user-onboarding'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        environment: orange-user-onboarding
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/orange-terraform-apply.yaml
                  parameters:
                    path: $(System.DefaultWorkingDirectory)/terraform/orange-user-onboarding
                    artifact_name: 'orange-user-onboarding-plan'

  
  - stage: UploadUsersListArtifact
    displayName: "Upload Users List Artifact (Green)"
    ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), in(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      dependsOn: ApplyUserOnboarding
      condition: and(
        not(failed()), 
        not(canceled()),
        in(dependencies.ApplyUserOnboarding.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'))
    ${{ else }}:
      dependsOn: []
      condition: and(
        not(failed()), 
        not(canceled()))
    jobs:
      - job: Upload
        displayName: 'Output users list artifact'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        steps:
          - template: templates/terraform-publish-output.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-user-onboarding
              artifact_name: 'orange-users-artifact'
              
  # Plan Stage
  - stage: PlanOrangeUserVnet
    dependsOn: UploadUsersListArtifact
    condition: and(
      not(failed()), 
      not(canceled()),
      in(dependencies.UploadUsersListArtifact.result, 'Succeeded', 'SucceededWithIssues'))
    displayName: "Plan Orange user vnet (Green)"
    jobs:
      - job: Plan
        displayName: 'Run Plan for orange-users-vnet'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        steps:
          - template: templates/orange-terraform-plan.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-users-vnet
              artifact_name: 'orange-users-vnet-plan'
              download_artifact_name: 'orange-users-artifact'
              download_artifact_path : '$(System.DefaultWorkingDirectory)/terraform/orange-users-vnet'
              terraform_vars:
                - name: team-quota-file-location
                  value: output.json

  #  Apply Stage
  - stage: ApplyUserVnet
    displayName: "Apply Orange user vnet (Green)"
    dependsOn: PlanOrangeUserVnet
    condition: and(
      not(failed()), 
      not(canceled()),
      eq(dependencies.PlanOrangeUserVnet.outputs['Plan.plan.plan_has_changes'], 'true'),
      in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
      in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for orange-users-vnet'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        environment: orange-no-approval
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/orange-terraform-apply.yaml
                  parameters:
                    path: $(System.DefaultWorkingDirectory)/terraform/orange-users-vnet
                    artifact_name: 'orange-users-vnet-plan'
                    download_artifact_name: 'orange-users-artifact'
                    download_artifact_path : '$(System.DefaultWorkingDirectory)/terraform/orange-users-vnet'

  - stage: PlanOrangeClusterOnboarding
    dependsOn: UploadUsersListArtifact
    condition: and(
      not(failed()), 
      not(canceled()),
      in(dependencies.UploadUsersListArtifact.result, 'Succeeded', 'SucceededWithIssues'))
    displayName: "Plan Cluster onboarding (AME)"
    jobs:
      - job: Plan
        displayName: 'Run Plan for orange-user-cluster-onboarding'
        pool: 'iridium-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 33e01921-4d64-4f8c-a055-5bdaffd5e33d
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        steps:
          - template: templates/terraform-plan.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-user-cluster-onboarding
              artifact_name: 'orange-user-cluster-onboarding-plan'
              oidc_enabled: true
              download_artifact_name: 'orange-users-artifact'
              download_artifact_path : '$(System.DefaultWorkingDirectory)/terraform/orange-user-cluster-onboarding'
              terraform_vars:
                - name: team-quota-file-location
                  value: output.json

  #  Apply Stage
  - stage: ApplyClusterOnboarding
    displayName: "Apply Orange cluster onboarding (AME)"
    dependsOn: PlanOrangeClusterOnboarding
    condition: and(
      not(failed()), 
      not(canceled()),
      eq(dependencies.PlanOrangeClusterOnboarding.outputs['Plan.plan.plan_has_changes'], 'true'),
      in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
      in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for orange-user-cluster-onboarding'
        pool: 'iridium-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 33e01921-4d64-4f8c-a055-5bdaffd5e33d
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        environment: orange-no-approval
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/terraform-apply.yaml
                  parameters:
                    path: $(System.DefaultWorkingDirectory)/terraform/orange-user-cluster-onboarding
                    artifact_name: 'orange-user-cluster-onboarding-plan'
                    oidc_enabled: true
                    download_artifact_name: 'orange-users-artifact'
                    download_artifact_path : '$(System.DefaultWorkingDirectory)/terraform/orange-user-cluster-onboarding'

  # Plan Stage
  - stage: PlanPrivateEndpointsApproval
    displayName: "Plan Private Endpoints Approval (Green)"
    dependsOn: ApplyClusterOnboarding
    condition: and(
      not(failed()), 
      not(canceled()),
      in(dependencies.ApplyClusterOnboarding.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'))
    jobs:
      - job: Plan
        displayName: 'Run Plan for orange-private-endpoint-approval'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        steps:
          - template: templates/orange-terraform-plan.yaml
            parameters:
              path: $(System.DefaultWorkingDirectory)/terraform/orange-private-endpoint-approval
              artifact_name: 'orange-private-endpoint-approval-plan'
              download_artifact_name: 'orange-users-artifact'
              download_artifact_path : '$(System.DefaultWorkingDirectory)/terraform/orange-private-endpoint-approval'
              terraform_vars:
                - name: team-quota-file-location
                  value: output.json
  #  Apply Stage
  - stage: ApplyPrivateEndpointsApproval
    displayName: "Apply Private Endpoints Approval (Green)"
    dependsOn: PlanPrivateEndpointsApproval
    condition: and(
      not(failed()), 
      not(canceled()),
      eq(dependencies.PlanPrivateEndpointsApproval.outputs['Plan.plan.plan_has_changes'], 'true'),
      in(variables['Build.Reason'], 'Manual','Schedule','IndividualCI','BatchedCI'),
      in(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: Apply
        displayName: 'Run Apply for orange-private-endpoint-approval'
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
        environment: orange-no-approval
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/orange-terraform-apply.yaml
                  parameters:
                    path: $(System.DefaultWorkingDirectory)/terraform/orange-private-endpoint-approval
                    artifact_name: 'orange-private-endpoint-approval-plan'
                    download_artifact_name: 'orange-users-artifact'
                    download_artifact_path : '$(System.DefaultWorkingDirectory)/terraform/orange-private-endpoint-approval'

  - stage: WandBManagement
    displayName: "Manage WandB State"
    dependsOn: UploadUsersListArtifact
    condition: and(
      not(failed()),
      not(canceled()),
      in(dependencies.UploadUsersListArtifact.result, 'Succeeded', 'SucceededWithIssues'))
    jobs:
      - job: UpdateWandB
        displayName: "Update Wandb"
        pool: 'orange-builder'
        variables:
          - name: ARM_TENANT_ID
            value: 8b9ebe14-d942-49e7-ace9-14496d0caff0
          - name: ARM_USE_AZUREAD
            value: true
          - name: ARM_USE_MSI
            value: true
          - name: ARM_STORAGE_USE_AZUREAD
            value: true
          - name: AZURE_KEY_VAULT
            value: "https://orange-onboarding-app-kv.vault.azure.net"
          - name: IS_DRY_RUN
            value: ${{ or(ne(variables['Build.SourceBranch'], 'refs/heads/main'), notIn(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI')) }}
        steps:
          - template: templates/orange-wandb-sync.yaml
            parameters:
              users_artifact_name: orange-users-artifact
              scripts_path: $(System.DefaultWorkingDirectory)/scripts/wandb
