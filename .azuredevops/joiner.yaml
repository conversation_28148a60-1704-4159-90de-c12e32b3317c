
pool: 'iridium-builder'

# This is not triggered by any change in this repo, we build the joiner image on demand
# Make sure you update corresponding terraform version.
trigger: none

steps:
  # This pipeline is playing a few tricks to get the right version of torchflow-mirror:
  # 1. We are not cloning the correct branch since it triggers Supply Chain failures
  # 2. We however are cloning the research branch, in a shallow manner, only such that azure devops ensure we have the correct SYSTEM_ACCESSTOKEN set.
  # 3. We then use the SYSTEM_ACCESSTOKEN to clone the correct branch.
  # 4. We also patch the Dockerfile on the fly to pull from mcr mirror to get around the dockerhub rate limits.
  # 5. We manually pushed the cargo-chef image version required to iridiumsdc.azurecr.io to get around the dockerhub rate limits, and we do a similar patch to the Dockerfile to pull from the mirror.
  - checkout: git://Mimco/_git/torchflow-mirror@microsoft/main-research
    fetchDepth: 1
    fetchTags: false

  - script: |
      git clone --branch orange/lemon/main-may --single-branch --depth 1 --no-tags --recurse-submodules=no https://$(System.AccessToken)@dev.azure.com/project-argos/Mimco/_git/torchflow-mirror $(System.DefaultWorkingDirectory)/torchflow-mirror
    displayName: 'Checkout torchflow-mirror'
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)

  - template: templates/az-setup.yaml

  - script: |
      az acr login --name iridiumsdc
    displayName: 'Login to ACR'

  - script: |
      sed -i -e 's/debian:bookworm/mcr.microsoft.com\/mirror\/docker\/library\/debian:bookworm/g' project/data-infra/joiner_store_rust_v2/Dockerfile
      sed -i -e 's/lukemathwalker\/cargo-chef:latest-rust-1.76.0/iridiumsdc.azurecr.io\/mirror\/docker\/lukemathwalker\/cargo-chef:latest-rust-1.81.0/g' project/data-infra/joiner_store_rust_v2/Dockerfile
  
      docker build -f project/data-infra/joiner_store_rust_v2/Dockerfile -t iridiumsdc.azurecr.io/observability/joiner_store_rust_v2:$(Build.BuildId) .
    displayName: 'Build Joiner Store'
    workingDirectory: '$(System.DefaultWorkingDirectory)/torchflow-mirror'


  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - script: |
        docker push iridiumsdc.azurecr.io/observability/joiner_store_rust_v2:$(Build.BuildId)
      displayName: 'Push Joiner Store'

  - script: |
      sed -i -e 's/python:3.11/iridiumsdc.azurecr.io\/python:3.11/g' project/data-infra/joiner_worker/Dockerfile
  
      docker build -f project/data-infra/joiner_worker/Dockerfile -t iridiumsdc.azurecr.io/observability/joiner_worker:$(Build.BuildId) .
    displayName: 'Build Joiner Worker'
    workingDirectory: '$(System.DefaultWorkingDirectory)/torchflow-mirror'

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - script: |
        docker push iridiumsdc.azurecr.io/observability/joiner_worker:$(Build.BuildId)
      displayName: 'Push Joiner Worker'

  - script: |
      sed -i -e 's/python:3.11/iridiumsdc.azurecr.io\/python:3.11/g' project/data-infra/joiner_watch_experiments/Dockerfile
  
      docker build -f project/data-infra/joiner_watch_experiments/Dockerfile -t iridiumsdc.azurecr.io/observability/joiner_watch_experiments:$(Build.BuildId) .
    displayName: 'Build Joiner Watcher'
    workingDirectory: '$(System.DefaultWorkingDirectory)/torchflow-mirror'

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - script: |
        docker push iridiumsdc.azurecr.io/observability/joiner_watch_experiments:$(Build.BuildId)
      displayName: 'Push Joiner Watcher'
  
  - script: |  
      docker build -f Dockerfile -t iridiumsdc.azurecr.io/observability/joiner_dash:$(Build.BuildId) .
    displayName: 'Build Joiner Dashboard'
    workingDirectory: '$(System.DefaultWorkingDirectory)/torchflow-mirror/project/data-infra/joiner_dash/'

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - script: |
        docker push iridiumsdc.azurecr.io/observability/joiner_dash:$(Build.BuildId)
      displayName: 'Push Joiner Dashboard'
