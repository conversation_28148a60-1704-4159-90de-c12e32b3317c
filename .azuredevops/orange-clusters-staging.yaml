trigger:
  # Batch builds of the main branch to serlize the execution of the pipelines
  batch: true
  branches:
    include:
      - staging/main
  paths:
    include:
      - terraform/*

pr:
  branches:
    include:
      - staging/main
  paths:
    include:
      - terraform/*

extends:
  template: templates/terraform-pipeline-multi-module-template.yaml
  parameters:
    require_apply_approval: true            # Rather than environment-based infra team approval, allow any user to approve the Apply stage.
    apply_branch: 'refs/heads/staging/main' # Only Apply changes from the staging branch.
    modules:
      - name: stage-southcentralus-hpe-1_conn
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/stage-southcentralus-hpe-1/deploy-stage-conn
        environment: 'orange-staging-no-approval'
        oidc_enabled: true
      - name: stage-southcentralus-hpe-1_apps
        path: $(System.DefaultWorkingDirectory)/terraform/clusters/stage-southcentralus-hpe-1/deploy-stage-apps
        environment: 'orange-staging-no-approval'
        oidc_enabled: true
        dependsOn:
          - stage-southcentralus-hpe-1_conn
