pool: 'iridium-builder'

trigger: none

steps:
  - template: templates/az-setup.yaml
  - script: |
      az acr login --name iridiumsdc
      docker build -t iridiumsdc.azurecr.io/infra/redis-exporter:v1.67.0-alpine .
      docker tag iridiumsdc.azurecr.io/infra/redis-exporter:v1.67.0-alpine iridiumsdc.azurecr.io/infra/redis-exporter:latest
    displayName: 'Build Redis exporter image'
    workingDirectory: images/redis-exporter

  - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - script: |
        docker push iridiumsdc.azurecr.io/infra/redis-exporter:v1.67.0-alpine
        docker push iridiumsdc.azurecr.io/infra/redis-exporter:latest
      displayName: 'Push Redis exporter image'