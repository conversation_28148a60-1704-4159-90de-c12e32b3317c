# This pipeline can be manually run to `terraform force-unlock` the terraform state for a particular AME pipeline.
# Sometimes, the state lock can get orphaned if a plan or apply is interrupted.
# Before running this pipeline, double check that no other pipelines are actually running against the target state.
#
# This operation requires a 'lock id' (to make sure you are unlocking what you expect!)
# You can find it in the error message from the failed lock acquisition, e.g.:
# │ Error: Error acquiring the state lock
# │
# │ Error message: state blob is already locked
# │ Lock Info:
# │   ID:        472b77eb-afbb-9f98-d24b-ea2f1942c160
# │   Path:      tfstate/orange-user-cluster-onboarding
# │   Operation: OperationTypePlan
# │   Who:       cloudtest@755ac996c000000
# │   Version:   1.9.8
# │   Created:   2025-06-15 01:30:41.943747537 +0000 UTC

name: Force unlock terraform state (AME)

trigger: none

pool: "iridium-builder"

parameters:
  - name: path
    type: string
    displayName: Path to Terraform entrypoint
    values:
      - terraform/orange-user-cluster-onboarding
      - terraform/clusters/prod-southcentralus-hpe-2
      - terraform/clusters/prod-southcentralus-hpe-3
      - terraform/clusters/prod-southcentralus-hpe-4
      - terraform/clusters/prod-southcentralus-hpe-5
      - terraform/clusters/prod-uksouth-7
      - terraform/clusters/prod-uksouth-8
      - terraform/clusters/prod-uksouth-9
      - terraform/clusters/prod-uksouth-15
      - terraform/clusters/prod-westus2-19
      - terraform/clusters/stage-southcentralus-hpe-1

      # TODO: Add all other AME terraform paths
  - name: lock_id
    type: string
    displayName: Lock ID to force unlock, from the terraform error message

variables:
  - template: templates/terraform-variables.yaml
    parameters:
      tenant: ame

steps:
  - checkout: self

  # az login and terraform install
  - template: templates/stage-setup.yaml

  - script: |
      terraform init
    displayName: "Init Terraform"
    workingDirectory: $(System.DefaultWorkingDirectory)/${{ parameters.path }}

  - script: |
      terraform force-unlock -force "${{ parameters.lock_id }}"
    displayName: "Force unlock Terraform state"
    workingDirectory: $(System.DefaultWorkingDirectory)/${{ parameters.path }}
