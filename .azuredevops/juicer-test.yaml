pool: "orange-builder"

pr:
  branches:
    include:
      - main
  paths:
    include:
      - src/juicer/*
      - .azuredevops/juicer-test.yaml

variables:
  GO_VERSION: '1.24.0'

steps:
  - task: GoTool@0
    displayName: 'Install Go $(GO_VERSION)'
    inputs:
      version: '$(GO_VERSION)'

  - bash: |
      go version
      echo "GOPATH: $GOPATH"
      echo "GOROOT: $GOROOT"
    displayName: 'Verify Go Installation'

  - bash: |
      go mod download
      go mod tidy
    workingDirectory: src/juicer
    displayName: 'Setup Dependencies'

  - bash: |
      make test
    workingDirectory: src/juicer
    displayName: 'Run Tests'

  - bash: |
      go build -v ./...
    workingDirectory: src/juicer
    displayName: 'Verify Build'
