
pool: 'iridium-builder'

# This is not triggered by any change in this repo, we build the brix image on demand
# We follow same tag information from openai and match the commit hash. This is
# temporary until we mirror tags.
# Make sure you update corresponding terraform version.
trigger: none

parameters:
  - name: brix_version
    type: string
    default: 'master'

  - name: acr_name
    type: string
    default: 'iridiumsdc'

jobs:
- job: BuildBrixDockerImages
  timeoutInMinutes: 120 # slow qemu build, so we need more time
  displayName: 'Build Brix Docker Images'
  steps:
    - checkout: git://Mimco/_git/brix@${{ parameters.brix_version }}

    - script: |
        docker run --privileged --rm mcr.microsoft.com/mirror/docker/tonistiigi/binfmt:qemu-v9.2.2-52 --install all
      displayName: 'Setup qemu'
        
    - script: |
        cd brix
        sed -i -e 's/ubuntu:latest/mcr.microsoft.com\/mirror\/docker\/library\/ubuntu:24.04/g' docker/brix-base.Dockerfile
        sed -i -e 's/golang:1.24.0/mcr.microsoft.com\/oss\/go\/microsoft\/golang:1.24/g' docker/brix-binaries.Dockerfile
        sed -i -e 's/alpine:3.11.6/mcr.microsoft.com\/mirror\/docker\/library\/alpine:3.16/g' docker/brix-binaries.Dockerfile
        sed -i -e 's/alpine:3.11.6/mcr.microsoft.com\/mirror\/docker\/library\/alpine:3.16/g' docker/brix-git-alternates.Dockerfile
        sed -i -e 's/alpine:3.11.6/mcr.microsoft.com\/mirror\/docker\/library\/alpine:3.16/g' docker/brix-installer.Dockerfile
        sed -i -e 's/gcr.io\/distroless\/base-debian12:latest/mcr.microsoft.com\/mirror\/gcr\/distroless\/cc-debian11:latest/g' docker/brix-state-metrics.Dockerfile

        git config --global user.email "${Build.QueuedBy}@microsoft.com"
        git config --global user.name "${Build.QueuedBy}"
        # git tag "v0.16.4-$(git rev-parse --short HEAD)"
        make IMAGE_REGISTRY=${{ parameters.acr_name }}.azurecr.io ARCH=amd64 build
        make IMAGE_REGISTRY=${{ parameters.acr_name }}.azurecr.io ARCH=arm64 build
      displayName: 'Make build'

    - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      - template: templates/az-setup.yaml

      - script: |
          az acr login --name ${{ parameters.acr_name }}
          cd brix
          make IMAGE_REGISTRY=${{ parameters.acr_name }}.azurecr.io ARCH=amd64 push
          make IMAGE_REGISTRY=${{ parameters.acr_name }}.azurecr.io ARCH=arm64 push

          make IMAGE_REGISTRY=${{ parameters.acr_name }}.azurecr.io manifest-create manifest-push
        displayName: 'Make push'
