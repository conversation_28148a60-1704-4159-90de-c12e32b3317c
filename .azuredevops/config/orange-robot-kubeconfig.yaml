# This is a kubeconfig for a robot user to access prod-uksouth-9.
# It is used in the orange-robot-test.yaml pipeline.
# TODO: This was a shortcut to get started; maybe the laptop setup script should generate kubeconfig for robots
# based on orange-clusters.yaml (if given a --robot flag).
apiVersion: v1
kind: Config
preferences: {}
current-context: prod-uksouth-9-oid
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURiakNDQWxhZ0F3SUJBZ0lRUmdSQnFwT1pRVXFjdHBoMGZEYzIwekFOQmdrcWhraUc5dzBCQVFzRkFEQWUKTVJ3d0dnWURWUVFERXhOdmFXUmpMWEJ5YjJRdGRXdHpiM1YwYUMwNU1CNFhEVEkxTURNeU9EQXdNek15TWxvWApEVEkyTURNeU9EQXdORE15TWxvd0hqRWNNQm9HQTFVRUF4TVRiMmxrWXkxd2NtOWtMWFZyYzI5MWRHZ3RPVENDCkFTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTHVsRThSODFlclk0QjBGdCtodEp5VjQKdFdHSVNIOWJGNFRuek9qSDV6NFp2cnZQM1VzeFRGbUpLeWhVQXZCWlNXalp5QWFLZ1J6aHZYUWxUczI0V2RwRwpVTUJDU0JWbTl5cFdLSnpzeVpPTTFrZDNPekYyQzR4QlRobEh1b2k5bXhuTGNKQW0yUldIalRJUjAvOVlDYmV5Cmc0RDhLSU0zVytyeWgrZ2NQNU5qQ2lMekpSUzBjMGsyOTBIUTVUb3AreERXQ0lrSlcyWWJrWUVTRE1zcStiekgKakZvQWsxRkg5TjcvTnFJTU5rWDhzNDJpcWVHMzdRSnlmMnJ2aVlFWHJ4YW5YeDZVUDI2OWNpNEJ1UWV5WEVBaQpIODROMndVeS9wQ0lMNTdTWTJkSml1bi9IazB1b0NJaFVFamljTnozcjFCb20zcWVjQ2VSS3ErbUtka05qbWtDCkF3RUFBYU9CcHpDQnBEQU9CZ05WSFE4QkFmOEVCQU1DQWI0d0NRWURWUjBUQkFJd0FEQVRCZ05WSFNVRUREQUsKQmdnckJnRUZCUWNEQVRBeUJnTlZIUkVFS3pBcGdpZHZhV1JqTFc5cFpDNXdjbTlrTFhWcmMyOTFkR2d0T1M1dgpjbUZ1WjJVdWFXNTBaWEp1WVd3d0h3WURWUjBqQkJnd0ZvQVV4OXpvR0tQOEdid1B4a0d1UGpMdDA5Q3MveWN3CkhRWURWUjBPQkJZRUZNZmM2QmlqL0JtOEQ4WkJyajR5N2RQUXJQOG5NQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUIKQVFBSy9rZUp5dlc2cmRWUGVSRVlzQTZPS1FEaFBpVnpWNXozMlVWL050L2hmem9oUW5YVTdzaFdwZmdBVFVuWgpMSDJjTS9YRGZ3Mm5TenlZUEU3elAyNXV1c0N0NVg0MEhVeUVHd3h6TTJEazcxTmJSUnMrUzd2enhIQ3Z6a3dzCjQ3LzFHWFJFZk0zRHZEa0xvUXBWMUwyNXk3MWVnbEhpYjJqSzJ0cmRMQUlWbmxhNzA2aGFRQ0N6UFRuYThIMEMKUW5BOS9vM3ozK012ZGMxTmJseFdHK0NiS3J1ekJXdEJEL3lJK3VzR3BVaUVvWVhwdDljVG0ySk9xd1VHQzBqbgo2SmM4LzNCalVYaGRoVzRmc29rSFR6OVRmKzZCVmQwbzlOa1FBQ2NFcmUvYytld0c1dHRUZ3BiaU5RMEdXMFpjCnhTRUMvSjBiN2dVYnUrRGJ1ckdYZDByQgotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    server: https://oidc-oid.prod-uksouth-9.orange.internal
  name: prod-uksouth-9-oid
contexts:
- context:
    cluster: prod-uksouth-9-oid
    namespace: default
    user: prod-uksouth-9-oid
  name: prod-uksouth-9-oid
users:
- name: prod-uksouth-9-oid
  user:
    exec:
      apiVersion: client.authentication.k8s.io/v1beta1
      command: kubelogin
      args:
      - get-token
      - -l=azurecli
      # oidc proxy app
      - --server-id=9d262de2-f0d9-4d54-8f96-9c79e49280c4
      env: null
      interactiveMode: Never
      provideClusterInfo: false