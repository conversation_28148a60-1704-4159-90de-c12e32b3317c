pool: 'iridium-builder'

trigger: none

pr:
  branches:
    include:
      - "*"
  paths:
    include:
      - .azuredevops/horse.yaml

jobs:
- job: horse_bridle
  displayName: 'Build and Push Horse Bridle'
  pool: 'iridium-builder'
  steps:
    - checkout: git://Mimco/_git/torchflow-mirror@orange/main
      fetchDepth: 1
      fetchTags: false

    - script: |
        git config --global user.email "${Build.QueuedBy}@microsoft.com"
        git config --global user.name "${Build.QueuedBy}"
        git tag "horse_bridle-$(git rev-parse --short HEAD)"
        sed -i -e 's/openai/iridiumsdc/g' justfile
        just build
      displayName: 'Build Horse Bridle'
      workingDirectory: project/runtime/projects/horse/horse_bridle

    - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      - template: templates/az-setup.yaml
      - script: |
          az acr login --name iridiumsdc
          just push
        displayName: 'Push Horse Bridle'
        workingDirectory: project/runtime/projects/horse/horse_bridle

- job: horse_request_proxy
  displayName: 'Build and Push Horse Request Proxy'
  pool: 'iridium-builder'
  steps:
    - checkout: git://Mimco/_git/torchflow-mirror@orange/main
      fetchDepth: 1
      fetchTags: false

    - script: |
        git config --global user.email "${Build.QueuedBy}@microsoft.com"
        git config --global user.name "${Build.QueuedBy}"
        git tag "horse_request_proxy-$(git rev-parse --short HEAD)"
        sed -i -e 's/openai/iridiumsdc/g' justfile
        just build
      displayName: 'Build Horse Request Proxy'
      workingDirectory: project/runtime/projects/horse/horse_request_proxy

    - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      - template: templates/az-setup.yaml
      - script: |
          az acr login --name iridiumsdc
          just push
        displayName: 'Push Horse Request Proxy'
        workingDirectory: project/runtime/projects/horse/horse_request_proxy

- job: horse_dashboard
  displayName: 'Build and Push Horse Dashboard'
  pool: 'iridium-builder'
  steps:
    - checkout: git://Mimco/_git/torchflow-mirror@orange/main
      fetchDepth: 1
      fetchTags: false

    - script: |
        git config --global user.email "${Build.QueuedBy}@microsoft.com"
        git config --global user.name "${Build.QueuedBy}"
        git tag "horse_dashboard-$(git rev-parse --short HEAD)"
        sed -i -e 's/openai/iridiumsdc/g' justfile
        just build
      displayName: 'Build Horse Dashboard'
      workingDirectory: project/runtime/projects/horse/horse_dashboard

    - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      - template: templates/az-setup.yaml
      - script: |
          az acr login --name iridiumsdc
          just push
        displayName: 'Push Horse Dashboard'
        workingDirectory: project/runtime/projects/horse/horse_dashboard

- job: horse_frontend
  displayName: 'Build and Push Horse Frontend'
  pool: 'iridium-builder'
  steps:
    - checkout: git://Mimco/_git/torchflow-mirror@orange/main
      fetchDepth: 1
      fetchTags: false

    - script: |
        git config --global user.email "${Build.QueuedBy}@microsoft.com"
        git config --global user.name "${Build.QueuedBy}"
        git tag "horse_frontend-$(git rev-parse --short HEAD)"
        sed -i -e 's/openai/iridiumsdc/g' justfile
        just build-fe
      displayName: 'Build Horse Frontend'
      workingDirectory: project/runtime/projects/horse/horse_frontend

    - ${{ if and(in(variables['Build.Reason'], 'Manual', 'Schedule', 'IndividualCI', 'BatchedCI'), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
      - template: templates/az-setup.yaml
      - script: |
          az acr login --name iridiumsdc
          just push
        displayName: 'Push Horse Frontend'
        workingDirectory: project/runtime/projects/horse/horse_frontend
