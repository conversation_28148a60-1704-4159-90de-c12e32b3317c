# Identities

## User onboarding service principal

**Application ID**: `581ce198-4fcd-4d9d-95e3-abd5d7580bd0`
**Application Name**: `iridium-onboarding`

We have a special service principal that is used to onboard users to the cluster. This service principal is created in the AME tenant and has the following permissions:

- `User.Read.All` - Read all users' full profiles
- `GroupMember.ReadWrite.All` - Read and write all group memberships
- `Application.ReadWrite.OwnedBy` - Read and write applications that the app owns

This service principal is used to create an application per user in the AME tenant, and generate a certificate for the user to use to authenticate to the cluster.

The process to create an applications is a 2 step process:
1- Manually create the application in the AME tenant.
2- Open a PR to get the application granted the necessary permissions. https://msazure.visualstudio.com/One/_git/TenantSec-IsoEng-CTAC/pullrequest/11259737


## iridium-builder msi

**MSI ID**: `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourcegroups/iridium-builder/providers/Microsoft.ManagedIdentity/userAssignedIdentities/msi_iridium_builder`

This is the MSI that is used on the 1ES agent pool [iridium-builder](https://dev.azure.com/project-argos/Mimco/_settings/agentqueues?queueId=860) to run the automation pipelines. This MSI has the following permissions:

| Role                                      | Scope                                                                                                    | Description                                              |
|-------------------------------------------|----------------------------------------------------------------------------------------------------------|----------------------------------------------------------|
| Contributor                               | Subscription-wide                                                                                       | Grants full access to manage all resources within the subscription to create and managed resources |
| Role Based Access Control Administrator   | Subscription-wide                                                                                       | Manages access control for resources within the subscription. |
| Storage Blob Data Contributor             | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-terraform/providers/Microsoft.Storage/storageAccounts/iridiumtfstate`                                                                      | To manage the terraform state. |
| Key Vault Certificate User                    | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-onboarding/providers/Microsoft.KeyVault/vaults/iridium-onboarding-app` | Allows to read the onboarding service principal (iridium-onbaording) certificate so that terraform can use the SP to execute certain operations related to managing user applications. |
| Storage Blob Data Contributor             | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-resources/providers/Microsoft.Storage/storageAccounts/iridiumoaiartifactsame` account                                                                             | To manage the gloval storage map. |
| Azure Kubernetes Service RBAC Cluster Admin | iridium clusters                                                              | Provides full admin access to manage AKS clusters to manage namespaces / configmaps / RBACs etc.     |
| Key Vault Secrets User  | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac/resourceGroups/iridium-tailscale/providers/Microsoft.KeyVault/vaults/iridiumtailscalekeys` | Allows to read the tailscale client id and secrets to run tailscale during the build process. |
| Key Vault Certificates Officer  | `/subscriptions/64467a16-0cdd-4a44-ad9b-83b5540828ac` | Allows to manage the certificate authorities. |