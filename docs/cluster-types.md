# Cluster Types

This repo supports building two distinct kinds of clusters: Iridium (`terraform/modules/iridium-cluster`) and Orange (`terraform/modules/orange-cluster`).
Clusters are built in three stages. Currently these stages are run in a single terraform invocation, but they can be run independently as well.
- `terraform/modules/stage-aks` which builds the actual AKS cluster, as well as configuring its virtual network and node pools
- `terraform/modules/stage-conn` which configures private endpoints, dns zones, role assignments, and everything else needed to connect the cluster to the data it needs
- `terraform/modules/stage-apps` which installs Brix, helm charts, kubernetes deployments and other in-cluster resources needed for Orange training

Iridium clusters are built from-scratch using all 3 of these stages, while Orange clusters take an existing Singularity AKS cluster and apply the later 2 layers to convert it for Orange training.

![Cluster Deployment Stages](cluster-stages.png)
