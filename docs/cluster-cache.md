# Cluster Cache

Cluster cache is a peer-to-peer cache service running in all workers and driver nodes. It is responsible for propagating the required chunks of data between the driver to all the workers, as required by the upload/download daemons. Workers can download both from drivers and other workers.

Currently cluster cache handles sharing of 2 types of data, dataset (default) and checkpoints.

There are 4 major components for cluster cache:

1. blobcache_svc

    The cluster-wide service deployed to k8s "scaling" namespace. blobcache_svc maintains the state of all hosts in the cluster's p2p disk cache. Currently the image is published to iridiumsdc.azurecr.io/infra/blobcache:0.1.

2. cachers_svc

    The cache server process runs in devboxes (driver/worker nodes) that heartbeats to blobcache-svc. It handles requests for dataset or checkpoints from peers.

3. blobcachecli

    Client code called during data loading loop within training. Use disk_cache library.

4. disk_cache

    The library used for disk caching, stores cache state on the disk rather than in memory.

## Branch Diff

- msft/staging/******** \
	Does not use Azure storage account, but chart still adds config map ref for "azure-storage-account", and secrete ref for "azure-service-principal".

- microsoft/staging \
	Use Azure storage account hard coded in this file.

```rust
const GLOBAL_KEY_ESCROW_STORAGE_ACCOUNT: &str = "oaiblobcache";
const GLOBAL_KEY_ESCROW_CONTAINER: &str = "key-escrow";
const SCALING_NAMESPACE: &str = "scaling";
```

## Cluster Cache in Strawberry

```mermaid
flowchart LR

subgraph driver[Driver]
	subgraph main process
	    run_qstar --> twapi[twapi]
	end
	subgraph Background Service
		upload_daemon --> cluster_cache["cluster_cache(cacher_svc)"]
	end
	twapi --> upload_daemon
end

subgraph worker["Worker (x N)"]
	subgraph Engine Main
		engine[Engine Manager] --> twapi2[twapi]
		twapi2 --> IXF
		sampler[Redis Sampler] <--> IXF
	end
	subgraph Background Service
		download_daemon <--> cluster_cache2["cluster_cache"]
		download_daemon --> engine
		cluster_cache --> download_daemon
	end
	subgraph "Rollout main (x M)"
		rollout_controller <--> sampler
	end
end

subgraph controller
	cluster_cache --register--> blobcache_svc
	cluster_cache2 --register--> blobcache_svc
end

run_qstar --> controller
controller --> worker

style driver color:#f66
style worker color:#f66
```
