# Splitting Terraform State Files for Multi-Stage Refactors

[https://medium.com/@adrianarba/terraform-how-i-split-my-monolithic-state-490916343dba](https://medium.com/@adrianarba/terraform-how-i-split-my-monolithic-state-490916343dba)

```bash
#!/bin/bash
#
# WARNING
# ########
# Manipulating terraform state files directly can lead to lost resource tracking if not done carefully.
# Ensure no other operations are being performed on state files (no other users, no pipelines, etc.)
# and take backups of all state files before attempting to migrate state.
# Don't run these commands unless you know what you're doing.
#
# This script is intended to run on a devbox with access to the cluster state file blob storage.
# It will split the state files for single-stage clusters into two separate state files.
# The important bit is the `terragrunt state mv` command, which takes a source resource
# and moves it to a destination in a separate local state file.
# This can be modified to split arbitrary modules into separate state files for refactoring purposes.

set -x

clusters=(prod-southcentralus-hpe-2 prod-southcentralus-hpe-3 prod-southcentralus-hpe-5 prod-uksouth-7 prod-uksouth-8 prod-uksouth-9 prod-uksouth-15 stage-southcentralus-hpe-1)
stages=(deploy-stage-conn deploy-stage-apps)

for cluster in "${clusters[@]}"
do
	echo "Splitting cluster $cluster..."
	cd ~/bootstrapping/terraform/clusters/$cluster
	touch terragrunt.hcl
	terragrunt init
	terragrunt state pull > local_backup.tfstate
	terragrunt state pull > local_working.tfstate

	for stage in "${stages[@]}"
	do
		cd ~/bootstrapping/terraform/clusters/$cluster/$stage
		touch terragrunt.hcl
		terragrunt init
		terragrunt state pull > local_backup.tfstate
		terragrunt state pull > local_working.tfstate
	done

	cd ~/bootstrapping/terraform/clusters/$cluster
	terragrunt state mv -state-out=deploy-stage-conn/local_working.tfstate "module.orange-cluster.module.stage-conn" "module.stage-conn"
	terragrunt state mv -state-out=deploy-stage-apps/local_working.tfstate "module.orange-cluster.module.stage-apps" "module.stage-apps"
	terragrunt state push -ignore-remote-version -force local_backup.tfstate

	for stage in "${stages[@]}"
	do
		cd ~/bootstrapping/terraform/clusters/$cluster/$stage
		terragrunt state push local_working.tfstate
		terragrunt init
	done
done

```