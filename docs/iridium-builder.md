# iridium-builder

The iridium-builder is a 1ES managed pool that is hosted under subscription `64467a16-0cdd-4a44-ad9b-83b5540828ac` under resource group `iridium-builder` and is assigned a managed identity `msi_iridium_builder`.

The msi is setup with multiple RBACs to be able to automate the infrastructure and application deployments. For more details refer to [identities.md](./identities.md).

The ubuntu used for builders base image can be found in [here](https://documentation.ubuntu.com/azure/azure-how-to/instances/find-ubuntu-images/).

## Tailscale
The builder runs tailscale as part of the build process to connect to the cluster. The tailscale client id and secrets are stored in the keyvault `iridiumtailscalekeys` under resource group `iridium-tailscale`. The builder is tagged with the tailscale tag `iridium-builder` which has access to the iridium clusters.

## Environments

We currently have setup Azure DevOps environments for the following environments:
- [iridium-user-onboarding](https://dev.azure.com/project-argos/Mimco/_environments/139) to manage approvals for user onboarding.