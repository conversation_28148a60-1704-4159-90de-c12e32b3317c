# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Script to automate creation of new Weights and Biases (WandB) user accounts."""

import argparse
import asyncio

from common import get_terraform_users
from logger import logger
from wandb_client import WandbClient


async def get_wandb_users(wandb_client: WandbClient):
    """Get the existing users aliases across all teams."""
    content = await wandb_client.get_users()
    users = {user["emails"]["Value"].split("@")[0] for user in content["Resources"]}
    return users


async def run(args: argparse.Namespace, wandb_client: WandbClient):
    wandb_users = await get_wandb_users(wandb_client=wandb_client)
    terraform_users = get_terraform_users(user_information=args.user_path)

    users_to_make = terraform_users - wandb_users
    user_creation_tasks = []
    for user in users_to_make:

        # Do not create new users for dry runs
        if args.dry_run:
            logger.info(f"Will create user {user}")
        else:
            user_creation_tasks.append(wandb_client.create_new_user(user=user))

    await asyncio.gather(*user_creation_tasks)
    logger.info("Users creation phase complete!")
