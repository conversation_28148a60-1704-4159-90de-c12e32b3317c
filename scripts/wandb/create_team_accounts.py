# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Script to automate creation of new Weights and Biases (WandB) team accounts."""

import argparse
import asyncio

from typing import Dict

from common import create_team_mapping
from logger import logger
from wandb_client import WandbClient


async def get_existing_team_accounts(wandb_client: WandbClient):
    """Get the existing teams in wandb and compile the IDs of the users in that team."""
    content = await wandb_client.get_teams()
    teams = {team["displayName"] for team in content["Resources"]}
    return teams


async def get_ids(wandb_client: WandbClient, user_team_mapping: Dict, is_dry_run: bool):
    """Retrieve the user IDs of all users in a team and map each team to a list of its corresponding user IDs."""
    content = await wandb_client.get_users()
    user_ids = {user["emails"]["Value"].split("@")[0]: user["id"] for user in content["Resources"]}

    user_ids_to_teams = {}
    for team, users in user_team_mapping.items():
        user_ids_to_teams[team] = []
        for user in users:
            user_id = user_ids.get(user, "")
            if not user_id:
                if is_dry_run:
                    logger.info(f"User {user} does not have a WandB id, but will during a main branch run")
                    continue
                raise Exception(f"User {user} is listed as member of team {team} but does not have an account")

            user_ids_to_teams[team].append(user_id)

    return user_ids_to_teams


async def run(args: argparse.Namespace, wandb_client: WandbClient):
    wandb_teams = await get_existing_team_accounts(wandb_client=wandb_client)
    users_in_teams = create_team_mapping(user_information=args.user_path)

    teams_to_make = set(users_in_teams.keys()) - wandb_teams
    ids_teams = await get_ids(wandb_client=wandb_client, user_team_mapping=users_in_teams, is_dry_run=args.dry_run)

    team_creation_tasks = []
    for team in teams_to_make:
        members = ids_teams.get(team, [])

        # Do not create new teams for dry runs
        if args.dry_run:
            logger.info(f"Will create team: {team} with members: {members}")
        else:
            team_creation_tasks.append(wandb_client.create_new_team(team_name=team, members=members))

    await asyncio.gather(*team_creation_tasks)
    logger.info("Teams creation phase complete :)")
