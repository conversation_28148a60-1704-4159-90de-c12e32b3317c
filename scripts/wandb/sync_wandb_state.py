#!/usr/bin/env -S uv run --script
# /// script
# requires-python = "==3.12"
# dependencies = [
#   "azure-identity",
#   "azure-keyvault-secrets",
#   "httpx",
#   "tenacity",
# ]
# ///

# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Script to run all Weights and Biases automations."""

import argparse
import asyncio
import base64
import httpx

from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential

import create_team_accounts
import create_user_accounts
import update_team_accounts
import remove_user_accounts
from wandb_client import WandbClient


async def main(args: argparse.Namespace):
    client = SecretClient(vault_url=args.vault_url, credential=DefaultAzureCredential())

    username = client.get_secret("wandb-username").value
    api_key = client.get_secret("wandb-api-key").value

    wandb_credentials = f"{username}:{api_key}"
    encoded_credentials = base64.b64encode(wandb_credentials.encode('utf-8')).decode('utf-8')
    headers = {
        "Authorization": f"Basic {encoded_credentials}"
    }

    # Create a single client to benefit from connection pooling
    async with httpx.AsyncClient(headers=headers) as httpx_client:
        wandb_client = WandbClient(client=httpx_client)

        await create_user_accounts.run(args=args, wandb_client=wandb_client)
        await create_team_accounts.run(args=args, wandb_client=wandb_client)
        await update_team_accounts.run(args=args, wandb_client=wandb_client)
        await remove_user_accounts.run(args=args, wandb_client=wandb_client)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("--vault-url", type=str, help="URL for the keyvault")
    parser.add_argument("--user-path", type=str, help="Path containing the info of users")
    parser.add_argument("--dry-run", choices=["True", "False"], help="If the run is a dry run (not run from main)")

    args = parser.parse_args()
    args.dry_run = args.dry_run == "True"

    asyncio.run(main(args))
