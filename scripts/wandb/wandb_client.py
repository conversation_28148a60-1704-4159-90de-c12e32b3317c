# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Weights and Biases Client."""

import httpx

from tenacity import retry, stop_after_attempt, wait_fixed
from typing import List

from logger import logger


class WandbClient:
    def __init__(self, client: httpx.AsyncClient):
        self.client = client
        self.team_url = "https://msaip.wandb.io/scim/Groups"
        self.user_url = "https://msaip.wandb.io/scim/Users"
        self.audit_logs_url = "https://msaip.wandb.io/admin/audit_logs"


    @retry(stop_after_attempt(3), wait_fixed(10))
    async def get_users(self):
        try:
            response = await self.client.get(url=self.user_url)
            response.raise_for_status()
        except Exception as e:
            raise Exception(f"Request to list users failed with error: {e}")
        return response.json()


    @retry(stop_after_attempt(3), wait_fixed(10))
    async def create_new_user(self, user: str):
        payload = {
            "schemas": [
                "urn:ietf:params:scim:schemas:core:2.0:User"
            ],
            "emails": [
                {
                    "primary": True,
                    "value": f"{user}@microsoft.com"
                }
            ],
            "userName": f"{user}"
        }
        logger.info(f"Creating user: {user}")
        try:
            response = await self.client.post(url=self.user_url, json=payload)
            response.raise_for_status()
        except Exception as e:
            raise Exception(f"Failed to create user with error: {e}")
        
    
    @retry(stop_after_attempt(3), wait_fixed(10))
    async def get_teams(self):
        try:
            response = await self.client.get(url=self.team_url)
            response.raise_for_status()
        except Exception as e:
            raise Exception(f"Request to list teams failed with error: {e}")
        return response.json()


    @retry(stop_after_attempt(3), wait_fixed(10))
    async def create_new_team(self, team_name: str, members: List[str]):
        formatted_members = [{"value": member} for member in members]
        payload = {
            "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
            "displayName": f"{team_name}",
            "members": formatted_members
        }
        logger.info(f"Creating team {team_name}")
        try:
            response = await self.client.post(url=self.team_url, json=payload)
            response.raise_for_status()
        except Exception as e:
            raise Exception(f"Failed to create team with error: {e}")
        logger.info(f"Team {team_name} created!")


    @retry(stop_after_attempt(3), wait_fixed(10))
    async def add_user_to_team(self, user_id: str, team_id: str):
        payload = {
            "schemas": ["urn:ietf:params:scim:api:messages:2.0:PatchOp"],
            "Operations": [
                {
                    "op": "add",
                    "path": "members",
                    "value": [
                        {
                            "value": user_id,
                        }
                    ]
                }
            ]
        }
        logger.info(f"Adding user with user id {user_id} to team with team id {team_id}")
        url = self.team_url + f"/{team_id}"
        try:
            response = await self.client.patch(url=url, json=payload)
            response.raise_for_status()
        except Exception as e:
            raise Exception(f"Adding {user_id} to team {team_id} failed with error: {e}")
        logger.info(f"Added user with user id {user_id} to team with team id {team_id}")


    @retry(stop_after_attempt(3), wait_fixed(10))
    async def delete_user(self, user_id: str):
        url = self.user_url + f"/{user_id}"
        logger.info(f"Deleting user with user id: {user_id}")
        try:
            response = await self.client.delete(url=url)
            response.raise_for_status()
        except Exception as e:
            raise Exception(f"Request to delete user {user_id} failed with error: {e}")
        logger.info(f"Successfully deleted user with user id: {user_id}")
