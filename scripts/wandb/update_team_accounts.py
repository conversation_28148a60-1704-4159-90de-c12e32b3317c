# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Script to automate adding users to already existing teams in Weights and Biases."""

import argparse
import asyncio

from typing import List

from common import create_team_mapping
from logger import logger
from wandb_client import WandbClient


async def get_wand_team_ids(wandb_client: WandbClient):
    content = await wandb_client.get_teams()
    team_ids = {team["displayName"]: team["id"] for team in content["Resources"]}
    return team_ids


def create_wandb_mappings(user_info: List):
    team_user_id_mapping = {}
    user_id_mapping = {}
    for user in user_info:
        user_id = user["id"]
        user_alias = user["emails"]["Value"].split("@")[0]
        user_id_mapping[user_alias] = user_id

        # New users will not yet be in a team
        teams_roles = user.get("teamRoles", [])
        for team in teams_roles:
            team_name = team["teamName"]
            if team_name in team_user_id_mapping:
                team_user_id_mapping[team_name].append(user_id)
            else:
                team_user_id_mapping[team_name] = [user_id]

    return team_user_id_mapping, user_id_mapping


async def run(args: argparse.Namespace, wandb_client: WandbClient):
    user_info = await wandb_client.get_users()
    wandb_team_ids = await get_wand_team_ids(wandb_client=wandb_client)

    wandb_team_user_ids, wandb_user_ids = create_wandb_mappings(user_info=user_info["Resources"])
    orange_team_users = create_team_mapping(user_information=args.user_path)

    team_update_tasks = []
    for team, users in orange_team_users.items():
        for user in users:
            user_id = wandb_user_ids.get(user, "")
            team_id = wandb_team_ids.get(team, "")
            team_members = wandb_team_user_ids.get(team, [])

            # For dry runs, new users and new teams will not have an id yet
            if user_id and team_id and user_id not in team_members:

                # Do not update teams on dry runs
                if args.dry_run:
                    logger.info(f"Will add {user} to team {team}")
                else:
                    team_update_tasks.append(wandb_client.add_user_to_team(user_id=user_id, team_id=team_id))

    await asyncio.gather(*team_update_tasks)
    logger.info("Teams update phase complete :)")
