# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Shared functions in WandB scripts."""

import json

from typing import Dict, Set


def get_terraform_users(user_information: str) -> Set[str]:
    with open(user_information, 'r') as f:
        content = json.load(f)
        user_info = content["users"]["value"]
        users = set(user_info.keys())
    return users


def create_team_mapping(user_information: str) -> Dict:
    """Map each team to a list of its corresponding members"""
    team_to_user = {}
    with open(user_information, 'r') as f:
        content = json.load(f)
        user_info = content["users"]["value"]

        for user, team_info in user_info.items():
            teams = team_info.get("teams", [])
            for team in teams:
                team_name = team.split("-")[-1].lower()
                if team_name in team_to_user:
                    team_to_user[team_name].append(user)
                else:
                    team_to_user[team_name] = [user]

    return team_to_user
