# ---------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# ---------------------------------------------------------

"""Script to automate account removal of offboarded users in Weights and Biases (WandB)."""

import argparse
import asyncio

from common import get_terraform_users
from logger import logger
from wandb_client import WandbClient


async def get_wandb_users(wandb_client: WandbClient):
    content = await wandb_client.get_users()
    user_info = {user["emails"]["Value"].split("@")[0]: user["id"] for user in content["Resources"]}
    return user_info


async def run(args: argparse.Namespace, wandb_client: WandbClient):
    orange_users = get_terraform_users(user_information=args.user_path)
    wandb_users_info = await get_wandb_users(wandb_client=wandb_client)

    # Delete users that exist in wandb that are not present in orange
    users_to_delete = set(wandb_users_info.keys()) - orange_users

    delete_tasks = []
    for user in users_to_delete:
        user_id = wandb_users_info[user]

        # Do not delete users in PR builds
        if args.dry_run:
            logger.info(f"Will delete user {user}'s account in WandB with user id {user_id}")
        else:
            delete_tasks.append(wandb_client.delete_user(user_id=user_id))

    await asyncio.gather(*delete_tasks)
    logger.info(f"User offboarding phase complete :)")
