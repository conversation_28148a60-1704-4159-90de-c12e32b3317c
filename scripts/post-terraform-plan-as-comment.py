#!/usr/bin/env -S uv run --script
# /// script
# dependencies = [
#   "aiohttp"
# ]
# ///

import os
import sys
import re
import json
import asyncio
import datetime
import aiohttp

# ----------------------------------------------------------------
# TerraformPlan: processes Terraform plan outputs and generates markdown.
# ----------------------------------------------------------------
class TerraformPlan:
    def __init__(self, plan_json_path: str, plan_diff_path: str):
        self.plan_json_path = plan_json_path
        self.plan_diff_path = plan_diff_path
        self.workspace = self.extract_workspace(plan_json_path)
        self.plan_data = self.load_json()
        self.diff = self.load_diff()
        if len(self.diff) > 14000:
            self.diff = self.diff[:14000] + "\n\nDiff truncated due to size limit. Please check the full diff in the pipeline logs."

    def extract_workspace(self, path: str) -> str:
        return os.path.basename(os.path.dirname(path)) or "unknown"

    def load_json(self) -> dict:
        with open(self.plan_json_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def load_diff(self) -> str:
        with open(self.plan_diff_path, 'r', encoding='utf-8') as f:
            return f.read()

    def count_changes(self):
        added = sum(1 for r in self.plan_data.get("resource_changes", []) 
                    if "create" in r.get("change", {}).get("actions", []))
        changed = sum(1 for r in self.plan_data.get("resource_changes", []) 
                      if "update" in r.get("change", {}).get("actions", []))
        destroyed = sum(1 for r in self.plan_data.get("resource_changes", []) 
                        if "delete" in r.get("change", {}).get("actions", []))
        return added, changed, destroyed

    def has_changes(self) -> bool:
        added, changed, destroyed = self.count_changes()
        return any([added, changed, destroyed])

    def plan_summary(self) -> str:
        added, changed, destroyed = self.count_changes()
        if not self.has_changes():
            return "💤 No changes"
        return f"🔄 Changes: {f'+{added}' if added > 0 else '0'} added, " \
               f"{f'~{changed}' if changed > 0 else '0'} changed, " \
               f"{f'-{destroyed}' if destroyed > 0 else '0'} destroyed"

    def generate_markdown(self) -> str:
        summary = self.plan_summary()
        now = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        unique_marker = f"<!-- terraform-plan-comment:{self.workspace} -->"
        markdown = f"""## Terraform Plan Results {"💤" if summary=="💤 No changes" else "🚀"}

| Module    | `{self.workspace}` |
|-----------|---------------------|
| Status    | **{summary}**       |
| Timestamp | {now}               |

<details>
<summary><strong>📝 Plan Diff</strong></summary>

```
{self.diff}
```

</details>

---

🔍 **Next Steps:**
* To apply these changes, complete and merge this pull request
* For issues, check the pipeline run details
* Learn more about [Terraform Plans](https://www.terraform.io/docs/cli/commands/plan.html)

{unique_marker}
"""
        return markdown

# ----------------------------------------------------------------
# AzureDevOpsClient: wraps the Azure DevOps REST API interactions.
# ----------------------------------------------------------------
class AzureDevOpsClient:
    def __init__(self, collection_uri: str, team_project: str, repository_id: str, pullrequest_id: str, access_token: str):
        self.collection_uri = collection_uri.rstrip('/')
        self.team_project = team_project
        self.repository_id = repository_id
        self.pullrequest_id = pullrequest_id
        self.access_token = access_token
        self.api_version = "7.0"
        self.base_url = f"{self.collection_uri}/{self.team_project}/_apis/git/repositories/{self.repository_id}" \
                        f"/pullRequests/{self.pullrequest_id}/threads"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}"
        }

    async def get_threads(self, session: aiohttp.ClientSession) -> dict:
        url = f"{self.base_url}?api-version={self.api_version}"
        async with session.get(url, headers=self.headers) as resp:
            if resp.status != 200:
                text = await resp.text()
                raise Exception(f"GET threads failed: {resp.status} {text}")
            return await resp.json()

    async def update_comment(self, session: aiohttp.ClientSession, thread_id: int, comment_id: int, content: str):
        url = f"{self.base_url}/{thread_id}/comments/{comment_id}?api-version={self.api_version}"
        payload = {"content": content}
        async with session.patch(url, headers=self.headers, json=payload) as resp:
            if resp.status not in (200, 204):
                text = await resp.text()
                raise Exception(f"PATCH comment failed: {resp.status} {text}")
            return await resp.json()

    async def delete_comment(self, session: aiohttp.ClientSession, thread_id: int, comment_id: int):
        """Delete a comment in a thread. This can effectively delete the thread if it's the only comment."""
        url = f"{self.base_url}/{thread_id}/comments/{comment_id}?api-version={self.api_version}"
        async with session.delete(url, headers=self.headers) as resp:
            if resp.status not in (200, 204):
                text = await resp.text()
                raise Exception(f"DELETE comment failed: {resp.status} {text}")
            return True

    async def create_thread(self, session: aiohttp.ClientSession, content: str, resolved: bool):
        url = f"{self.base_url}?api-version={self.api_version}"
        payload = {
            "comments": [
                {
                    "parentCommentId": 0,
                    "content": content,
                    "commentType": 1
                }
            ],
            "status": 4 if resolved else 1  # 4 = resolved, 1 = active
        }
        async with session.post(url, headers=self.headers, json=payload) as resp:
            if resp.status not in (200, 201):
                text = await resp.text()
                raise Exception(f"POST thread failed: {resp.status} {text}")
            return await resp.json()

# ----------------------------------------------------------------
# Main async workflow to process the plan and update/create the PR comment.
# ----------------------------------------------------------------
async def main():
    plan_json_path = os.getenv("PLAN_JSON_PATH", "terraform_plan.json")
    plan_diff_path = os.getenv("PLAN_DIFF_PATH", "terraform_plan_diff.txt")
    collection_uri = os.getenv("AZDO_COLLECTION_URI") or os.getenv("System.CollectionUri")
    team_project = os.getenv("AZDO_TEAM_PROJECT") or os.getenv("System.TeamProject")
    repository_id = os.getenv("AZDO_REPOSITORY_ID") or os.getenv("Build.Repository.ID")
    pullrequest_id = os.getenv("AZDO_PULLREQUEST_ID") or os.getenv("System.PullRequest.PullRequestId")
    access_token = os.getenv("AZDO_ACCESS_TOKEN") or os.getenv("System.AccessToken")
    delete_thread_before_create = os.getenv("DELETE_THREAD_BEFORE_CREATE", "true").lower() == "true"

    if not all([collection_uri, team_project, repository_id, pullrequest_id, access_token]):
        print("Missing required Azure DevOps environment variables.")
        sys.exit(1)

    tf_plan = TerraformPlan(plan_json_path, plan_diff_path)
    markdown = tf_plan.generate_markdown()
    unique_marker = f"<!-- terraform-plan-comment:{tf_plan.workspace} -->"
    if not tf_plan.has_changes():
        print("No changes detected in the Terraform plan; exiting.")
        return

    client = AzureDevOpsClient(collection_uri, team_project, repository_id, pullrequest_id, access_token)

    async with aiohttp.ClientSession() as session:
        threads = await client.get_threads(session)
        matching_threads = []
        
        # Find all matching threads/comments with the unique marker
        if "value" in threads:
            for thread in threads["value"]:
                for comment in thread.get("comments", []):
                    if unique_marker in comment.get("content", ""):
                        matching_threads.append({
                            "thread_id": thread["id"],
                            "comment_id": comment.get("id")
                        })
                        break
        
        # Handle case where matching threads are found
        if matching_threads:
            if delete_thread_before_create:
                print(f"Found {len(matching_threads)} matching threads; deleting all as DELETE_THREAD_BEFORE_CREATE=true")
                for match in matching_threads:
                    thread_id = match["thread_id"]
                    comment_id = match["comment_id"]
                    print(f"Deleting comment [{comment_id}] in thread [{thread_id}]")
                    await client.delete_comment(session, thread_id, comment_id)
                
                print("Creating new thread after deletion.")
                # Marking thread as resolved to avoid too many open threads
                await client.create_thread(session, markdown, True)
        else:
            print("No existing comment thread found; creating a new thread.")
            # Marking thread as resolved to avoid too many open threads
            await client.create_thread(session, markdown, True)
        print("Operation completed.")

if __name__ == "__main__":
    asyncio.run(main())
