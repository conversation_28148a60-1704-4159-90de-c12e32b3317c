Dealing with SciClone
---
There are many places where code tries to access data. OAI has SciClone which manages data movements by performing regional movement, cloning and synchronization. These will fail due to hard-coded Azure Storage paths pointing to OAI accounts.

Also, data movement will fail because SciClone is an independent supercomputing service. Code will attempt to reach out to this service to request data movement, which will also fail. Typically the error will be attempting to get a token in `operations.py`.

In either of the cases, we can use [`storage-map.json`](storage-map.json) to trick it into thinking that either the data exist in our region, or the data has already been cloned.

### Bypassing data cloning of unnecessary datasets
In many code instances, OAI code will attempt to localize datasets in modele `__init__.py` with no chance of parameterizing the location. In many cases, the use-case does not actually need the data.

To work around the problem, we trick SciClone to think that the data already cloned:

example: `az://oaidatasets2/data-gym/encodings`

First we add an entry in `storage-map.json` [template in terraform](terraform/modules/storage-map/template/storage-map.json):

```json
    "az://oaidatasets2/data-gym/encodings": {
      "home": "az://oaidatasets2/data-gym/encodings",
      "homes": {
        "nowhereregion": "az://oaidatasets2/data-gym/encodings"
      },
      "clones": {
        "uksouth": "az://iridiumdata/data-gym/encodings"
      }
    }
```
then PR and merge the changes. This will trigger a pipeline to update the storag map via terraform.

Then we create the necessary Storage Blob containers and dummy SciClone files. See [this source file](https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/lib/sciclone_utils/sciclone_utils/operations.py&version=GBmicrosoft/staging&line=458&lineEnd=475&lineStartColumn=1&lineEndColumn=54&lineStyle=plain&_a=contents) for information about SciClone cloning markers:

```bash
$ az storage container create --auth-mode login --account-name iridiumdata --name data-gym
$ az storage blob upload --auth-mode login --blob-url https://iridiumdata.blob.core.windows.net/data-gym/.sciclone/encodings/.sciclone_complete -f /tmp/empty
$ az storage blob upload --auth-mode login --blob-url https://iridiumdata.blob.core.windows.net/data-gym/applied-encodings/placeholder -f /tmp/empty
```
